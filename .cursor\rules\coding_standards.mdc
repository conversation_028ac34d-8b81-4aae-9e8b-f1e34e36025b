---
description: 
globs: 
alwaysApply: true
---
{
  "name": "编码规范",
  "description": "多租户前后端分离框架系统的编码规范",
  "glob": "**/*.php"
}

# 编码规范

本项目遵循PSR-2编码规范和PSR-4自动加载规范，并有一些项目特定的规范。

## 命名规范

### PHP命名规范

- **类名**: 使用大驼峰命名法(PascalCase)
  - 控制器: `AdminController`
  - 模型: `AdminModel`
  - 服务: `AdminService`

- **方法名**: 使用小驼峰命名法(camelCase)
  - 获取数据: `getAdminInfo()`
  - 保存数据: `saveAdminInfo()`
  - 删除数据: `deleteAdmin()`

- **变量名**: 使用小驼峰命名法(camelCase)
  - `$userId`
  - `$adminName`
  - `$roleList`

- **常量名**: 使用大写下划线命名法(SNAKE_CASE)
  - `MAX_LOGIN_ATTEMPTS`
  - `DEFAULT_PASSWORD`

### 数据库命名规范

- **表名**: 使用下划线命名法(snake_case)，使用前缀`system_`
  - `system_admin`
  - `system_role`
  - `system_menu`

- **字段名**: 使用下划线命名法(snake_case)
  - `user_id`
  - `login_time`
  - `create_at`

## 注释规范

### 类注释

```php
/**
 * 管理员服务类
 * 
 * 提供管理员相关的业务逻辑处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class AdminService
{
    // 类内容
}
```

### 方法注释

```php
/**
 * 获取管理员信息
 * 
 * @param int $adminId 管理员ID
 * @return array 管理员信息
 * @throws \Exception 当管理员不存在时抛出异常
 */
public function getAdminInfo(int $adminId): array
{
    // 方法内容
}
```

## 代码格式

- 缩进使用4个空格，不使用Tab
- 行宽不超过120个字符
- 运算符两侧添加空格
- 控制结构关键字后必须有一个空格
- 函数调用时，函数名与左括号之间不能有空格

## 控制器规范

- 控制器方法应该尽量简洁，主要业务逻辑应放在服务层
- 控制器负责参数验证和返回结果
- 返回统一使用JSON格式

```php
/**
 * 获取管理员列表
 */
public function index()
{
    $params = $this->request->param();
    $list = $this->adminService->getList($params);
    
    return json([
        'code' => 0,
        'message' => 'success',
        'data' => $list
    ]);
}
```

## 服务层规范

- 服务层负责具体业务逻辑实现
- 服务层方法应该是原子的，一个方法只做一件事
- 复杂业务逻辑应该拆分为多个小方法

## 模型层规范

- 模型负责数据库操作
- 字段定义、关联关系定义应该在模型中完成
- 复杂的SQL查询应该使用查询构造器

## 异常处理规范

- 使用try-catch捕获异常
- 自定义异常类型，便于区分不同类型的异常
- 异常信息应该明确，便于定位问题
