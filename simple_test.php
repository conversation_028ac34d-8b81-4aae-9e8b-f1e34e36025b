<?php
// 简化的工作流表单测试
require_once 'vendor/autoload.php';

use app\workflow\factory\DynamicWorkflowFactory;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

echo "🚀 开始测试工作流表单...\n\n";

// 测试业务类型
$businessCodes = [
    'hr_business_trip',
    'hr_outing', 
    'office_sample_mail',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'ims_outbound_approval',
    'ims_shipment_approval'
];

foreach ($businessCodes as $businessCode) {
    echo "📝 测试 {$businessCode}...\n";
    
    try {
        $service = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
        
        if ($service && $service instanceof \app\workflow\interfaces\FormServiceInterface) {
            echo "  ✅ Service创建成功: " . get_class($service) . "\n";
        } else {
            echo "  ❌ Service创建失败\n";
        }
        
    } catch (Exception $e) {
        echo "  ❌ 错误: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "测试完成！\n";
