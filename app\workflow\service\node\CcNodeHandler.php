<?php
declare(strict_types=1);

namespace app\workflow\service\node;

use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;
// WorkflowOperationConstant已合并到WorkflowStatusConstant
use app\workflow\service\WorkflowContext;
use app\workflow\service\WorkflowEngine;
use app\workflow\service\WorkflowHistoryService;
use app\workflow\service\WorkflowTaskService;
use app\workflow\model\WorkflowTask;
use app\workflow\model\WorkflowHistory;
use think\facade\Log;
use app\workflow\service\WorkflowInstanceService;
use app\system\service\AdminService;
use think\facade\Db;

/**
 * 抄送节点处理器
 */
class CcNodeHandler extends AbstractNodeHandler
{
	/**
	 * 处理抄送节点
	 *
	 * @param array           $instance 工作流实例
	 * @param array           $node     当前节点
	 * @param WorkflowContext $context  工作流上下文
	 * @return bool
	 */
	protected function handleNode(array $instance, array $node, WorkflowContext $context): bool
	{
		Log::info('处理抄送节点: ' . ($node['nodeName'] ?? '未知') . ', 节点ID: ' . ($node['nodeId'] ?? '未知'));
		Log::info('抄送节点详情: ' . json_encode($node));
		
		// 验证节点类型是否为抄送节点
		if (($node['type'] ?? '') != WorkflowStatusConstant::NODE_TYPE_COPYER && ($node['type'] ?? '') != (string)WorkflowStatusConstant::NODE_TYPE_COPYER) {
			Log::error('节点类型错误，期望抄送节点(type=' . WorkflowStatusConstant::NODE_TYPE_COPYER . ')，实际类型: ' . ($node['type'] ?? '未知'));
			return false;
		}
		
		// 创建抄送任务
		$this->createCcTasks($instance, $node);
		
		// 处理抄送节点的下一个节点（如果有）
		if (!empty($node['childNode'])) {
			Log::info('抄送节点处理完成，继续处理下一节点: ' . ($node['childNode']['nodeName'] ?? '未知'));
			return $this->handleNextNode($instance, $node['childNode'], $context);
		}
		
		// 如果没有下一个节点，完成工作流
		Log::info('抄送节点处理完成，没有下一节点，流程结束');
		return $this->completeWorkflow($instance, $context);
	}
	
	/**
	 * 创建抄送任务
	 *
	 * @param array $instance 工作流实例
	 * @param array $node     抄送节点
	 * @return void
	 */
	protected function createCcTasks(array $instance, array $node): void
	{
		// 获取抄送人列表
		$ccUsers = $node['nodeUserList'] ?? [];
		if (empty($ccUsers)) {
			Log::error('抄送人列表为空: ' . json_encode($node));
			return;
		}
		
		Log::info('开始创建抄送任务，节点: ' . ($node['nodeName'] ?? '未知') . ', 抄送人数: ' . count($ccUsers));
		
		// 记录当前上下文信息
		Log::info('CcNodeHandler 上下文信息', [
			'admin_id'                      => request()->adminId ?? 0,
			'tenant_id'                     => request()->tenantId ?? 0,
			'effective_tenant_id'           => get_effective_tenant_id(),
			'should_apply_tenant_isolation' => should_apply_tenant_isolation()
		]);
		
		// 更新当前节点 - 直接实例化模型
		$instanceModel = new \app\workflow\model\WorkflowInstance();
		$instanceModel->where('id', $instance['id'])
		              ->update(['current_node' => $node['nodeId']]);
		
		// 记录已处理的用户，避免重复处理
		$processedUsers = [];
		
		foreach ($ccUsers as $user) {
			// 检查用户ID是否有效
			$userId = isset($user['id'])
				? intval($user['id'])
				: 0;
			if ($userId <= 0) {
				Log::warning('无效的抄送用户ID: ' . json_encode($user));
				continue;
			}
			
			// 避免重复处理同一用户
			if (in_array($userId, $processedUsers)) {
				Log::info('跳过重复的抄送用户: ' . $userId);
				continue;
			}
			$processedUsers[] = $userId;
			
			// 直接实例化模型查询任务记录，BaseModel自动应用租户隔离
			$taskModel    = new WorkflowTask();
			$existingTask = $taskModel->where([
				'instance_id' => $instance['id'],
				'node_id'     => $node['nodeId'],
				'approver_id' => $userId,
				'task_type'   => WorkflowStatusConstant::TASK_TYPE_CC
			])
			                          ->find();
			
			if ($existingTask) {
				Log::info('该用户已有抄送任务记录，跳过: 用户ID=' . $userId);
				continue;
			}
			
			// 获取用户姓名
			$userName = $this->getUserName($userId);
			
			// 创建抄送任务
			$taskData = [
				'task_id'       => 'cc_' . md5($instance['id'] . $node['nodeId'] . $userId . microtime(true)),
				'instance_id'   => $instance['id'],
				'process_id'    => $instance['process_id'],
				'node_id'       => $node['nodeId'],
				'node_name'     => $node['nodeName'] ?? '抄送节点',
				'node_type'     => 'cc',
				'task_type'     => WorkflowStatusConstant::TASK_TYPE_CC,
				'approver_id'   => $userId,
				'approver_name' => $userName,
				'status'        => 0,
				// 待处理
				'sort'          => 0,
				'created_at'    => date('Y-m-d H:i:s'),
				'tenant_id'     => $instance['tenant_id'] ?? 0
			];
			
			Log::info('创建抄送任务数据: ' . json_encode($taskData));
			
			try {
				// 直接实例化模型创建任务，BaseModel自动处理租户隔离和数据权限
				$newTaskModel = new WorkflowTask();
				$taskId       = $newTaskModel->saveByCreate($taskData);
				
				if (!$taskId) {
					Log::error('抄送任务创建失败，没有返回ID');
					continue;
				}
				
				Log::info('抄送任务创建成功，ID: ' . $taskId);
				
				// 直接实例化模型查询历史记录，BaseModel自动应用租户隔离
				$historyModel    = new WorkflowHistory();
				$existingHistory = $historyModel->where([
					'instance_id' => $instance['id'],
					'node_id'     => $node['nodeId'],
					'operator_id' => $userId,
					'operation'   => WorkflowStatusConstant::OPERATION_CC
				])
				                                ->find();
				
				if ($existingHistory) {
					Log::info('该用户已有抄送历史记录，跳过创建: 用户ID=' . $userId);
					continue;
				}
				
				// 记录任务历史
				$historyData = [
					'instance_id'    => $instance['id'],
					'process_id'     => $instance['process_id'],
					'task_id'        => $taskData['task_id'],
					'node_id'        => $node['nodeId'],
					'node_name'      => $node['nodeName'] ?? '抄送节点',
					'node_type'      => 'cc',
					'operator_id'    => $userId,
					'operation'      => WorkflowStatusConstant::OPERATION_CC,
					'remark'         => '抄送任务',
					'operation_time' => date('Y-m-d H:i:s'),
					'tenant_id'      => $instance['tenant_id'] ?? 0
				];
				
				// 直接实例化模型创建历史记录，BaseModel自动处理租户隔离和数据权限
				$newHistoryModel = new WorkflowHistory();
				$historyId       = $newHistoryModel->saveByCreate($historyData);
				Log::info('抄送历史记录创建结果: ' . ($historyId
						? '成功，ID: ' . $historyId
						: '失败'));
				
				// 查询任务详情以获取完整信息 - 直接实例化模型
				$taskDetailModel = new WorkflowTask();
				$taskInfo        = $taskDetailModel->find($taskId);
				
				// 发送任务通知（暂时跳过，避免通知系统的模型状态污染）
				try {
					$notificationResult = $this->sendCcNotification($instance, $taskInfo
						? $taskInfo->toArray()
						: $taskData, $user);
					Log::info('发送抄送通知结果: ' . ($notificationResult
							? '成功'
							: '失败'));
				}
				catch (\Exception $notifyException) {
					Log::warning('发送抄送通知失败，但不影响主流程: ' . $notifyException->getMessage());
					// 通知失败不影响主流程
				}
			}
			catch (\Exception $e) {
				Log::error('创建抄送任务异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
			}
		}
	}
	
	/**
	 * 发送抄送通知
	 *
	 * @param array $instance 工作流实例
	 * @param array $task     任务数据
	 * @param array $user     抄送人
	 * @return bool
	 */
	protected function sendCcNotification(array $instance, array $task, array $user): bool
	{
		try {
			Log::info('准备发送抄送通知, 用户ID: ' . $user['id'] . ', 用户名: ' . $user['name']);
			
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用扁平结构，确保所有字段都存在
			$variables = [
				'title'          => $instance['title'] ?? '未知标题',
				// 流程标题
				'submitter_name' => $instance['submitter_name'] ?? '',
				// 提交人姓名
				'node_name'      => $task['node_name'] ?? '未知节点',
				// 节点名称
				'created_at'     => $instance['created_at'] ?? date('Y-m-d H:i:s'),
				// 创建时间
				'cc_time'        => date('Y-m-d H:i:s'),
				// 抄送时间
				'detail_url'     => '/workflow/task/detail?id=' . $task['id']
				// 详情链接
			];
			
			Log::info('发送抄送通知, 通知变量: ' . json_encode($variables));
			
			// 发送抄送通知
			$noticeService->send(WorkflowStatusConstant::MODULE_NAME, WorkflowStatusConstant::MESSAGE_TASK_CC, $variables, [$user['id']]);
			
			Log::info('抄送通知发送成功');
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送抄送通知失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
			return false;
		}
	}
	
	/**
	 * 完成工作流
	 *
	 * @param array           $instance 工作流实例
	 * @param WorkflowContext $context  工作流上下文
	 * @return bool
	 */
	protected function completeWorkflow(array $instance, WorkflowContext $context): bool
	{
		Log::info('抄送节点完成工作流: 实例ID=' . $instance['id']);
		
		// 从上下文或实例中获取当前任务ID
		// 抄送节点完成时，需要排除刚刚完成的审批任务
		$currentTaskId = (new WorkflowTask())->where([
				'instance_id' => $instance['id'],
				'task_type'   => 0,
				// 审批任务
				'status'      => 1
				// 已完成
			])
		                                     ->order('handle_time', 'desc')
		                                     ->limit(1)
		                                     ->value('id');
		// 调用工作流引擎的完成方法，传递要排除的任务ID
		$engine = new WorkflowEngine();
		return $engine->completeWorkflow($instance, (int)$currentTaskId);
	}
	
	/**
	 * 获取用户姓名
	 *
	 * @param int $userId 用户ID
	 * @return string
	 */
	private function getUserName(int $userId): string
	{
		try {
			$admin = Db::name('system_admin')
			           ->where('id', $userId)
			           ->field('real_name, username')
			           ->find();
			
			if ($admin) {
				return $admin['real_name']
					?: $admin['username'];
			}
			
			return '';
		}
		catch (\Exception $e) {
			Log::error('获取用户姓名失败: ' . $e->getMessage());
			return '';
		}
	}
}