<?php
declare(strict_types=1);

namespace app\common\core\traits\model;

use think\db\BaseQuery as Query;
use think\model\concern\SoftDelete;

/**
 * 租户隔离与软删除组合 Trait
 * 
 * 解决 SoftDelete trait 与 BaseModel 中租户隔离功能的方法冲突问题
 * 同时支持软删除和租户隔离功能
 */
trait TenantSoftDelete
{
    // 引入软删除 trait
    use SoftDelete;
    
    /**
     * 获取当前模型的数据库查询对象
     * 同时支持软删除和租户隔离功能
     *
     * @param array $scope 设置不使用的全局查询范围
     * @return Query
     */
    public function db($scope = []): Query
    {
        // 先调用 Model 的 db 方法获取基础查询对象
        $query = parent::db($scope);

        // 手动应用软删除条件（复制自 SoftDelete trait 的 withNoTrashed 方法）
        $field = $this->getDeleteTimeField(true);
        if ($field) {
            $condition = is_null($this->defaultSoftDelete) ? ['null', ''] : ['=', $this->defaultSoftDelete];
            $query->useSoftDelete($field, $condition);
        }

        // 应用租户隔离条件（如果启用）
        if (property_exists($this, 'enableTenantIsolation') && $this->enableTenantIsolation) {
            $this->applyTenantIsolation($query);
        }

        return $query;
    }
    
    /**
     * 应用租户隔离到查询对象
     *
     * @param Query $query 查询对象
     * @return void
     */
	protected function applyTenantIsolation(Query $query): void
	{
		// 获取租户字段名
		$tenantField = property_exists($this, 'tenantField') ? $this->tenantField : 'tenant_id';
		
		// 当前表如果存在租户字段，则应用租户隔离
		
		
		// 获取有效的租户ID（考虑租户切换）
		$effectiveTenantId = get_effective_tenant_id();
		
		// 断言：TokenAuthMiddleware已经确保了tenant_id >= 0
		assert($effectiveTenantId >= 0, 'Effective tenant ID should be >= 0 after TokenAuthMiddleware validation');
		
		// 应用租户隔离（tenant_id=0也是有效租户）
		$query->where($tenantField, $effectiveTenantId);
	}
}
