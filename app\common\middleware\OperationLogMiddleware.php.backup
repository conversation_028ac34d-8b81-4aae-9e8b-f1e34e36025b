<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\system\service\OperationLogService;
use app\system\service\PermissionService;
use app\system\model\MenuModel;
use app\common\utils\CacheUtil;
use think\facade\App;
use think\facade\Config;
use think\Response;

/**
 * 操作日志中间件
 */
class OperationLogMiddleware
{
	/**
	 * 操作日志服务
	 *
	 * @var \app\system\service\OperationLogService
	 */
	protected $logService;
	
	/**
	 * 不需要记录日志的请求方法
	 *
	 * @var array
	 */
	protected $exceptMethods = [
		'OPTIONS',
		'HEAD'
	];
	
	/**
	 * 不需要记录日志的路由
	 *
	 * @var array
	 */
	protected $exceptRoutes = [];
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->logService    = OperationLogService::getInstance();
		$this->exceptRoutes  = Config::get('log.operation.except_routes', []);
		$this->exceptMethods = Config::get('log.operation.except_methods', [
			'OPTIONS',
			'HEAD'
		]);
	}
	
	/**
	 * 处理请求
	 *
	 * @param \think\Request $request
	 * @param \Closure       $next
	 * @return Response
	 */
	public function handle($request, \Closure $next)
	{
		// 获取请求信息
		$method = $request->method();
		
		// 如果是不需要记录的请求方法，直接跳过
		if (in_array($method, $this->exceptMethods)) {
			return $next($request);
		}
		
		// 获取当前路由
		$route = $request->pathinfo();
		
		// 如果是不需要记录的路由，直接跳过
		foreach ($this->exceptRoutes as $exceptRoute) {
			if (str_starts_with($route, $exceptRoute)) {
				return $next($request);
			}
		}
		
		// 记录开始时间
		$startTime = microtime(true);
		
		// 执行下一个中间件或控制器，获取响应
		$response = $next($request);
		
		// 计算执行时间
		$executionTime = intval((microtime(true) - $startTime) * 1000);
		
		[
			$controller,
			$action
		] = explode('@', $request->rule()
		                         ->getName());
		
		
		// 处理请求参数，过滤敏感信息
		$params = $request->param();
		$params = $this->filterSensitiveData($params);
		
		// 获取用户信息
		$admin = $request->adminInfo ?? null;
		
		$adminData = $admin['data'] ?? [];
		$adminId   = $adminData['id'] ?? 0;
		$tenantId  = $adminData['tenant_id'] ?? 0;
		// 构建日志数据
		$logData = [
			'admin_id'       => $adminId,
			'controller'     => $controller,
			'action'         => $action,
			'url'            => $route,
			'method'         => $method,
			'params'         => $params,
			'code'           => $response->getCode(),
			//			'result'         => $result,
			'ip'             => $request->ip(),
			'user_agent'     => $request->header('user-agent'),
			'execution_time' => $executionTime,
			'creator_id'     => $adminId,
			'tenant_id'     => $tenantId,
		];
		
		// 异步记录日志，不影响主业务流程
		try {
			$this->logService->create($logData, $tenantId);
		}
		catch (\Exception $e) {
			// 记录日志失败不影响正常业务
			App::log()
			   ->error('记录操作日志失败：' . $e->getMessage());
		}
		
		return $response;
	}
	
	/**
	 * 过滤敏感数据
	 *
	 * @param array $data
	 * @return array
	 */
	protected function filterSensitiveData(array $data): array
	{
		$sensitiveFields = Config::get('log.operation.sensitive_fields', [
			'password',
			'old_password',
			'new_password',
			'confirm_password',
			'token',
			'secret',
			'credit_card'
		]);
		
		foreach ($data as $key => $value) {
			if (in_array($key, $sensitiveFields)) {
				$data[$key] = '******';
			}
			elseif (is_array($value)) {
				$data[$key] = $this->filterSensitiveData($value);
			}
		}
		
		return $data;
	}
} 