<!DOCTYPE html>
<html>
<head>
    <title>审批状态测试</title>
</head>
<body>
    <h1>审批状态字段适配测试</h1>
    
    <h2>数据库字段定义</h2>
    <p><code>approval_status</code> tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废'</p>
    
    <h2>前端状态映射</h2>
    <table border="1">
        <tr>
            <th>状态值</th>
            <th>状态名称</th>
            <th>标签类型</th>
        </tr>
        <tr>
            <td>0</td>
            <td>草稿</td>
            <td>info</td>
        </tr>
        <tr>
            <td>1</td>
            <td>审批中</td>
            <td>warning</td>
        </tr>
        <tr>
            <td>2</td>
            <td>已通过</td>
            <td>success</td>
        </tr>
        <tr>
            <td>3</td>
            <td>已拒绝</td>
            <td>danger</td>
        </tr>
        <tr>
            <td>4</td>
            <td>已终止</td>
            <td>info</td>
        </tr>
        <tr>
            <td>5</td>
            <td>已撤回</td>
            <td>info</td>
        </tr>
        <tr>
            <td>6</td>
            <td>已作废</td>
            <td>warning</td>
        </tr>
    </table>
    
    <h2>修改内容</h2>

    <h3>合同与回款页面 (crm_contract_receivable/list.vue)</h3>
    <ul>
        <li>✅ 导入了统一的工作流状态常量和函数</li>
        <li>✅ 移除了原有的 getApprovalStatusText 和 getApprovalStatusType 函数</li>
        <li>✅ 更新表格中的状态显示使用 getInstanceStatusText 和 getInstanceStatusTagType</li>
        <li>✅ 更新详情对话框中的状态显示</li>
        <li>✅ 更新权限判断使用 WorkflowStatus 常量</li>
        <li>✅ 更新模板中的操作按钮条件判断</li>
        <li>✅ 搜索表单选项已包含所有状态（0-6）</li>
    </ul>

    <h3>合同管理页面 (crm_contract/list.vue)</h3>
    <ul>
        <li>✅ 导入了统一的工作流状态常量和函数</li>
        <li>✅ 移除了原有的 getContractStatusText 和 getContractStatusType 函数</li>
        <li>✅ 更新详情对话框中的状态显示使用 getInstanceStatusText 和 getInstanceStatusTagType</li>
        <li>✅ 更新权限判断使用 WorkflowStatus 常量</li>
        <li>✅ 更新模板中的操作按钮条件判断</li>
        <li>✅ 搜索表单选项已包含所有状态（0-6）</li>
        <li>✅ 表格中使用 CrmApprovalStatus 组件显示状态</li>
    </ul>

    <h3>CrmApprovalStatus 组件</h3>
    <ul>
        <li>✅ 修正状态映射以匹配数据库定义</li>
        <li>✅ 更新注释说明状态值含义</li>
        <li>✅ 调整状态文本和图标配置</li>
    </ul>
    
    <h2>测试要点</h2>
    <ol>
        <li>确认所有状态值（0-6）都能正确显示对应的文本</li>
        <li>确认状态标签颜色正确</li>
        <li>确认删除操作只在草稿状态（0）时可用</li>
        <li>确认作废操作在非已作废状态（非6）时可用</li>
        <li>确认搜索筛选包含所有状态选项</li>
    </ol>
</body>
</html>
