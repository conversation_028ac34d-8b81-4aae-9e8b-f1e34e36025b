/**
 * 财务模块常量定义
 * 与后端FinanceExpenseReimbursement模型保持一致
 */

// ==================== 报销类型定义 ====================
export const ExpenseType = {
  TRAVEL: 1,        // 差旅费
  TRANSPORT: 2,     // 交通费
  MEAL: 3,          // 餐费
  ACCOMMODATION: 4, // 住宿费
  OFFICE: 5,        // 办公费
  COMMUNICATION: 6, // 通讯费
  OTHER: 7          // 其他
} as const

// 报销类型文本映射
export const ExpenseTypeMap = {
  [ExpenseType.TRAVEL]: '差旅费',
  [ExpenseType.TRANSPORT]: '交通费',
  [ExpenseType.MEAL]: '餐费',
  [ExpenseType.ACCOMMODATION]: '住宿费',
  [ExpenseType.OFFICE]: '办公费',
  [ExpenseType.COMMUNICATION]: '通讯费',
  [ExpenseType.OTHER]: '其他'
} as const

// 报销类型选项（用于下拉框）
export const ExpenseTypeOptions = [
  { label: '差旅费', value: ExpenseType.TRAVEL },
  { label: '交通费', value: ExpenseType.TRANSPORT },
  { label: '餐费', value: ExpenseType.MEAL },
  { label: '住宿费', value: ExpenseType.ACCOMMODATION },
  { label: '办公费', value: ExpenseType.OFFICE },
  { label: '通讯费', value: ExpenseType.COMMUNICATION },
  { label: '其他', value: ExpenseType.OTHER }
] as const

// ==================== 工具函数 ====================

/**
 * 获取报销类型文本
 */
export const getExpenseTypeText = (type: number): string => {
  return ExpenseTypeMap[type] || '未知类型'
}

/**
 * 获取报销类型选项
 */
export const getExpenseTypeOptions = () => {
  return ExpenseTypeOptions
}

// 类型定义
export type ExpenseTypeValue = typeof ExpenseType[keyof typeof ExpenseType]
