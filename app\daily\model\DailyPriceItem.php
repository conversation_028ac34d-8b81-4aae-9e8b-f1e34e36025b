<?php
declare(strict_types=1);

namespace app\daily\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\ims\model\ImsSupplier;
use app\crm\model\CrmProduct;

/**
 * 每日报价明细表模型
 */
class DailyPriceItem extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'daily_price_item';
	
	// 类型转换
	protected $type = [
		'order_id'        => 'integer',
		'supplier_id'     => 'integer',
		'product_id'      => 'integer',
		'unit_price'      => 'float',
		'old_price'       => 'float',
		'price_change'    => 'float',
		'change_rate'     => 'float',
		'stock_price'     => 'float',
		'stock_qty'       => 'float',
		'is_manual_price' => 'integer',
		'sort_order'      => 'integer',
	];
	
	/**
	 * 获取价格变动类型文本
	 */
	public function getPriceChangeTypeAttr($value, $data)
	{
		if ($data['price_change'] > 0) {
			return '涨价';
		}
		elseif ($data['price_change'] < 0) {
			return '降价';
		}
		else {
			return '-';
		}
	}
	
	/**
	 * 获取价格变动类型样式类
	 */
	public function getPriceChangeClassAttr($value, $data)
	{
		if ($data['price_change'] > 0) {
			return 'price-rise';
		}
		elseif ($data['price_change'] < 0) {
			return 'price-fall';
		}
		else {
			return 'price-stable';
		}
	}
	
	/**
	 * 获取变动比例百分比文本
	 */
	public function getChangeRatePercentAttr($value, $data)
	{
		return number_format($data['change_rate'], 2) . '%';
	}

	/**
	 * 获取完整的价格变动格式文本
	 * 格式：+5.20(+2.08%) 或 -3.50(-1.25%) 或 无变动
	 */
	public function getPriceChangeFormatAttr($value, $data)
	{
		$priceChange = $data['price_change'] ?? 0;
		$changeRate = $data['change_rate'] ?? 0;

		if ($priceChange == 0) {
			return '无变动';
		}

		$sign = $priceChange > 0 ? '+' : '';
		$priceText = $sign . number_format($priceChange, 2);
		$rateText = $sign . number_format($changeRate, 2) . '%';

		return "{$priceText}({$rateText})";
	}
	
	/**
	 * 获取是否手动修改价格文本
	 */
	public function getIsManualPriceTextAttr($value, $data)
	{
		return $data['is_manual_price']
			? '手动修改'
			: '自动继承';
	}
	
	/**
	 * 关联报价单
	 */
	public function orders()
	{
		return $this->belongsTo(DailyPriceOrder::class, 'order_id', 'id');
	}
	
	/**
	 * 关联供应商
	 */
	public function supplier()
	{
		return $this->belongsTo(ImsSupplier::class, 'supplier_id', 'id');
	}
	
	/**
	 * 关联产品
	 */
	public function product()
	{
		return $this->belongsTo(CrmProduct::class, 'product_id', 'id');
	}
	
	/**
	 * 计算价格变动
	 */
	public function calculatePriceChange()
	{
		$this->price_change = $this->unit_price - $this->old_price;
		
		if ($this->old_price > 0) {
			$this->change_rate = ($this->price_change / $this->old_price) * 100;
		}
		else {
			$this->change_rate = 0;
		}
	}
	
	/**
	 * 设置单价时自动计算变动
	 */
	public function setUnitPriceAttr($value)
	{
		$this->data['unit_price'] = $value;
		if (isset($this->data['old_price'])) {
			$this->calculatePriceChange();
		}
		return $value;
	}
	
	/**
	 * 设置原价时自动计算变动
	 */
	public function setOldPriceAttr($value)
	{
		$this->data['old_price'] = $value;
		if (isset($this->data['unit_price'])) {
			$this->calculatePriceChange();
		}
		return $value;
	}
}