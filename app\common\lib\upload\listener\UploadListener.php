<?php

namespace app\common\lib\upload\listener;

use think\facade\Log;

class UploadListener
{
	/**
	 * 上传完成事件
	 *
	 * @param array $attachment
	 */
	public function onUploadAfter(array $attachment)
	{
		// 记录日志
		Log::info('文件上传成功：' . json_encode($attachment, JSON_UNESCAPED_UNICODE));
		
		// 可以在这里处理上传后的业务逻辑
		// 例如：图片压缩、水印添加等
	}
	
	/**
	 * 上传回调事件
	 *
	 * @param array $attachment
	 */
	public function onUploadCallback(array $attachment)
	{
		// 记录日志
		Log::info('文件上传回调成功：' . json_encode($attachment, JSON_UNESCAPED_UNICODE));
	}
	
	/**
	 * 删除文件事件
	 *
	 * @param array $attachment
	 */
	public function onUploadDelete(array $attachment)
	{
		// 记录日志
		Log::info('文件删除成功：' . json_encode($attachment, JSON_UNESCAPED_UNICODE));
	}
}