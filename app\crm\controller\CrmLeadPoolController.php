<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmLeadPoolService;
use think\response\Json;

/**
 * 线索池控制器
 */
class CrmLeadPoolController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CrmLeadPoolService
	 */
	protected CrmLeadPoolService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用专用的线索池服务
		$this->service = CrmLeadPoolService::getInstance();
	}
	
	/**
	 * 获取线索池列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		$result = $this->service->getPoolList($params);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}
	
	/**
	 * 认领线索
	 *
	 * @param int $id 线索ID
	 * @return Json
	 */
	public function claimLead(int $id): Json
	{
		return $this->service->claimLead($id)
			? $this->success('认领成功')
			: $this->error('认领失败');
	}
	
	/**
	 * 分配线索
	 *
	 * @param int $id 线索ID
	 * @return Json
	 */
	public function assign(int $id): Json
	{
		try {
			$toUserId = $this->request->post('to_user_id');
			$reason   = $this->request->post('reason', '');
			
			if (empty($toUserId)) {
				return $this->error('请选择分配目标用户');
			}
			
			return $this->service->assignLead($id, $toUserId, $reason)
				? $this->success('分配成功')
				: $this->error('分配失败');
		}
		catch (\Exception $e) {
			return $this->error('分配失败');
		}
	}
	
	/**
	 * 获取下拉选项
	 *
	 * @return Json
	 */
	public function options(): Json
	{
		$params     = $this->request->param();
		$labelField = $params['label_field'] ?? 'lead_name';
		$valueField = $params['value_field'] ?? 'id';
		$where      = $params['where'] ?? [];
		
		$result = $this->service->getSelectOptions($where, $labelField, $valueField);
		return $this->success('获取成功', $result);
	}
	
}