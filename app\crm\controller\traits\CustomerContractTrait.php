<?php
declare(strict_types=1);

namespace app\crm\controller\traits;

use app\crm\model\CrmContract;
use app\workflow\service\UnifiedWorkflowService;
use app\crm\service\CustomerPermissionService;
use app\workflow\model\WorkflowDefinition;
use app\workflow\model\WorkflowInstance;
use app\workflow\model\WorkflowType;
use think\facade\Db;
use think\response\Json;

/**
 * 客户合同操作Trait
 */
trait CustomerContractTrait
{
	/**
	 * 新增合同
	 */
	public function addContract(): Json
	{
		$data       = $this->request->post();
		$customerId = $data['customer_id'] ?? 0;
		
		if (!$customerId) {
			return $this->error('客户ID不能为空');
		}
		
		// 验证客户访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateCustomerAccess((int)$customerId, (int)get_user_id())) {
		// return $this->error('无权限操作此客户');
		//        }
		
		try {
			// 生成合同编号
			if (empty($data['contract_no'])) {
				$data['contract_no'] = $this->generateContractNo();
			}
			$data['owner_user_id'] = get_user_id();
			if (empty($data['payment_deadline'])) {
				unset($data['payment_deadline']);
			}
			if (empty($data['sign_date'])) {
				unset($data['sign_date']);
			}
			if (empty($data['start_date'])) {
				unset($data['start_date']);
			}
			if (empty($data['end_date'])) {
				unset($data['end_date']);
			}
			$data['approval_status']      = 0;
			$data['workflow_instance_id'] = 0;
			$contract                     = (new CrmContract())->saveByCreate($data);
			return $this->success('合同添加成功', $contract);
			
		}
		catch (\Exception $e) {
			return $this->error('合同添加失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 编辑合同
	 */
	public function editContract(): Json
	{
		$data       = $this->request->post();
		$contractId = $data['id'] ?? 0;
		
		if (!$contractId) {
			return $this->error('合同ID不能为空');
		}
		
		// 验证合同访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContractAccess((int)$contractId, (int)get_user_id())) {
		// return $this->error('无权限操作此合同');
		//	}
		
		try {
			$contract = CrmContract::findOrEmpty($contractId);
			if ($contract->isEmpty()) {
				return $this->error('合同不存在');
			}
			$data['approval_status']      = 0;
			$data['workflow_instance_id'] = 0;
			if (empty($data['payment_deadline'])) {
				unset($data['payment_deadline']);
			}
			if (empty($data['sign_date'])) {
				unset($data['sign_date']);
			}
			if (empty($data['start_date'])) {
				unset($data['start_date']);
			}
			if (empty($data['end_date'])) {
				unset($data['end_date']);
			}
			$contract->saveByUpdate($data);
			
			return $this->success('合同更新成功', $contract);
			
		}
		catch (\Exception $e) {
			return $this->error('合同更新失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 删除合同
	 */
	public function deleteContract(): Json
	{
		$data       = $this->request->post();
		$contractId = $data['id'] ?? 0;
		
		if (!$contractId) {
			return $this->error('合同ID不能为空');
		}
		
		// 验证合同访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContractAccess((int)$contractId, (int)get_user_id())) {
		// return $this->error('无权限操作此合同');
		//}
		
		try {
			$contract = CrmContract::findOrEmpty($contractId);
			if ($contract->isEmpty()) {
				return $this->error('合同不存在');
			}
			
			// 检查审批状态 - 只允许删除草稿状态
			if ($contract->approval_status !== 0) {
				return $this->error('只有草稿状态的合同才能删除，其他状态请使用作废功能');
			}
			
			// 检查是否有关联的回款记录
			$receivableCount = $contract->receivables()
			                            ->count();
			
			if ($receivableCount > 0) {
				return $this->error('该合同存在回款记录，无法删除');
			}
			
			// 软删除
			$contract->together([
				'receivables',
				'products'
			])
			         ->delete();
			
			return $this->success('合同删除成功');
			
		}
		catch (\Exception $e) {
			return $this->error('合同删除失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 合同详情
	 */
	public function contractDetail(): Json
	{
		$contractId = $this->request->param('id', 0);
		
		if (!$contractId) {
			return $this->error('合同ID不能为空');
		}
		
		// 验证合同访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContractAccess((int)$contractId, (int)get_user_id())) {
		//		return $this->error('无权限访问此合同');
		//	}
		
		try {
			$contract = CrmContract::with([
				'customer',
				'creator'
			])
			                       ->find($contractId);
			
			if (!$contract) {
				return $this->error('合同不存在');
			}
			
			return $this->success('获取成功', $contract);
			
		}
		catch (\Exception $e) {
			return $this->error('获取合同详情失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 合同列表
	 */
	public function contractList(): Json
	{
		$customerId = $this->request->param('customer_id', 0);
		$page       = $this->request->param('page', 1, 'int');
		$limit      = $this->request->param('limit', 15, 'int');
		
		if (!$customerId) {
			return $this->error('客户ID不能为空');
		}
		
		// 验证客户访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateCustomerAccess((int)$customerId, (int)get_user_id())) {
		//		return $this->error('无权限访问此客户');
		//	}
		
		try {
			$query = CrmContract::with([
				'workflow'
			])
			                    ->where('customer_id', $customerId)
			                    ->order('created_at desc');
			
			$total = $query->count();
			$list  = $query->page($page, $limit)
			               ->select();
			
			return $this->success('获取成功', [
				'list'  => $list,
				'total' => $total,
				'page'  => $page,
				'limit' => $limit
			]);
			
		}
		
		catch (\Exception $e) {
			return $this->error('获取合同列表失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 提交审批
	 */
	public function submitContractApproval(): Json
	{
		$data       = $this->request->post();
		$contractId = intval($data['id'] ?? 0);
		
		if (!$contractId) {
			return $this->error('合同ID不能为空');
		}
		
		// 验证合同访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContractAccess((int)$contractId, (int)get_user_id())) {
		// return $this->error('无权限操作此合同');
		//	}
		try {
			$contract = CrmContract::with(['workflow'])
			                       ->findOrEmpty($contractId);
			if ($contract->isEmpty()) {
				return $this->error('合同不存在');
			}
			
			// 检查合同状态
			$approvalStatus = $contract['approval_status'];
			if (!in_array($approvalStatus, [
				0,
				5
			])) {
				return $this->error('只有草稿状态的合同才能提交审批');
			}
			// 使用统一工作流服务提交审批
			$unifiedWorkflowService = new UnifiedWorkflowService();
			$result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
				'business_code' => 'crm_contract',
				'business_id' => $contractId,
				'operator_id' => get_user_id()
			]);
			
			if (!$result['success']) {
				return $this->error($result['message']);
			}
			return $this->success($result['message'], $result);
			
		}
		catch (\Exception $e) {
			return $this->error('提交审批失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 生成合同编号
	 */
	private function generateContractNo(): string
	{
		$prefix = 'HT';
		$date   = date('Ymd');
		$random = str_pad((string)mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
		
		return $prefix . $date . $random;
	}
	
	/**
	 * todo 暂时废弃，使用终止代替 撤回合同审批
	 */
	public function withdrawContractApproval(): Json
	{
		$data       = $this->request->post();
		$contractId = intval($data['id'] ?? 0);
		
		if (!$contractId) {
			return $this->error('合同ID不能为空');
		}
		
		try {
			$contract = CrmContract::with(['workflow'])
			                       ->find($contractId);
			if (!$contract) {
				return $this->error('合同不存在');
			}
			// 使用统一工作流服务撤回审批
			try {
				$unifiedWorkflowService = new UnifiedWorkflowService();
				$result = $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
					'business_code' => 'crm_contract',
					'business_id' => $contractId,
					'operator_id' => get_user_id(),
					'reason' => '用户撤回申请'
				]);

				return $this->success($result['message'], $result);
			}
			catch (\Exception $e) {
				return $this->error('撤回失败：' . $e->getMessage());
			}
			
		}
		catch (\Exception $e) {
			return $this->error('撤回失败：' . $e->getMessage());
		}
	}
}
