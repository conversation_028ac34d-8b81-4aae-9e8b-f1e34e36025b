<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 联系人表模型 - 基于crm_data.sql表结构
 */
class CrmContact extends BaseModel
{
	use CreatorTrait;

    // 设置表名
    protected $name = 'crm_contact';

    // 字段类型转换
    protected $type = [
        'gender' => 'integer',
        'importance' => 'integer',
        'is_primary' => 'integer',
        'birthday' => 'date',
    ];

    // 关联客户
    public function customer()
    {
        return $this->belongsTo(CrmCustomer::class, 'customer_id', 'id')->bind([
			'customer_name'
        ]);
    }

    // 获取性别文本
    public function getGenderTextAttr($value, $data)
    {
        $genderMap = [
            0 => '保密',
            1 => '男',
            2 => '女'
        ];
        return $genderMap[$data['gender']] ?? '保密';
    }

    // 获取重要程度文本
    public function getImportanceTextAttr($value, $data)
    {
        $importanceMap = [
            0 => '普通',
            1 => '重要',
            2 => '核心'
        ];
        return $importanceMap[$data['importance']] ?? '普通';
    }

    // 获取角色类型文本
    public function getRoleTypeTextAttr($value, $data)
    {
        $roleMap = [
            'decision' => '决策者',
            'user' => '使用者',
            'influence' => '影响者'
        ];
        return $roleMap[$data['role_type']] ?? '';
    }

    public function getImpSceneFields(): array
    {
        return [
            'customer_id' => [
                'label' => '客户ID',
                'type'  => 'number',
            ],
            'name' => [
                'label' => '联系人姓名',
                'type'  => 'text',
            ],
            'gender' => [
                'label' => '性别',
                'type'  => 'select',
                'options' => [
                    0 => '保密',
                    1 => '男',
                    2 => '女'
                ]
            ],
            'position' => [
                'label' => '职位',
                'type'  => 'text',
            ],
            'department' => [
                'label' => '部门',
                'type'  => 'text',
            ],
            'mobile' => [
                'label' => '手机号',
                'type'  => 'text',
            ],
            'phone' => [
                'label' => '电话',
                'type'  => 'text',
            ],
            'email' => [
                'label' => '邮箱',
                'type'  => 'email',
            ],
            'wechat' => [
                'label' => '微信',
                'type'  => 'text',
            ],
            'qq' => [
                'label' => 'QQ',
                'type'  => 'text',
            ],
            'importance' => [
                'label' => '重要程度',
                'type'  => 'select',
                'options' => [
                    0 => '普通',
                    1 => '重要',
                    2 => '核心'
                ]
            ],
            'role_type' => [
                'label' => '角色类型',
                'type'  => 'select',
                'options' => [
                    'decision' => '决策者',
                    'user' => '使用者',
                    'influence' => '影响者'
                ]
            ],
            'birthday' => [
                'label' => '生日',
                'type'  => 'date',
            ],
            'address' => [
                'label' => '地址',
                'type'  => 'textarea',
            ],
            'is_primary' => [
                'label' => '是否主要联系人',
                'type'  => 'select',
                'options' => [
                    0 => '否',
                    1 => '是'
                ]
            ]
        ];
    }
	
	public function getDefaultSearchFields(): array
	{
		return [
			'customer_id' => ['type' => 'eq'],
			'name'         => ['type' => 'like'],
			'role_type'         => ['type' => 'eq'],
			'importance'        => ['type' => 'eq'],
			'gender'        => ['type' => 'eq'],
			'mobile'   => ['type' => 'like'],
		];
	}
}