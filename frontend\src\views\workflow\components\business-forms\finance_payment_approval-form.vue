<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <ElFormItem label="付款事由" prop="reason">
          <ElInput
            v-model="formData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入付款事由"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="付款总额" prop="payment_amount">
              <ElInputNumber
                v-model="formData.payment_amount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入付款总额"
                :disabled="!isEditable"
              />
              <span style="margin-left: 8px; color: #909399">元</span>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="付款方式" prop="payment_method">
              <ElSelect
                v-model="formData.payment_method"
                placeholder="请选择付款方式"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="option in paymentMethodOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                >
                  <div style="display: flex; align-items: center">
                    <ElIcon :style="{ color: option.color, marginRight: '8px' }">
                      <component :is="option.icon" />
                    </ElIcon>
                    <span>{{ option.label }}</span>
                  </div>
                </ElOption>
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="支付日期" prop="payment_date">
              <ElDatePicker
                v-model="formData.payment_date"
                type="date"
                placeholder="请选择支付日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="支付对象" prop="payee_name">
              <ElInput
                v-model="formData.payee_name"
                placeholder="请输入支付对象"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="开户行" prop="payee_bank">
              <ElInput
                v-model="formData.payee_bank"
                placeholder="请输入开户行"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="银行账户" prop="payee_account">
              <ElInput
                v-model="formData.payee_account"
                placeholder="请输入银行账户"
                :disabled="!isEditable"
                @input="handleBankAccountInput"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import { getDefaultPaymentMethod, PAYMENT_METHOD_OPTIONS } from '@/utils/payment'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface FinancePaymentFormData {
    id?: number
    payment_no: string
    payee_name: string
    payee_account: string
    payee_bank: string
    payment_amount: number
    payment_date: string
    payment_method: number | null
    reason: string
    remark: string
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `付款申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起付款申请'
  })

  /** 表单数据 */
  const formData = reactive<FinancePaymentFormData & any>({
    payment_no: '',
    payee_name: '',
    payee_account: '',
    payee_bank: '',
    payment_amount: 0,
    payment_date: '',
    payment_method: getDefaultPaymentMethod(),
    reason: '',
    remark: '',
    approval_status: 0
  })

  /** 支付方式选项 */
  const paymentMethodOptions = PAYMENT_METHOD_OPTIONS

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    payment_no: [{ required: true, message: '请输入付款单号', trigger: 'blur' }],
    payee_name: [{ required: true, message: '请输入支付对象', trigger: 'blur' }],
    payment_amount: [
      { required: true, message: '请输入付款金额', trigger: 'blur' },
      { type: 'number', min: 0.01, message: '付款金额必须大于0', trigger: 'blur' }
    ],
    payment_date: [{ required: true, message: '请选择支付日期', trigger: 'change' }],
    payment_method: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
    reason: [{ required: true, message: '请输入付款事由', trigger: 'blur' }],
    payee_bank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
    payee_account: [
      { required: true, message: '请输入银行账户', trigger: 'blur' },
      { min: 10, message: '银行账户长度不能少于10位', trigger: 'blur' }
    ]
  }

  // ==================== 方法定义 ====================

  /**
   * 处理银行账户输入，只允许数字
   */
  const handleBankAccountInput = (value: string) => {
    // 只允许数字
    formData.payee_account = value.replace(/[^\d]/g, '')
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('finance_payment_approval-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      payment_no: '',
      payee_name: '',
      payee_account: '',
      payee_bank: '',
      payment_amount: 0,
      payment_date: '',
      payment_method: getDefaultPaymentMethod(),
      reason: '',
      remark: '',
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        console.log('付款申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('finance_payment_approval-form setFormData called with:', data)
    Object.assign(formData, data)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('finance_payment_approval-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: FinancePaymentFormData = {
        payment_no: formData.payment_no,
        payee_name: formData.payee_name,
        payee_account: formData.payee_account,
        payee_bank: formData.payee_bank,
        payment_amount: formData.payment_amount,
        payment_date: formData.payment_date,
        payment_method: formData.payment_method,
        reason: formData.reason,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('付款申请保存数据:', submitData)
      emit('save', submitData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: FinancePaymentFormData = {
        payment_no: formData.payment_no,
        payee_name: formData.payee_name,
        payee_account: formData.payee_account,
        payee_bank: formData.payee_bank,
        payment_amount: formData.payment_amount,
        payment_date: formData.payment_date,
        payment_method: formData.payment_method,
        reason: formData.reason,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('付款申请提交数据:', submitData)
      emit('submit', submitData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }
</style>
