# 第三阶段简化执行指南

## 🎯 执行概述
**基于现有技术栈**：利用已有的状态管理系统和API封装模式  
**开发重点**：专注于业务组件开发，减少基础设施工作  
**预计时间**：2-3天（相比原计划节省1-2天）

## 📋 第二阶段完成情况确认

### ✅ 必须确认的检查项
在开始第三阶段前，请确认以下关键项目：

1. **后端接口测试**
   ```bash
   # 测试提交审批接口
   curl -X POST "http://localhost:3006/daily/daily-price-order/1/submit-approval"
   
   # 测试明细保存接口
   curl -X POST "http://localhost:3006/daily/daily-price-order/1/items"
   
   # 测试从昨日复制接口
   curl -X POST "http://localhost:3006/daily/daily-price-order/copy-yesterday"
   ```

2. **工作流配置确认**
   ```sql
   SELECT * FROM workflow_type WHERE code = 'daily_price_order';
   ```

3. **数据表确认**
   ```sql
   SHOW TABLES LIKE 'daily_price_%';
   ```

**⚠️ 重要**：只有第二阶段完全通过测试，才能开始第三阶段工作！

---

## 🚀 第三阶段执行步骤

### 步骤1：API接口封装（0.5天）⭐⭐⭐

#### 任务目标
基于 `crmCustomerDetail.ts` 的模式，创建每日报价单的API接口

#### 具体操作
1. **创建API文件**
   ```bash
   # 创建目录
   mkdir -p frontend/src/api/daily
   
   # 创建API文件
   touch frontend/src/api/daily/dailyPriceOrder.ts
   ```

2. **复制参考代码**
   - 参考：`frontend/src/api/crm/crmCustomerDetail.ts`
   - 复制类结构和方法模式
   - 修改URL路径为 `daily/daily_price_order`

3. **实现核心接口**
   ```typescript
   export class DailyPriceOrderApi {
     // 基础CRUD（复用现有模式）
     static getList(params?: any)
     static getDetail(id: number)
     static create(data: any)
     static update(id: number, data: any)
     static delete(id: number)
     
     // 审批操作（新增）
     static submitApproval(id: number)
     static withdrawApproval(id: number)
     static voidOrder(id: number, reason: string)
     
     // 业务操作（新增）
     static saveItems(orderId: number, items: any[])
     static copyFromYesterday(targetDate: string)
   }
   ```

4. **测试API接口**
   - 在浏览器控制台测试每个接口
   - 确保返回数据格式正确

#### 验收标准
- [ ] ✅ API文件创建完成
- [ ] ✅ 所有接口方法实现
- [ ] ✅ 接口调用测试通过
- [ ] ✅ TypeScript类型定义完整

---

### 步骤2：核心组件开发（1.5天）⭐⭐⭐

#### 任务目标
开发审批状态展示和明细编辑的核心组件

#### 2.1 审批状态卡片组件（0.5天）
```vue
<!-- ApprovalStatusCard.vue -->
<template>
  <el-card class="approval-status-card">
    <div class="status-header">
      <el-tag :type="statusType" size="large">
        {{ statusText }}
      </el-tag>
      <div class="status-time">{{ formatTime }}</div>
    </div>
    
    <div class="status-progress" v-if="showProgress">
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="提交审批"></el-step>
        <el-step title="审批中"></el-step>
        <el-step title="审批完成"></el-step>
      </el-steps>
    </div>
    
    <div class="status-actions">
      <el-button v-if="canSubmit" type="primary" @click="handleSubmit">
        提交审批
      </el-button>
      <el-button v-if="canWithdraw" type="warning" @click="handleWithdraw">
        撤回审批
      </el-button>
      <el-button v-if="canVoid" type="danger" @click="handleVoid">
        作废
      </el-button>
    </div>
  </el-card>
</template>
```

#### 2.2 明细表格编辑组件（1天）
```vue
<!-- PriceItemTable.vue -->
<template>
  <div class="price-item-table">
    <div class="table-toolbar">
      <el-button type="primary" @click="addItem">添加明细</el-button>
      <el-button @click="copyYesterday">从昨日复制</el-button>
    </div>
    
    <el-table :data="items" border>
      <el-table-column label="供应商">
        <template #default="{ row, $index }">
          <el-select v-model="row.supplier_id" @change="onSupplierChange($index)">
            <el-option 
              v-for="supplier in suppliers" 
              :key="supplier.id"
              :label="supplier.name" 
              :value="supplier.id"
            />
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column label="产品">
        <template #default="{ row }">
          <el-select v-model="row.product_id">
            <el-option 
              v-for="product in getProductsBySupplier(row.supplier_id)" 
              :key="product.id"
              :label="product.name" 
              :value="product.id"
            />
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column label="原价格">
        <template #default="{ row }">
          <el-input-number v-model="row.old_price" :precision="2" />
        </template>
      </el-table-column>
      
      <el-table-column label="现价格">
        <template #default="{ row }">
          <el-input-number 
            v-model="row.unit_price" 
            :precision="2"
            @change="calculateChange(row)"
          />
        </template>
      </el-table-column>
      
      <el-table-column label="价格变动">
        <template #default="{ row }">
          <PriceChangeIndicator :change="row.price_change" :rate="row.change_rate" />
        </template>
      </el-table-column>
      
      <el-table-column label="操作">
        <template #default="{ $index }">
          <el-button type="danger" size="small" @click="removeItem($index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

#### 验收标准
- [ ] ✅ 审批状态卡片显示正确
- [ ] ✅ 明细表格编辑功能正常
- [ ] ✅ 价格变动计算准确
- [ ] ✅ 组件交互流畅

---

### 步骤3：页面集成（0.5天）⭐⭐

#### 任务目标
将组件集成到生成器生成的页面中

#### 3.1 扩展列表页
```vue
<!-- index.vue -->
<template>
  <div class="daily-price-order-list">
    <!-- 基于生成器页面扩展 -->
    <div class="filter-bar">
      <el-select v-model="filterStatus" placeholder="审批状态">
        <el-option label="全部" value="" />
        <el-option label="草稿" :value="0" />
        <el-option label="审批中" :value="1" />
        <el-option label="已通过" :value="2" />
      </el-select>
    </div>
    
    <!-- 原有表格基础上添加状态列和操作列 -->
    <el-table-column label="审批状态">
      <template #default="{ row }">
        <el-tag :type="getStatusType(row.approval_status)">
          {{ getStatusText(row.approval_status) }}
        </el-tag>
      </template>
    </el-table-column>
    
    <el-table-column label="操作">
      <template #default="{ row }">
        <el-button v-if="canSubmit(row)" size="small" @click="submitApproval(row.id)">
          提交审批
        </el-button>
        <el-button v-if="canWithdraw(row)" size="small" @click="withdrawApproval(row.id)">
          撤回
        </el-button>
      </template>
    </el-table-column>
  </div>
</template>
```

#### 3.2 扩展表单页
```vue
<!-- form.vue -->
<template>
  <div class="daily-price-order-form">
    <!-- 审批状态卡片 -->
    <ApprovalStatusCard 
      v-if="formData.id"
      :order="formData"
      @submit="handleSubmitApproval"
      @withdraw="handleWithdrawApproval"
      @void="handleVoidOrder"
    />
    
    <!-- 基础表单（生成器生成） -->
    <el-form :model="formData" :rules="rules">
      <el-form-item label="报价日期" prop="price_date">
        <el-date-picker v-model="formData.price_date" />
      </el-form-item>
      
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" readonly />
      </el-form-item>
    </el-form>
    
    <!-- 明细表格 -->
    <PriceItemTable 
      v-model="priceItems"
      :readonly="!canEdit"
      @change="handleItemsChange"
    />
  </div>
</template>
```

#### 验收标准
- [ ] ✅ 列表页筛选功能正常
- [ ] ✅ 表单页组件集成正确
- [ ] ✅ 页面路由跳转正常
- [ ] ✅ 数据保存和加载正确

---

## 🔧 技术要点

### 1. 复用现有技术栈
```typescript
// 使用现有的状态管理
import { useStore } from '@/stores'

// 使用现有的API模式
import { DailyPriceOrderApi } from '@/api/daily/dailyPriceOrder'

// 使用现有的组件库
import { ElMessage, ElMessageBox } from 'element-plus'
```

### 2. 权限控制逻辑
```typescript
const canEdit = computed(() => {
  return [0, 3, 5].includes(formData.value.approval_status) // 草稿、已拒绝、已撤回
})

const canSubmit = computed(() => {
  return formData.value.approval_status === 0 && priceItems.value.length > 0
})
```

### 3. 价格变动计算
```typescript
const calculateChange = (item: any) => {
  item.price_change = item.unit_price - item.old_price
  item.change_rate = item.old_price > 0 ? 
    (item.price_change / item.old_price) * 100 : 0
}
```

---

## 📊 进度跟踪

### 每日检查点
- **第1天上午**：API接口封装完成
- **第1天下午**：审批状态卡片组件完成
- **第2天全天**：明细表格编辑组件完成
- **第3天上午**：页面集成完成
- **第3天下午**：测试和优化

### 质量检查
- [ ] **功能测试**：所有功能正常运行
- [ ] **UI测试**：界面美观，交互流畅
- [ ] **兼容性测试**：主流浏览器兼容
- [ ] **性能测试**：页面加载速度正常

---

## 🎯 成功标准

### 最终验收
- [ ] ✅ **API接口**：所有接口调用正常
- [ ] ✅ **核心组件**：审批状态和明细编辑功能完整
- [ ] ✅ **页面集成**：列表、表单、详情页功能正常
- [ ] ✅ **用户体验**：操作流畅，反馈及时
- [ ] ✅ **代码质量**：符合项目规范

### 交付物
1. **API接口文件**：`src/api/daily/dailyPriceOrder.ts`
2. **核心组件**：审批状态卡片、明细表格编辑组件
3. **扩展页面**：列表页、表单页、详情页
4. **测试报告**：功能测试和用户体验测试结果

---

**开始时间**：第二阶段验收通过后  
**预期完成**：2-3个工作日  
**下一阶段**：功能完善与部署上线
