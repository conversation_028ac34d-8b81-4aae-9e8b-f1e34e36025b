# 消息中心变量映射开发规范

## 📋 规范概述

**制定时间**: 2025-07-16
**更新时间**: 2025-07-16
**适用范围**: 所有消息中心相关的模板和代码开发
**规范目标**: 建立正确的变量映射机制，确保代码传入与模板显示的一致性

## 🎯 核心原则

### 1. 变量映射机制
- **代码传入**: 使用英文键名（如 `title`、`task_name`）
- **模板配置**: 使用英文字段路径（如 `title`、`task_name`）
- **模板内容**: 使用中文变量名（如 `${流程标题}`、`${任务名称}`）
- **用户界面**: 显示中文变量名称（如"流程标题"、"任务名称"）

### 2. 统一性原则
- 所有模块的代码传入必须使用英文键名
- 相同含义的变量在不同模板中使用相同的英文键名和中文显示名
- 模板配置中的字段路径必须与代码传入的键名一致

### 3. 语义清晰原则
- 英文键名应该清晰表达其含义，使用标准的英文单词
- 中文变量名应该符合业务人员的理解习惯
- 避免使用缩写或简写，优先使用完整的单词

### 4. 简洁明了原则
- 英文键名使用下划线命名法（如 `task_name`、`created_at`）
- 中文变量名长度适中，一般不超过6个汉字
- 保持命名的一致性和可读性

### 5. 业务导向原则
- 中文变量名应该符合业务人员的理解习惯
- 优先使用行业标准术语
- 考虑用户的使用场景和阅读体验

## 📚 标准变量映射词典

### 通用变量

| 英文键名 | 中文变量名 | 使用场景 | 示例值 | 备注 |
|----------|------------|----------|--------|------|
| `title` | `标题` | 通用标题 | "系统维护通知" | 通用场景使用 |
| `content` | `内容` | 通用内容 | "系统将于今晚维护" | 通用场景使用 |
| `name` | `名称` | 通用名称 | "张三" | 通用场景使用 |
| `created_at` | `创建时间` | 通用时间 | "2025-07-16 15:30:00" | 通用场景使用 |

### 工作流相关变量

| 英文键名 | 中文变量名 | 使用场景 | 示例值 | 备注 |
|----------|------------|----------|--------|------|
| `title` | `流程标题` | 工作流标题 | "张三的请假申请" | 工作流专用 |
| `task_name` | `任务名称` | 工作流任务 | "部门经理审批" | 工作流节点名称 |
| `node_name` | `节点名称` | 工作流节点 | "财务审核" | 工作流节点名称 |
| `submitter_name` | `提交人` | 工作流提交人 | "张三" | 流程发起人 |
| `approver_name` | `审批人` | 工作流审批人 | "李四" | 当前审批人 |
| `提交时间` | 工作流提交时间 | "2025-07-16 15:30:00" | 流程发起时间 |
| `审批时间` | 工作流审批时间 | "2025-07-16 16:30:00" | 审批完成时间 |
| `审批结果` | 工作流审批结果 | "通过" / "拒绝" | 审批结果 |
| `审批意见` | 工作流审批意见 | "符合公司规定" | 审批人意见 |
| `催办人` | 工作流催办人 | "王五" | 催办操作人 |
| `催办时间` | 工作流催办时间 | "2025-07-16 17:30:00" | 催办时间 |
| `催办原因` | 工作流催办原因 | "任务超时未处理" | 催办原因 |
| `转交人` | 工作流转交人 | "李四" | 转交操作人 |
| `接收人` | 工作流接收人 | "王五" | 转交目标人 |
| `转交时间` | 工作流转交时间 | "2025-07-16 18:30:00" | 转交时间 |
| `抄送时间` | 工作流抄送时间 | "2025-07-16 19:30:00" | 抄送时间 |
| `终止时间` | 工作流终止时间 | "2025-07-16 20:30:00" | 流程终止时间 |
| `终止人` | 工作流终止人 | "系统管理员" | 终止操作人 |
| `终止原因` | 工作流终止原因 | "申请人主动撤回" | 终止原因 |

### CRM相关变量

| 变量名 | 使用场景 | 示例值 | 备注 |
|--------|----------|--------|------|
| `客户名称` | CRM客户 | "张三" | 客户姓名 |
| `客户电话` | CRM客户电话 | "13800138000" | 客户联系电话 |
| `线索名称` | CRM线索 | "张三的咨询" | 线索标题 |
| `商机名称` | CRM商机 | "张三的采购需求" | 商机标题 |
| `合同编号` | CRM合同编号 | "CT202507160001" | 合同编号 |
| `合同金额` | CRM合同金额 | "100000.00" | 合同金额 |
| `报价单编号` | CRM报价单编号 | "QT202507160001" | 报价单编号 |
| `最终金额` | CRM最终金额 | "50000.00" | 报价单最终金额 |
| `原阶段` | CRM阶段变更 | "初步接触" | 变更前阶段 |
| `新阶段` | CRM阶段变更 | "需求确认" | 变更后阶段 |
| `变更原因` | CRM变更原因 | "客户明确采购意向" | 阶段变更原因 |

### 人事相关变量

| 变量名 | 使用场景 | 示例值 | 备注 |
|--------|----------|--------|------|
| `请假类型` | 人事请假 | "年假" | 请假类型 |
| `申请人` | 人事申请 | "张三" | 申请人姓名 |
| `开始时间` | 人事时间 | "2025-07-17 09:00:00" | 开始时间 |
| `结束时间` | 人事时间 | "2025-07-19 18:00:00" | 结束时间 |

### 财务相关变量

| 变量名 | 使用场景 | 示例值 | 备注 |
|--------|----------|--------|------|
| `付款事项` | 财务付款 | "办公用品采购" | 付款事项 |
| `发票类型` | 财务发票 | "增值税专用发票" | 发票类型 |

### 库存相关变量

| 变量名 | 使用场景 | 示例值 | 备注 |
|--------|----------|--------|------|
| `商品名称` | 库存商品 | "笔记本电脑" | 商品名称 |
| `当前库存` | 库存数量 | "5" | 当前库存数量 |
| `订单编号` | 采购订单 | "PO202507160001" | 采购订单编号 |

### 系统相关变量

| 变量名 | 使用场景 | 示例值 | 备注 |
|--------|----------|--------|------|
| `公告标题` | 系统公告 | "系统维护通知" | 公告标题 |
| `发布时间` | 系统发布 | "2025-07-16 15:30:00" | 发布时间 |
| `发布人` | 系统发布 | "系统管理员" | 发布人 |

## 💻 代码实现规范

### 1. 模板创建规范

```php
// ✅ 正确示例
$templateData = [
    'code' => 'workflow_task_approval',
    'name' => '工作流任务审批通知',
    'title' => '您有一个待审批任务',
    'content' => '标题：${流程标题}\n当前环节：${任务名称}\n提交人：${提交人}',
    'variables_config' => json_encode([
        'variables' => [
            [
                'name' => '流程标题',
                'code' => 'title',           // 注意：这里仍使用英文code
                'field' => 'title',          // 这里仍使用英文field
                'required' => true,
                'description' => '工作流程标题'
            ]
        ]
    ])
];
```

### 2. 消息发送规范

```php
// ✅ 正确示例 - 使用中文键名
$variables = [
    '流程标题' => $instance['title'],
    '任务名称' => $task['node_name'],
    '提交人'   => $instance['submitter_name'],
    '提交时间' => $instance['created_at']
];

NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    $variables,
    $recipients
);

// ❌ 错误示例 - 使用英文键名
$variables = [
    'title'          => $instance['title'],      // 错误
    'task_name'      => $task['node_name'],      // 错误
    'submitter_name' => $instance['submitter_name'], // 错误
    'created_at'     => $instance['created_at']  // 错误
];
```

### 3. 变量映射辅助类

```php
/**
 * 消息变量映射辅助类
 */
class NoticeVariableHelper
{
    /**
     * 将英文字段映射为中文变量键名
     */
    public static function mapToChineseKeys(array $data, string $templateType): array
    {
        $mappings = self::getFieldMappings($templateType);
        $result = [];
        
        foreach ($mappings as $chineseKey => $englishField) {
            if (isset($data[$englishField])) {
                $result[$chineseKey] = $data[$englishField];
            }
        }
        
        return $result;
    }
    
    /**
     * 获取字段映射配置
     */
    private static function getFieldMappings(string $templateType): array
    {
        $mappings = [
            'workflow_approval' => [
                '流程标题' => 'title',
                '任务名称' => 'node_name',
                '提交人'   => 'submitter_name',
                '提交时间' => 'created_at'
            ],
            'crm_customer' => [
                '客户名称' => 'customer_name',
                '客户电话' => 'phone'
            ]
            // ... 其他映射
        ];
        
        return $mappings[$templateType] ?? [];
    }
}

// 使用示例
$variables = NoticeVariableHelper::mapToChineseKeys($data, 'workflow_approval');
```

## 🧪 测试验证规范

### 1. 单元测试要求

```php
/**
 * 测试消息变量替换
 */
public function testChineseVariableReplacement()
{
    $template = "标题：${流程标题}\n任务：${任务名称}";
    $variables = [
        '流程标题' => '测试流程',
        '任务名称' => '测试任务'
    ];
    
    $result = NoticeTemplateService::getInstance()->renderTemplate($template, $variables);
    
    $this->assertEquals("标题：测试流程\n任务：测试任务", $result);
    $this->assertStringNotContainsString('${', $result); // 确保没有未替换的变量
}
```

### 2. 集成测试要求

```php
/**
 * 测试完整的消息发送流程
 */
public function testCompleteMessageFlow()
{
    $variables = [
        '流程标题' => '张三的请假申请',
        '任务名称' => '部门经理审批',
        '提交人'   => '张三',
        '提交时间' => '2025-07-16 15:30:00'
    ];
    
    $messageId = NoticeDispatcherService::getInstance()->send(
        'workflow',
        'task_approval',
        $variables,
        [1]
    );
    
    $this->assertNotEmpty($messageId);
    
    // 验证消息内容
    $message = NoticeMessage::find($messageId);
    $this->assertStringContainsString('张三的请假申请', $message->content);
    $this->assertStringNotContainsString('${', $message->content);
}
```

## 📋 开发检查清单

### 新建模板时
- [ ] 模板内容使用中文变量名 `${变量名}`
- [ ] 变量名符合标准词典规范
- [ ] 变量名语义清晰、简洁明了
- [ ] 已添加到标准词典（如果是新变量）

### 编写发送代码时
- [ ] 变量键名使用中文
- [ ] 变量键名与模板中的变量名完全一致
- [ ] 已处理所有必填变量
- [ ] 已添加错误处理

### 测试验证时
- [ ] 测试消息发送成功
- [ ] 验证变量替换正确
- [ ] 检查无未替换变量
- [ ] 验证用户接收正常

### 代码审查时
- [ ] 检查变量命名规范
- [ ] 验证模板内容正确
- [ ] 确认测试覆盖完整
- [ ] 检查文档更新

## 🚫 常见错误和避免方法

### 1. 混用中英文变量名
```php
// ❌ 错误：混用中英文
$variables = [
    '流程标题' => $title,
    'task_name' => $taskName  // 错误：应该用中文
];

// ✅ 正确：统一使用中文
$variables = [
    '流程标题' => $title,
    '任务名称' => $taskName
];
```

### 2. 变量名不一致
```php
// ❌ 错误：模板和代码中变量名不一致
// 模板：${流程标题}
// 代码：
$variables = ['标题' => $title]; // 错误：应该是'流程标题'

// ✅ 正确：保持一致
$variables = ['流程标题' => $title];
```

### 3. 缺少必填变量
```php
// ❌ 错误：缺少必填变量
$variables = [
    '流程标题' => $title
    // 缺少其他必填变量
];

// ✅ 正确：提供所有必填变量
$variables = [
    '流程标题' => $title,
    '任务名称' => $taskName,
    '提交人'   => $submitter,
    '提交时间' => $createdAt
];
```

## 📈 规范执行和监督

### 1. 代码审查要求
- 所有涉及消息中心的代码必须经过规范检查
- 新建模板必须符合中文变量名规范
- 修改现有模板时必须同步更新代码

### 2. 自动化检查
- 建议在CI/CD流程中加入变量名规范检查
- 定期扫描模板内容，发现不规范的变量名
- 自动化测试覆盖所有消息模板

### 3. 培训和文档
- 新员工入职时必须学习此规范
- 定期更新标准变量名词典
- 维护最佳实践案例库

## 🔄 规范更新机制

### 1. 版本管理
- 规范文档采用版本管理
- 重大变更需要团队评审
- 保持向后兼容性

### 2. 反馈机制
- 鼓励开发人员提出改进建议
- 定期收集使用反馈
- 持续优化规范内容

### 3. 更新流程
- 提出变更建议
- 团队讨论和评审
- 更新文档和词典
- 通知相关人员
- 更新培训材料

---

**规范版本**: v1.0  
**最后更新**: 2025-07-16  
**下次审查**: 2025-10-16
