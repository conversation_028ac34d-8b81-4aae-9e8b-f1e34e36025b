<?php
declare(strict_types=1);

namespace app\daily\service;

use app\common\core\base\BaseService;
use app\daily\model\DailyPriceItem;
use app\daily\model\DailyPriceOrder;
use think\facade\Db;
use think\facade\Log;

/**
 * 每日报价明细表服务类
 */
class DailyPriceItemService extends BaseService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new DailyPriceItem();
        parent::__construct();
    }



    /**
     * 批量更新明细价格
     */
    public function batchUpdatePrices(array $updates): array
    {
        if (empty($updates)) {
            return $this->error('更新数据不能为空');
        }

        Db::startTrans();
        try {
            foreach ($updates as $update) {
                if (empty($update['id']) || !isset($update['unit_price'])) {
                    continue;
                }

                $item = DailyPriceItem::find($update['id']);
                if (!$item) {
                    continue;
                }

                // 计算价格变动
                if (isset($update['old_price']) && $update['old_price'] > 0) {
                    $item->price_change = $update['unit_price'] - $update['old_price'];
                    $item->change_rate = ($item->price_change / $update['old_price']) * 100;
                } else {
                    $item->price_change = 0;
                    $item->change_rate = 0;
                }

                $item->unit_price = $update['unit_price'];
                $item->is_manual_price = 1; // 标记为手动修改
                $item->updated_id = get_user_id();
                $item->save();
            }

            Db::commit();
            return $this->success('批量更新成功');
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('批量更新价格失败：' . $e->getMessage());
            return $this->error('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除明细
     */
    public function deleteItem(int $itemId): array
    {
        try {
            $item = DailyPriceItem::find($itemId);
            if (!$item) {
                return $this->error('明细不存在');
            }

            // 检查报价单状态
            $order = DailyPriceOrder::find($item->order_id);
            if (!$order || !in_array($order->approval_status, [
                DailyPriceOrder::STATUS_DRAFT,
                DailyPriceOrder::STATUS_REJECTED,
                DailyPriceOrder::STATUS_RECALLED
            ])) {
                return $this->error('报价单当前状态不允许删除明细');
            }

            Db::startTrans();
            try {
                $item->delete();
                
                // 更新报价单总数
                $remainingCount = DailyPriceItem::where('order_id', $item->order_id)->count();
                $order->total_items = $remainingCount;
                $order->updated_id = get_user_id();
                $order->save();

                Db::commit();
                return $this->success('删除成功', ['total_items' => $remainingCount]);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('删除明细失败：' . $e->getMessage());
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
}
