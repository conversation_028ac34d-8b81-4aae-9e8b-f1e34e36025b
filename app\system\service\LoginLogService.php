<?php

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\system\model\LoginLogModel;

class LoginLogService extends BaseService
{
	
	public function __construct()
	{
		$this->model = new LoginLogModel();
		parent::__construct();
	}
	
	// 记录日志
	public function create(array $data): bool
	{
		return $this->model->save($data);
	}
	
	public function getList(array $params): array
	{
		
		$where = [];
		
		// todo 条件搜索待完善，数据权限
		if (!empty($params['username'])) {
			$where[] = [
				[
					'username|real_name',
					'like',
					'%' . $params['username'] . '%'
				],
			];
		}
		
		if (!empty($params['status'])) {
			$where[] = [
				'status',
				'=',
				$params['status']
			];
		}
		
		if (!empty($params['created_at'])) {
			$where[] = [
				'created_at',
				'between',
				[
					$params['created_at'][0],
					$params['created_at'][1]
				]
			];
		}
		
		// 排序
		$order = 'created_at desc';
		
		// 分页
		$total = $this->getCrudService()
		              ->getCount($where);
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;
		
		$list = $this->getCrudService()
		             ->getPageList($where, $order, $page, $limit, [
			             'admin',
		             ]);
		
		return [
			'list'  => $list,
			'total' => $total,
			'page'  => $page,
			'limit' => $limit,
		];
	}
	
	public function getDetail($id)
	{
		return $this->getCrudService()
		            ->getOne([
			            [
				            'id',
				            '=',
				            $id
			            ]
		            ]);
	}
	
	public function delete($id): bool
	{
		$info = $this->getCrudService()
		             ->getOne([
			             [
				             'id',
				             '=',
				             $id
			             ]
		             ]);
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		return $info->delete();
	}
	
	
}