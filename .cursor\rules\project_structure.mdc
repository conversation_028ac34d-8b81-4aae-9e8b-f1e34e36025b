---
description: 
globs: 
alwaysApply: true
---
{
  "name": "项目结构",
  "description": "多租户前后端分离框架系统的项目结构说明",
  "glob": "**/*.php"
}

# 项目结构

这是一个基于ThinkPHP 8的多租户前后端分离管理系统框架。项目遵循ThinkPHP的标准目录结构，并进行了模块化设计。

## 目录结构

- [app/](mdc:app) - 应用目录
  - [app/system/](mdc:app/system) - 系统模块
    - [app/system/controller/](mdc:app/system/controller) - 控制器目录
      - [app/system/controller/permission/](mdc:app/system/controller/permission) - 权限相关控制器
    - [app/system/model/](mdc:app/system/model) - 模型目录
    - [app/system/service/](mdc:app/system/service) - 服务层目录
    - [app/system/validate/](mdc:app/system/validate) - 验证器目录
  - [app/common/](mdc:app/common) - 公共模块
- [config/](mdc:config) - 配置文件目录
- [public/](mdc:public) - 公共资源目录
- [route/](mdc:route) - 路由定义目录
- [runtime/](mdc:runtime) - 运行时目录
- [vendor/](mdc:vendor) - Composer依赖目录

## 核心文件

- [app/AppService.php](mdc:app/AppService.php) - 应用服务提供者
- [app/BaseController.php](mdc:app/BaseController.php) - 基础控制器
- [app/common.php](mdc:app/common.php) - 公共函数文件
- [app/middleware.php](mdc:app/middleware.php) - 全局中间件定义文件
- [base.sql](mdc:base.sql) - 数据库结构文件

