<?php
declare(strict_types=1);

namespace app\system\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\system\service\SystemArticleCategoryService;
use think\response\Json;

/**
 * 文章分类表控制器
 */
class ArticleCategoryController extends BaseController
{
	use CrudControllerTrait;
	
	/**
	 * @var SystemArticleCategoryService
	 */
	protected SystemArticleCategoryService $service;
	
	
	/**
	 * 构造函数
	 */
	public function initialize(): void
	{
		parent::initialize();
		$this->service = SystemArticleCategoryService::getInstance();
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		$result = $this->service->search($params, [], [
			'creator'
		]);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id], [
			'creator'
		]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}
	
	/**
	 * 状态切换
	 */
	public function status($id): Json
	{
		$result = $this->service->updateField($id, 'status', $this->request->post('status'));
		return $this->success('操作成功', $result);
	}
} 