<template>
  <div class="purchase-form-view">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="采购单号">
        {{ formData.purchase_no || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="采购类型">
        {{ getPurchaseTypeName(formData.purchase_type) }}
      </el-descriptions-item>

      <el-descriptions-item label="采购日期">
        {{ formData.purchase_date || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="供应商">
        {{ formData.supplier_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="所在部门">
        {{ formData.dept_name || '-' }}
      </el-descriptions-item>

      <!--      <el-descriptions-item label="联系人">
              {{ formData.contact_person || '-' }}
            </el-descriptions-item>

            <el-descriptions-item label="联系电话">
              {{ formData.contact_phone || '-' }}
            </el-descriptions-item>-->

      <el-descriptions-item label="预计到货日期">
        {{ formData.expected_date || '-' }}
      </el-descriptions-item>

      <!--      <el-descriptions-item label="总数量">
              {{ formData.total_quantity || 0 }}
            </el-descriptions-item>-->

      <el-descriptions-item label="总金额">
        ¥{{ formData.total_amount || 0 }}
      </el-descriptions-item>

      <el-descriptions-item label="采购事由" :span="2">
        {{ formData.reason || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        {{ formData.remark }}
      </el-descriptions-item>

      <!-- 采购明细 -->
      <el-descriptions-item
        label="采购明细"
        :span="2"
        v-if="formData.items && formData.items.length > 0"
      >
        <el-table :data="formData.items" border size="small">
          <el-table-column prop="product_name" label="产品名称" />
          <el-table-column prop="quantity" label="数量" />
          <el-table-column prop="unit_price" label="单价" />
          <el-table-column prop="total_amount" label="小计" />
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem, ElTable, ElTableColumn } from 'element-plus'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'ims_purchase_approval'
    }
  })

  // 获取采购类型名称
  const getPurchaseTypeName = (type: number): string => {
    const types = {
      1: '原材料采购',
      2: '设备采购',
      3: '办公用品采购',
      4: '其他采购'
    }
    return types[type] || '未知类型'
  }
</script>

<style scoped lang="scss"></style>
