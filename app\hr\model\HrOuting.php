<?php
declare(strict_types=1);

namespace app\hr\model;

use app\common\core\base\BaseModel;
use app\workflow\constants\WorkflowStatusConstant;

/**
 * 外出申请表模型
 */
class HrOuting extends BaseModel
{
	// 设置表名
	protected $name = 'hr_outing';
	
	// 字段类型转换
	protected $type = [
		'workflow_instance_id' => 'integer',
		'approval_status'      => 'integer',
		'submitter_id'         => 'integer',
		'duration'             => 'float',
		'creator_id'           => 'integer',
	];
	
	public function getAttachmentAttr($value)
	{
		return empty($value)
			? []
			: explode(',', $value);
	}
	
	// 审批状态常量
	const STATUS_DRAFT      = WorkflowStatusConstant::STATUS_DRAFT;
	const STATUS_PROCESSING = WorkflowStatusConstant::STATUS_PROCESSING;
	const STATUS_COMPLETED  = WorkflowStatusConstant::STATUS_COMPLETED;
	const STATUS_REJECTED   = WorkflowStatusConstant::STATUS_REJECTED;
	const STATUS_TERMINATED = WorkflowStatusConstant::STATUS_TERMINATED;
	const STATUS_RECALLED   = WorkflowStatusConstant::STATUS_RECALLED;
	const STATUS_VOID       = WorkflowStatusConstant::STATUS_VOID;
	
	/**
	 * 获取默认搜索字段
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'approval_status' => ['type' => 'eq'],
			'start_time'      => ['type' => 'datetime'],
			'end_time'        => ['type' => 'datetime'],
			'duration'        => ['type' => 'between'],
			'purpose'         => ['type' => 'like'],
			'submit_time'     => ['type' => 'datetime'],
			'approval_time'   => ['type' => 'datetime'],
			'created_at'      => ['type' => 'date'],
		];
	}
	
	/**
	 * 关联提交人
	 */
	public function submitter()
	{
		return $this->belongsTo(\app\system\model\SystemAdmin::class, 'submitter_id', 'id')
		            ->bind([
			            'submitter_name' => 'username'
		            ]);
	}
	
	/**
	 * 关联创建人
	 */
	public function creator()
	{
		return $this->belongsTo(\app\system\model\SystemAdmin::class, 'creator_id', 'id')
		            ->bind([
			            'creator_name' => 'username'
		            ]);
	}
	
	/**
	 * 获取审批状态文本
	 */
	public function getApprovalStatusTextAttr($value, $data)
	{
		$statuses = [
			self::STATUS_DRAFT      => '草稿',
			self::STATUS_PROCESSING => '审批中',
			self::STATUS_COMPLETED  => '已通过',
			self::STATUS_REJECTED   => '已拒绝',
			self::STATUS_TERMINATED => '已终止',
			self::STATUS_RECALLED   => '已撤回',
			self::STATUS_VOID       => '已作废',
		];
		
		return $statuses[$data['approval_status']] ?? '未知';
	}
}
