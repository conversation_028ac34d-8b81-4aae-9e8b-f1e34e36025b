<?php
declare(strict_types=1);

namespace app\system\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\system\service\SystemDictTypeService;
use think\Request;

/**
 * 字典类型表控制器
 */
class SystemDictTypeController extends BaseController
{
    use CrudControllerTrait;
    
    /**
     * 服务实例
     * @var SystemDictTypeService
     */
    protected $service;
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->service = SystemDictTypeService::getInstance();
    }
} 