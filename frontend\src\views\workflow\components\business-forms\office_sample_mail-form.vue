<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="样品名称" prop="sample_name">
              <ElInput
                v-model="formData.sample_name"
                placeholder="请输入样品名称"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="寄件人手机号" prop="sender_phone">
              <ElInput
                v-model="formData.sender_phone"
                placeholder="请输入寄件人手机号"
                :disabled="!isEditable"
                @input="handlePhoneInput"
                maxlength="11"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="收件信息" prop="delivery_address">
          <ElInput
            v-model="formData.delivery_address"
            type="textarea"
            :rows="3"
            placeholder="请输入收件人姓名、地址、联系方式等信息"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="样品描述" prop="sample_description">
          <ElInput
            v-model="formData.sample_description"
            type="textarea"
            :rows="3"
            placeholder="请输入样品描述"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
          <div style="margin-top: 8px; color: #909399; font-size: 12px">
            提示：请包含标签名称、数量、邮寄单位等信息
          </div>
        </ElFormItem>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface OfficeSampleMailFormData {
    id?: number
    sample_name: string
    sample_description: string
    sender_phone: string
    delivery_address: string
    remark: string
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `样品邮寄申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起样品邮寄申请'
  })

  /** 表单数据 */
  const formData = reactive<OfficeSampleMailFormData & any>({
    sample_name: '',
    sample_description: '',
    sender_phone: '',
    delivery_address: '',
    remark: '',
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    sample_name: [{ required: true, message: '请输入样品名称', trigger: 'blur' }],
    sample_description: [{ required: true, message: '请输入样品描述', trigger: 'blur' }],
    sender_phone: [
      { required: true, message: '请输入寄件人手机号', trigger: 'blur' },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ],
    delivery_address: [{ required: true, message: '请输入收件信息', trigger: 'blur' }]
  }

  // ==================== 方法定义 ====================

  /**
   * 处理手机号输入，只允许数字
   */
  const handlePhoneInput = (value: string) => {
    // 只保留数字
    const numericValue = value.replace(/[^\d]/g, '')
    formData.sender_phone = numericValue
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('office_sample_mail-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      sample_name: '',
      sample_description: '',
      sender_phone: '',
      delivery_address: '',
      remark: '',
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        console.log('样品邮寄申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('office_sample_mail-form setFormData called with:', data)
    Object.assign(formData, data)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('office_sample_mail-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: OfficeSampleMailFormData = {
        sample_name: formData.sample_name,
        sample_description: formData.sample_description,
        sender_phone: formData.sender_phone,
        delivery_address: formData.delivery_address,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('样品邮寄申请保存数据:', submitData)
      emit('save', submitData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: OfficeSampleMailFormData = {
        sample_name: formData.sample_name,
        sample_description: formData.sample_description,
        sender_phone: formData.sender_phone,
        delivery_address: formData.delivery_address,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('样品邮寄申请提交数据:', submitData)
      emit('submit', submitData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }
</style>
