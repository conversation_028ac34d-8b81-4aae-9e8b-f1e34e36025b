
# 字段注释驱动的CRUD生成器文档

## 一、设计文档

### 1.1 系统架构

CRUD生成器基于ThinkPHP框架开发，采用字段注释驱动方式，通过解析数据库表结构和字段注释来生成完整的前后端CRUD代码。系统架构如下：

```
app/common/core/crud/
├── command/                  # 命令行工具
│   ├── CrudGenerator.php     # CRUD代码生成器主类
│   ├── CommentMigrator.php   # 表注释迁移工具
│   ├── helpers/              # 辅助类
│   │   ├── CrudHelper.php    # CRUD生成辅助类
│   │   ├── FieldCommentParser.php # 字段注释解析器
│   │   └── config.php        # 配置文件
│   └── templates/            # 代码模板
│       └── crud/             # CRUD模板
├── docs/                     # 文档
│   └── 字段注释驱动的CRUD生成方案.md  # 设计方案文档
├── interface/                # 接口定义
├── traits/                   # 功能特性
└── CrudService.php           # CRUD服务基类
```

### 1.2 核心组件

#### 1.2.1 CrudGenerator

负责生成完整CRUD代码，包括模型、服务类、控制器、路由和前端代码。主要功能：

- 获取表结构信息
- 解析字段注释中的标记和配置
- 生成后端PHP代码（模型、服务类、控制器）
- 生成前端Vue代码（API接口、列表页面）

#### 1.2.2 FieldCommentParser

专门用于解析字段注释中的标记信息，支持多种格式：

- 基本格式：`字段描述 | @标记1 @标记2=值 @标记3=值1,值2`
- 支持简写标记：如`@s=like`代表`@search=like`
- 支持多值标记：如`@scene=list,detail,select`
- 支持描述中的选项信息：如`状态:1=启用,0=禁用`

#### 1.2.3 CrudHelper

提供CRUD生成过程中所需的各种辅助功能：

- 模板渲染
- 配置解析
- 字段类型转换
- 场景配置生成
- 搜索配置生成
- 排序配置生成
- 表单控件配置生成

#### 1.2.4 CommentMigrator

提供从表注释驱动迁移到字段注释驱动的工具：

- 解析表注释中的配置
- 将配置迁移到对应字段的注释中
- 保留全局配置在表注释中
- 生成修改字段注释的SQL语句

### 1.3 工作流程

1. 用户通过命令行启动CRUD生成器
2. 系统获取指定表的结构信息
3. 解析表注释和字段注释中的配置标记
4. 根据解析结果和模板生成后端代码
5. 根据解析结果和模板生成前端代码
6. 输出生成结果

## 二、说明文档

### 2.1 功能特性

#### 2.1.1 字段注释驱动

将配置信息从表注释迁移到各个字段的注释中，使配置与字段定义紧密关联：

- 提高代码可读性和可维护性
- 降低配置复杂度
- 避免表注释过长
- 方便单个字段配置的修改

#### 2.1.2 标记系统

支持丰富的标记语法，用于配置字段的各种行为：

- 搜索：`@search=like`或`@s=like`
- 排序：`@order`或`@o`
- 编辑：`@edit`或`@e`
- 场景：`@scene=list,detail,select`
- 验证：`@validate=required,min:3,max:50`或`@v=required,min:3,max:50`
- 关联：`@relation=belongsTo:Role:role_id:id`或`@r=belongsTo:Role:role_id:id`
- 表单控件：`@control=select`或`@c=select`
- 选项：`@options=启用:1,禁用:0`或`@opt=启用:1,禁用:0`

#### 2.1.3 代码生成

一键生成完整的CRUD代码：

- 后端：模型、服务类、控制器、路由
- 前端：API接口、列表页面（包含搜索、表格、表单、详情）
- 自动生成验证规则
- 自动处理关联关系
- 自动配置表单控件

#### 2.1.4 迁移工具

提供从表注释驱动迁移到字段注释驱动的工具：

- 自动迁移搜索、排序、编辑等配置
- 保留全局配置在表注释中
- 支持预演模式，查看但不执行SQL

### 2.2 技术实现

#### 2.2.1 命令行工具

基于ThinkPHP的命令行组件实现，提供两个命令：

- `php think crud:generate table_name`：生成CRUD代码
- `php think crud:migrate table_name`：迁移表注释到字段注释

#### 2.2.2 模板引擎

使用简单的字符串替换方式实现模板渲染：

- 模板变量格式：`{{VARIABLE_NAME}}`
- 支持模型、服务类、控制器、路由、前端代码等多种模板

#### 2.2.3 数据库交互

使用ThinkPHP的Db门面与数据库交互：

- 获取表结构
- 获取字段信息
- 执行SQL语句

## 三、使用文档

### 3.1 安装配置

1. 确保已安装ThinkPHP框架（v6.0+）
2. 将CRUD生成器代码放置在app/common/core/crud目录下
3. 注册命令，在app/command.php中添加：

```php
return [
    'crud:generate' => 'app\common\core\crud\command\CrudGenerator',
    'crud:migrate'  => 'app\common\core\crud\command\CommentMigrator',
];
```

### 3.2 数据库表设计

按照字段注释驱动的方式设计数据库表：

```sql
CREATE TABLE `system_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID | @o @scene=list,detail,select',
  `username` varchar(50) NOT NULL COMMENT '用户名 | @s=like @e @o @req @scene=list,detail,select @v=required,min:3,max:50 @w=120',
  `password` varchar(100) NOT NULL COMMENT '密码 | @h @f @req @v=required,min:6,max:20 @scene=add',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称 | @s=like @e @scene=list,detail @c=input',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用 | @s=eq @e @o @scene=list,detail @c=switch @fmt=status',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
COMMENT='用户表 @module:admin @order:id:desc @enable_permission:true';
```

### 3.3 生成CRUD代码

使用命令行生成CRUD代码：

```bash
# 基本用法
php think crud:generate table_name

# 指定模块
php think crud:generate table_name --module=admin

# 生成前端代码
php think crud:generate table_name --with-frontend

# 强制覆盖已存在文件
php think crud:generate table_name --force
```

### 3.4 从表注释迁移到字段注释

使用迁移工具将表注释配置迁移到字段注释：

```bash
# 查看将要执行的SQL（不执行）
php think crud:migrate table_name --dry-run

# 执行迁移
php think crud:migrate table_name
```

### 3.5 字段注释标记参考

| 标记 | 完整写法 | 简写 | 说明 | 可能的值 | 示例 |
|------|---------|-----|------|---------|------|
| @search | @search=type | @s=type | 搜索字段及类型 | eq, neq, like, between, date | @search=like 或 @s=like |
| @hide | @hide | @h | 默认隐藏字段 | - | @hide 或 @h |
| @edit | @edit | @e | 允许单独编辑 | - | @edit 或 @e |
| @scene | @scene=场景列表 | - | 字段所属场景 | list, detail, select | @scene=list,detail |
| @validate | @validate=规则 | @v=规则 | 验证规则 | required, min:3 | @validate=required,min:3 |
| @control | @control=类型 | @c=类型 | 表单控件类型 | input, select, switch | @control=select |
| @formatter | @formatter=类型 | @fmt=类型 | 表格格式化方式 | date, status, tag | @formatter=status |

### 3.6 示例

#### 3.6.1 创建数据库表

```sql
CREATE TABLE `blog_article` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '文章ID | @o @scene=list,detail,select',
  `title` varchar(100) NOT NULL COMMENT '标题 | @s=like @e @req @scene=list,detail,select @v=required,max:100',
  `content` text COMMENT '内容 | @e @scene=detail @c=textarea',
  `category_id` int unsigned DEFAULT NULL COMMENT '分类ID | @s=eq @e @scene=list,detail @r=belongsTo:Category:category_id:id',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=已发布,0=草稿 | @s=eq @e @o @scene=list,detail @c=switch @fmt=status',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间 | @s=date @o @scene=list,detail @fmt=datetime',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间 | @o @scene=detail @fmt=datetime',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
COMMENT='博客文章 @module:blog @order:id:desc @enable_permission:true';
```

#### 3.6.2 生成CRUD代码

```bash
php think crud:generate blog_article --module=blog --with-frontend
```

#### 3.6.3 生成的文件结构

```
app/
├── blog/
│   ├── controller/
│   │   └── BlogArticleController.php
│   ├── model/
│   │   └── BlogArticleModel.php
│   ├── service/
│   │   └── BlogArticleService.php
│   └── route/
│       └── route.php
└── frontend/src/views/
    └── blog_article/
        ├── blog_articleApi.ts
        └── list.vue
```

## 四、前后端模板优化建议

### 4.1 后端模板优化

1. **模型模板**:
   - 增加软删除、数据权限、多租户等特性的自动配置
   - 优化关联关系代码生成，支持更多关联类型
   - 添加常用作用域方法的自动生成

2. **服务类模板**:
   - 增加批量操作方法（批量删除、批量更新等）
   - 添加导入导出功能的支持
   - 增加缓存机制支持

3. **控制器模板**:
   - 增加API文档注解
   - 完善错误处理和异常捕获
   - 添加更多实用的辅助方法

### 4.2 前端模板优化

1. **API接口模板**:
   - 增加TypeScript类型定义的完整性
   - 添加请求参数和响应数据的接口定义
   - 优化错误处理机制

2. **列表页面模板**:
   - 升级为组合式API (Composition API)风格
   - 增加高级筛选功能
   - 优化表格组件，支持更多自定义操作
   - 添加数据可视化图表组件
   - 增强表单验证和交互体验
   - 优化移动端适配

3. **新增页面模板**:
   - 添加详情页模板
   - 添加统计分析页模板
   - 添加树形结构管理页模板
   - 添加导入导出页面模板

### 4.3 其他优化建议

1. **国际化支持**:
   - 添加多语言支持，生成i18n配置文件

2. **主题定制**:
   - 支持主题切换和定制

3. **权限控制**:
   - 增强权限控制机制，支持更细粒度的权限配置

4. **代码质量**:
   - 增加代码注释和文档字符串
   - 优化代码格式，提高可读性
   - 添加单元测试模板

这些优化将使CRUD生成器生成的代码更加现代化、功能更完善，更好地满足复杂业务需求。
