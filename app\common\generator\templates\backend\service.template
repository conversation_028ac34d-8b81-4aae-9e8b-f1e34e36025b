<?php
declare(strict_types=1);

namespace {{NAMESPACE}};

use app\common\core\base\BaseService;
use app\{{MODULE}}\model\{{ENTITY_NAME}};
{{#HAS_EXPORT}}
use app\common\core\crud\traits\ExportableTrait;
{{/HAS_EXPORT}}
{{#HAS_IMPORT}}
use app\common\core\crud\traits\ImportableTrait;
{{/HAS_IMPORT}}

/**
 * {{COMMENT}}服务类
 */
class {{ENTITY_NAME}}Service extends BaseService
{
{{#HAS_EXPORT}}
    use ExportableTrait;
{{/HAS_EXPORT}}
{{#HAS_IMPORT}}
    use ImportableTrait;
{{/HAS_IMPORT}}
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new {{ENTITY_NAME}}();
        parent::__construct();
    }
    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [
{{#search_fields}}
            '{{field}}' => ['type' => '{{type}}'],
{{/search_fields}}
        ];
    }
    
    /**
     * 获取验证规则
     * 
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            // 在这里定义验证规则
            // 例如：'username' => 'require|unique:{{TABLE_NAME}}',
        ];
        
        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('{{PRIMARY_KEY}}', $ids)->delete();
    }
} 