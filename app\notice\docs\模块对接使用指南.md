# 模块对接使用指南

## 📋 概述

本文档详细说明了各业务模块如何集成和使用消息中心服务，包括工作流、CRM、人事、财务等模块的具体对接方式。

## 🔧 基础对接步骤

### 1. 引入服务类
```php
use app\notice\service\NoticeDispatcherService;
```

### 2. 基本调用格式
```php
$result = NoticeDispatcherService::getInstance()->send(
    $module,     // 模块名称
    $action,     // 动作名称
    $data,       // 业务数据
    $recipients, // 接收人ID数组
    $options     // 可选参数
);
```

## 📦 各模块对接示例

### 1. 工作流模块 (Workflow)

#### 1.1 任务审批通知
```php
// 场景：提交审批任务时通知审批人
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    [
        'task_name' => $task['node_name'],
        'title' => $instance['title'],
        'submitter_name' => $instance['submitter_name'],
        'created_at' => $instance['start_time'],
        'detail_url' => url('workflow/task/detail', ['id' => $task['id']])
    ],
    [$task['approver_id']],
    [
        'creator_id' => $instance['submitter_id'],
        'business_id' => $instance['id'],
        'module_code' => 'workflow'
    ]
);
```

#### 1.2 审批结果通知
```php
// 场景：审批完成后通知申请人
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approved',
    [
        'title' => $instance['title'],
        'result' => $status == 2 ? '通过' : '驳回',
        'opinion' => $task['opinion'],
        'approver_name' => $task['approver_name'],
        'completed_at' => date('Y-m-d H:i:s')
    ],
    [$instance['submitter_id']],
    [
        'creator_id' => $task['approver_id'],
        'business_id' => $instance['id']
    ]
);
```

#### 1.3 抄送通知
```php
// 场景：流程抄送给相关人员
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_cc',
    [
        'title' => $instance['title'],
        'submitter_name' => $instance['submitter_name'],
        'node_name' => $task['node_name'],
        'cc_time' => date('Y-m-d H:i:s')
    ],
    $ccUserIds,
    [
        'creator_id' => $instance['submitter_id'],
        'send_channels' => 'site,wework'
    ]
);
```

#### 1.4 催办通知
```php
// 场景：催办超时未处理的任务
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_urge',
    [
        'title' => $instance['title'],
        'task_name' => $task['node_name'],
        'urger_name' => $urger['real_name'],
        'created_at' => date('Y-m-d H:i:s'),
        'reason' => $reason
    ],
    [$task['approver_id']],
    [
        'creator_id' => $urgerId,
        'priority' => 1  // 高优先级
    ]
);
```

#### 1.5 转交通知
```php
// 场景：任务转交给其他人处理
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_transfer',
    [
        'title' => $instance['title'],
        'node_name' => $task['node_name'],
        'from_user' => $fromUser['real_name'],
        'to_user' => $toUser['real_name'],
        'transfer_time' => date('Y-m-d H:i:s'),
        'detail_url' => url('workflow/task/detail', ['id' => $task['id']])
    ],
    [$toUserId],
    [
        'creator_id' => $fromUserId,
        'business_id' => $instance['id']
    ]
);
```

#### 1.6 终止通知
```php
// 场景：工作流被终止
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_terminated',
    [
        'title' => $instance['title'],
        'result' => '已终止',
        'submit_time' => $instance['created_at'],
        'terminate_time' => date('Y-m-d H:i:s'),
        'terminate_by' => $operator['real_name'],
        'reason' => $reason,
        'detail_url' => url('workflow/detail', ['id' => $instance['id']])
    ],
    [$instance['submitter_id']],
    [
        'creator_id' => $operatorId,
        'business_id' => $instance['id']
    ]
);
```

#### 1.7 作废通知 🆕
```php
// 场景：工作流被作废
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_void',
    [
        'title' => $instance['title'],
        'result' => '已作废',
        'submit_time' => $instance['created_at'],
        'void_time' => date('Y-m-d H:i:s'),
        'void_by' => $operator['real_name'],
        'reason' => $reason,
        'detail_url' => url('workflow/detail', ['id' => $instance['id']])
    ],
    [$instance['submitter_id']],
    [
        'creator_id' => $operatorId,
        'business_id' => $instance['id']
    ]
);
```

### 2. CRM模块

#### 2.1 线索转化通知
```php
// 场景：线索转化为客户时通知相关人员
NoticeDispatcherService::getInstance()->send(
    'crm',
    'lead_convert',
    [
        'lead_name' => $lead['lead_name'],
        'customer_name' => $customer['customer_name']
    ],
    [$lead['owner_user_id']],
    [
        'creator_id' => get_user_id(),
        'business_id' => $customer['id'],
        'module_code' => 'crm'
    ]
);
```

#### 2.2 客户分配通知
```php
// 场景：客户分配给销售人员
NoticeDispatcherService::getInstance()->send(
    'crm',
    'customer_assign',
    [
        'customer_name' => $customer['customer_name'],
        'customer_phone' => $customer['phone']
    ],
    [$newOwnerId],
    [
        'creator_id' => get_user_id(),
        'business_id' => $customer['id']
    ]
);
```

#### 2.3 商机阶段变更通知
```php
// 场景：商机阶段发生变更
NoticeDispatcherService::getInstance()->send(
    'crm',
    'business_stage_change',
    [
        'business_name' => $business['business_name'],
        'old_stage' => $oldStage['stage_name'],
        'new_stage' => $newStage['stage_name'],
        'change_reason' => $reason
    ],
    [$business['owner_user_id']],
    [
        'creator_id' => get_user_id(),
        'business_id' => $business['id']
    ]
);
```

#### 2.4 合同审批通知
```php
// 场景：合同提交审批
NoticeDispatcherService::getInstance()->send(
    'crm',
    'contract_approval',
    [
        'contract_no' => $contract['contract_no'],
        'customer_name' => $contract['customer_name'],
        'contract_amount' => $contract['contract_amount']
    ],
    $approverIds,
    [
        'creator_id' => $contract['creator_id'],
        'business_id' => $contract['id'],
        'send_channels' => 'site,email'
    ]
);
```

### 3. 人事模块 (HR)

#### 3.1 请假申请通知
```php
// 场景：员工提交请假申请
NoticeDispatcherService::getInstance()->send(
    'attendance',
    'leave_apply',
    [
        'leave_type' => $leave['type_name'],
        'applicant_name' => $leave['creator_name'],
        'start_time' => $leave['start_time'],
        'end_time' => $leave['end_time']
    ],
    $approverIds,
    [
        'creator_id' => $leave['creator_id'],
        'business_id' => $leave['id']
    ]
);
```

#### 3.2 请假审批结果通知
```php
// 场景：请假审批完成
NoticeDispatcherService::getInstance()->send(
    'attendance',
    'leave_result',
    [
        'leave_type' => $leave['type_name'],
        'approval_result' => $leave['status_text'],
        'approver_name' => $approver['real_name'],
        'approve_time' => date('Y-m-d H:i:s')
    ],
    [$leave['creator_id']],
    [
        'creator_id' => $approverId,
        'business_id' => $leave['id']
    ]
);
```

### 4. 财务模块 (Finance)

#### 4.1 付款通知
```php
// 场景：付款申请提交
NoticeDispatcherService::getInstance()->send(
    'finance',
    'payment_notice',
    [
        'payment_item' => $payment['title'],
        'applicant_name' => $payment['creator_name'],
        'apply_time' => $payment['created_at']
    ],
    $financeUserIds,
    [
        'creator_id' => $payment['creator_id'],
        'business_id' => $payment['id']
    ]
);
```

### 5. 系统模块 (System)

#### 5.1 系统通知
```php
// 场景：系统维护、公告等通知
NoticeDispatcherService::getInstance()->send(
    'system',
    'notice',
    [
        'title' => '系统维护通知',
        'content' => '系统将于今晚22:00-24:00进行维护，请提前保存工作。'
    ],
    $allUserIds,
    [
        'creator_id' => 1,
        'send_channels' => 'site,email,sms',
        'priority' => 2  // 紧急
    ]
);
```

## 🎯 最佳实践

### 1. 错误处理
```php
try {
    $result = NoticeDispatcherService::getInstance()->send(
        'workflow',
        'task_approval',
        $data,
        $recipients,
        $options
    );
    
    if (!$result) {
        Log::error('消息发送失败', ['data' => $data]);
    }
} catch (\Exception $e) {
    Log::error('消息发送异常: ' . $e->getMessage());
}
```

### 2. 批量发送优化
```php
// 避免在循环中发送消息，应该收集接收人后批量发送
$recipients = [];
foreach ($tasks as $task) {
    $recipients[] = $task['approver_id'];
}

// 批量发送
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    $commonData,
    array_unique($recipients),
    $options
);
```

### 3. 延迟发送
```php
// 延迟30分钟发送催办通知
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_urge',
    $data,
    $recipients,
    [
        'delay_minutes' => 30,
        'creator_id' => get_user_id()
    ]
);
```

### 4. 多渠道发送
```php
// 重要通知通过多个渠道发送
NoticeDispatcherService::getInstance()->send(
    'system',
    'notice',
    $data,
    $recipients,
    [
        'send_channels' => 'site,email,sms',
        'priority' => 2
    ]
);
```

## 🔍 调试和测试

### 1. 开启调试日志
```php
// 在发送前开启日志
Log::info('准备发送消息', [
    'module' => $module,
    'action' => $action,
    'recipients' => $recipients
]);
```

### 2. 测试模板变量
```php
// 测试模板变量是否完整
$template = NoticeTemplateService::getInstance()->getTemplateByCode('workflow_task_approval');
$variables = json_decode($template['variables_config'], true);

foreach ($variables['variables'] as $var) {
    if ($var['required'] && !isset($data[$var['field']])) {
        throw new \Exception("缺少必填变量: {$var['name']}");
    }
}
```

### 3. 验证接收人
```php
// 验证接收人是否存在
$validRecipients = [];
foreach ($recipients as $userId) {
    if (AdminModel::where('id', $userId)->count() > 0) {
        $validRecipients[] = $userId;
    }
}
```

## ⚠️ 注意事项

1. **模板编码规范**: 必须遵循 `{模块}_{动作}` 格式
2. **变量命名**: 变量名应该清晰明确，避免使用中文作为字段名
3. **接收人验证**: 发送前应验证接收人是否存在且有效
4. **错误处理**: 必须处理发送失败的情况
5. **性能考虑**: 避免在高频操作中同步发送消息，建议使用队列
6. **权限控制**: 确保只向有权限的用户发送相关消息

## 📞 技术支持

如有问题，请查看：
1. 系统日志：`runtime/log/`
2. 队列状态：`notice_queue` 表
3. 消息记录：`notice_message` 表
4. 模板配置：`notice_template` 表
