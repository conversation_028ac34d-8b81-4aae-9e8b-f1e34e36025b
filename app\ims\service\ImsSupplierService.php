<?php
declare(strict_types=1);

namespace app\ims\service;

use app\common\core\base\BaseService;
use app\ims\model\ImsSupplier;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 供应商表服务类
 */
class ImsSupplierService extends BaseService
{

    use ExportableTrait;


    use ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new ImsSupplier();
        parent::__construct();
    }

    /**
     * 删除前检查关联
     */
    public function delete($id): bool
    {
        $supplier = $this->model->find($id);
        if (!$supplier) {
            throw new \Exception('供应商不存在');
        }

        // 检查是否有关联产品
        if (!$supplier->canDelete()) {
            throw new \Exception('该供应商下还有产品，无法删除');
        }

        return parent::delete($id);
    }

    /**
     * 获取供应商选项（用于产品表单）
     */
    public function getOptions(): array
    {
        $suppliers = $this->model->where('status', 1)
                                ->field('id,name,code')
                                ->order('name asc')
                                ->select()
                                ->toArray();

        // 为每个供应商添加显示标签和value字段，格式：供应商名称 (编码)
        foreach ($suppliers as &$supplier) {
            $supplier['label'] = $supplier['name'] . (!empty($supplier['code']) ? ' (' . $supplier['code'] . ')' : '');
            $supplier['value'] = $supplier['id']; // 添加value字段用于前端选择器
        }

        return $suppliers;
    }

    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

            'name' => ['type' => 'like'],

            'code' => ['type' => 'like'],

            'contact_name' => ['type' => 'like'],

            'phone' => ['type' => 'like'],

            'province' => ['type' => 'eq'],

            'city' => ['type' => 'eq'],

            'district' => ['type' => 'eq'],

            'status' => ['type' => 'eq'],

        ];
    }
    
    /**
     * 获取验证规则
     * 
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            // 在这里定义验证规则
            // 例如：'username' => 'require|unique:ims_supplier',
        ];
        
        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除时的检查
     *
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        // 检查每个供应商是否可以删除
        $suppliers = $this->model->whereIn('id', $ids)->select();
        $cannotDelete = [];

        foreach ($suppliers as $supplier) {
            if (!$supplier->canDelete()) {
                $cannotDelete[] = $supplier->name;
            }
        }

        if (!empty($cannotDelete)) {
            throw new \Exception('以下供应商下还有产品，无法删除：' . implode('、', $cannotDelete));
        }

        return $this->model->whereIn('id', $ids)->delete();
    }
} 