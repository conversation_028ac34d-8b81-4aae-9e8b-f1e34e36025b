<?php
declare(strict_types=1);

namespace app\notice\service\interfaces;

/**
 * 消息通知系统核心服务接口
 */
interface NoticeServiceInterface
{
    /**
     * 发送消息
     *
     * @param string $templateCode 模板编码
     * @param array $variables 模板变量
     * @param array $recipients 接收人ID数组
     * @param array $options 选项参数
     * @return int|bool 成功返回消息ID，失败返回false
     */
    public function send(string $templateCode, array $variables, array $recipients, array $options = []): int|bool;
    
    /**
     * 获取用户未读消息数量
     *
     * @param int $userId 用户ID
     * @return int 未读消息数量
     */
    public function getUnreadCount(int $userId): int;
    
    /**
     * 标记消息为已读
     *
     * @param int $messageId 消息ID
     * @return bool 是否成功
     */
    public function markAsRead(int $messageId): bool;
    
    /**
     * 批量标记消息为已读
     *
     * @param array $messageIds 消息ID数组
     * @return bool 是否成功
     */
    public function batchMarkAsRead(array $messageIds): bool;
    
    /**
     * 标记全部消息为已读
     *
     * @param int $userId 用户ID
     * @return bool 是否成功
     */
    public function markAllAsRead(int $userId): bool;
    
    /**
     * 获取用户消息列表
     *
     * @param int $userId 用户ID
     * @param array $filters 过滤条件
     * @param array $pagination 分页参数
     * @return array 消息列表
     */
    public function getUserMessages(int $userId, array $filters = [], array $pagination = []): array;
    
    /**
     * 删除用户消息
     *
     * @param int $messageId 消息ID
     * @param int $userId 用户ID
     * @return bool 是否成功
     */
    public function deleteUserMessage(int $messageId, int $userId): bool;
    
    /**
     * 批量删除用户消息
     *
     * @param array $messageIds 消息ID数组
     * @param int $userId 用户ID
     * @return bool 是否成功
     */
    public function batchDeleteUserMessages(array $messageIds, int $userId): bool;
} 