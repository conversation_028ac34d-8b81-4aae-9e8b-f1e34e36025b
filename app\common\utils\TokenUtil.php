<?php
declare(strict_types=1);

namespace app\common\utils;

use app\common\core\constants\CacheConstant;
use think\facade\Cache;

/**
 * 令牌工具类
 */
class TokenUtil
{
	
	/**
	 * 生成token并缓存
	 *
	 * @param int   $userId   用户ID
	 * @param array $userData 用户数据
	 * @param int   $expire   过期时间（秒）
	 * @return string
	 */
	public static function generate(int $adminId, array $adminData = [], int $expire = 7200): string
	{
		// 生成唯一token
		$token = md5(uniqid((string)$adminId, true) . time() . rand(100000, 999999));
		
		// 缓存数据
		$cacheData = [
			'admin_id'     => $adminId,
			'data'        => $adminData,
			'create_time' => time()
		];
		
		// 设置缓存，使用用户ID作为tag
		Cache::tag(self::getUserTag())
		     ->set($token, $cacheData, $expire);
		
		return $token;
	}
	
	/**
	 * 刷新token有效期
	 *
	 * @param string $token  token字符串
	 * @param int    $expire 过期时间（秒）
	 * @return bool
	 */
	public static function refresh(string $token, int $expire = 7200): bool
	{
		if (!$tokenData = Cache::get($token)) {
			return false;
		}
		
		// 更新缓存时间
		Cache::tag(self::getUserTag())
		     ->set($token, $tokenData, $expire);
		
		return true;
	}
	
	/**
	 * 删除指定token
	 *
	 * @param string $token token字符串
	 * @return bool
	 */
	public static function delete(string $token): bool
	{
		return Cache::delete($token);
	}
	
	/**
	 * 清空所有用户token
	 *
	 * @return bool
	 */
	public static function clear(): bool
	{
		return Cache::tag(self::getUserTag())
		            ->clear();
	}
	
	/**
	 * 获取用户缓存标签
	 *
	 * @return string
	 */
	protected static function getUserTag(): string
	{
		return CacheConstant::AUTH_TAG_PREFIX;
	}
	
	public static function getTokenInfo($token)
	{
		if (!$tokenData = Cache::get($token)) {
			return false;
		}
		
		return $tokenData;
	}
} 