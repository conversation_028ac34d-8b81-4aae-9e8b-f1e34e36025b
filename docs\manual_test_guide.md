# 工作流表单手动测试指南

## 🚀 快速测试步骤

### **准备工作**
1. 打开浏览器访问：http://localhost:3006/#/office/workflow/application
2. 使用账号登录：admin / 123456
3. 确保数据库配置正确（执行check_workflow_config.sql检查）

### **测试流程**
每个表单都按照以下步骤测试：
1. 点击"申请"按钮
2. 选择对应的申请类型
3. 填写表单数据
4. 测试保存功能
5. 测试编辑功能
6. 测试提交功能

## 📝 具体测试用例

### **1. 出差申请 (hr_business_trip)**
```
测试数据：
- 出差开始时间：2025-07-29 09:00
- 出差结束时间：2025-07-31 18:00
- 出差地点：北京
- 出差事由：客户拜访
- 明细1：2025-07-29, 北京, 客户会议
- 明细2：2025-07-30, 北京, 项目洽谈
- 备注：重要客户拜访

验证点：
✅ 天数自动计算（3天）
✅ 明细表格操作正常
✅ 至少一个明细验证
```

### **2. 外出申请 (hr_outing)**
```
测试数据：
- 开始时间：2025-07-28 14:00
- 结束时间：2025-07-28 17:00
- 外出事由：银行办事

验证点：
✅ 时长自动计算（3小时）
✅ FormUploader组件正常
```

### **3. 样品邮寄申请 (office_sample_mail)**
```
测试数据：
- 样品名称：产品样品A
- 样品描述：新款产品样品，用于客户测试
- 寄件人电话：***********
- 收件信息：张三，北京市朝阳区xxx路xxx号，***********
- 备注：加急处理

验证点：
✅ 必填字段验证
✅ 表单保存和提交
```

### **4. 付款申请 (finance_payment_approval)**
```
测试数据：
- 付款事由：供应商货款支付
- 付款总额：50000
- 付款方式：银行转账
- 支付日期：2025-07-30
- 支付对象：ABC供应商
- 开户行：中国银行北京分行
- 银行账户：1234567890123456789
- 备注：月度货款结算

验证点：
✅ 字段顺序正确
✅ 必填字段验证
```

### **5. 报销申请 (finance_expense_reimbursement)**
```
测试数据：
- 报销金额：1500
- 报销类型：差旅费
- 明细1：高铁票费用
- 明细2：住宿费用
- 明细3：餐费
- 备注：出差报销

验证点：
✅ 明细表格适老化样式
✅ 至少一个明细验证
✅ FormUploader组件正常
```

### **6. 出库申请 (ims_outbound_approval)**
```
测试数据：
- 所在部门：销售部
- 出库日期：2025-07-29
- 接收单位：选择客户
- 明细1：产品A, 数量10, 单价100
- 明细2：产品B, 数量5, 单价200
- 备注：客户订单出库

验证点：
✅ DepartmentTreeSelect组件
✅ ApiSelect客户选择组件
✅ 单价大写自动转换
✅ 总金额自动计算（2000元）
✅ 总金额大写自动转换
✅ 至少一个明细验证
```

### **7. 出货申请 (ims_shipment_approval)**
```
测试数据：
- 所在部门：销售部
- 出货日期：2025-07-30
- 接收单位：选择客户
- 明细1：产品C, 数量8, 单价150
- 明细2：产品D, 数量12, 单价80
- 备注：月度出货

验证点：
✅ DepartmentTreeSelect组件
✅ ApiSelect客户选择组件
✅ 单价大写自动转换
✅ 总金额自动计算（2160元）
✅ 总金额大写自动转换
✅ 至少一个明细验证
```

## 🔍 重点检查项

### **1. 表单打开检查**
- 点击申请按钮后，选择器正常弹出
- 选择申请类型后，对应表单正常打开
- 表单字段显示完整，无缺失

### **2. 组件功能检查**
- DepartmentTreeSelect：部门选择正常
- ApiSelect：客户数据加载正常
- FormUploader：文件上传正常
- 明细表格：添加/删除/编辑正常

### **3. 自动计算检查**
- 出差申请：天数自动计算
- 外出申请：时长自动计算
- 出库/出货：单价大写、总金额、总金额大写

### **4. 数据验证检查**
- 必填字段验证提示
- 明细至少一个验证
- 数值范围验证
- 日期格式验证

### **5. 保存和提交检查**
- 保存草稿功能正常
- 编辑已保存数据正常
- 提交审批功能正常
- 状态更新正确

## 🐛 常见问题排查

### **问题1：表单不弹出**
```
可能原因：
- workflow_type表缺少记录
- workflow_definition表缺少记录
- 前端路由配置问题

排查方法：
1. 执行check_workflow_config.sql检查数据库
2. 检查浏览器控制台错误
3. 检查网络请求是否正常
```

### **问题2：组件报错**
```
可能原因：
- ApiSelect使用方式错误
- TypeScript类型定义问题
- 组件导入路径错误

排查方法：
1. 检查浏览器控制台Vue警告
2. 检查组件props传递
3. 检查组件导入语句
```

### **问题3：保存失败**
```
可能原因：
- 后端Service未实现
- 数据验证规则错误
- 数据库表结构问题

排查方法：
1. 检查网络请求响应
2. 检查后端日志
3. 检查Service实现
```

### **问题4：自动计算不工作**
```
可能原因：
- 计算方法实现错误
- 数据绑定问题
- 监听器未正确设置

排查方法：
1. 检查computed属性
2. 检查watch监听器
3. 检查数据响应性
```

## 📊 测试结果记录

请按照以下格式记录测试结果：

```
表单：[表单名称]
时间：[测试时间]

✅ 表单打开正常
✅ 保存功能正常  
✅ 编辑功能正常
✅ 提交功能正常
✅ 组件集成正常
✅ 自动计算正常
✅ 数据验证正常

问题记录：
[如有问题，详细描述]
```

## 🎯 测试完成标准

所有7个表单都满足以下条件：
- ✅ 表单能正常打开
- ✅ 保存功能正常工作
- ✅ 编辑功能正常工作
- ✅ 提交功能正常工作
- ✅ 数据验证正确执行
- ✅ 特殊功能正常（自动计算、组件集成等）

**请按照此指南进行测试，并将测试结果反馈给我！**
