<?php
declare(strict_types=1);

namespace app\notice\model;

use app\common\core\base\BaseModel;
use think\Model;

/**
 * 通知模板模型
 * @property int $id 主键ID
 * @property string $code 模板编码
 * @property string $name 模板名称
 * @property string $title 模板标题
 * @property string $content 模板内容
 * @property string $variables_config 变量配置JSON格式
 * @property string $module_code 所属模块
 * @property string $send_channels 默认发送通道
 * @property int $status 状态：1启用，0禁用
 * @property string $remark 备注说明
 * @property int $created_by 创建人ID
 * @property int $updated_by 更新人ID
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class NoticeTemplateModel extends BaseModel
{
    // 设置表名
    protected $name = 'notice_template';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 创建时间字段
    protected $createTime = 'created_at';
    
    // 更新时间字段
    protected $updateTime = 'updated_at';
    
    /**
     * 获取变量配置
     * 
     * @param bool $asArray 是否返回数组
     * @return array|object 变量配置
     */
    public function getVariablesConfig(bool $asArray = true)
    {
        $config = $this->variables_config ? json_decode($this->variables_config, $asArray) : null;
        
        if (!$config && $asArray) {
            return ['variables' => []];
        }
        
        return $config;
    }
    
    /**
     * 设置变量配置
     * 
     * @param array $config 变量配置
     * @return $this
     */
    public function setVariablesConfig(array $config)
    {
        $this->variables_config = json_encode($config, JSON_UNESCAPED_UNICODE);
        return $this;
    }
    
    /**
     * 获取模块列表
     * @return array 模块列表
     */
    public static function getModuleList(): array
    {
        return [
            'crm' => 'CRM',
            'erp' => '进销存',
            'oa' => '办公',
            'workflow' => '工作流',
            'finance' => '财务',
            'system' => '系统',
            'approval' => '审批',
            'attendance' => '假勤',
            'inventory' => '库存',
            'notice' => '公告'
        ];
    }
    
    /**
     * 获取模块文本
     * @param string $moduleCode 模块编码
     * @return string 模块文本
     */
    public static function getModuleText(string $moduleCode): string
    {
        $list = self::getModuleList();
        return $list[$moduleCode] ?? $moduleCode;
    }
    
    /**
     * 获取通道列表
     * @return array 通道列表
     */
    public static function getChannelList(): array
    {
        return [
            'site' => '站内信',
            'email' => '邮件',
            'sms' => '短信',
            'wework' => '企业微信',
            'dingtalk' => '钉钉',
            'webhook' => 'Webhook'
        ];
    }
    
    /**
     * 获取通道文本
     * @param string $channels 通道字符串，多个通道用逗号分隔
     * @return string 通道文本
     */
    public static function getChannelText(string $channels): string
    {
        if (empty($channels)) {
            return '';
        }
        
        $list = self::getChannelList();
        $channelArray = explode(',', $channels);
        $result = [];
        
        foreach ($channelArray as $channel) {
            $result[] = $list[$channel] ?? $channel;
        }
        
        return implode(', ', $result);
    }
} 