<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\crm\service\CrmTagService;
use think\response\Json;

/**
 * 标签表控制器
 */
class CrmTagController extends BaseController
{
	use CrudControllerTrait;
	
	/**
	 * @var CrmTagService
	 */
	protected CrmTagService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = CrmTagService::getInstance();
		$this->service->getCrudService()
		              ->setEnableDataPermission(false);
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params               = $this->request->param();
		$params['sort_field'] = 'sort';
		$result               = $this->service->search($params, $this->service->getSearchFields(), ['creator']);
		return $this->success('获取成功', $result);
	}
	
}