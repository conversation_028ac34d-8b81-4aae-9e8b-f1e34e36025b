<?php
declare(strict_types=1);

namespace {{NAMESPACE}};

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;{{#HAS_IMPORT_EXPORT}}
use app\common\core\crud\traits\ControllerImportExportTrait;{{/HAS_IMPORT_EXPORT}}
use app\{{MODULE}}\service\{{ENTITY_NAME}}Service;

/**
 * {{COMMENT}}控制器
 */
class {{ENTITY_NAME}}Controller extends BaseController
{
    use CrudControllerTrait{{#HAS_IMPORT_EXPORT}}, ControllerImportExportTrait{{/HAS_IMPORT_EXPORT}};
    /**
     * @var {{ENTITY_NAME}}Service
     */
    protected $service;

    /**
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();

        // 使用单例模式获取Service实例
        $this->service = {{ENTITY_NAME}}Service::getInstance();
    }

    /**
     * 状态切换
     */
    public function status($id)
    {
        $status = $this->request->post('status');
        $result = $this->service->updateField($id, 'status', $status);
        return $this->success('状态更新成功', $result);
    }

}