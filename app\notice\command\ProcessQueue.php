<?php
declare(strict_types=1);

namespace app\notice\command;

use app\notice\service\NoticeQueueService;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 处理消息队列命令
 */
class ProcessQueue extends Command
{
    /**
     * 配置命令
     */
    protected function configure()
    {
        $this->setName('notice:process-queue')
            ->setDescription('处理消息通知队列')
            ->addOption('limit', 'l', Option::VALUE_OPTIONAL, '每次处理的最大数量', 10)
            ->addOption('clean', 'c', Option::VALUE_NONE, '是否清理已处理的队列项')
            ->addOption('days', 'd', Option::VALUE_OPTIONAL, '清理多少天前的已处理队列项', 7);
    }
    
    /**
     * 执行命令
     *
     * @param Input $input 输入对象
     * @param Output $output 输出对象
     * @return int 返回状态码
     */
    protected function execute(Input $input, Output $output)
    {
        $limit = (int)$input->getOption('limit');
        $clean = $input->getOption('clean');
        $days = (int)$input->getOption('days');
        
        $output->writeln("<info>开始处理消息队列，处理限制: {$limit}</info>");
        
        $queueService = NoticeQueueService::getInstance();
        $result = $queueService->processQueue($limit);
        
        $output->writeln("<info>处理完成，成功: {$result['success']}，失败: {$result['failed']}</info>");
        
        if ($clean) {
            $output->writeln("<info>开始清理 {$days} 天前的已处理队列项</info>");
            $count = $queueService->cleanProcessedItems($days);
            $output->writeln("<info>清理完成，共清理 {$count} 条记录</info>");
        }
        
        return 0; // 成功返回0
    }
} 