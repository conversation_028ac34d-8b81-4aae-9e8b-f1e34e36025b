<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmContractProduct;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 合同产品明细表服务类
 */
class CrmContractProductService extends BaseService
{

    use ExportableTrait;


    use ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmContractProduct();
        parent::__construct();
    }
    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

            'contract_id' => ['type' => 'eq'],

            'product_id' => ['type' => 'eq'],

            'price' => ['type' => 'between'],

            'quantity' => ['type' => 'between'],

            'discount' => ['type' => 'between'],

            'discount_rate' => ['type' => 'between'],

            'subtotal' => ['type' => 'between'],

        ];
    }
    
    /**
     * 获取验证规则
     * 
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            // 在这里定义验证规则
            // 例如：'username' => 'require|unique:crm_contract_product',
        ];
        
        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('id', $ids)->delete();
    }
} 