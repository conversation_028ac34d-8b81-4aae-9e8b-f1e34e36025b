import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

/**
 * 每日报价单操作相关接口
 * 参考 CrmCustomerDetailApi 的设计模式
 */
export class DailyPriceOrderApi {
  // ==================== 基础CRUD操作 ====================

  /**
   * 获取报价单列表
   * @param params 查询参数
   */
  static getList(params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: 'daily/daily_price_order',
      params
    })
  }

  /**
   * 获取报价单详情
   * @param id 报价单ID
   */
  static getDetail(id: number) {
    return request.get<BaseResult>({
      url: `daily/daily_price_order/${id}`
    })
  }

  /**
   * 创建报价单
   * @param data 报价单数据
   */
  static create(data: any) {
    return request.post<BaseResult>({
      url: 'daily/daily_price_order',
      data
    })
  }

  /**
   * 更新报价单
   * @param id 报价单ID
   * @param data 报价单数据
   */
  static update(id: number, data: any) {
    return request.put<BaseResult>({
      url: `daily/daily_price_order/${id}`,
      data
    })
  }

  /**
   * 删除报价单
   * @param id 报价单ID
   */
  static delete(id: number) {
    return request.delete<BaseResult>({
      url: `daily/daily_price_order/${id}`
    })
  }

  // ==================== 明细操作 ====================

  /**
   * 保存明细
   * @param orderId 报价单ID
   * @param items 明细数据
   */
  static saveItems(orderId: number, items: any[]) {
    return request.post<BaseResult>({
      url: `daily/daily_price_order/${orderId}/items`,
      data: { order_id: orderId, items }
    })
  }

  /**
   * 获取明细列表
   * @param orderId 报价单ID
   */
  static getItems(orderId: number) {
    return request.get<BaseResult>({
      url: `daily/daily_price_item`,
      params: { order_id: orderId }
    })
  }

  // ==================== 审批操作 ====================

  /**
   * 提交审批
   * @param id 报价单ID
   */
  static submitApproval(id: number) {
    return request.post<BaseResult>({
      url: `daily/daily_price_order/${id}/submit-approval`,
      data: { id }
    })
  }

  /**
   * 撤回审批
   * @param id 报价单ID
   */
  static withdrawApproval(id: number) {
    return request.post<BaseResult>({
      url: `daily/daily_price_order/${id}/withdraw-approval`,
      data: { id }
    })
  }

  /**
   * 作废报价单
   * @param id 报价单ID
   * @param reason 作废原因
   */
  static voidOrder(id: number, reason: string) {
    return request.post<BaseResult>({
      url: `daily/daily_price_order/${id}/void`,
      data: { id, reason }
    })
  }

  // ==================== 业务操作 ====================

  /**
   * 从昨日复制
   * @param targetDate 目标日期
   */
  static copyFromYesterday(targetDate: string) {
    return request.post<BaseResult>({
      url: 'daily/daily_price_order/copy-yesterday',
      data: { target_date: targetDate }
    })
  }

  /**
   * 导出报价单
   * @param id 报价单ID
   */
  static export(id: number) {
    return request.get<BaseResult>({
      url: `daily/daily_price_order/${id}/export`
    })
  }

  /**
   * 获取统计数据
   * @param params 查询参数
   */
  static getStatistics(params?: any) {
    return request.get<BaseResult>({
      url: 'daily/daily_price_order/statistics',
      params
    })
  }

  // ==================== 辅助查询接口 ====================

  /**
   * 获取供应商列表
   */
  static getSupplierList() {
    return request.get<BaseResult>({
      url: 'ims/supplier',
      params: { status: 1 }
    })
  }

  /**
   * 获取产品列表
   * @param supplierId 供应商ID
   */
  static getProductList(supplierId?: number) {
    return request.get<BaseResult>({
      url: 'crm/product',
      params: { supplier_id: supplierId, status: 1 }
    })
  }

  /**
   * 获取价格历史
   * @param productId 产品ID
   * @param supplierId 供应商ID
   * @param days 查询天数
   */
  static getPriceHistory(productId: number, supplierId: number, days: number = 30) {
    return request.get<BaseResult>({
      url: 'daily/daily_price_history',
      params: { 
        product_id: productId, 
        supplier_id: supplierId,
        days 
      }
    })
  }
}

// ==================== 类型定义 ====================

/**
 * 报价单数据类型
 */
export interface DailyPriceOrder {
  id?: number
  tenant_id?: number
  title: string
  price_date: string
  total_items: number
  remark?: string
  status: number
  approval_status?: number
  workflow_instance_id?: number
  submit_time?: string
  approval_time?: string
  submitter_id?: number
  void_reason?: string
  void_time?: string
  void_user_id?: number
  creator_id?: number
  updated_id?: number
  created_at?: string
  updated_at?: string
}

/**
 * 报价明细数据类型
 */
export interface DailyPriceItem {
  id?: number
  tenant_id?: number
  order_id: number
  supplier_id: number
  product_id: number
  unit_price: number
  old_price: number
  price_change: number
  change_rate: number
  stock_price: number
  stock_qty: number
  policy_remark?: string
  is_manual_price: number
  sort_order: number
  status: number
  created_at?: string
  updated_at?: string
  
  // 关联数据
  supplier?: any
  product?: any
}

/**
 * 审批状态枚举
 */
export enum ApprovalStatus {
  DRAFT = 0,        // 草稿
  PENDING = 1,      // 审批中
  APPROVED = 2,     // 已通过
  REJECTED = 3,     // 已拒绝
  TERMINATED = 4,   // 已终止
  RECALLED = 5,     // 已撤回
  VOIDED = 6        // 已作废
}

/**
 * 审批状态文本映射
 */
export const ApprovalStatusText = {
  [ApprovalStatus.DRAFT]: '草稿',
  [ApprovalStatus.PENDING]: '审批中',
  [ApprovalStatus.APPROVED]: '已通过',
  [ApprovalStatus.REJECTED]: '已拒绝',
  [ApprovalStatus.TERMINATED]: '已终止',
  [ApprovalStatus.RECALLED]: '已撤回',
  [ApprovalStatus.VOIDED]: '已作废'
}

/**
 * 审批状态样式类映射
 */
export const ApprovalStatusClass = {
  [ApprovalStatus.DRAFT]: 'status-draft',
  [ApprovalStatus.PENDING]: 'status-pending',
  [ApprovalStatus.APPROVED]: 'status-approved',
  [ApprovalStatus.REJECTED]: 'status-rejected',
  [ApprovalStatus.TERMINATED]: 'status-terminated',
  [ApprovalStatus.RECALLED]: 'status-recalled',
  [ApprovalStatus.VOIDED]: 'status-voided'
}
