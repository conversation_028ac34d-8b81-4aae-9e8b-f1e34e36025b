<?php

namespace app\common\lib\upload\driver;

use app\common\lib\upload\UploadStorageInterface;
use OSS\OssClient;

class AliyunDriver implements UploadStorageInterface
{
	public function getUploadToken(array $config, array $params = []): string|array
	{
		$client = new OssClient(
			$config['access_key_id'],
			$config['access_key_secret'],
			$config['endpoint']
		);
		
		$fileName = md5(microtime(true) . mt_rand(1000, 9999)) . '.' . ($params['extension'] ?? 'tmp');
		$savePath = $config['base_path'] . date('Y/m/d') . '/' . $fileName;
		
		// 设置回调
		$callbackParam = [
			'callbackUrl' => $params['callback_url'] ?? request()->domain() . '/api/attachment/callback/alioss',
			'callbackBody' => json_encode([
				'filename' => '${object}',
				'size' => '${size}',
				'mimeType' => '${mimeType}',
				'height' => '${imageInfo.height}',
				'width' => '${imageInfo.width}',
				'tenant_id' => $params['tenant_id'] ?? 0,
				'cate_id' => $params['cate_id'] ?? 0,
			]),
			'callbackBodyType' => 'application/json',
		];
		
		$callbackString = json_encode($callbackParam);
		$base64CallbackBody = base64_encode($callbackString);
		
		$policyArray = [
			'expiration' => date('Y-m-d\TH:i:s\Z', time() + 3600),
			'conditions' => [
				['content-length-range', 0, 1048576000], // 限制上传大小
			],
		];
		
		$policyJson = json_encode($policyArray);
		$base64Policy = base64_encode($policyJson);
		
		$signature = base64_encode(hash_hmac('sha1', $base64Policy, $config['access_key_secret'], true));
		
		return [
			'accessid' => $config['access_key_id'],
			'policy' => $base64Policy,
			'signature' => $signature,
			'host' => $config['domain'],
			'key' => $savePath,
			'callback' => $base64CallbackBody,
			'dir' => $config['base_path'] . date('Y/m/d') . '/',
		];
	}
	
	public function upload(array $file, array $config): array
	{
		$client = new OssClient(
			$config['access_key_id'],
			$config['access_key_secret'],
			$config['endpoint']
		);
		
		$fileName = md5(microtime(true) . mt_rand(1000, 9999)) . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
		$savePath = $config['base_path'] . date('Y/m/d') . '/' . $fileName;
		
		$result = $client->uploadFile(
			$config['bucket'],
			$savePath,
			$file['tmp_name']
		);
		
		return [
			'name' => $file['name'],
			'real_name' => $file['name'],
			'path' => $savePath,
			'url' => $config['domain'] . '/' . $savePath,
			'extension' => pathinfo($file['name'], PATHINFO_EXTENSION),
			'size' => $file['size'],
			'mime_type' => $file['type'],
			'storage' => 'alioss',
			'storage_id' => $result['etag'],
		];
	}
	
	public function delete(string $filePath, array $config): bool
	{
		$client = new OssClient(
			$config['access_key_id'],
			$config['access_key_secret'],
			$config['endpoint']
		);
		
		$client->deleteObject($config['bucket'], $filePath);
		
		return true;
	}
	
	public function callback(array $params, array $config): array
	{
		// 验证回调签名
		$authStr = $_SERVER['HTTP_AUTHORIZATION'];
		$path = $_SERVER['REQUEST_URI'];
		$body = file_get_contents('php://input');
		
		// 验证签名逻辑...
		$client = new OssClient(
			$config['access_key_id'],
			$config['access_key_secret'],
			$config['endpoint']
		);
		
		if (!$client->verifyCallback($authStr, $path, $body)) {
			throw new \Exception('回调签名验证失败');
		}
		
		$callbackData = json_decode($body, true);
		
		return [
			'name' => basename($callbackData['filename']),
			'real_name' => basename($callbackData['filename']),
			'path' => $callbackData['filename'],
			'url' => $config['domain'] . '/' . $callbackData['filename'],
			'extension' => pathinfo($callbackData['filename'], PATHINFO_EXTENSION),
			'size' => $callbackData['size'],
			'mime_type' => $callbackData['mimeType'],
			'storage' => 'alioss',
			'storage_id' => '',
			'tenant_id' => $callbackData['tenant_id'],
			'cate_id' => $callbackData['cate_id'],
		];
	}
}