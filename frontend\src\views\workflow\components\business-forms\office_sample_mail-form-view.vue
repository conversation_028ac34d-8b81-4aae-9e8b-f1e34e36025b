<template>
  <div class="sample-mail-form-view">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="样品名称">
        {{ formData.sample_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="寄件人手机号">
        {{ formData.sender_phone || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="收件信息" :span="2">
        {{ formData.delivery_address || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="样品描述" :span="2">
        {{ formData.sample_description || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        {{ formData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem } from 'element-plus'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'office_sample_mail'
    }
  })
</script>

<style scoped lang="scss">
</style>
