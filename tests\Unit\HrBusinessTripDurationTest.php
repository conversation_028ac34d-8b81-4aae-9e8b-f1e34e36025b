<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use app\hr\service\HrBusinessTripService;

/**
 * 出差申请天数计算测试
 * 验证前后端算法一致性
 */
class HrBusinessTripDurationTest extends TestCase
{
    private HrBusinessTripService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new HrBusinessTripService();
    }

    /**
     * 测试单天出差（同一天）
     */
    public function testSameDayTrip()
    {
        $items = [
            [
                'start_time' => '2025-07-29 09:00:00',
                'end_time'   => '2025-07-29 18:00:00',
            ]
        ];

        $duration = $this->invokePrivateMethod('calculateBusinessTripDuration', [$items]);
        
        // 前端算法：同一天应该返回1天
        $this->assertEquals(1.0, $duration, '同一天出差应该计算为1天');
    }

    /**
     * 测试跨天出差
     */
    public function testMultiDayTrip()
    {
        $items = [
            [
                'start_time' => '2025-07-29 09:00:00',
                'end_time'   => '2025-07-31 18:00:00',
            ]
        ];

        $duration = $this->invokePrivateMethod('calculateBusinessTripDuration', [$items]);
        
        // 前端算法：2025-07-29到2025-07-31应该返回3天
        $this->assertEquals(3.0, $duration, '7月29日到7月31日应该计算为3天');
    }

    /**
     * 测试多个行程明细
     */
    public function testMultipleItineraries()
    {
        $items = [
            [
                'start_time' => '2025-07-29 09:00:00',
                'end_time'   => '2025-07-29 12:00:00',
            ],
            [
                'start_time' => '2025-07-30 14:00:00',
                'end_time'   => '2025-07-31 18:00:00',
            ]
        ];

        $duration = $this->invokePrivateMethod('calculateBusinessTripDuration', [$items]);
        
        // 应该取最早开始时间(7-29)和最晚结束时间(7-31)，计算为3天
        $this->assertEquals(3.0, $duration, '多个行程明细应该按整体时间范围计算');
    }

    /**
     * 测试空数据
     */
    public function testEmptyItems()
    {
        $items = [];
        $duration = $this->invokePrivateMethod('calculateBusinessTripDuration', [$items]);
        $this->assertEquals(0.0, $duration, '空数据应该返回0天');
    }

    /**
     * 测试无效时间数据
     */
    public function testInvalidTimeData()
    {
        $items = [
            [
                'start_time' => '',
                'end_time'   => '2025-07-31 18:00:00',
            ],
            [
                'start_time' => '2025-07-29 09:00:00',
                'end_time'   => '',
            ]
        ];

        $duration = $this->invokePrivateMethod('calculateBusinessTripDuration', [$items]);
        $this->assertEquals(0.0, $duration, '无效时间数据应该返回0天');
    }

    /**
     * 测试边界情况：跨月出差
     */
    public function testCrossMonthTrip()
    {
        $items = [
            [
                'start_time' => '2025-07-31 09:00:00',
                'end_time'   => '2025-08-02 18:00:00',
            ]
        ];

        $duration = $this->invokePrivateMethod('calculateBusinessTripDuration', [$items]);
        
        // 7月31日到8月2日应该是3天
        $this->assertEquals(3.0, $duration, '跨月出差计算应该正确');
    }

    /**
     * 测试前端JavaScript算法对应的PHP实现
     * 模拟前端calculateDays函数的逻辑
     */
    public function testFrontendAlgorithmEquivalent()
    {
        // 测试用例：与前端文档中的示例保持一致
        $testCases = [
            ['2025-07-29', '2025-07-31', 3], // 3天
            ['2025-07-29 09:00:00', '2025-07-29 18:00:00', 1], // 同一天
            ['2025-07-28', '2025-07-30', 3], // 3天
        ];

        foreach ($testCases as [$start, $end, $expected]) {
            $items = [
                [
                    'start_time' => $start,
                    'end_time'   => $end,
                ]
            ];

            $duration = $this->invokePrivateMethod('calculateBusinessTripDuration', [$items]);
            $this->assertEquals(
                (float)$expected,
                $duration,
                "从{$start}到{$end}应该计算为{$expected}天"
            );
        }
    }

    /**
     * 测试DateCalculator工具类的直接调用
     */
    public function testDateCalculatorDirectCall()
    {
        // 直接测试DateCalculator工具类
        $testCases = [
            ['2025-07-29', '2025-07-31', 3],
            ['2025-07-29 09:00:00', '2025-07-29 18:00:00', 1],
            ['2025-07-28', '2025-07-30', 3],
        ];

        foreach ($testCases as [$start, $end, $expected]) {
            $days = \app\common\utils\DateCalculator::calculateDays($start, $end);
            $this->assertEquals(
                $expected,
                $days,
                "DateCalculator: 从{$start}到{$end}应该计算为{$expected}天"
            );
        }
    }

    /**
     * 调用私有方法的辅助函数
     */
    private function invokePrivateMethod(string $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        
        return $method->invokeArgs($this->service, $parameters);
    }
}
