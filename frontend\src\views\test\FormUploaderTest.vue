<template>
  <div class="form-uploader-test">
    <h2>FormUploader 多文件上传测试</h2>
    
    <el-card class="test-card">
      <template #header>
        <span>图片多文件上传测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="图片附件">
          <FormUploader
            v-model="testForm.images"
            fileType="image"
            :limit="5"
            :multiple="true"
            returnValueMode="string"
            buttonText="选择多个图片"
            tipText="支持选择多个图片文件，最多5个"
          />
        </el-form-item>
        
        <el-form-item label="当前值">
          <el-input
            v-model="testForm.images"
            type="textarea"
            :rows="3"
            readonly
            placeholder="上传后的文件URL会显示在这里"
          />
        </el-form-item>
        
        <el-form-item label="文件数量">
          <el-tag>{{ fileCount }} 个文件</el-tag>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>文档多文件上传测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="文档附件">
          <FormUploader
            v-model="testForm.documents"
            fileType="file"
            :limit="3"
            :multiple="true"
            returnValueMode="string"
            buttonText="选择多个文档"
            tipText="支持选择多个文档文件，最多3个"
            accept=".pdf,.doc,.docx,.xls,.xlsx"
          />
        </el-form-item>
        
        <el-form-item label="当前值">
          <el-input
            v-model="testForm.documents"
            type="textarea"
            :rows="3"
            readonly
            placeholder="上传后的文件URL会显示在这里"
          />
        </el-form-item>
        
        <el-form-item label="文件数量">
          <el-tag>{{ documentCount }} 个文件</el-tag>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>使用说明</span>
      </template>
      
      <el-alert
        title="多文件选择方法"
        type="info"
        :closable="false"
        show-icon
      >
        <p>1. 点击"选择文件"按钮</p>
        <p>2. 在文件选择对话框中，按住 Ctrl 键（Windows）或 Cmd 键（Mac）</p>
        <p>3. 点击多个文件进行选择</p>
        <p>4. 点击"打开"按钮确认选择</p>
        <p>5. 所有选中的文件会同时开始上传</p>
      </el-alert>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { FormUploader } from '@/components/custom/FormUploader'

  // 测试表单数据
  const testForm = ref({
    images: '',
    documents: ''
  })

  // 计算文件数量
  const fileCount = computed(() => {
    if (!testForm.value.images) return 0
    return testForm.value.images.split(',').filter(url => url.trim()).length
  })

  const documentCount = computed(() => {
    if (!testForm.value.documents) return 0
    return testForm.value.documents.split(',').filter(url => url.trim()).length
  })
</script>

<style scoped lang="scss">
  .form-uploader-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;

    .test-card {
      margin-bottom: 20px;
    }

    h2 {
      text-align: center;
      margin-bottom: 30px;
      color: #303133;
    }

    .el-alert {
      p {
        margin: 5px 0;
        line-height: 1.5;
      }
    }
  }
</style>
