<?php
declare(strict_types=1);

namespace app\common\core\crud;

use app\common\core\crud\interface\CrudInterface;
use app\common\core\crud\traits\DataFilterTrait;
use app\common\core\crud\traits\FieldSceneTrait;
use app\common\core\crud\traits\ModelConfigTrait;
use app\common\core\crud\traits\OrderTrait;
use app\common\core\crud\traits\QueryBuilderTrait;
use app\common\core\crud\traits\SearchTrait;
use app\common\core\crud\traits\ValidatorTrait;
use app\common\core\crud\traits\FormatableFieldsTrait;
use app\common\core\crud\traits\ExportableTrait;
use app\common\core\crud\traits\ImportableTrait;
use app\common\core\traits\DataPermissionTrait;
use app\common\core\traits\TransactionTrait;
use app\common\core\crud\traits\CrudOperationsTrait;
use think\Model;

/**
 * CRUD服务类
 * 提供标准的CRUD操作实现
 */
class CrudService implements CrudInterface
{
	use DataPermissionTrait, TransactionTrait, DataFilterTrait, ModelConfigTrait, OrderTrait, QueryBuilderTrait, SearchTrait, CrudOperationsTrait, FieldSceneTrait, ValidatorTrait, FormatableFieldsTrait, ExportableTrait, ImportableTrait;
	
	/**
	 * 当前操作的模型实例
	 */
	protected ?Model $model = null;
	
	/**
	 * 数据权限字段
	 */
	protected string $dataRangeField = 'creator_id';
	
	/**
	 * 是否启用数据权限过滤
	 */
	protected bool $enableDataPermission = true;
	
	/**
	 * 默认隐藏的字段
	 */
	protected array $hiddenFields = [];
	
	/**
	 * 默认排序
	 */
	protected array $defaultOrder = ['id' => 'desc'];
	
	/**
	 * 默认搜索字段配置
	 */
	protected array $defaultSearchFields = [];
	
	/**
	 * 允许单字段编辑的字段
	 */
	protected array $allowUpdateFields = [];
	
	/**
	 * 禁止单字段编辑的字段
	 */
	protected array $forbidUpdateFields = [
		'id',
		'salt',
		'tenant_id',
		'creator_id',
		'created_at',
		'updated_at',
		'deleted_at'
	];
	
	/**
	 * 允许排序的字段
	 */
	protected array $allowSortFields = [];
	
	/**
	 * 构造函数
	 */
	public function __construct(?Model $model = null)
	{
		if ($model) {
			$this->setModel($model);
		}
	}
	
	public function getDataRangeField(): string
	{
		return $this->dataRangeField;
	}
	
	public function setDataRangeField(string $dataRangeField): void
	{
		$this->dataRangeField = $dataRangeField;
	}
	
	/**
	 * 获取文件保存路径
	 *
	 * @return string 保存路径
	 */
	public function getSavePath($pathString = 'export'): string
	{
		$path = runtime_path() . $pathString;
		if (!is_dir($path)) {
			mkdir($path, 0755, true);
		}
		return $path;
	}
	
	/**
	 * 获取字段选项映射
	 *
	 * @param array $field 字段配置
	 * @return array 选项映射
	 */
	public function getOptionsMap(array $field): array
	{
		// 如果字段中已经有选项配置，直接使用
		if (!empty($field['options'])) {
			if (is_array($field['options'])) {
				return $field['options'];
			}
			
			// 处理选项字符串 (格式: 选项1:值1,选项2:值2)
			if (is_string($field['options'])) {
				$options = [];
				$items   = explode(',', $field['options']);
				foreach ($items as $item) {
					if (strpos($item, ':') !== false) {
						list($label, $value) = explode(':', $item);
						$options[trim($value)] = trim($label);
					}
				}
				return $options;
			}
		}
		
		// 从字段注释中解析选项
		$comment = $field['comment'] ?? '';
		if (!empty($comment)) {
			$pattern = '/([^:]+):((?:[^,=]+=[^,]+,)*[^,=]+=[^,]+)/';
			if (preg_match($pattern, $comment, $matches)) {
				$optionsStr = $matches[2];
				$options    = [];
				$items      = explode(',', $optionsStr);
				foreach ($items as $item) {
					if (str_contains($item, '=')) {
						list($value, $label) = explode('=', $item);
						$options[trim($value)] = trim($label);
					}
				}
				return $options;
			}
		}
		
		// 默认状态映射
		if ($field['format'] === 'status') {
			return [
				1 => '启用',
				0 => '禁用'
			];
		}
		
		return [];
	}
	
	
	/**
	 * 获取列字母
	 *
	 * @param int $colNum 列数字
	 * @return string 列字母
	 */
	public function getColumnLetter(int $colNum): string
	{
		$dividend   = $colNum;
		$columnName = '';
		
		while ($dividend > 0) {
			$modulo     = ($dividend - 1) % 26;
			$columnName = chr(65 + $modulo) . $columnName;
			$dividend   = floor(($dividend - $modulo) / 26);
		}
		
		return $columnName;
	}
	
	public function getModel(): ?Model
	{
		return $this->model;
	}
	
	public function setDefaultSearchFields(array $defaultSearchFields): void
	{
		$this->defaultSearchFields = $defaultSearchFields;
	}
}