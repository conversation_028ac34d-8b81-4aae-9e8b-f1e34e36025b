<?php


namespace app\workflow\constants;

/**
 * 统一工作流常量
 * 解决多个常量文件冗余和维护问题
 *
 * @package app\workflow\constants
 */
class WorkflowStatusConstant
{
	// ==================== 通用状态定义 ====================
	
	/**
	 * 通用状态：草稿/待处理
	 * - workflow_instance: 已保存(草稿)
	 * - workflow_task: 待处理
	 * - 业务表: 草稿
	 */
	const STATUS_DRAFT = 0;
	
	/**
	 * 通用状态：进行中/已同意
	 * - workflow_instance: 审批中
	 * - workflow_task: 已同意
	 * - 业务表: 审批中
	 */
	const STATUS_PROCESSING = 1;
	
	/**
	 * 通用状态：已完成/已拒绝
	 * - workflow_instance: 已通过
	 * - workflow_task: 已拒绝
	 * - 业务表: 已通过
	 */
	const STATUS_COMPLETED = 2;
	
	/**
	 * 通用状态：已拒绝/已转交
	 * - workflow_instance: 已拒绝
	 * - workflow_task: 已转交
	 * - 业务表: 已拒绝
	 */
	const STATUS_REJECTED = 3;
	
	/**
	 * 通用状态：已终止
	 * - 所有表: 已终止
	 */
	const STATUS_TERMINATED = 4;
	
	/**
	 * 通用状态：已撤回
	 * - 所有表: 已撤回
	 */
	const STATUS_RECALLED = 5;
	
	/**
	 * 通用状态：已作废
	 * - workflow_instance: 已作废
	 * - 业务表: 已作废
	 * - workflow_task: 不适用
	 */
	const STATUS_VOID = 6;

	/**
	 * 任务状态：已跳过
	 * - workflow_task: 已跳过（用于被取消的待处理任务）
	 * - 使用场景：多人审批中，一人拒绝后其他待处理任务的状态
	 */
	const STATUS_SKIPPED = 7;
	
	// ==================== 向后兼容别名 ====================
	
	const SAVED      = self::STATUS_DRAFT;
	const APPROVING  = self::STATUS_PROCESSING;
	const APPROVED   = self::STATUS_COMPLETED;
	const REJECTED   = self::STATUS_REJECTED;
	const TERMINATED = self::STATUS_TERMINATED;
	const RECALLED   = self::STATUS_RECALLED;
	const VOID       = self::STATUS_VOID;
	
	/**
	 * 任务类型：审批任务
	 */
	const TASK_TYPE_APPROVAL = 0;
	
	/**
	 * 任务类型：抄送任务
	 */
	const TASK_TYPE_CC = 1;
	
	/**
	 * 节点类型：发起人
	 */
	const NODE_TYPE_PROMOTER = 0;
	
	/**
	 * 节点类型：审批人
	 */
	const NODE_TYPE_APPROVER = 1;
	
	/**
	 * 节点类型：抄送人
	 */
	const NODE_TYPE_COPYER = 2;
	
	/**
	 * 节点类型：条件
	 */
	const NODE_TYPE_CONDITION = 3;
	
	/**
	 * 节点类型：条件分支
	 */
	const NODE_TYPE_BRANCH = 4;
	
	/**
	 * 审批模式：任意一人通过即可
	 */
	const APPROVAL_MODE_ANY = 1;
	
	/**
	 * 审批模式：所有人通过
	 */
	const APPROVAL_MODE_ALL = 2;
	
	/**
	 * 审批模式：按顺序依次审批
	 */
	const APPROVAL_MODE_SEQUENCE = 3;
	
	const MODULE_NAME = 'workflow';
	
	/**
	 * 消息通知：工作流任务审批通知
	 */
	const MESSAGE_TASK_APPROVAL = 'task_approval';
	
	/**
	 * 消息通知：工作流任务审批结果通知
	 */
	const MESSAGE_TASK_APPROVED = 'task_approved';
	
	/**
	 * 消息通知：工作流任务抄送通知
	 */
	const MESSAGE_TASK_CC = 'task_cc';
	
	/**
	 * 消息通知：工作流催办通知
	 */
	const MESSAGE_TASK_URGE = 'task_urge';
	
	/**
	 * 消息通知：工作流转交任务通知
	 */
	const MESSAGE_TASK_TRANSFER = 'task_transfer';
	
	/**
	 * 消息通知：工作流终止通知
	 */
	const MESSAGE_TASK_TERMINATED = 'task_terminated';
	
	/**
	 * 消息通知：工作流作废通知
	 */
	const MESSAGE_TASK_VOID = 'task_void';
	
	// 实例状态别名（为了代码可读性）
	const STATUS_SAVED    = self::STATUS_DRAFT;
	const STATUS_PENDING  = self::STATUS_PROCESSING;
	const STATUS_APPROVED = self::STATUS_COMPLETED;
	
	// 任务状态
	const TASK_STATUS_PENDING  = 0;       // 待处理
	const TASK_STATUS_APPROVED = 1;       // 已通过
	const TASK_STATUS_REJECTED = 2;       // 已拒绝
	const TASK_STATUS_VOID     = 3;       // 已作废
	
	// ==================== 操作类型定义 ====================
	
	const OPERATION_AGREE         = 1;         // 同意/通过
	const OPERATION_REJECT        = 2;         // 拒绝
	const OPERATION_TRANSFER      = 3;         // 转交
	const OPERATION_TERMINATE     = 4;         // 终止
	const OPERATION_RECALL        = 5;         // 撤回
	const OPERATION_CANCEL        = 6;         // 作废
	const OPERATION_CC            = 7;         // 抄送
	const OPERATION_PROCESS_START = 8;         // 流程开始
	const OPERATION_PROCESS_END   = 9;         // 流程结束
	const OPERATION_URGE          = 10;        // 催办
	
	// ==================== 状态文本映射方法 ====================
	
	/**
	 * 获取实例状态文本
	 */
	public static function getInstanceStatusText(int $status): string
	{
		$map = [
			self::STATUS_DRAFT      => '草稿',
			self::STATUS_PROCESSING => '审批中',
			self::STATUS_COMPLETED  => '已通过',
			self::STATUS_REJECTED   => '已拒绝',
			self::STATUS_TERMINATED => '已终止',
			self::STATUS_RECALLED   => '已撤回',
			self::STATUS_VOID       => '已作废'
		];
		return $map[$status] ?? '未知状态';
	}
	
	/**
	 * 获取任务状态文本
	 */
	public static function getTaskStatusText(int $status): string
	{
		$map = [
			self::STATUS_DRAFT      => '待处理',
			self::STATUS_PROCESSING => '已同意',
			self::STATUS_COMPLETED  => '已拒绝',
			self::STATUS_REJECTED   => '已转交',
			self::STATUS_TERMINATED => '已终止',
			self::STATUS_RECALLED   => '已撤回',
			self::STATUS_SKIPPED    => '已跳过'
		];
		return $map[$status] ?? '未知状态';
	}
	
	/**
	 * 获取业务状态文本
	 */
	public static function getBusinessStatusText(int $status): string
	{
		return self::getInstanceStatusText($status); // 业务状态与实例状态一致
	}
	
	/**
	 * 获取操作类型文本
	 */
	public static function getOperationText(int $operation): string
	{
		$map = [
			self::OPERATION_AGREE         => '同意',
			self::OPERATION_REJECT        => '拒绝',
			self::OPERATION_TRANSFER      => '转交',
			self::OPERATION_TERMINATE     => '终止',
			self::OPERATION_RECALL        => '撤回',
			self::OPERATION_CANCEL        => '作废',
			self::OPERATION_CC            => '抄送',
			self::OPERATION_PROCESS_START => '流程开始',
			self::OPERATION_PROCESS_END   => '流程结束',
			self::OPERATION_URGE          => '催办'
		];
		return $map[$operation] ?? '未知操作';
	}
}