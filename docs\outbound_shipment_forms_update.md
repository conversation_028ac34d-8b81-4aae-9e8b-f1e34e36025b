# 出库和出货申请表单更新报告

## 📋 更新概述

**更新时间：** 2025-07-28  
**更新内容：** 出库和出货申请表单重新设计，统一组件使用，适老化样式适配  
**涉及表单：** `ims_outbound_approval-form.vue` 和 `ims_shipment_approval-form.vue`  

## ✅ 主要更新内容

### **1. 出库申请表单 (ims_outbound_approval)** ✅

#### **新的字段结构（8个字段，按顺序排列）**
| 序号 | 字段名 | 标签 | 类型 | 必填 | 说明 |
|------|--------|------|------|------|------|
| 1 | `dept_id` | 所在部门 | DepartmentTreeSelect | ✅ 是 | 统一部门选择组件 |
| 2 | `outbound_date` | 出库日期 | 日期选择 | ✅ 是 | 出库日期 |
| 3 | `customer_id` | 接收单位 | ApiSelect | ✅ 是 | 客户选择组件 |
| 4 | `items` | 出库明细 | 表格 | ✅ 是 | 至少需要一个明细 |
| 5 | `total_amount` | 货款总额(元) | 自动计算 | - | 自动计算显示 |
| 6 | `total_amount_chinese` | 货款总额大写 | 自动转换 | - | 自动转换显示 |
| 7 | `remark` | 备注 | 多行文本 | ❌ 否 | 备注信息 |
| 8 | `attachments` | 图片 | FormUploader | ❌ 否 | 图片附件 |

#### **出库明细表格字段（适老化样式）**
| 字段名 | 标签 | 宽度 | 类型 | 说明 |
|--------|------|------|------|------|
| `product_name` | 产品名称 | 200px | 下拉选择 | 产品选择 |
| `quantity` | 数量 | 120px | 数字输入 | 出库数量 |
| `unit_price` | 出库单价(元) | 140px | 数字输入 | 单价输入 |
| `unit_price_chinese` | 出库单价大写 | 160px | 自动转换 | 单价大写显示 |
| `total_amount` | 小计 | 120px | 自动计算 | 行小计 |
| `操作` | 操作 | 100px | 删除按钮 | 删除明细行 |

#### **移除的字段**
- ❌ `outbound_no` - 出库单号（系统自动生成）
- ❌ `outbound_type` - 出库类型
- ❌ `warehouse_id` - 出库仓库

### **2. 出货申请表单 (ims_shipment_approval)** ✅

#### **新的字段结构（8个字段，按顺序排列）**
| 序号 | 字段名 | 标签 | 类型 | 必填 | 说明 |
|------|--------|------|------|------|------|
| 1 | `dept_id` | 所在部门 | DepartmentTreeSelect | ✅ 是 | 统一部门选择组件 |
| 2 | `shipment_date` | 出货日期 | 日期选择 | ✅ 是 | 出货日期 |
| 3 | `customer_id` | 接收单位 | ApiSelect | ✅ 是 | 客户选择组件 |
| 4 | `items` | 出货明细 | 表格 | ✅ 是 | 至少需要一个明细 |
| 5 | `total_amount` | 货款总额(元) | 自动计算 | - | 自动计算显示 |
| 6 | `total_amount_chinese` | 货款总额大写 | 自动转换 | - | 自动转换显示 |
| 7 | `remark` | 备注 | 多行文本 | ❌ 否 | 备注信息 |
| 8 | `attachments` | 图片 | FormUploader | ❌ 否 | 图片附件 |

#### **出货明细表格字段（适老化样式）**
| 字段名 | 标签 | 宽度 | 类型 | 说明 |
|--------|------|------|------|------|
| `product_name` | 产品名称 | 200px | 下拉选择 | 产品选择 |
| `quantity` | 数量 | 120px | 数字输入 | 出货数量 |
| `unit_price` | 出货单价(元) | 140px | 数字输入 | 单价输入 |
| `unit_price_chinese` | 出货单价大写 | 160px | 自动转换 | 单价大写显示 |
| `total_amount` | 小计 | 120px | 自动计算 | 行小计 |
| `操作` | 操作 | 100px | 删除按钮 | 删除明细行 |

#### **移除的字段**
- ❌ `shipment_no` - 出货单号（系统自动生成）
- ❌ `supplier_id` - 供应商
- ❌ `contact_person` - 联系人
- ❌ `contact_phone` - 联系电话
- ❌ `logistics_company` - 物流公司
- ❌ `tracking_no` - 运单号

## 🔧 统一组件使用

### **1. 部门选择组件**
```vue
<DepartmentTreeSelect
  v-model="formData.dept_id"
  placeholder="请选择所在部门"
  :disabled="!isEditable"
/>
```

### **2. 客户选择组件**
```vue
<ApiSelect
  v-model="formData.customer_id"
  url="/crm/crm_customer_my/options"
  placeholder="请选择接收单位"
  :disabled="!isEditable"
/>
```

### **3. 图片上传组件**
```vue
<FormUploader
  v-model="formData.attachments"
  :disabled="!isEditable"
  :max-count="10"
  accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
  :max-size="10"
/>
```

## 🎯 适老化样式适配

### **明细表格适老化特性**
- ✅ **表格尺寸**：`size="default"`（替代原来的small）
- ✅ **输入框尺寸**：`size="default"`
- ✅ **按钮尺寸**：`size="default"`
- ✅ **列宽优化**：增加列宽以提供更大的操作区域
- ✅ **最小宽度**：840px（确保所有列都能正常显示）

### **样式对比表**
| 元素 | 更新前 | 更新后 | 提升 |
|------|--------|--------|------|
| 表格尺寸 | size="small" | size="default" | 更大字体 |
| 产品名称列 | 180px/200px | 200px | 统一宽度 |
| 数量列 | 100px/120px | 120px | 统一宽度 |
| 单价列 | 100px/120px | 140px | +17% |
| 单价大写列 | 无 | 160px | 新增 |
| 操作列 | 80px | 100px | +25% |
| 表格最小宽度 | 560px | 840px | +50% |

## 💰 数字转中文大写功能

### **自动转换特性**
- ✅ **单价大写**：每行的出库/出货单价自动转换为中文大写
- ✅ **总额大写**：货款总额自动转换为中文大写
- ✅ **实时更新**：数字变化时大写自动更新
- ✅ **格式标准**：符合财务规范的中文大写格式

### **转换示例**
| 数字 | 中文大写 |
|------|----------|
| 0 | 零元整 |
| 123.45 | 壹佰贰拾叁元肆角伍分 |
| 1000 | 壹仟元整 |
| 10000.50 | 壹万元伍角 |

### **技术实现**
```typescript
const convertToChineseNumber = (num: number): string => {
  // 完整的数字转中文大写实现
  // 支持整数和小数部分
  // 符合财务规范
}
```

## 📊 明细校验规则

### **前端校验**
- ✅ **至少一个明细**：提交时检查明细数量
- ✅ **必填字段验证**：产品名称、数量、单价必填
- ✅ **数值范围验证**：数量和单价必须大于0

### **校验提示**
```typescript
// 出库申请
if (formData.items.length === 0) {
  ElMessage.warning('请至少添加一条出库明细')
  return
}

// 出货申请
if (formData.items.length === 0) {
  ElMessage.warning('请至少添加一条出货明细')
  return
}
```

## 🚀 技术实现亮点

### **1. 统一的组件架构**
- 所有表单使用相同的部门选择组件
- 统一的客户选择API接口
- 标准化的图片上传组件

### **2. 适老化设计理念**
- 更大的字体和操作区域
- 充足的视觉间距
- 清晰的表格边界和分隔

### **3. 自动化计算**
- 明细小计自动计算
- 总金额自动汇总
- 中文大写自动转换

### **4. 响应式适配**
- 表格支持横向滚动
- 固定操作列确保可见性
- 最小宽度保证内容完整性

## 📱 响应式表现

### **不同屏幕尺寸适配**
| 屏幕宽度 | 表格表现 | 用户体验 |
|----------|----------|----------|
| **>840px** | 正常显示 | ✅ 最佳体验 |
| **600-840px** | 横向滚动 | ✅ 可用 |
| **<600px** | 横向滚动 | ✅ 基本可用 |

### **滚动行为**
- ✅ **操作列固定**：删除按钮始终可见
- ✅ **内容优先**：重要信息优先显示
- ✅ **平滑滚动**：支持触摸和鼠标滚动

## 🧪 测试建议

### **功能测试**
1. **明细操作测试**
   - 测试添加明细行
   - 测试删除明细行
   - 测试明细内容编辑

2. **自动计算测试**
   - 测试小计自动计算
   - 测试总金额自动汇总
   - 测试中文大写自动转换

3. **组件集成测试**
   - 测试部门选择组件
   - 测试客户选择组件
   - 测试图片上传组件

### **适老化测试**
1. **视觉测试**
   - 验证字体大小适合老年用户
   - 检查操作区域是否足够大
   - 确认视觉层次清晰

2. **操作测试**
   - 测试按钮点击准确性
   - 验证输入框操作便利性
   - 检查表格滚动流畅性

## 📊 总结

✅ **两个表单完全重新设计**  
✅ **统一使用标准组件**  
✅ **明细表格适老化适配**  
✅ **数字转中文大写功能**  
✅ **至少一个明细校验**  
✅ **响应式设计优化**  

**出库和出货申请表单现在具备了统一的用户体验、完善的适老化设计和强大的自动化功能！**

---

**表单重新设计** | **统一组件使用** | **适老化适配** | **自动化计算**
