<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmWorkReport;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 工作报告表服务类
 */
class CrmWorkReportService extends BaseService
{
	
	use ExportableTrait, ImportableTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmWorkReport();
		parent::__construct();
		$this->crudService->setDefaultSearchFields($this->getSearchFields());
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'title' => ['type' => 'like'],
			
			'type' => ['type' => 'eq'],
			
			'report_date' => ['type' => 'eq'],
		
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			// 在这里定义验证规则
			// 例如：'username' => 'require|unique:crm_work_report',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	/**
	 * 复制汇报
	 *
	 * @param int $id 原汇报ID
	 * @return int
	 */
	public function copy(int $id): int
	{
		$original = $this->getCrudService()
		                 ->getDetail($id);
		$data     = $original->toArray();
		
		// 移除主键和时间戳
		unset($data['id'], $data['created_at'], $data['updated_at']);
		
		// 修改标题和日期
		$data['title']       = '复制-' . $data['title'];
		$data['report_date'] = date('Y-m-d');
		
		return $this->getCrudService()
		            ->add($data);
	}
}