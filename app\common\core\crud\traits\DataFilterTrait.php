<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

/**
 * 数据过滤相关功能
 */
trait DataFilterTrait
{
	/**
	 * @deprecated  有效的操作符列表
	 */
	/*private const VALID_OPERATORS = [
		'eq', '=', '<>', '!=', '>', '>=', '<', '<=',
		'like', 'not like', 'between', 'not between',
		'in', 'not in', 'null', 'not null',
		'exists', 'not exists', 'exp', 'find_in_set',
		'time', 'between time', 'not between time'
	];*/
	
	/**
	 * 有效的操作符列表 - 哈希表优化
	 */
	private const VALID_OPERATORS = [
		'eq'               => true,
		'='                => true,
		'<>'               => true,
		'!='               => true,
		'>'                => true,
		'>='               => true,
		'<'                => true,
		'<='               => true,
		'like'             => true,
		'not like'         => true,
		'between'          => true,
		'not between'      => true,
		'in'               => true,
		'not in'           => true,
		'null'             => true,
		'not null'         => true,
		'exists'           => true,
		'not exists'       => true,
		'exp'              => true,
		'find_in_set'      => true,
		'time'             => true,
		'between time'     => true,
		'not between time' => true
	];
	
	/**
	 * 过滤查询条件，防止SQL注入
	 */
	protected function filterWhereCondition(array $where): array
	{
		$result = [];
		foreach ($where as $key => $value) {
			// 如果是索引数组形式的条件
			if (is_numeric($key)) {
				if (is_array($value) && count($value) >= 3) {
					$field = $value[0];
					$op    = strtolower($value[1]);
					$val   = $value[2];
					
					// 检查字段和操作符是否有效
					if ($this->isValidField($field) && $this->isValidOperator($op)) {
						$result[] = [
							$field,
							$op,
							$this->filterValue($val, $op)
						];
					}
				}
			}
			else {
				// 如果是关联数组形式的条件
				if ($this->isValidField($key)) {
					$result[$key] = $this->filterValue($value);
				}
			}
		}
		return $result;
	}
	
	/**
	 * 检查字段是否有效
	 */
	protected function isValidField(string $field): bool
	{
		// 防止SQL注入，检查字段名是否包含危险字符
		return !preg_match('/[^a-zA-Z0-9_\.]/', $field);
	}
	
	/**
	 * 检查操作符是否有效
	 */
	protected function isValidOperator(string $op): bool
	{
		return isset(self::VALID_OPERATORS[strtolower($op)]);
	}
	
	/**
	 * 过滤值，防止SQL注入
	 */
	protected function filterValue($value, string $op = 'eq')
	{
		if (is_array($value)) {
			$result = [];
			foreach ($value as $k => $v) {
				$result[$k] = is_string($v)
					? $this->filterString($v)
					: $v;
			}
			return $result;
		}
		
		return is_string($value)
			? $this->filterString($value, $op)
			: $value;
	}
	
	/**
	 * 过滤字符串，防止SQL注入
	 */
	protected function filterString(string $value, string $op = 'eq'): string
	{
		// 对于LIKE操作，过滤掉通配符
		if (stripos($op, 'like') !== false) {
			// 转义LIKE的特殊字符 % 和 _
			$value = str_replace([
				'%',
				'_'
			], [
				'\%',
				'\_'
			], $value);
		}
		
		return $value;
	}
	
	/**
	 * 过滤提交的数据
	 */
	protected function filterData(array $data): array
	{
		$result = [];
		foreach ($data as $key => $value) {
			// 检查字段是否有效且存在于模型中
			if ($this->isValidField($key) && $this->model->hasField($key)) {
				$result[$key] = is_string($value)
					? $this->filterString($value)
					: $value;
			}
		}
		return $result;
	}
} 