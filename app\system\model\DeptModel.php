<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 部门模型
 */
class DeptModel extends BaseModel
{
    
    /**
     * 表名
     * @var string
     */
    protected $name = 'system_dept';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'parent_id'   => 'int',
        'name'        => 'string',
        'code'        => 'string',
        'leader_name' => 'string',
        'phone'       => 'string',
        'email'       => 'string',
        'sort'        => 'int',
        'status'      => 'int',
        'remark'      => 'string',
        'creator_id'  => 'int',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
        'deleted_at'  => 'datetime',
        'tenant_id'   => 'int',
    ];
	
	protected $append = [
		'status_text'
	];
	
	public function getStatusTextAttr($value, $data)
	{
		$status = $data['status'] ?? 0;
		$statusMap = [
			0 => '禁用',
			1 => '正常'
		];
		return $statusMap[$status] ?? '';
	}
	
	public function admins()
	{
		return $this->hasMany(AdminModel::class, 'dept_id', 'id');
	}
	
} 