<?php
/**
 * CrudService性能测试
 * 对比原版本和优化版本的性能差异
 */

require_once __DIR__ . '/../../../../vendor/autoload.php';

use think\facade\Db;
use think\facade\Log;

// 初始化应用
$app = new \think\App();
$app->initialize();

echo "=== CrudService性能基准测试 ===\n\n";

try {
    // 设置request上下文
    $request = request();
    $request->adminId = 1;
    $request->tenantId = 0;
    
    // 从数据库获取完整的管理员信息
    $adminInfo = Db::name('system_admin')->where('id', 1)->find();
    if ($adminInfo) {
        $request->adminInfo = [
            'admin_id' => $adminInfo['id'],
            'data' => $adminInfo
        ];
        echo "✅ 管理员信息设置成功\n\n";
    } else {
        echo "❌ 无法获取管理员信息\n";
        return;
    }
    
    // 测试数据准备
    $testData = [
        'task_id' => 'perf_test_' . time(),
        'instance_id' => 94,
        'process_id' => 'PERF-TEST-' . time(),
        'node_id' => 'test-node-' . uniqid(),
        'node_name' => '性能测试节点',
        'node_type' => 'test',
        'task_type' => 1,
        'approver_id' => 1,
        'approver_name' => '测试用户',
        'status' => 0,
        'sort' => 0,
        'tenant_id' => 0
    ];
    
    $iterations = 100; // 测试迭代次数
    
    echo "测试配置:\n";
    echo "  - 迭代次数: {$iterations}\n";
    echo "  - 测试数据大小: " . strlen(json_encode($testData)) . " bytes\n\n";
    
    // 1. 测试模型实例化性能
    echo "1. 模型实例化性能测试...\n";
    
    $startTime = microtime(true);
    $startMemory = memory_get_usage();
    
    for ($i = 0; $i < $iterations; $i++) {
        $model = new \app\workflow\model\WorkflowTask();
        unset($model); // 立即释放
    }
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage();
    
    $instanceTime = ($endTime - $startTime) * 1000; // 转换为毫秒
    $instanceMemory = $endMemory - $startMemory;
    
    echo "  - 总耗时: " . number_format($instanceTime, 3) . " ms\n";
    echo "  - 平均每次: " . number_format($instanceTime / $iterations, 3) . " ms\n";
    echo "  - 内存变化: " . number_format($instanceMemory / 1024, 2) . " KB\n\n";
    
    // 2. 测试原版CrudService连续操作
    echo "2. 原版CrudService连续操作测试...\n";
    
    $originalService = new \app\workflow\service\WorkflowTaskService();
    
    $startTime = microtime(true);
    $startMemory = memory_get_usage();
    $originalSuccessCount = 0;
    
    for ($i = 0; $i < 10; $i++) { // 减少迭代次数，避免数据库压力
        try {
            $data = array_merge($testData, [
                'task_id' => 'original_' . time() . '_' . $i
            ]);
            
            $result = $originalService->getCrudService()->add($data);
            if ($result) {
                $originalSuccessCount++;
            }
        } catch (\Exception $e) {
            echo "    - 第{$i}次操作失败: " . $e->getMessage() . "\n";
        }
    }
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage();
    
    $originalTime = ($endTime - $startTime) * 1000;
    $originalMemory = $endMemory - $startMemory;
    
    echo "  - 成功次数: {$originalSuccessCount}/10\n";
    echo "  - 总耗时: " . number_format($originalTime, 3) . " ms\n";
    echo "  - 平均每次: " . number_format($originalTime / 10, 3) . " ms\n";
    echo "  - 内存变化: " . number_format($originalMemory / 1024, 2) . " KB\n\n";
    
    // 3. 测试直接模型实例化操作
    echo "3. 直接模型实例化操作测试...\n";
    
    $startTime = microtime(true);
    $startMemory = memory_get_usage();
    $directSuccessCount = 0;
    
    for ($i = 0; $i < 10; $i++) {
        try {
            $data = array_merge($testData, [
                'task_id' => 'direct_' . time() . '_' . $i
            ]);
            
            // 每次创建新的模型实例
            $model = new \app\workflow\model\WorkflowTask();
            $result = $model->saveByCreate($data);
            
            if ($result) {
                $directSuccessCount++;
            }
        } catch (\Exception $e) {
            echo "    - 第{$i}次操作失败: " . $e->getMessage() . "\n";
        }
    }
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage();
    
    $directTime = ($endTime - $startTime) * 1000;
    $directMemory = $endMemory - $startMemory;
    
    echo "  - 成功次数: {$directSuccessCount}/10\n";
    echo "  - 总耗时: " . number_format($directTime, 3) . " ms\n";
    echo "  - 平均每次: " . number_format($directTime / 10, 3) . " ms\n";
    echo "  - 内存变化: " . number_format($directMemory / 1024, 2) . " KB\n\n";
    
    // 4. 测试内存泄漏情况
    echo "4. 内存泄漏测试...\n";
    
    $initialMemory = memory_get_usage();
    
    // 大量创建和销毁模型实例
    for ($i = 0; $i < 1000; $i++) {
        $model = new \app\workflow\model\WorkflowTask();
        unset($model);
        
        if ($i % 100 == 0) {
            $currentMemory = memory_get_usage();
            $memoryDiff = $currentMemory - $initialMemory;
            echo "    - 第{$i}次迭代，内存变化: " . number_format($memoryDiff / 1024, 2) . " KB\n";
        }
    }
    
    $finalMemory = memory_get_usage();
    $totalMemoryDiff = $finalMemory - $initialMemory;
    
    echo "  - 总内存变化: " . number_format($totalMemoryDiff / 1024, 2) . " KB\n";
    echo "  - 是否存在内存泄漏: " . ($totalMemoryDiff > 1024 * 100 ? "可能存在" : "正常") . "\n\n";
    
    // 5. 性能对比总结
    echo "5. 性能对比总结:\n";
    echo "  - 模型实例化平均耗时: " . number_format($instanceTime / $iterations, 3) . " ms\n";
    echo "  - 原版CrudService成功率: " . number_format($originalSuccessCount / 10 * 100, 1) . "%\n";
    echo "  - 直接模型实例化成功率: " . number_format($directSuccessCount / 10 * 100, 1) . "%\n";
    
    if ($directTime > 0 && $originalTime > 0) {
        $performanceRatio = $originalTime / $directTime;
        echo "  - 性能比较: 直接实例化比原版" . 
             ($performanceRatio > 1 ? "快" : "慢") . 
             number_format(abs($performanceRatio - 1) * 100, 1) . "%\n";
    }
    
    // 6. 结论和建议
    echo "\n6. 结论和建议:\n";
    
    if ($directSuccessCount > $originalSuccessCount) {
        echo "  ✅ 直接模型实例化方案在可靠性方面更优\n";
    }
    
    if ($instanceTime / $iterations < 1) {
        echo "  ✅ 模型实例化性能开销可忽略（< 1ms）\n";
    }
    
    if ($totalMemoryDiff < 1024 * 100) {
        echo "  ✅ 无明显内存泄漏问题\n";
    }
    
    echo "  📋 建议：基于测试结果，推荐在CrudService中采用每次操作创建新模型实例的方案\n";
    
    echo "\n=== 性能测试完成 ===\n";

} catch (\Exception $e) {
    echo "❌ 测试过程中发生错误：" . $e->getMessage() . "\n";
    echo "错误堆栈：\n" . $e->getTraceAsString() . "\n";
}
