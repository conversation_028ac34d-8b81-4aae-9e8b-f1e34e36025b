<?php
declare(strict_types=1);

namespace app\common\utils;

use think\facade\Log;
use think\facade\Db;

/**
 * 权限审计工具类
 * 提供权限检查日志记录、异常访问告警和权限变更审计跟踪
 */
class PermissionAuditUtil
{
    /**
     * 审计日志类型
     */
    const AUDIT_TYPE_ACCESS = 'access';           // 访问审计
    const AUDIT_TYPE_PERMISSION = 'permission';   // 权限变更审计
    const AUDIT_TYPE_SECURITY = 'security';       // 安全事件审计
    
    /**
     * 安全事件级别
     */
    const SECURITY_LEVEL_INFO = 'info';
    const SECURITY_LEVEL_WARNING = 'warning';
    const SECURITY_LEVEL_DANGER = 'danger';
    const SECURITY_LEVEL_CRITICAL = 'critical';
    
    /**
     * 记录数据访问审计
     *
     * @param array $params 审计参数
     * @return void
     */
    public static function logDataAccess(array $params): void
    {
        $auditData = [
            'type' => self::AUDIT_TYPE_ACCESS,
            'admin_id' => $params['admin_id'] ?? 0,
            'tenant_id' => $params['tenant_id'] ?? 0,
            'resource' => $params['resource'] ?? '',
            'action' => $params['action'] ?? '',
            'data_count' => $params['data_count'] ?? 0,
            'permission_scope' => $params['permission_scope'] ?? [],
            'query_conditions' => $params['query_conditions'] ?? [],
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'request_time' => date('Y-m-d H:i:s'),
            'execution_time' => $params['execution_time'] ?? 0
        ];
        
        // 记录到日志文件
        Log::channel('permission_audit')->info('数据访问审计', $auditData);
        
        // 异步写入数据库（可选）
        if (config('permission.audit.save_to_db', false)) {
            self::saveAuditToDatabase($auditData);
        }
    }
    
    /**
     * 记录权限变更审计
     *
     * @param array $params 审计参数
     * @return void
     */
    public static function logPermissionChange(array $params): void
    {
        $auditData = [
            'type' => self::AUDIT_TYPE_PERMISSION,
            'operator_id' => $params['operator_id'] ?? 0,
            'tenant_id' => $params['tenant_id'] ?? 0,
            'target_type' => $params['target_type'] ?? '', // user, role, dept
            'target_id' => $params['target_id'] ?? 0,
            'change_type' => $params['change_type'] ?? '', // create, update, delete
            'old_data' => $params['old_data'] ?? [],
            'new_data' => $params['new_data'] ?? [],
            'change_fields' => $params['change_fields'] ?? [],
            'reason' => $params['reason'] ?? '',
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'change_time' => date('Y-m-d H:i:s')
        ];
        
        // 记录到日志文件
        Log::channel('permission_audit')->warning('权限变更审计', $auditData);
        
        // 异步写入数据库
        if (config('permission.audit.save_to_db', false)) {
            self::saveAuditToDatabase($auditData);
        }
        
        // 触发权限缓存更新
        self::triggerPermissionCacheUpdate($auditData);
    }
    
    /**
     * 记录安全事件
     *
     * @param string $level 安全级别
     * @param string $event 事件描述
     * @param array $params 事件参数
     * @return void
     */
    public static function logSecurityEvent(string $level, string $event, array $params = []): void
    {
        $auditData = [
            'type' => self::AUDIT_TYPE_SECURITY,
            'level' => $level,
            'event' => $event,
            'admin_id' => $params['admin_id'] ?? 0,
            'tenant_id' => $params['tenant_id'] ?? 0,
            'details' => $params['details'] ?? [],
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'event_time' => date('Y-m-d H:i:s')
        ];
        
        // 根据安全级别选择日志级别
        $logLevel = match($level) {
            self::SECURITY_LEVEL_CRITICAL => 'critical',
            self::SECURITY_LEVEL_DANGER => 'error',
            self::SECURITY_LEVEL_WARNING => 'warning',
            default => 'info'
        };
        
        Log::channel('security_audit')->{$logLevel}('安全事件', $auditData);
        
        // 高危事件立即写入数据库并发送告警
        if (in_array($level, [self::SECURITY_LEVEL_DANGER, self::SECURITY_LEVEL_CRITICAL])) {
            self::saveAuditToDatabase($auditData);
            self::sendSecurityAlert($auditData);
        }
    }
    
    /**
     * 检测异常访问模式
     *
     * @param array $accessData 访问数据
     * @return void
     */
    public static function detectAnomalousAccess(array $accessData): void
    {
        $adminId = $accessData['admin_id'] ?? 0;
        $tenantId = $accessData['tenant_id'] ?? 0;
        
        if ($adminId <= 0) {
            return;
        }
        
        try {
            // 检测频繁访问
            if (self::isFrequentAccess($adminId, $tenantId)) {
                self::logSecurityEvent(
                    self::SECURITY_LEVEL_WARNING,
                    '检测到频繁访问',
                    [
                        'admin_id' => $adminId,
                        'tenant_id' => $tenantId,
                        'details' => ['type' => 'frequent_access']
                    ]
                );
            }
            
            // 检测异常时间访问
            if (self::isOffHoursAccess()) {
                self::logSecurityEvent(
                    self::SECURITY_LEVEL_INFO,
                    '非工作时间访问',
                    [
                        'admin_id' => $adminId,
                        'tenant_id' => $tenantId,
                        'details' => ['type' => 'off_hours_access', 'time' => date('H:i:s')]
                    ]
                );
            }
            
            // 检测跨租户访问尝试
            if (self::isCrossTenantAccess($accessData)) {
                self::logSecurityEvent(
                    self::SECURITY_LEVEL_DANGER,
                    '检测到跨租户访问尝试',
                    [
                        'admin_id' => $adminId,
                        'tenant_id' => $tenantId,
                        'details' => $accessData
                    ]
                );
            }
            
        } catch (\Exception $e) {
            Log::error('异常访问检测失败: ' . $e->getMessage(), [
                'admin_id' => $adminId,
                'tenant_id' => $tenantId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 检测是否为频繁访问
     *
     * @param int $adminId 管理员ID
     * @param int $tenantId 租户ID
     * @return bool
     */
    private static function isFrequentAccess(int $adminId, int $tenantId): bool
    {
        $cacheKey = "frequent_access_check:{$adminId}:{$tenantId}";
        $cache = cache($cacheKey, 0);
        
        // 1分钟内访问超过100次认为是频繁访问
        $threshold = config('permission.audit.frequent_access_threshold', 100);
        $timeWindow = config('permission.audit.frequent_access_window', 60);
        
        if ($cache >= $threshold) {
            return true;
        }
        
        // 增加计数器
        cache($cacheKey, $cache + 1, $timeWindow);
        
        return false;
    }
    
    /**
     * 检测是否为非工作时间访问
     *
     * @return bool
     */
    private static function isOffHoursAccess(): bool
    {
        $hour = (int)date('H');
        $workStartHour = config('permission.audit.work_start_hour', 8);
        $workEndHour = config('permission.audit.work_end_hour', 18);
        
        return $hour < $workStartHour || $hour > $workEndHour;
    }
    
    /**
     * 检测是否为跨租户访问
     *
     * @param array $accessData 访问数据
     * @return bool
     */
    private static function isCrossTenantAccess(array $accessData): bool
    {
        // 这里可以实现具体的跨租户访问检测逻辑
        // 例如：检查查询条件中是否包含其他租户的数据
        
        $queryConditions = $accessData['query_conditions'] ?? [];
        $currentTenantId = $accessData['tenant_id'] ?? 0;
        
        // 简单示例：检查查询条件中是否有不同的tenant_id
        if (isset($queryConditions['tenant_id']) && 
            $queryConditions['tenant_id'] != $currentTenantId) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 保存审计数据到数据库
     *
     * @param array $auditData 审计数据
     * @return void
     */
    private static function saveAuditToDatabase(array $auditData): void
    {
        try {
            // 异步队列处理，避免影响主业务性能
            // 这里可以使用队列系统，如Redis队列、RabbitMQ等
            
            // 简单示例：直接写入数据库
            Db::table('system_audit_log')->insert([
                'type' => $auditData['type'],
                'level' => $auditData['level'] ?? 'info',
                'admin_id' => $auditData['admin_id'] ?? 0,
                'tenant_id' => $auditData['tenant_id'] ?? 0,
                'content' => json_encode($auditData, JSON_UNESCAPED_UNICODE),
                'ip_address' => $auditData['ip_address'] ?? '',
                'user_agent' => $auditData['user_agent'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Exception $e) {
            Log::error('保存审计数据到数据库失败: ' . $e->getMessage(), [
                'audit_data' => $auditData,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 发送安全告警
     *
     * @param array $auditData 审计数据
     * @return void
     */
    private static function sendSecurityAlert(array $auditData): void
    {
        try {
            // 这里可以实现具体的告警机制
            // 例如：发送邮件、短信、钉钉消息等
            
            $alertData = [
                'title' => '安全告警：' . $auditData['event'],
                'level' => $auditData['level'],
                'time' => $auditData['event_time'],
                'admin_id' => $auditData['admin_id'],
                'tenant_id' => $auditData['tenant_id'],
                'ip_address' => $auditData['ip_address'],
                'details' => $auditData['details']
            ];
            
            // 记录告警发送日志
            Log::channel('security_alert')->critical('安全告警已发送', $alertData);
            
        } catch (\Exception $e) {
            Log::error('发送安全告警失败: ' . $e->getMessage(), [
                'audit_data' => $auditData,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 触发权限缓存更新
     *
     * @param array $changeData 变更数据
     * @return void
     */
    private static function triggerPermissionCacheUpdate(array $changeData): void
    {
        try {
            $targetType = $changeData['target_type'] ?? '';
            $targetId = $changeData['target_id'] ?? 0;
            $tenantId = $changeData['tenant_id'] ?? 0;
            
            switch ($targetType) {
                case 'role':
                    DataPermissionCacheUtil::handlePermissionChangeEvent('role_changed', [
                        'role_id' => $targetId,
                        'tenant_id' => $tenantId
                    ]);
                    break;
                    
                case 'dept':
                    DataPermissionCacheUtil::handlePermissionChangeEvent('dept_changed', [
                        'dept_id' => $targetId,
                        'tenant_id' => $tenantId
                    ]);
                    break;
                    
                case 'user':
                    DataPermissionCacheUtil::handlePermissionChangeEvent('user_changed', [
                        'user_id' => $targetId,
                        'tenant_id' => $tenantId
                    ]);
                    break;
            }
            
        } catch (\Exception $e) {
            Log::error('触发权限缓存更新失败: ' . $e->getMessage(), [
                'change_data' => $changeData,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
