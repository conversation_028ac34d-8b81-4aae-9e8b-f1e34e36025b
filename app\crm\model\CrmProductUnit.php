<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 产品单位表模型
 */
class CrmProductUnit extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_product_unit';
	
	// 设置主键
	protected $pk = 'id';
	
	protected $append = [
		'creator',
	];
	
	public function getDefaultSearchFields(): array
	{
		return [
			'unit_name' => ['type' => 'eq'],
			'unit_code' => ['type' => 'eq'],
			'status'    => ['type' => 'eq'],
		];
	}
}