-- =====================================================
-- 为出库和出货申请表添加总金额大写字段
-- 执行时间：2025-07-29
-- =====================================================

-- 1. 为出库申请表添加总金额大写字段
ALTER TABLE `ims_outbound_approval` 
ADD COLUMN `total_amount_chinese` varchar(200) NOT NULL DEFAULT '' 
COMMENT '总金额大写' 
AFTER `total_amount`;

-- 2. 为出货申请表添加总金额大写字段
ALTER TABLE `ims_shipment_approval` 
ADD COLUMN `total_amount_chinese` varchar(200) NOT NULL DEFAULT '' 
COMMENT '总金额大写' 
AFTER `total_amount`;

-- 3. 为出库明细表添加供应商ID字段（如果不存在）
-- 检查字段是否存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ims_outbound_item' 
    AND COLUMN_NAME = 'supplier_id'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `ims_outbound_item` ADD COLUMN `supplier_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT ''供应商ID'' AFTER `outbound_id`',
    'SELECT ''supplier_id字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为出货明细表添加供应商ID字段（如果不存在）
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ims_shipment_item' 
    AND COLUMN_NAME = 'supplier_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `ims_shipment_item` ADD COLUMN `supplier_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT ''供应商ID'' AFTER `shipment_id`',
    'SELECT ''supplier_id字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 为出库明细表添加产品单位字段（如果不存在）
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ims_outbound_item' 
    AND COLUMN_NAME = 'product_unit'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `ims_outbound_item` ADD COLUMN `product_unit` varchar(20) NOT NULL DEFAULT '''' COMMENT ''产品单位'' AFTER `product_id`',
    'SELECT ''product_unit字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 为出货明细表添加产品单位字段（如果不存在）
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ims_shipment_item' 
    AND COLUMN_NAME = 'product_unit'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `ims_shipment_item` ADD COLUMN `product_unit` varchar(20) NOT NULL DEFAULT '''' COMMENT ''产品单位'' AFTER `product_id`',
    'SELECT ''product_unit字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 添加索引优化
-- 为出库明细表的供应商ID添加索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ims_outbound_item' 
    AND INDEX_NAME = 'idx_supplier_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `ims_outbound_item` ADD INDEX `idx_supplier_id` (`supplier_id`)',
    'SELECT ''idx_supplier_id索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为出货明细表的供应商ID添加索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ims_shipment_item' 
    AND INDEX_NAME = 'idx_supplier_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `ims_shipment_item` ADD INDEX `idx_supplier_id` (`supplier_id`)',
    'SELECT ''idx_supplier_id索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 验证表结构
SELECT 
    'ims_outbound_approval' as table_name,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ims_outbound_approval'
AND COLUMN_NAME IN ('total_amount', 'total_amount_chinese', 'total_quantity')
ORDER BY ORDINAL_POSITION;

SELECT 
    'ims_shipment_approval' as table_name,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ims_shipment_approval'
AND COLUMN_NAME IN ('total_amount', 'total_amount_chinese', 'total_quantity')
ORDER BY ORDINAL_POSITION;

SELECT 
    'ims_outbound_item' as table_name,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ims_outbound_item'
AND COLUMN_NAME IN ('supplier_id', 'product_id', 'product_unit', 'quantity', 'unit_price', 'total_amount')
ORDER BY ORDINAL_POSITION;

SELECT 
    'ims_shipment_item' as table_name,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ims_shipment_item'
AND COLUMN_NAME IN ('supplier_id', 'product_id', 'product_unit', 'quantity', 'unit_price', 'total_amount')
ORDER BY ORDINAL_POSITION;

-- 执行完成提示
SELECT 
    '出库和出货申请表字段添加完成' as 状态,
    '已添加总金额大写字段和必要的明细字段' as 说明,
    NOW() as 执行时间;
