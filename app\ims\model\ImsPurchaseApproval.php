<?php
declare(strict_types=1);

namespace app\ims\model;

use app\common\core\base\BaseModel;
use app\workflow\constants\WorkflowStatusConstant;

/**
 * 采购申请表模型
 */
class ImsPurchaseApproval extends BaseModel
{
    // 设置表名
    protected $name = 'ims_purchase_approval';
    
    // 设置主键
    protected $pk = 'id';
    
    // 字段类型转换
    protected $type = [
        'workflow_instance_id' => 'integer',
        'approval_status' => 'integer',
        'submitter_id' => 'integer',
        'purchase_type' => 'integer',
        'supplier_id' => 'integer',
        'total_amount' => 'float',
        'total_quantity' => 'float',
        'payment_method' => 'integer',
        'creator_id' => 'integer',
    ];
    
    // 审批状态常量
    const STATUS_DRAFT = WorkflowStatusConstant::STATUS_DRAFT;
    const STATUS_PROCESSING = WorkflowStatusConstant::STATUS_PROCESSING;
    const STATUS_COMPLETED = WorkflowStatusConstant::STATUS_COMPLETED;
    const STATUS_REJECTED = WorkflowStatusConstant::STATUS_REJECTED;
    const STATUS_TERMINATED = WorkflowStatusConstant::STATUS_TERMINATED;
    const STATUS_RECALLED = WorkflowStatusConstant::STATUS_RECALLED;
    const STATUS_VOID = WorkflowStatusConstant::STATUS_VOID;
    
    // 采购类型常量
    const TYPE_REGULAR = 1;   // 常规采购
    const TYPE_URGENT = 2;    // 紧急采购
    const TYPE_RESTOCK = 3;   // 补货采购
    
    // 付款方式常量
    const PAYMENT_BANK_TRANSFER = 1; // 银行转账
    const PAYMENT_CASH = 2;          // 现金
    const PAYMENT_CHECK = 3;         // 支票
    const PAYMENT_OTHER = 4;         // 其他
    
    /**
     * 获取默认搜索字段
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'purchase_no' => ['type' => 'like'],
            'purchase_type' => ['type' => 'eq'],
            'approval_status' => ['type' => 'eq'],
            'supplier_id' => ['type' => 'eq'],
            'expected_date' => ['type' => 'date'],
            'contact_person' => ['type' => 'like'],
            'contact_phone' => ['type' => 'like'],
            'total_amount' => ['type' => 'between'],
            'total_quantity' => ['type' => 'between'],
            'payee_name' => ['type' => 'like'],
            'payee_account' => ['type' => 'like'],
            'payee_bank' => ['type' => 'like'],
            'payment_method' => ['type' => 'eq'],
            'submit_time' => ['type' => 'datetime'],
            'approval_time' => ['type' => 'datetime'],
            'created_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 关联采购明细
     */
    public function items()
    {
        return $this->hasMany(ImsPurchaseItem::class, 'purchase_id', 'id');
    }
    
    /**
     * 关联供应商
     */
    public function supplier()
    {
        return $this->belongsTo(\app\ims\model\ImsSupplier::class, 'supplier_id', 'id')->bind([
            'supplier_name' => 'name'
        ]);
    }
    
    /**
     * 关联提交人
     */
    public function submitter()
    {
        return $this->belongsTo(\app\system\model\SystemAdmin::class, 'submitter_id', 'id')->bind([
            'submitter_name' => 'username'
        ]);
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(\app\system\model\SystemAdmin::class, 'creator_id', 'id')->bind([
            'creator_name' => 'username'
        ]);
    }
    
    /**
     * 获取采购类型文本
     */
    public function getPurchaseTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_REGULAR => '常规采购',
            self::TYPE_URGENT => '紧急采购',
            self::TYPE_RESTOCK => '补货采购',
        ];
        
        return $types[$data['purchase_type']] ?? '未知';
    }
    
    /**
     * 获取付款方式文本
     */
    public function getPaymentMethodTextAttr($value, $data)
    {
        $methods = [
            self::PAYMENT_BANK_TRANSFER => '银行转账',
            self::PAYMENT_CASH => '现金',
            self::PAYMENT_CHECK => '支票',
            self::PAYMENT_OTHER => '其他',
        ];
        
        return $methods[$data['payment_method']] ?? '未知';
    }
    
    /**
     * 获取审批状态文本
     */
    public function getApprovalStatusTextAttr($value, $data)
    {
        $statuses = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PROCESSING => '审批中',
            self::STATUS_COMPLETED => '已通过',
            self::STATUS_REJECTED => '已拒绝',
            self::STATUS_TERMINATED => '已终止',
            self::STATUS_RECALLED => '已撤回',
            self::STATUS_VOID => '已作废',
        ];
        
        return $statuses[$data['approval_status']] ?? '未知';
    }
}
