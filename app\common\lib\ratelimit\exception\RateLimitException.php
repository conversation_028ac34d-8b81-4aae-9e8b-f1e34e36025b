<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\exception;

use app\common\lib\ratelimit\rule\Rule;

/**
 * 限流异常类
 */
class RateLimitException extends \RuntimeException
{
    /**
     * 规则
     */
    protected Rule $rule;
    
    /**
     * 剩余可用请求次数
     */
    protected int $remaining;
    
    /**
     * 重置时间（秒）
     */
    protected int $resetTime;
    
    /**
     * 构造函数
     *
     * @param Rule $rule 限流规则
     * @param int $remaining 剩余可用请求次数
     * @param int $resetTime 重置时间（秒）
     * @param string $message 错误信息
     * @param int $code 错误码
     * @param \Throwable|null $previous 上一个异常
     */
    public function __construct(
        Rule $rule,
        int $remaining,
        int $resetTime,
        string $message = '请求频率超限',
        int $code = 429,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->rule = $rule;
        $this->remaining = $remaining;
        $this->resetTime = $resetTime;
    }
    
    /**
     * 获取规则
     * 
     * @return Rule 规则
     */
    public function getRule(): Rule
    {
        return $this->rule;
    }
    
    /**
     * 获取剩余可用请求次数
     * 
     * @return int 剩余可用请求次数
     */
    public function getRemaining(): int
    {
        return $this->remaining;
    }
    
    /**
     * 获取重置时间（秒）
     * 
     * @return int 重置时间
     */
    public function getResetTime(): int
    {
        return $this->resetTime;
    }
    
    /**
     * 获取异常数据（用于响应）
     * 
     * @return array 异常数据
     */
    public function getExceptionData(): array
    {
        return [
            'code' => $this->getCode(),
            'message' => $this->getMessage(),
            'data' => [
                'rule_name' => $this->rule->getName(),
                'rule_type' => $this->rule->getType(),
                'remaining' => $this->remaining,
                'reset_time' => $this->resetTime,
            ],
        ];
    }
} 