<template>
  <div class="expense-form-view">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="报销金额(元)">
        ¥{{ formData.total_amount || 0 }}
      </el-descriptions-item>

      <el-descriptions-item label="报销类型">
        {{ getExpenseTypeText(formData.expense_type) }}
      </el-descriptions-item>

      <!-- 报销明细 -->
      <el-descriptions-item
        label="报销明细"
        :span="2"
        v-if="formData.items && formData.items.length > 0"
      >
        <el-table :data="formData.items" border size="default">
          <el-table-column prop="description" label="费用说明" width="400" />
        </el-table>
      </el-descriptions-item>

      <el-descriptions-item
        label="图片附件"
        :span="2"
        v-if="formData.attachment && formData.attachment.length > 0"
      >
        <div class="attachment-preview">
          <el-image
            v-for="(attachment, index) in formData.attachment"
            :key="index"
            :src="attachment"
            :preview-src-list="formData.attachment"
            :initial-index="index"
            fit="cover"
            class="attachment-image"
            preview-teleported
            :hide-on-click-modal="true"
          />
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        {{ formData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem, ElImage, ElLink, ElTable, ElTableColumn } from 'element-plus'
  import { getExpenseTypeText } from '@/constants/finance'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'finance_expense_reimbursement'
    }
  })

  /**
   * 格式化文件大小
   */
  const formatFileSize = (size: number): string => {
    if (!size) return '0B'
    const units = ['B', 'KB', 'MB', 'GB']
    let index = 0
    while (size >= 1024 && index < units.length - 1) {
      size /= 1024
      index++
    }
    return `${size.toFixed(1)}${units[index]}`
  }
</script>

<style scoped lang="scss">
  .attachment-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .attachment-image {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #dcdfe6;
  }
</style>
