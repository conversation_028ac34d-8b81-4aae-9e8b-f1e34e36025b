-- 系统消息通知模板初始化SQL（租户ID=0）
-- 创建日期：2023-09-01
-- 最后更新：2023-09-01

-- 清空现有数据（谨慎使用）
-- DELETE FROM `notice_template` WHERE `tenant_id` = 0;
-- DELETE FROM `notice_template_tenant_config` WHERE `tenant_id` = 0;

-- 插入系统默认消息模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `module_code`, `send_channels`, `mobile_template`, `status`, `creator_id`, `tenant_id`, `variables_config`)
VALUES 
('system_notice', '系统通知', '系统通知：${标题}', '尊敬的用户：\n${内容}\n如有疑问，请联系系统管理员。', '', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "通知标题"
    },
    {
      "name": "内容",
      "code": "content",
      "field": "content",
      "required": true,
      "description": "通知正文内容"
    }
  ]
}'),
('workflow_task_approval', '工作流任务审批通知', '您有一个待审批任务：${任务名称}', '您有一个新的待审批任务\n流程标题：${流程标题}\n当前环节：${任务名称}\n提交人：${提交人姓名}\n提交时间：${提交时间}\n请及时处理！', 'workflow', 'site,wework', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "任务名称",
      "code": "task_name",
      "field": "task.name",
      "required": true,
      "description": "当前任务名称"
    },
    {
      "name": "流程标题",
      "code": "process_title",
      "field": "process.title",
      "required": true,
      "description": "工作流标题"
    },
    {
      "name": "提交人姓名",
      "code": "submitter_name",
      "field": "process.creator.name",
      "required": true,
      "description": "流程提交人姓名"
    },
    {
      "name": "提交时间",
      "code": "submit_time",
      "field": "process.created_at",
      "required": true,
      "description": "流程提交时间"
    }
  ]
}'),
('workflow_task_approved', '工作流任务审批结果通知', '您的申请已审批完成：${流程标题}', '您的申请已审批完成\n流程标题：${流程标题}\n审批结果：${审批结果}\n审批意见：${审批意见}\n审批人：${审批人}\n审批时间：${审批时间}', 'workflow', 'site,email', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "流程标题",
      "code": "process_title",
      "field": "process.title",
      "required": true,
      "description": "工作流标题"
    },
    {
      "name": "审批结果",
      "code": "result",
      "field": "task.result",
      "required": true,
      "description": "审批结果：通过/拒绝"
    },
    {
      "name": "审批意见",
      "code": "opinion",
      "field": "task.opinion",
      "required": false,
      "description": "审批人填写的意见"
    },
    {
      "name": "审批人",
      "code": "approver_name",
      "field": "task.operator.name",
      "required": true,
      "description": "审批操作人姓名"
    },
    {
      "name": "审批时间",
      "code": "approve_time",
      "field": "task.completed_at",
      "required": true,
      "description": "审批完成时间"
    }
  ]
}'),
('workflow_task_urge', '工作流催办通知', '催办提醒：${流程标题}', '您有一个待办任务被催办\n流程标题：${流程标题}\n当前环节：${任务名称}\n催办人：${催办人}\n催办时间：${催办时间}\n催办原因：${催办原因}\n请尽快处理！', 'workflow', 'site,sms,wework', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "流程标题",
      "code": "process_title",
      "field": "process.title",
      "required": true,
      "description": "工作流标题"
    },
    {
      "name": "任务名称",
      "code": "task_name",
      "field": "task.name",
      "required": true,
      "description": "当前任务名称"
    },
    {
      "name": "催办人",
      "code": "urger_name",
      "field": "urger.name",
      "required": true,
      "description": "发起催办的人员姓名"
    },
    {
      "name": "催办时间",
      "code": "urge_time",
      "field": "urge.created_at",
      "required": true,
      "description": "催办发起时间"
    },
    {
      "name": "催办原因",
      "code": "urge_reason",
      "field": "urge.reason",
      "required": false,
      "description": "催办填写的原因"
    }
  ]
}'),
('workflow_task_transfer', '工作流转交任务通知', '您收到一个转交任务：${任务名称}', '您收到一个转交任务\n任务名称：${任务名称}\n转交人：${转交人}\n转交时间：${转交时间}\n转交原因：${转交原因}\n请及时处理！', 'workflow', 'site,wework', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "任务名称",
      "code": "task_name",
      "field": "task.name",
      "required": true,
      "description": "当前任务名称"
    },
    {
      "name": "转交人",
      "code": "transfer_user",
      "field": "transfer.user.name",
      "required": true,
      "description": "转交操作人姓名"
    },
    {
      "name": "转交时间",
      "code": "transfer_time",
      "field": "transfer.created_at",
      "required": true,
      "description": "转交操作时间"
    },
    {
      "name": "转交原因",
      "code": "transfer_reason",
      "field": "transfer.reason",
      "required": false,
      "description": "转交填写的原因"
    }
  ]
}'),

-- 审批模块
('approval_request', '审批申请通知', '您有一个新的审批申请：${审批事项}', '审批事项：${审批事项}\n申请人：${申请人}\n申请时间：${申请时间}\n请及时处理。', 'approval', 'site,email', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "审批事项",
      "code": "approval_item",
      "field": "approval.title",
      "required": true,
      "description": "审批的事项标题"
    },
    {
      "name": "申请人",
      "code": "applicant_name",
      "field": "approval.creator.name",
      "required": true,
      "description": "申请人姓名"
    },
    {
      "name": "申请时间",
      "code": "apply_time",
      "field": "approval.created_at",
      "required": true,
      "description": "申请提交时间"
    }
  ]
}'),
('approval_result', '审批结果通知', '您的审批申请已处理：${审批事项}', '审批事项：${审批事项}\n审批结果：${审批结果}\n审批人：${审批人}\n审批时间：${审批时间}', 'approval', 'site,email', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "审批事项",
      "code": "approval_item",
      "field": "approval.title",
      "required": true,
      "description": "审批的事项标题"
    },
    {
      "name": "审批结果",
      "code": "approval_result",
      "field": "approval.result",
      "required": true,
      "description": "审批结果：通过/拒绝"
    },
    {
      "name": "审批人",
      "code": "approver_name",
      "field": "approver.name",
      "required": true,
      "description": "审批人姓名"
    },
    {
      "name": "审批时间",
      "code": "approve_time",
      "field": "approval.updated_at",
      "required": true,
      "description": "审批操作时间"
    }
  ]
}'),

-- 假勤模块
('attendance_leave_apply', '请假申请通知', '请假申请：${请假类型}', '您有一个新的请假申请\n请假类型：${请假类型}\n申请人：${申请人}\n开始时间：${开始时间}\n结束时间：${结束时间}\n请及时审批。', 'attendance', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "请假类型",
      "code": "leave_type",
      "field": "leave.type_name",
      "required": true,
      "description": "请假类型名称"
    },
    {
      "name": "申请人",
      "code": "applicant_name",
      "field": "leave.creator.name",
      "required": true,
      "description": "请假申请人姓名"
    },
    {
      "name": "开始时间",
      "code": "start_time",
      "field": "leave.start_time",
      "required": true,
      "description": "请假开始时间"
    },
    {
      "name": "结束时间",
      "code": "end_time",
      "field": "leave.end_time",
      "required": true,
      "description": "请假结束时间"
    }
  ]
}'),
('attendance_leave_result', '请假审批结果通知', '请假审批结果：${请假类型}', '您的请假申请已审批\n请假类型：${请假类型}\n审批结果：${审批结果}\n审批人：${审批人}\n审批时间：${审批时间}', 'attendance', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "请假类型",
      "code": "leave_type",
      "field": "leave.type_name",
      "required": true,
      "description": "请假类型名称"
    },
    {
      "name": "审批结果",
      "code": "approval_result",
      "field": "leave.status_text",
      "required": true,
      "description": "审批结果：通过/拒绝"
    },
    {
      "name": "审批人",
      "code": "approver_name",
      "field": "approver.name",
      "required": true,
      "description": "审批人姓名"
    },
    {
      "name": "审批时间",
      "code": "approve_time",
      "field": "leave.updated_at",
      "required": true,
      "description": "审批操作时间"
    }
  ]
}'),

-- CRM模块
('crm_customer_create', 'CRM客户创建通知', '新客户创建：${客户名称}', '有新客户被创建\n客户名称：${客户名称}\n创建人：${创建人}\n创建时间：${创建时间}', 'crm', 'site,email', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "客户名称",
      "code": "customer_name",
      "field": "customer.name",
      "required": true,
      "description": "客户名称"
    },
    {
      "name": "创建人",
      "code": "creator_name",
      "field": "customer.creator.name",
      "required": true,
      "description": "创建客户的用户姓名"
    },
    {
      "name": "创建时间",
      "code": "create_time",
      "field": "customer.created_at",
      "required": true,
      "description": "客户创建时间"
    }
  ]
}'),
('crm_customer_follow', 'CRM客户跟进提醒', '客户跟进提醒：${客户名称}', '请及时跟进客户\n客户名称：${客户名称}\n跟进内容：${跟进内容}\n跟进时间：${跟进时间}', 'crm', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "客户名称",
      "code": "customer_name",
      "field": "customer.name",
      "required": true,
      "description": "客户名称"
    },
    {
      "name": "跟进内容",
      "code": "follow_content",
      "field": "follow.content",
      "required": true,
      "description": "需要跟进的内容"
    },
    {
      "name": "跟进时间",
      "code": "follow_time",
      "field": "follow.plan_time",
      "required": true,
      "description": "计划跟进时间"
    }
  ]
}'),

-- 进销存模块
('inventory_low_stock', '库存预警通知', '库存预警：${商品名称}', '商品库存低于预警值\n商品名称：${商品名称}\n当前库存：${当前库存}\n请及时补货。', 'inventory', 'site,email', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "商品名称",
      "code": "product_name",
      "field": "product.name",
      "required": true,
      "description": "商品名称"
    },
    {
      "name": "当前库存",
      "code": "current_stock",
      "field": "product.stock",
      "required": true,
      "description": "当前库存数量"
    }
  ]
}'),
('purchase_order_approval', '采购订单审批通知', '采购订单待审批：${订单编号}', '有新的采购订单待审批\n订单编号：${订单编号}\n申请人：${申请人}\n申请时间：${申请时间}', 'inventory', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "订单编号",
      "code": "order_no",
      "field": "order.order_no",
      "required": true,
      "description": "采购订单编号"
    },
    {
      "name": "申请人",
      "code": "applicant_name",
      "field": "order.creator.name",
      "required": true,
      "description": "订单创建人姓名"
    },
    {
      "name": "申请时间",
      "code": "apply_time",
      "field": "order.created_at",
      "required": true,
      "description": "订单创建时间"
    }
  ]
}'),

-- 财务模块
('finance_payment_notice', '付款通知', '付款通知：${付款事项}', '有新的付款需要处理\n付款事项：${付款事项}\n申请人：${申请人}\n申请时间：${申请时间}', 'finance', 'site,email', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "付款事项",
      "code": "payment_item",
      "field": "payment.title",
      "required": true,
      "description": "付款事项标题"
    },
    {
      "name": "申请人",
      "code": "applicant_name",
      "field": "payment.creator.name",
      "required": true,
      "description": "付款申请人姓名"
    },
    {
      "name": "申请时间",
      "code": "apply_time",
      "field": "payment.created_at",
      "required": true,
      "description": "付款申请时间"
    }
  ]
}'),
('finance_invoice_notice', '发票通知', '发票通知：${发票类型}', '有新的发票需要处理\n发票类型：${发票类型}\n申请人：${申请人}\n申请时间：${申请时间}', 'finance', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "发票类型",
      "code": "invoice_type",
      "field": "invoice.type_text",
      "required": true,
      "description": "发票类型"
    },
    {
      "name": "申请人",
      "code": "applicant_name",
      "field": "invoice.creator.name",
      "required": true,
      "description": "发票申请人姓名"
    },
    {
      "name": "申请时间",
      "code": "apply_time",
      "field": "invoice.created_at",
      "required": true,
      "description": "发票申请时间"
    }
  ]
}'),

-- 公告模块
('notice_announcement', '公告通知', '公告：${公告标题}', '有新的公告发布\n公告标题：${公告标题}\n发布时间：${发布时间}\n发布人：${发布人}', 'notice', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "公告标题",
      "code": "announcement_title",
      "field": "announcement.title",
      "required": true,
      "description": "公告标题"
    },
    {
      "name": "发布时间",
      "code": "publish_time",
      "field": "announcement.publish_time",
      "required": true,
      "description": "公告发布时间"
    },
    {
      "name": "发布人",
      "code": "publisher_name",
      "field": "announcement.publisher.name",
      "required": true,
      "description": "公告发布人姓名"
    }
  ]
}');

-- 为系统消息模板创建租户配置
-- 注意：需要先运行上面的模板插入语句，以获得正确的template_id
INSERT INTO `notice_template_tenant_config` (`tenant_id`, `template_id`, `template_code`, `is_enabled`, `site_enabled`, `email_enabled`, `sms_enabled`, `wework_enabled`, `dingtalk_enabled`, `webhook_enabled`, `creator_id`)
SELECT 
    0 as `tenant_id`,
    `id` as `template_id`,
    `code` as `template_code`,
    1 as `is_enabled`,
    CASE WHEN FIND_IN_SET('site', `send_channels`) > 0 THEN 1 ELSE 0 END as `site_enabled`,
    CASE WHEN FIND_IN_SET('email', `send_channels`) > 0 THEN 1 ELSE 0 END as `email_enabled`,
    CASE WHEN FIND_IN_SET('sms', `send_channels`) > 0 THEN 1 ELSE 0 END as `sms_enabled`,
    CASE WHEN FIND_IN_SET('wework', `send_channels`) > 0 THEN 1 ELSE 0 END as `wework_enabled`,
    CASE WHEN FIND_IN_SET('dingtalk', `send_channels`) > 0 THEN 1 ELSE 0 END as `dingtalk_enabled`,
    0 as `webhook_enabled`,
    1 as `creator_id`
FROM
    `notice_template`
WHERE
    `tenant_id` = 0; 

-- 工作流模块，添加workflow_approval_request审批申请通知模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `module_code`, `send_channels`, `mobile_template`, `status`, `creator_id`, `tenant_id`, `variables_config`)
VALUES 
('workflow_approval_request', '审批申请通知', '审批事项：${审批事项}', '审批事项：${审批事项}\n申请人：${申请人}\n申请时间：${申请时间}\n请及时处理。', 'workflow', 'site', NULL, 1, 1, 0, '{
  "variables": [
    {
      "name": "审批事项",
      "code": "approval_item",
      "field": "approval.title",
      "required": true,
      "description": "审批的事项标题"
    },
    {
      "name": "申请人",
      "code": "applicant_name",
      "field": "approval.creator.name",
      "required": true,
      "description": "申请人姓名"
    },
    {
      "name": "申请时间",
      "code": "apply_time",
      "field": "approval.created_at",
      "required": true,
      "description": "申请提交时间"
    }
  ]
}'); 