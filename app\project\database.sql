-- 项目任务管理模块数据库设计
-- 适用于100人以下企业的轻量级项目管理系统
-- 基于多租户架构，支持ThinkPHP8 CRUD生成器
-- 
-- 设计原则：
-- 1. 所有表支持多租户隔离（tenant_id字段）
-- 2. 所有表支持软删除（deleted_at字段）
-- 3. 统一审计字段（creator_id, updated_id, created_at, updated_at）
-- 4. 遵循现有数据库命名规范
-- 5. 优化索引设计，每表限制5个索引
--
-- 生成器使用说明：
-- 1. 执行SQL创建表结构
-- 2. 按顺序执行生成器命令：php think generator:crud 表名 --module=project --frontend --overwrite
-- 3. 在system_menu表中添加相应的菜单路由

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 项目表
-- ----------------------------
DROP TABLE IF EXISTS `project_project`;
CREATE TABLE `project_project` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '项目ID',
    `name` varchar(100) NOT NULL COMMENT '项目名称 | @s=like @e @exp @imp @val=required',
    `code` varchar(50) NOT NULL COMMENT '项目编码 | @s=eq @e @exp @imp @val=required',
    `description` text COMMENT '项目描述 | @e @c=textarea @exp @imp',
    `template_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '项目模板ID | @s=eq @e @exp @imp',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '项目状态：1计划中，2进行中，3已暂停，4已完成，5已取消 | @s=eq @e @c=select @exp @imp @val=required',
    `priority` tinyint(1) NOT NULL DEFAULT 2 COMMENT '优先级：1低，2中，3高，4紧急 | @s=eq @e @c=select @exp @imp',
    `start_date` date NULL COMMENT '开始日期 | @s=between @e @c=date @exp @imp',
    `end_date` date NULL COMMENT '结束日期 | @s=between @e @c=date @exp @imp',
    `actual_start_date` date NULL COMMENT '实际开始日期 | @s=between @e @c=date @exp @imp',
    `actual_end_date` date NULL COMMENT '实际结束日期 | @s=between @e @c=date @exp @imp',
    `progress` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '项目进度(%) | @e @exp @imp @val=number',
    `budget` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '项目预算 | @e @exp @imp @val=number',
    `actual_cost` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际成本 | @e @exp @imp @val=number',
    `owner_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '项目负责人ID | @s=eq @e @c=select @exp @imp @val=required',
    `department_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属部门ID | @s=eq @e @c=select @exp @imp',
    `is_public` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否公开：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `is_archived` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否归档：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `tags` varchar(500) NOT NULL DEFAULT '' COMMENT '项目标签 | @e @c=tags @exp @imp',
    `cover_image` text COMMENT '项目封面 | @e @c=upload @col=image',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_id`, `code`, `deleted_at`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_id`),
    KEY `idx_tenant_dept` (`tenant_id`, `department_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='项目表';

-- ----------------------------
-- 项目模板表
-- ----------------------------
DROP TABLE IF EXISTS `project_template`;
CREATE TABLE `project_template` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `name` varchar(100) NOT NULL COMMENT '模板名称 | @s=like @e @exp @imp @val=required',
    `code` varchar(50) NOT NULL COMMENT '模板编码 | @s=eq @e @exp @imp @val=required',
    `description` text COMMENT '模板描述 | @e @c=textarea @exp @imp',
    `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '模板分类：general通用，agile敏捷开发，marketing营销活动，operation运营项目 | @s=eq @e @c=select @exp @imp',
    `config` text COMMENT '模板配置JSON | @e @c=textarea @exp @imp',
    `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统模板：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序 | @e @exp @imp @val=number',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_id`, `code`, `deleted_at`),
    KEY `idx_tenant_category` (`tenant_id`, `category`),
    KEY `idx_tenant_active` (`tenant_id`, `is_active`),
    KEY `idx_sort` (`sort`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='项目模板表';

-- ----------------------------
-- 项目成员表
-- ----------------------------
DROP TABLE IF EXISTS `project_member`;
CREATE TABLE `project_member` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '成员ID',
    `project_id` bigint(20) UNSIGNED NOT NULL COMMENT '项目ID | @s=eq @e @exp @imp @val=required',
    `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID | @s=eq @e @c=select @exp @imp @val=required',
    `role` varchar(50) NOT NULL DEFAULT 'member' COMMENT '项目角色：owner负责人，manager管理员，developer开发者，tester测试员，designer设计师，member普通成员 | @s=eq @e @c=select @exp @imp',
    `permissions` varchar(500) NOT NULL DEFAULT '' COMMENT '权限列表JSON | @e @c=textarea @exp @imp',
    `joined_at` datetime NULL COMMENT '加入时间 | @s=between @e @c=datetime @exp @imp',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_project_user` (`project_id`, `user_id`, `deleted_at`),
    KEY `idx_tenant_project` (`tenant_id`, `project_id`),
    KEY `idx_tenant_user` (`tenant_id`, `user_id`),
    KEY `idx_tenant_role` (`tenant_id`, `role`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='项目成员表';

-- ----------------------------
-- 任务状态流转表
-- ----------------------------
DROP TABLE IF EXISTS `project_task_status`;
CREATE TABLE `project_task_status` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '状态ID',
    `name` varchar(50) NOT NULL COMMENT '状态名称 | @s=like @e @exp @imp @val=required',
    `code` varchar(50) NOT NULL COMMENT '状态编码 | @s=eq @e @exp @imp @val=required',
    `color` varchar(20) NOT NULL DEFAULT '#1664FF' COMMENT '状态颜色 | @e @c=color @exp @imp',
    `icon` varchar(50) NOT NULL DEFAULT '' COMMENT '状态图标 | @e @exp @imp',
    `type` varchar(20) NOT NULL DEFAULT 'todo' COMMENT '状态类型：todo待办，doing进行中，testing测试中，done已完成，closed已关闭 | @s=eq @e @c=select @exp @imp',
    `is_initial` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否初始状态：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `is_final` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否最终状态：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序 | @e @exp @imp @val=number',
    `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统状态：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_id`, `code`, `deleted_at`),
    KEY `idx_tenant_type` (`tenant_id`, `type`),
    KEY `idx_tenant_system` (`tenant_id`, `is_system`),
    KEY `idx_sort` (`sort`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='任务状态表';

-- ----------------------------
-- 任务表
-- ----------------------------
DROP TABLE IF EXISTS `project_task`;
CREATE TABLE `project_task` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `project_id` bigint(20) UNSIGNED NOT NULL COMMENT '项目ID | @s=eq @e @exp @imp @val=required',
    `parent_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父任务ID | @s=eq @e @exp @imp',
    `title` varchar(200) NOT NULL COMMENT '任务标题 | @s=like @e @exp @imp @val=required',
    `description` text COMMENT '任务描述 | @e @c=editor @exp @imp',
    `task_no` varchar(50) NOT NULL COMMENT '任务编号 | @s=like @e @exp @imp',
    `status_id` bigint(20) UNSIGNED NOT NULL COMMENT '状态ID | @s=eq @e @c=select @exp @imp @val=required',
    `priority` tinyint(1) NOT NULL DEFAULT 2 COMMENT '优先级：1低，2中，3高，4紧急 | @s=eq @e @c=select @exp @imp',
    `assignee_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID | @s=eq @e @c=select @exp @imp',
    `reporter_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '报告人ID | @s=eq @e @c=select @exp @imp',
    `estimated_hours` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '预估工时(小时) | @e @exp @imp @val=number',
    `actual_hours` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '实际工时(小时) | @e @exp @imp @val=number',
    `progress` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '任务进度(%) | @e @exp @imp @val=number',
    `start_date` date NULL COMMENT '开始日期 | @s=between @e @c=date @exp @imp',
    `due_date` date NULL COMMENT '截止日期 | @s=between @e @c=date @exp @imp',
    `completed_at` datetime NULL COMMENT '完成时间 | @s=between @e @c=datetime @exp @imp',
    `tags` varchar(500) NOT NULL DEFAULT '' COMMENT '任务标签 | @e @c=tags @exp @imp',
    `is_milestone` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否里程碑：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序 | @e @exp @imp @val=number',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_task_no` (`tenant_id`, `task_no`, `deleted_at`),
    KEY `idx_tenant_project` (`tenant_id`, `project_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status_id`),
    KEY `idx_tenant_assignee` (`tenant_id`, `assignee_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='任务表';

-- ----------------------------
-- 任务关联关系表
-- ----------------------------
DROP TABLE IF EXISTS `project_task_relation`;
CREATE TABLE `project_task_relation` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `source_task_id` bigint(20) UNSIGNED NOT NULL COMMENT '源任务ID | @s=eq @e @exp @imp @val=required',
    `target_task_id` bigint(20) UNSIGNED NOT NULL COMMENT '目标任务ID | @s=eq @e @exp @imp @val=required',
    `relation_type` varchar(20) NOT NULL DEFAULT 'blocks' COMMENT '关联类型：blocks阻塞，depends依赖，relates关联，duplicates重复 | @s=eq @e @c=select @exp @imp',
    `description` varchar(500) NOT NULL DEFAULT '' COMMENT '关联描述 | @e @c=textarea @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_source_target` (`source_task_id`, `target_task_id`, `relation_type`, `deleted_at`),
    KEY `idx_tenant_source` (`tenant_id`, `source_task_id`),
    KEY `idx_tenant_target` (`tenant_id`, `target_task_id`),
    KEY `idx_tenant_type` (`tenant_id`, `relation_type`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='任务关联关系表';

-- ----------------------------
-- 任务评论表
-- ----------------------------
DROP TABLE IF EXISTS `project_task_comment`;
CREATE TABLE `project_task_comment` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
    `task_id` bigint(20) UNSIGNED NOT NULL COMMENT '任务ID | @s=eq @e @exp @imp @val=required',
    `parent_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父评论ID | @s=eq @e @exp @imp',
    `content` text NOT NULL COMMENT '评论内容 | @e @c=editor @exp @imp @val=required',
    `mention_users` varchar(500) NOT NULL DEFAULT '' COMMENT '@提醒用户ID列表JSON | @e @c=textarea @exp @imp',
    `attachments` text COMMENT '附件列表JSON | @e @c=upload @col=files',
    `is_internal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否内部评论：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_task` (`tenant_id`, `task_id`),
    KEY `idx_tenant_parent` (`tenant_id`, `parent_id`),
    KEY `idx_tenant_creator` (`tenant_id`, `created_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='任务评论表';

-- ----------------------------
-- 任务操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `project_task_log`;
CREATE TABLE `project_task_log` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `task_id` bigint(20) UNSIGNED NOT NULL COMMENT '任务ID | @s=eq @e @exp @imp @val=required',
    `action` varchar(50) NOT NULL COMMENT '操作类型：created创建，updated更新，status_changed状态变更，assigned分配，commented评论 | @s=eq @e @c=select @exp @imp',
    `field_name` varchar(50) NOT NULL DEFAULT '' COMMENT '变更字段名 | @s=eq @e @exp @imp',
    `old_value` text COMMENT '原值 | @e @c=textarea @exp @imp',
    `new_value` text COMMENT '新值 | @e @c=textarea @exp @imp',
    `description` varchar(500) NOT NULL DEFAULT '' COMMENT '操作描述 | @e @c=textarea @exp @imp',
    `ip_address` varchar(45) NOT NULL DEFAULT '' COMMENT 'IP地址 | @s=eq @e @exp @imp',
    `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理 | @e @c=textarea @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_task` (`tenant_id`, `task_id`),
    KEY `idx_tenant_action` (`tenant_id`, `action`),
    KEY `idx_tenant_creator` (`tenant_id`, `created_id`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB COMMENT='任务操作日志表';

-- ----------------------------
-- 任务附件表
-- ----------------------------
DROP TABLE IF EXISTS `project_task_attachment`;
CREATE TABLE `project_task_attachment` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `task_id` bigint(20) UNSIGNED NOT NULL COMMENT '任务ID | @s=eq @e @exp @imp @val=required',
    `file_name` varchar(255) NOT NULL COMMENT '文件名 | @s=like @e @exp @imp @val=required',
    `file_path` varchar(500) NOT NULL COMMENT '文件路径 | @e @exp @imp @val=required',
    `file_size` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小(字节) | @e @exp @imp @val=number',
    `file_type` varchar(100) NOT NULL DEFAULT '' COMMENT '文件类型 | @s=eq @e @exp @imp',
    `mime_type` varchar(100) NOT NULL DEFAULT '' COMMENT 'MIME类型 | @s=eq @e @exp @imp',
    `download_count` int(11) NOT NULL DEFAULT 0 COMMENT '下载次数 | @e @exp @imp @val=number',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_task` (`tenant_id`, `task_id`),
    KEY `idx_tenant_type` (`tenant_id`, `file_type`),
    KEY `idx_tenant_creator` (`tenant_id`, `created_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='任务附件表';

-- ----------------------------
-- 工时记录表
-- ----------------------------
DROP TABLE IF EXISTS `project_time_log`;
CREATE TABLE `project_time_log` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '工时ID',
    `project_id` bigint(20) UNSIGNED NOT NULL COMMENT '项目ID | @s=eq @e @exp @imp @val=required',
    `task_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '任务ID | @s=eq @e @exp @imp',
    `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID | @s=eq @e @c=select @exp @imp @val=required',
    `work_date` date NOT NULL COMMENT '工作日期 | @s=between @e @c=date @exp @imp @val=required',
    `start_time` time NULL COMMENT '开始时间 | @e @c=time @exp @imp',
    `end_time` time NULL COMMENT '结束时间 | @e @c=time @exp @imp',
    `hours` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '工作时长(小时) | @e @exp @imp @val=number @val=required',
    `description` varchar(500) NOT NULL DEFAULT '' COMMENT '工作描述 | @e @c=textarea @exp @imp',
    `billable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否计费：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1待审核，2已审核，3已拒绝 | @s=eq @e @c=select @exp @imp',
    `approved_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核人ID | @s=eq @e @c=select @exp @imp',
    `approved_at` datetime NULL COMMENT '审核时间 | @s=between @e @c=datetime @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_project` (`tenant_id`, `project_id`),
    KEY `idx_tenant_task` (`tenant_id`, `task_id`),
    KEY `idx_tenant_user` (`tenant_id`, `user_id`),
    KEY `idx_tenant_date` (`tenant_id`, `work_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='工时记录表';

-- ----------------------------
-- 项目文档表
-- ----------------------------
DROP TABLE IF EXISTS `project_document`;
CREATE TABLE `project_document` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文档ID',
    `project_id` bigint(20) UNSIGNED NOT NULL COMMENT '项目ID | @s=eq @e @exp @imp @val=required',
    `parent_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父文档ID | @s=eq @e @exp @imp',
    `title` varchar(200) NOT NULL COMMENT '文档标题 | @s=like @e @exp @imp @val=required',
    `content` longtext COMMENT '文档内容 | @e @c=editor @exp @imp',
    `type` varchar(20) NOT NULL DEFAULT 'doc' COMMENT '文档类型：doc文档，requirement需求，design设计，api接口，other其他 | @s=eq @e @c=select @exp @imp',
    `format` varchar(20) NOT NULL DEFAULT 'markdown' COMMENT '文档格式：markdown，html，text | @s=eq @e @c=select @exp @imp',
    `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '文档版本 | @s=eq @e @exp @imp',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1草稿，2发布，3归档 | @s=eq @e @c=select @exp @imp',
    `is_public` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否公开：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `tags` varchar(500) NOT NULL DEFAULT '' COMMENT '文档标签 | @e @c=tags @exp @imp',
    `attachments` text COMMENT '附件列表JSON | @e @c=upload @col=files',
    `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '查看次数 | @e @exp @imp @val=number',
    `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序 | @e @exp @imp @val=number',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_project` (`tenant_id`, `project_id`),
    KEY `idx_tenant_type` (`tenant_id`, `type`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_parent` (`tenant_id`, `parent_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='项目文档表';

-- ----------------------------
-- 项目统计表
-- ----------------------------
DROP TABLE IF EXISTS `project_statistics`;
CREATE TABLE `project_statistics` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
    `project_id` bigint(20) UNSIGNED NOT NULL COMMENT '项目ID | @s=eq @e @exp @imp @val=required',
    `stat_date` date NOT NULL COMMENT '统计日期 | @s=between @e @c=date @exp @imp @val=required',
    `total_tasks` int(11) NOT NULL DEFAULT 0 COMMENT '总任务数 | @e @exp @imp @val=number',
    `completed_tasks` int(11) NOT NULL DEFAULT 0 COMMENT '已完成任务数 | @e @exp @imp @val=number',
    `in_progress_tasks` int(11) NOT NULL DEFAULT 0 COMMENT '进行中任务数 | @e @exp @imp @val=number',
    `overdue_tasks` int(11) NOT NULL DEFAULT 0 COMMENT '逾期任务数 | @e @exp @imp @val=number',
    `total_hours` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '总工时 | @e @exp @imp @val=number',
    `completed_hours` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '已完成工时 | @e @exp @imp @val=number',
    `active_members` int(11) NOT NULL DEFAULT 0 COMMENT '活跃成员数 | @e @exp @imp @val=number',
    `progress_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '进度完成率(%) | @e @exp @imp @val=number',
    `efficiency_score` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '效率评分 | @e @exp @imp @val=number',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_project_date` (`project_id`, `stat_date`),
    KEY `idx_tenant_project` (`tenant_id`, `project_id`),
    KEY `idx_tenant_date` (`tenant_id`, `stat_date`),
    KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB COMMENT='项目统计表';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- 插入系统默认项目模板
INSERT INTO `project_template` (`id`, `name`, `code`, `description`, `category`, `config`, `is_system`, `is_active`, `sort`, `tenant_id`, `created_id`, `updated_id`, `created_at`, `updated_at`) VALUES
(1, '敏捷开发项目', 'agile_dev', '适用于软件开发的敏捷项目管理模板，包含需求分析、设计、开发、测试、发布等阶段', 'agile', '{"phases":["需求分析","设计","开发","测试","发布"],"default_statuses":["待办","进行中","测试中","已完成"],"default_roles":["产品经理","开发工程师","测试工程师","UI设计师"]}', 1, 1, 1, 0, 0, 0, NOW(), NOW()),
(2, '营销活动项目', 'marketing_campaign', '适用于营销活动策划和执行的项目模板，包含策划、设计、执行、监控等阶段', 'marketing', '{"phases":["活动策划","创意设计","渠道准备","活动执行","效果监控"],"default_statuses":["待办","进行中","待审核","已完成"],"default_roles":["营销经理","设计师","文案","运营专员"]}', 1, 1, 2, 0, 0, 0, NOW(), NOW()),
(3, '运营项目', 'operation_project', '适用于日常运营工作的项目模板，包含计划、执行、监控、优化等阶段', 'operation', '{"phases":["需求分析","方案设计","资源准备","执行实施","效果评估"],"default_statuses":["待办","进行中","待确认","已完成"],"default_roles":["运营经理","运营专员","数据分析师","客服"]}', 1, 1, 3, 0, 0, 0, NOW(), NOW()),
(4, '通用项目', 'general_project', '通用项目管理模板，适用于各种类型的项目', 'general', '{"phases":["启动","计划","执行","监控","收尾"],"default_statuses":["待办","进行中","已完成","已关闭"],"default_roles":["项目经理","团队成员","相关方"]}', 1, 1, 4, 0, 0, 0, NOW(), NOW());

-- 插入系统默认任务状态
INSERT INTO `project_task_status` (`id`, `name`, `code`, `color`, `icon`, `type`, `is_initial`, `is_final`, `sort`, `is_system`, `tenant_id`, `created_id`, `updated_id`, `created_at`, `updated_at`) VALUES
(1, '待办', 'todo', '#8C8C8C', 'el-icon-time', 'todo', 1, 0, 1, 1, 0, 0, 0, NOW(), NOW()),
(2, '进行中', 'doing', '#1664FF', 'el-icon-loading', 'doing', 0, 0, 2, 1, 0, 0, 0, NOW(), NOW()),
(3, '测试中', 'testing', '#FF8800', 'el-icon-warning', 'testing', 0, 0, 3, 1, 0, 0, 0, NOW(), NOW()),
(4, '待审核', 'review', '#722ED1', 'el-icon-view', 'review', 0, 0, 4, 1, 0, 0, 0, NOW(), NOW()),
(5, '已完成', 'done', '#00BC70', 'el-icon-check', 'done', 0, 1, 5, 1, 0, 0, 0, NOW(), NOW()),
(6, '已关闭', 'closed', '#F54A45', 'el-icon-close', 'closed', 0, 1, 6, 1, 0, 0, 0, NOW(), NOW());

-- ----------------------------
-- 索引优化说明
-- ----------------------------
-- 1. 每个表的索引数量控制在5个以内，避免过多索引影响写入性能
-- 2. 优先为租户隔离、状态查询、关联查询创建复合索引
-- 3. 为软删除字段创建单独索引，提高查询效率
-- 4. 为时间字段创建索引，支持时间范围查询
-- 5. 为排序字段创建索引，提高列表查询性能

-- ----------------------------
-- 表关系说明
-- ----------------------------
-- 1. project_project (项目) 1:N project_task (任务)
-- 2. project_project (项目) 1:N project_member (成员)
-- 3. project_project (项目) 1:N project_document (文档)
-- 4. project_task (任务) 1:N project_task_comment (评论)
-- 5. project_task (任务) 1:N project_task_log (日志)
-- 6. project_task (任务) 1:N project_task_attachment (附件)
-- 7. project_task (任务) N:N project_task_relation (关联)
-- 8. project_task (任务) N:1 project_task_status (状态)
-- 9. project_project (项目) N:1 project_template (模板)

-- ----------------------------
-- 生成器命令执行顺序
-- ----------------------------
-- 1. php think generator:crud project_template --module=project --frontend --overwrite
-- 2. php think generator:crud project_task_status --module=project --frontend --overwrite
-- 3. php think generator:crud project_project --module=project --frontend --overwrite
-- 4. php think generator:crud project_member --module=project --frontend --overwrite
-- 5. php think generator:crud project_task --module=project --frontend --overwrite
-- 6. php think generator:crud project_task_relation --module=project --frontend --overwrite
-- 7. php think generator:crud project_task_comment --module=project --frontend --overwrite
-- 8. php think generator:crud project_task_log --module=project --frontend --overwrite
-- 9. php think generator:crud project_task_attachment --module=project --frontend --overwrite
-- 10. php think generator:crud project_time_log --module=project --frontend --overwrite
-- 11. php think generator:crud project_document --module=project --frontend --overwrite
-- 12. php think generator:crud project_statistics --module=project --frontend --overwrite

SET FOREIGN_KEY_CHECKS = 1;
