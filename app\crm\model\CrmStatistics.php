<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;

/**
 * 数据统计表模型
 */
class CrmStatistics extends BaseModel
{
    // 设置表名
    protected $name = 'crm_statistics';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段转换
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // 软删除
    protected string $deleteTime = 'deleted_at';

    public function __construct(array $data = [])
    {
        parent::__construct($data);
    }
}