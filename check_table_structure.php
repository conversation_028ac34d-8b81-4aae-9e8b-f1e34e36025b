<?php
/**
 * 检查数据库表结构
 */

// 数据库连接配置
$host = '*************';
$port = '3306';
$database = 'www_bs_com';
$username = 'www_bs_com';
$password = 'PdadjMXmNy8Pn9tj';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 检查表结构 ===\n\n";
    
    // 检查产品分类表结构
    echo "crm_product_category 表结构:\n";
    echo str_repeat("-", 60) . "\n";
    $stmt = $pdo->query("DESCRIBE crm_product_category");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        printf("%-20s %-15s %-8s %-8s %-10s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key'], 
            $column['Default'], 
            $column['Extra']
        );
    }
    
    echo "\n";
    
    // 检查产品单位表结构
    echo "crm_product_unit 表结构:\n";
    echo str_repeat("-", 60) . "\n";
    $stmt = $pdo->query("DESCRIBE crm_product_unit");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        printf("%-20s %-15s %-8s %-8s %-10s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key'], 
            $column['Default'], 
            $column['Extra']
        );
    }
    
    echo "\n";
    
    // 检查产品表结构
    echo "crm_product 表结构:\n";
    echo str_repeat("-", 60) . "\n";
    $stmt = $pdo->query("DESCRIBE crm_product");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        printf("%-20s %-15s %-8s %-8s %-10s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key'], 
            $column['Default'], 
            $column['Extra']
        );
    }
    
    echo "\n";
    
    // 查看现有数据
    echo "=== 现有数据示例 ===\n\n";
    
    echo "产品分类数据:\n";
    $stmt = $pdo->query("SELECT * FROM crm_product_category LIMIT 5");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (!empty($categories)) {
        $firstRow = $categories[0];
        echo "字段: " . implode(", ", array_keys($firstRow)) . "\n";
        foreach ($categories as $category) {
            echo "ID: {$category['id']}, 数据: " . json_encode($category, JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        echo "没有数据\n";
    }
    
    echo "\n产品单位数据:\n";
    $stmt = $pdo->query("SELECT * FROM crm_product_unit LIMIT 5");
    $units = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (!empty($units)) {
        $firstRow = $units[0];
        echo "字段: " . implode(", ", array_keys($firstRow)) . "\n";
        foreach ($units as $unit) {
            echo "ID: {$unit['id']}, 数据: " . json_encode($unit, JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        echo "没有数据\n";
    }
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "执行失败: " . $e->getMessage() . "\n";
    exit(1);
}
