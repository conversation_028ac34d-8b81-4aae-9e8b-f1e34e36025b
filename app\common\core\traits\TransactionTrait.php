<?php
declare(strict_types=1);

namespace app\common\core\traits;

use Closure;
use think\facade\Db;
use Throwable;

/**
 * 事务特性
 * 提供统一的事务处理功能
 */
trait TransactionTrait
{
    /**
     * 开启事务
     */
    protected function startTrans(): void
    {
        Db::startTrans();
    }
    
    /**
     * 提交事务
     */
    protected function commit(): void
    {
        Db::commit();
    }
    
    /**
     * 回滚事务
     */
    protected function rollback(): void
    {
        Db::rollback();
    }
    
    /**
     * 在事务中执行操作
     *
     * @param Closure $callback 回调函数
     * @return mixed 回调函数返回值
     * @throws Throwable 当回调函数抛出异常时
     */
    protected function transaction(Closure $callback)
    {
        try {
            Db::startTrans();
            $result = $callback();
            Db::commit();
            return $result;
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }
} 