---
description: 
globs: 
alwaysApply: false
---
{
  "name": "前端编码规范",
  "description": "Art Design Pro前端项目的编码规范",
  "glob": "frontend/**/*.{js,ts,vue,scss,css}"
}

# 前端编码规范

Art Design Pro前端项目采用Vue 3 + TypeScript技术栈，遵循以下编码规范。

## 命名规范

### 文件命名

- **Vue组件文件**: 使用大驼峰命名法(PascalCase)
  - `UserList.vue`
  - `LoginForm.vue`
  - `HeaderNavigation.vue`

- **TypeScript/JavaScript文件**: 使用小驼峰命名法(camelCase)
  - `userApi.ts`
  - `formatUtils.ts`
  - `dateHelper.ts`

- **样式文件**: 使用短横线命名法(kebab-case)
  - `main-layout.scss`
  - `theme-variables.scss`

### 代码命名

- **组件名**: 使用大驼峰命名法(PascalCase)
  ```vue
  <script setup lang="ts">
  defineOptions({
    name: 'UserList'
  })
  </script>
  ```

- **变量名**: 使用小驼峰命名法(camelCase)
  ```ts
  const userName = 'admin'
  const roleList = []
  ```

- **常量名**: 使用大写下划线命名法(SNAKE_CASE)
  ```ts
  const MAX_COUNT = 100
  const API_BASE_URL = '/api'
  ```

- **函数名**: 使用小驼峰命名法(camelCase)，动词开头
  ```ts
  function getUserInfo() {}
  function formatDate() {}
  ```

- **CSS类名**: 使用短横线命名法(kebab-case)
  ```css
  .header-container {}
  .menu-item-active {}
  ```

## 代码格式

- 缩进使用2个空格，不使用Tab
- 行宽不超过100个字符
- 使用单引号而不是双引号
- 不使用分号结尾
- 对象和数组的最后一项不加逗号
- 运算符两侧添加空格
- 函数名和括号之间不加空格

## Vue组件规范

### 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

// 选项
defineOptions({
  name: 'ComponentName'
})

// Props
const props = defineProps<{
  title: string
  list: Array<string>
}>()

// Emits
const emit = defineEmits<{
  (e: 'update', value: string): void
  (e: 'delete', id: number): void
}>()

// 响应式数据
const count = ref(0)

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 方法
function increment() {
  count.value++
}

// 生命周期钩子
onMounted(() => {
  console.log('Component mounted')
})
</script>

<style scoped lang="scss">
/* 样式内容 */
</style>
```

### Props定义

- 使用TypeScript类型定义Props
- 为所有Props提供默认值或标记为必填

```ts
// 方式1：使用defineProps
const props = defineProps<{
  title: string
  list?: Array<string>
}>()

// 方式2：使用withDefaults
const props = withDefaults(defineProps<{
  title: string
  list?: Array<string>
}>(), {
  list: () => []
})
```

### 事件处理

- 事件处理函数使用on前缀
- 使用defineEmits定义组件事件

```ts
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

function onInputChange(e: Event) {
  const value = (e.target as HTMLInputElement).value
  emit('update:modelValue', value)
  emit('change', value)
}
```

## TypeScript规范

- 为所有变量和函数参数添加类型注解
- 使用接口(interface)定义对象类型
- 使用类型别名(type)定义联合类型或交叉类型
- 避免使用any类型，优先使用unknown或具体类型

```ts
// 接口定义
interface User {
  id: number
  name: string
  role: string
  permissions?: string[]
}

// 类型别名
type Status = 'active' | 'inactive' | 'pending'

// 函数类型
function fetchUserData(id: number): Promise<User> {
  // 实现
}
```

## API请求规范

- 所有API请求函数放在`src/api`目录下
- API请求函数使用动词开头，表明操作类型
- 使用统一的请求工具函数
- 处理请求错误和异常

```ts
// src/api/user.ts
import request from '@/utils/http'

export function getUserList(params: UserQueryParams) {  
  return request.get<PaginationResult<any[]>>({
      url: '/api/users',
      params
    })
}

export function add(data: UserCreateData) {  
  return request.post<BaseResult>({
      url: `/api/users/add`,
      data
    })
}
```

## 状态管理规范

- 使用Pinia进行状态管理
- 按模块划分store
- 使用选项式API或组合式API定义store

```ts
// src/stores/user.ts
import { defineStore } from 'pinia'
import { getUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: localStorage.getItem('token') || '',
    permissions: []
  }),
  getters: {
    isLogin: (state) => !!state.token,
    hasPermission: (state) => (permission) => state.permissions.includes(permission)
  },
  actions: {
    async login(username, password) {
      // 实现登录逻辑
    },
    async getUserInfo() {
      // 获取用户信息
    },
    logout() {
      // 退出登录
    }
  }
})
```

## 路由规范

- 路由配置按模块划分
- 使用路由元信息(meta)定义路由属性
- 异步加载路由组件，提高首屏加载速度

```ts
// src/router/modules/system.ts
export default {
  path: '/system',
  component: () => import('@/layouts/MainLayout.vue'),
  meta: {
    title: '系统管理',
    icon: 'setting',
    permission: 'system'
  },
  children: [
    {
      path: 'users',
      name: 'SystemUsers',
      component: () => import('@/views/system/users/index.vue'),
      meta: {
        title: '用户管理',
        permission: 'system:user'
      }
    },
    {
      path: 'roles',
      name: 'SystemRoles',
      component: () => import('@/views/system/roles/index.vue'),
      meta: {
        title: '角色管理',
        permission: 'system:role'
      }
    }
  ]
}
```

## 样式规范

- 使用SCSS预处理器
- 组件样式使用scoped属性
- 全局样式放在`src/assets/styles`目录下
- 使用变量定义主题色和常用样式
- 遵循BEM命名规范

```scss
// 组件样式
.header {
  &__logo {
    width: 200px;
  }
  
  &__menu {
    display: flex;
    
    &--active {
      color: var(--el-color-primary);
    }
  }
}
```

