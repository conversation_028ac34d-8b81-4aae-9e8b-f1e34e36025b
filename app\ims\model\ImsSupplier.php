<?php
declare(strict_types=1);

namespace app\ims\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\crm\model\CrmProduct;
use think\model\relation\HasMany;

/**
 * 供应商表模型
 */
class ImsSupplier extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'ims_supplier';
	
	protected $append = [
		'creator',
		'product_count'
	];

	public function getDefaultSearchFields(): array
	{
		return [
			'name'   => ['type' => 'eq'],
			'contact_name'    => ['type' => 'eq'],
		];
	}

	/**
	 * 关联产品
	 */
	public function products(): HasMany
	{
		return $this->hasMany(CrmProduct::class, 'supplier_id', 'id');
	}

	/**
	 * 检查是否可以删除（有关联产品时不能删除）
	 */
	public function canDelete(): bool
	{
		return $this->products()->count() === 0;
	}

	/**
	 * 获取关联产品数量
	 */
	public function getProductCountAttr(): int
	{
		return $this->products()->count();
	}
}