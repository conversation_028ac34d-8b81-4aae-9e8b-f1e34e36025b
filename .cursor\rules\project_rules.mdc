---
description: 
globs: 
alwaysApply: true
---
{
  "name": "项目规则",
  "description": "多租户前后端分离框架系统的项目规则",
  "glob": "**/*"
}

# 多租户前后端分离框架系统

本项目是一个基于ThinkPHP 8和Vue 3的多租户前后端分离框架系统，支持RBAC权限管理、动态表单、工作流等功能。

## 项目架构

### 后端架构

- 框架：ThinkPHP 8
- PHP版本：8.2
- 数据库：MySQL
- 缓存：Redis

### 前端架构

- 框架：Vue 3
- 语言：TypeScript
- 构建工具：Vite
- UI库：Element Plus
- 状态管理：Pinia
- 路由：Vue Router
- CSS预处理器：SCSS

## 目录结构

### 后端目录结构

```
├── app                 # 应用目录
│   ├── controller      # 控制器目录
│   ├── model           # 模型目录
│   ├── service         # 服务层目录
│   ├── validate        # 验证器目录
│   ├── middleware      # 中间件目录
│   └── common          # 公共函数目录
├── config              # 配置文件目录
├── public              # 公共资源目录
├── runtime             # 运行时目录
└── vendor              # 第三方依赖目录
```

### 前端目录结构

```
frontend/art-design-pro/
├── src                 # 源代码目录
│   ├── api             # API接口
│   ├── assets          # 静态资源
│   ├── components      # 公共组件
│   ├── composables     # 组合式函数
│   ├── config          # 配置文件
│   ├── directives      # 自定义指令
│   ├── enums           # 枚举定义
│   ├── language        # 多语言配置
│   ├── plugins         # 插件
│   ├── router          # 路由配置
│   ├── store           # 状态管理
│   ├── stores          # Pinia存储
│   ├── types           # 类型定义
│   ├── utils           # 工具函数
│   └── views           # 页面视图
├── public              # 公共资源
└── node_modules        # 依赖包
```

## 命名规范

### 后端命名规范

- **控制器**：PascalCase，如`AdminController`
- **模型**：PascalCase，如`AdminModel`
- **服务**：PascalCase，如`AdminService`
- **表名**：snake_case，如`system_admin`
- **类**：PascalCase
- **方法**：camelCase
- **变量**：camelCase

### 前端命名规范

- **组件**：PascalCase，如`UserList.vue`
- **视图**：PascalCase，如`Dashboard.vue`
- **组合式函数**：camelCase，如`useUserStore`
- **工具函数**：camelCase，如`formatDate`
- **存储**：camelCase，如`userStore`
- **CSS类名**：kebab-case，如`header-container`

## 编码规范

### 后端编码规范

- 缩进：4个空格
- 文档注释风格：phpdoc
- 行长度限制：120

### 前端编码规范

- 缩进：2个空格
- 行长度限制：100
- 代码检查工具：ESLint, Prettier, Stylelint
- 提交规范：cz-git

## 前端特性

- 多主题切换
- 全局搜索
- 锁屏功能
- 多标签页
- 全局面包屑
- 多语言支持
- 图标库
- 富文本编辑器
- Echarts图表
- 工具包
- 网络异常处理
- 权限管理
- 移动端适配

## 后端模块

- 管理员管理
- 角色管理
- 菜单管理
- 部门管理
- 岗位管理
- 租户管理
- 日志管理
- 附件管理
- 通知管理
- 消息管理
- 字典管理

## 数据库规范

- 表前缀：system_
- 字段命名：snake_case
- 通用字段：id, created_at, updated_at, deleted_at, tenant_id
