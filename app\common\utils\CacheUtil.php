<?php
declare(strict_types=1);

namespace app\common\utils;

use app\common\core\constants\CacheConstant;
use think\facade\Cache;

/**
 * 缓存工具类
 * 提供统一的缓存操作接口，支持多租户隔离、自动过期时间设置
 */
class CacheUtil
{
	/**
	 * 租户缓存前缀
	 */
	private const TENANT_PREFIX = CacheConstant::TENANT_PREFIX;
	
	/**
	 * 总后台缓存前缀
	 */
	private const ADMIN_PREFIX = CacheConstant::ADMIN_PREFIX;
	
	/**
	 * 默认过期时间（秒）
	 */
	private const DEFAULT_EXPIRE = 3600;
	
	/**
	 * 获取租户ID
	 *
	 * @return int|null 租户ID，如果是总后台则返回null
	 */
	private static function getTenantId(): ?int
	{
		try {
			// 优先使用权限函数（考虑租户切换）
			if (function_exists('get_effective_tenant_id')) {
				return get_effective_tenant_id();
			}

			// 降级方案：直接从request获取
			$tenantId = request()->tenantId ?? null;
			return $tenantId !== null ? (int)$tenantId : null;

		} catch (\Exception $e) {
			\think\facade\Log::error('获取租户ID失败: ' . $e->getMessage());
			return null;
		}
	}
	
	/**
	 * 生成缓存键
	 *
	 * @param string   $key      原始缓存键
	 * @param bool     $isTenant 是否是租户缓存
	 * @param int|null $tenantId 指定租户ID，如果为null则自动获取
	 * @return string 生成的完整缓存键
	 */
	private static function generateKey(string $key, bool $isTenant = true, ?int $tenantId = null): string
	{
		if ($isTenant) {
			// 获取租户ID，如果没有指定则自动获取
			$currentTenantId = $tenantId ?? self::getTenantId();
			if ($currentTenantId) {
				return self::TENANT_PREFIX . $currentTenantId . ':' . $key;
			}
		}
		
		// 非租户或未获取到租户ID，使用总后台前缀
		return self::ADMIN_PREFIX . $key;
	}
	
	/**
	 * 设置缓存
	 *
	 * @param string   $key      缓存键
	 * @param mixed    $value    缓存值
	 * @param int|null $expire   过期时间（秒），null表示使用默认过期时间
	 * @param bool     $isTenant 是否为租户缓存
	 * @param int|null $tenantId 指定租户ID，如果为null则自动获取
	 * @return bool 设置是否成功
	 */
	public static function set(
		string $key, mixed $value, ?int $expire = null, bool $isTenant = true, ?int $tenantId = null
	): bool
	{
		$cacheKey = self::generateKey($key, $isTenant, $tenantId);
		return Cache::set($cacheKey, $value, $expire ?? self::DEFAULT_EXPIRE);
	}
	
	/**
	 * 获取缓存
	 *
	 * @param string     $key      缓存键
	 * @param mixed|null $default  默认值
	 * @param bool       $isTenant 是否为租户缓存
	 * @param int|null   $tenantId 指定租户ID，如果为null则自动获取
	 * @return mixed 缓存值或默认值
	 */
	public static function get(string $key, mixed $default = null, bool $isTenant = true, ?int $tenantId = null): mixed
	{
		$cacheKey = self::generateKey($key, $isTenant, $tenantId);
		return Cache::get($cacheKey, $default);
	}
	
	/**
	 * 删除缓存
	 *
	 * @param string   $key      缓存键
	 * @param bool     $isTenant 是否为租户缓存
	 * @param int|null $tenantId 指定租户ID，如果为null则自动获取
	 * @return bool 删除是否成功
	 */
	public static function delete(string $key, bool $isTenant = true, ?int $tenantId = null): bool
	{
		$cacheKey = self::generateKey($key, $isTenant, $tenantId);
		return Cache::delete($cacheKey);
	}
	
	/**
	 * 清除指定租户的所有缓存
	 *
	 * @param int|null $tenantId 租户ID，为null则清除当前租户缓存
	 * @return bool 清除是否成功
	 */
	public static function clearTenantCache(?int $tenantId = null): bool
	{
		$tenantId = $tenantId ?? self::getTenantId();
		if (!$tenantId) {
			return false;
		}

		try {
			// 使用Redis SCAN命令进行模式匹配删除
			$redis = Cache::store('redis')->handler();
			$pattern = self::TENANT_PREFIX . $tenantId . ':*';
			$iterator = null;
			$deletedCount = 0;

			do {
				$keys = $redis->scan($iterator, ['MATCH' => $pattern, 'COUNT' => 100]);
				if (!empty($keys)) {
					$redis->del($keys);
					$deletedCount += count($keys);
				}
			} while ($iterator > 0);

			\think\facade\Log::info('清理租户缓存完成', [
				'tenant_id' => $tenantId,
				'deleted_keys' => $deletedCount
			]);

			return true;
		} catch (\Exception $e) {
			\think\facade\Log::error('清理租户缓存失败: ' . $e->getMessage(), [
				'tenant_id' => $tenantId,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 清除总后台缓存
	 *
	 * @return bool 清除是否成功
	 */
	public static function clearAdminCache(): bool
	{
		$pattern = self::ADMIN_PREFIX . '*';
		return Cache::tag($pattern)
		            ->clear();
	}
	
	/**
	 * 缓存标记
	 *
	 * @param string   $tag      标签名
	 * @param bool     $isTenant 是否为租户缓存
	 * @param int|null $tenantId 指定租户ID，如果为null则自动获取
	 * @return \think\cache\TagSet
	 */
	public static function tag(string $tag, bool $isTenant = true, ?int $tenantId = null)
	{
		$tagKey = self::generateKey($tag, $isTenant, $tenantId);
		return Cache::tag($tagKey);
	}
	
	/**
	 * 缓存是否存在
	 *
	 * @param string   $key      缓存键
	 * @param bool     $isTenant 是否为租户缓存
	 * @param int|null $tenantId 指定租户ID，如果为null则自动获取
	 * @return bool 缓存是否存在
	 */
	public static function has(string $key, bool $isTenant = true, ?int $tenantId = null): bool
	{
		$cacheKey = self::generateKey($key, $isTenant, $tenantId);
		return Cache::has($cacheKey);
	}
	
	/**
	 * 设置缓存（永久）
	 *
	 * @param string   $key      缓存键
	 * @param mixed    $value    缓存值
	 * @param bool     $isTenant 是否为租户缓存
	 * @param int|null $tenantId 指定租户ID，如果为null则自动获取
	 * @return bool 设置是否成功
	 */
	public static function forever(string $key, mixed $value, bool $isTenant = true, ?int $tenantId = null): bool
	{
		$cacheKey = self::generateKey($key, $isTenant, $tenantId);
		return Cache::set($cacheKey, $value, 0);
	}
	
	/**
	 * 获取并删除缓存
	 *
	 * @param string     $key      缓存键
	 * @param mixed|null $default  默认值
	 * @param bool       $isTenant 是否为租户缓存
	 * @param int|null   $tenantId 指定租户ID，如果为null则自动获取
	 * @return mixed 缓存值或默认值
	 */
	public static function pull(string $key, mixed $default = null, bool $isTenant = true, ?int $tenantId = null): mixed
	{
		$cacheKey = self::generateKey($key, $isTenant, $tenantId);
		return Cache::pull($cacheKey, $default);
	}
	
	/**
	 * 如果不存在则写入缓存
	 *
	 * @param string   $key      缓存键
	 * @param mixed    $value    缓存值
	 * @param int|null $expire   过期时间（秒），null表示使用默认过期时间
	 * @param bool     $isTenant 是否为租户缓存
	 * @param int|null $tenantId 指定租户ID，如果为null则自动获取
	 * @return bool 设置是否成功
	 */
	public static function remember(
		string $key, mixed $value, ?int $expire = null, bool $isTenant = true, ?int $tenantId = null
	)
	{
		$cacheKey = self::generateKey($key, $isTenant, $tenantId);
		return Cache::remember($cacheKey, $value, $expire ?? self::DEFAULT_EXPIRE);
	}
} 