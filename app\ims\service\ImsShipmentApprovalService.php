<?php
declare(strict_types=1);

namespace app\ims\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\common\utils\NumberConverter;
use app\crm\model\CrmProduct;
use app\ims\model\ImsShipmentApproval;
use app\ims\model\ImsShipmentItem;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use think\facade\Log;

/**
 * 出货申请服务类
 */
class ImsShipmentApprovalService extends BaseService implements FormServiceInterface
{
	protected string $modelClass = ImsShipmentApproval::class;
	
	public function __construct()
	{
		$this->model = new ImsShipmentApproval();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = $this->model->findOrEmpty($id);
		
		if ($model->isEmpty()) {
			throw new BusinessException('出货申请记录不存在');
		}
		
		return $model->toArray();
	}
	
	/**
	 * 创建表单数据
	 */
	public function saveForm(array $data): array
	{
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			// 验证数据
			$validatedData = $this->validateFormData($formData, 'create');
			
			// 创建主记录
			$id = $this->model->saveByCreate($validatedData);
			
			// 处理明细数据
			if (!empty($formData['items'])) {
				$this->saveItems($id, $formData['items']);
			}
			
			// 返回完整数据
			$formData = $this->getFormData($id);
			
			return [
				$id,
				$formData
			];
			
		}
		catch (\Exception $e) {
			Log::error('出货申请创建失败: ' . $e->getMessage(), [
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('出货申请创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				throw new BusinessException('出货申请记录不存在');
			}
			
			// 验证数据
			$validatedData = $this->validateFormData($data, 'update');
			
			// 更新主记录
			$result = $model->saveByUpdate($validatedData);
			
			// 处理明细数据
			if (isset($data['items']) && is_array($data['items'])) {
				$this->saveItems($id, $data['items']);
			}
			
			return $result;
			
		}
		catch (\Exception $e) {
			Log::error('出货申请更新失败: ' . $e->getMessage(), [
				'id'    => $id,
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('出货申请更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			$updateData = ['approval_status' => $status];
			
			// 处理额外数据
			if (!empty($extra['workflow_instance_id'])) {
				$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
			}
			
			if (!empty($extra['submit_time'])) {
				$updateData['submit_time'] = $extra['submit_time'];
			}
			
			if (!empty($extra['approval_time'])) {
				$updateData['approval_time'] = $extra['approval_time'];
			}
			
			return $model->saveByUpdate($updateData);
			
		}
		catch (\Exception $e) {
			Log::error('出货申请状态更新失败: ' . $e->getMessage(), [
				'id'     => $id,
				'status' => $status,
				'extra'  => $extra,
				'trace'  => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 删除表单
	 */
	public function deleteForm(int $id): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			// 软删除主记录
			$model->delete();
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('出货申请删除失败: ' . $e->getMessage(), [
				'id'    => $id,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 获取流程实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		if (is_array($formData)) {
			$totalAmount  = $formData['total_amount'] ?? 0;
			$customerName = $formData['customer_name'] ?? '';
			
			if ($totalAmount && $customerName) {
				return "出货申请-{$customerName}-{$totalAmount}元";
			}
			
			if ($totalAmount) {
				return "出货申请-{$totalAmount}元";
			}
		}
		
		return '出货申请';
	}
	
	/**
	 * 验证表单数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		$rules = [
			'dept_id'       => 'require',
			'shipment_date' => 'require|date',
			'customer_id'   => 'require',
		];
		
		$messages = [
			'dept_id.require'       => '请选择部门',
			'shipment_date.require' => '请选择出货日期',
			'shipment_date.date'    => '出货日期格式错误',
			'customer_id.require'   => '请选择客户',
		];
		
		$validate = validate($rules, $messages);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		// 验证明细项（至少需要一个）
		if (empty($data['items'])) {
			throw new BusinessException('至少添加一条明细');
		}
		
		if (count($data['items']) > 1) {
			throw new BusinessException('最多添加一条明细');
		}
		
		// 移除不需要的字段
		unset($data['items']); // 明细数据单独处理
		
		return $data;
	}
	
	/**
	 * 工作流状态变更后的处理
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		// TODO: Implement afterWorkflowStatusChange() method.
		return true;
	}
	
	/**
	 * 保存明细数据
	 */
	private function saveItems(int $shipmentId, array $items): void
	{
		// 先删除原有明细
		$list = ImsShipmentItem::where('shipment_id', $shipmentId)
		                       ->field('id')
		                       ->select();
		if (!$list->isEmpty()) {
			$list->delete();
		}
		
		$totalAmount   = 0;
		$totalQuantity = 0;
		
		// 保存新明细，至少需要有一项明细
		foreach ($items as $item) {
			// 验证必填字段
			$this->validateItemData($item);
			
			// 计算小计金额
			$quantity        = floatval($item['quantity'] ?? 0);
			$unitPrice       = floatval($item['unit_price'] ?? 0);
			$totalAmountItem = NumberConverter::safeMultiply($quantity, $unitPrice);
			
			$productInfo = CrmProduct::where('id', $item['product_id'])
			                         ->where('supplier_id', $item['supplier_id'])
			                         ->findOrEmpty();
			
			if ($productInfo->isEmpty()) {
				throw new BusinessException('产品信息不存在');
			}
			
			// 准备保存数据
			$unitInfo = $productInfo->unit;
			
			// 准备保存数据
			$itemData = [
				'shipment_id'  => $shipmentId,
				'supplier_id'  => $item['supplier_id'],
				'product_id'   => $item['product_id'],
				'quantity'     => $quantity,
				'unit_price'   => $unitPrice,
				'product_unit' => $unitInfo['unit_name'],
				'total_amount' => $totalAmountItem
			];
			
			$itemModel = new ImsShipmentItem();
			$itemModel->saveByCreate($itemData);
			
			// 累计总金额和总数量
			$totalAmount   = NumberConverter::safeAdd($totalAmount, $totalAmountItem);
			$totalQuantity = NumberConverter::safeAdd($totalQuantity, $quantity);
		}
		
		// 更新主表的总金额、总数量和大写金额
		$this->updateTotalAmounts($shipmentId, $totalAmount, $totalQuantity);
	}
	
	/**
	 * 验证明细项数据
	 */
	private function validateItemData(array $item): void
	{
		// 验证供应商ID
		if (empty($item['supplier_id'])) {
			throw new BusinessException('请选择供应商');
		}
		
		// 验证产品ID
		if (empty($item['product_id'])) {
			throw new BusinessException('请选择产品');
		}
		
		// 验证出货数量
		if (empty($item['quantity']) || floatval($item['quantity']) <= 0) {
			throw new BusinessException('出货数量必须大于0');
		}
		
		// 验证单价
		if (empty($item['unit_price']) || floatval($item['unit_price']) <= 0) {
			throw new BusinessException('单价必须大于0');
		}
	}
	
	/**
	 * 更新总金额、总数量和大写金额
	 */
	private function updateTotalAmounts(int $shipmentId, float $totalAmount, float $totalQuantity): void
	{
		$updateData = [
			'total_amount'       => $totalAmount,
			'total_quantity'     => $totalQuantity,
			'total_amount_upper' => NumberConverter::convertToChineseNumber($totalAmount),
		];
		
		$this->model->where('id', $shipmentId)
		            ->update($updateData);
	}
}
