<script setup lang="ts">
import { ref, reactive, defineExpose, defineEmits } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import { {{EntityName}}Api } from '@/api/{{moduleName}}/{{entityName}}'
import { ApiStatus } from '@/utils/http/status'
{{customImports}}

const emit = defineEmits(['success'])

// 对话框状态
const dialogVisible = ref(false)
const dialogType = ref('add') // add或edit
const loading = ref(false)

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
{{#fields}}
  {{name}}: {{defaultValue}}, // {{comment}}
{{/fields}}
})

// 表单验证规则
const rules = {
{{#formRules}}
  {{name}}: [
    {{#required}}{ required: true, message: '请输入{{label}}', trigger: 'blur' },{{/required}}
    {{#rules}}
    { {{rule}} },
    {{/rules}}
  ],
{{/formRules}}
}

// 显示对话框
const showDialog = async (type: string, id?: number) => {
  dialogType.value = type
  dialogVisible.value = true

  // 重置表单数据
  Object.assign(formData, {
  {{#fields}}
    {{name}}: {{defaultValue}},
  {{/fields}}
  })

  // 编辑模式下获取详情数据
  if (type === 'edit' && id) {
    try {
      loading.value = true
      const res = await {{EntityName}}Api.detail(id)
      if (res.code === ApiStatus.success) {
        // 处理数据类型转换
        const data = { ...res.data }
        {{#dataConversions}}
        {{{conversion}}}
        {{/dataConversions}}

        Object.assign(formData, data)
      } else {
        ElMessage.error(res.message || '获取详情失败')
      }
    } finally {
      loading.value = false
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 处理提交数据转换
        const submitData = { ...formData }
        {{#submitConversions}}
        {{{conversion}}}
        {{/submitConversions}}

        let res

        if (dialogType.value === 'add') {
          res = await {{EntityName}}Api.add(submitData)
        } else {
          res = await {{EntityName}}Api.update(submitData)
        }

        if (res.code === ApiStatus.success) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          dialogVisible.value = false
          emit('success')
        } else {
          ElMessage.error(res.message || '操作失败')
        }
      } finally {
        loading.value = false
      }
    }
  })
}

// 暴露方法给父组件
defineExpose({
  showDialog
})
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '新增{{entityTitle}}' : '编辑{{entityTitle}}'"
    width="800px"
    top="5vh"
    destroy-on-close
  >
    <div class="dialog-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
      {{#formItems}}
      <ElFormItem label="{{label}}" prop="{{prop}}">
        {{#isInput}}
        <ElInput v-model="formData.{{prop}}" placeholder="请输入{{label}}" />
        {{/isInput}}
        {{#isTextarea}}
        <ElInput
          v-model="formData.{{prop}}"
          type="textarea"
          :rows="3"
          placeholder="请输入{{label}}"
        />
        {{/isTextarea}}
        {{#isNumber}}
        <ElInputNumber
          v-model="formData.{{prop}}"
          controls-position="right"
          style="width: 100%"
          placeholder="请输入{{label}}"
        />
        {{/isNumber}}
        {{#isSelect}}
        <ElSelect v-model="formData.{{prop}}" placeholder="请选择{{label}}" style="width: 100%">
          {{#options}}
          <ElOption label="{{label}}" value="{{value}}" />
          {{/options}}
        </ElSelect>
        {{/isSelect}}
        {{#isRadio}}
        <ElRadioGroup v-model="formData.{{prop}}">
          {{#options}}
          <ElRadio :label="{{value}}">{{label}}</ElRadio>
          {{/options}}
        </ElRadioGroup>
        {{/isRadio}}
        {{#isCheckbox}}
        <ElCheckboxGroup v-model="formData.{{prop}}">
          {{#options}}
          <ElCheckbox :value="{{value}}">{{label}}</ElCheckbox>
          {{/options}}
        </ElCheckboxGroup>
        {{/isCheckbox}}
        {{#isSwitch}}
        <ElSwitch
          v-model="formData.{{prop}}"
          :active-value="{{activeValue}}"
          :inactive-value="{{inactiveValue}}"
        />
        {{/isSwitch}}
        {{#isDatePicker}}
        <ElDatePicker
          v-model="formData.{{prop}}"
          type="{{dateType}}"
          style="width: 100%"
          placeholder="请选择{{label}}"
          value-format="{{valueFormat}}"
        />
        {{/isDatePicker}}
        {{#isTimePicker}}
        <ElTimePicker
          v-model="formData.{{prop}}"
          style="width: 100%"
          placeholder="请选择{{label}}"
        />
        {{/isTimePicker}}
        {{#isUpload}}
        <!-- 上传组件 -->
        {{#isFile}}
        <FormUploader
          v-model="formData.{{prop}}"
          fileType="file"
          :limit="{{#isMultiple}}5{{/isMultiple}}{{^isMultiple}}1{{/isMultiple}}"
          {{#isMultiple}}:multiple="true"{{/isMultiple}}
          buttonText="上传{{label}}"
        />
        {{/isFile}}
        {{#isImage}}
        <FormUploader
          v-model="formData.{{prop}}"
          fileType="image"
          :limit="{{#isMultiple}}5{{/isMultiple}}{{^isMultiple}}1{{/isMultiple}}"
          {{#isMultiple}}:multiple="true"{{/isMultiple}}
          buttonText="上传{{label}}"
          :useMediaSelector="true"
        />
        {{/isImage}}
        {{#isVideo}}
        <FormUploader
          v-model="formData.{{prop}}"
          fileType="video"
          :limit="{{#isMultiple}}3{{/isMultiple}}{{^isMultiple}}1{{/isMultiple}}"
          {{#isMultiple}}:multiple="true"{{/isMultiple}}
          buttonText="上传{{label}}"
          :useMediaSelector="true"
        />
        {{/isVideo}}
        {{#isAudio}}
        <FormUploader
          v-model="formData.{{prop}}"
          fileType="audio"
          :limit="{{#isMultiple}}3{{/isMultiple}}{{^isMultiple}}1{{/isMultiple}}"
          {{#isMultiple}}:multiple="true"{{/isMultiple}}
          buttonText="上传{{label}}"
          :useMediaSelector="true"
        />
        {{/isAudio}}
        {{/isUpload}}
        {{#isColorPicker}}
        <ElColorPicker v-model="formData.{{prop}}" />
        {{/isColorPicker}}
        {{#isCustom}}
        {{customTemplate}}
        {{/isCustom}}
      </ElFormItem>
      {{/formItems}}
      </ElForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitForm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.dialog-content {
  max-height: calc(90vh - 200px);
  overflow-y: auto;
  padding: 0 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  border-top: 1px solid #e4e7ed;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.avatar-uploader .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  line-height: 100px;
}
</style>