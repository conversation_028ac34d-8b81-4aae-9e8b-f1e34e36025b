<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="采购类型" prop="procurement_type">
              <ElSelect
                v-model="formData.procurement_type"
                placeholder="请选择采购类型"
                :disabled="!isEditable"
                clearable
              >
                <ElOption
                  v-for="item in procurementTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="交付日期" prop="delivery_date">
              <ElDatePicker
                v-model="formData.delivery_date"
                type="date"
                placeholder="请选择交付日期"
                :disabled="!isEditable"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="物品名称" prop="item_name">
              <ElInput
                v-model="formData.item_name"
                placeholder="请输入物品名称"
                :disabled="!isEditable"
                maxlength="200"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="采购来源单位" prop="supplier_name">
              <ElInput
                v-model="formData.supplier_name"
                placeholder="请输入采购来源单位名称"
                :disabled="!isEditable"
                maxlength="200"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="8">
            <ElFormItem label="单价" prop="unit_price">
              <ElInput
                v-model="formData.unit_price"
                :disabled="!isEditable"
                placeholder="请输入单价"
                style="width: 100%"
                @input="handleUnitPriceInput"
                @blur="calculatePaymentAmount"
              >
                <template #append>元</template>
              </ElInput>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="数量" prop="quantity">
              <ElInput
                v-model="formData.quantity"
                :disabled="!isEditable"
                placeholder="请输入数量"
                style="width: 100%"
                @input="handleQuantityInput"
                @blur="calculatePaymentAmount"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="付款金额" prop="payment_amount">
              <ElInputNumber
                v-model="formData.payment_amount"
                :min="0.01"
                :precision="2"
                :disabled="true"
                placeholder="自动计算"
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="付款金额大写" prop="payment_amount_words">
          <ElInput
            v-model="formData.payment_amount_words"
            placeholder="自动生成"
            :disabled="true"
            maxlength="500"
          />
        </ElFormItem>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="收款人" prop="payee_name">
              <ElInput
                v-model="formData.payee_name"
                placeholder="请输入收款人姓名"
                :disabled="!isEditable"
                maxlength="100"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="开户行" prop="bank_name">
              <ElInput
                v-model="formData.bank_name"
                placeholder="请输入开户行名称"
                :disabled="!isEditable"
                maxlength="200"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="收款账号" prop="bank_account">
              <ElInput
                v-model="formData.bank_account"
                placeholder="请输入收款账号"
                :disabled="!isEditable"
                maxlength="50"
                show-word-limit
                @input="handleBankAccountInput"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="支付方式" prop="payment_method">
              <ElSelect
                v-model="formData.payment_method"
                placeholder="请选择支付方式"
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="item in paymentMethodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="1000"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="图片">
          <FormUploader
            v-model="formData.attachment"
            :disabled="!isEditable"
            :limit="5"
            multiple
            fileType="image"
          />
        </ElFormItem>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import FormUploader from '@/components/custom/FormUploader/index.vue'
  import { convertToChineseNumber } from '@/utils/number'
  import { PAYMENT_METHOD_OPTIONS, getDefaultPaymentMethod } from '@/utils/payment'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface OfficeProcurementFormData {
    id?: number
    procurement_type?: number
    delivery_date: string
    item_name: string
    supplier_name: string
    unit_price: string | number
    quantity: string | number
    payment_amount: number
    payment_amount_words: string
    payee_name: string
    bank_name: string
    bank_account: string
    payment_method: number
    remark: string
    attachment: any[]
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `采购申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起采购申请'
  })

  /** 表单数据 */
  const formData = reactive<OfficeProcurementFormData & any>({
    procurement_type: undefined,
    delivery_date: '',
    item_name: '',
    supplier_name: '',
    unit_price: '',
    quantity: '',
    payment_amount: 0,
    payment_amount_words: '',
    payee_name: '',
    bank_name: '',
    bank_account: '',
    payment_method: getDefaultPaymentMethod(),
    remark: '',
    attachment: [],
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 选项数据 */
  const procurementTypeOptions = ref([
    { value: 1, label: '生产原料' },
    { value: 2, label: '办公用品' },
    { value: 3, label: '其他' }
  ])

  const paymentMethodOptions = PAYMENT_METHOD_OPTIONS

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    delivery_date: [{ required: true, message: '请选择交付日期', trigger: 'change' }],
    item_name: [{ required: true, message: '请输入物品名称', trigger: 'blur' }],
    supplier_name: [{ required: true, message: '请输入采购来源单位名称', trigger: 'blur' }],
    unit_price: [{ required: true, message: '请输入单价', trigger: 'blur' }],
    quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
    payment_amount: [{ required: true, message: '付款金额不能为空', trigger: 'blur' }],
    payment_amount_words: [{ required: true, message: '付款金额大写不能为空', trigger: 'blur' }],
    payee_name: [{ required: true, message: '请输入收款人姓名', trigger: 'blur' }],
    bank_name: [{ required: true, message: '请输入开户行名称', trigger: 'blur' }],
    bank_account: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
    payment_method: [{ required: true, message: '请选择支付方式', trigger: 'change' }]
  }

  // ==================== 方法定义 ====================

  /**
   * 处理单价输入，只允许数字和小数点
   */
  const handleUnitPriceInput = (value: string) => {
    // 只允许数字和小数点，最多保留两位小数
    const numericValue = value.replace(/[^\d.]/g, '')
    const parts = numericValue.split('.')
    if (parts.length > 2) {
      // 如果有多个小数点，只保留第一个
      formData.unit_price = parts[0] + '.' + parts.slice(1).join('')
    } else if (parts.length === 2 && parts[1].length > 2) {
      // 如果小数位超过2位，截取前2位
      formData.unit_price = parts[0] + '.' + parts[1].substring(0, 2)
    } else {
      formData.unit_price = numericValue
    }
  }

  /**
   * 处理数量输入，只允许正整数
   */
  const handleQuantityInput = (value: string) => {
    // 只允许正整数
    const numericValue = value.replace(/[^\d]/g, '')
    formData.quantity = numericValue
  }

  /**
   * 处理银行账号输入，只允许数字
   */
  const handleBankAccountInput = (value: string) => {
    // 只允许数字
    const numericValue = value.replace(/[^\d]/g, '')
    formData.bank_account = numericValue
  }

  /**
   * 计算付款金额
   */
  const calculatePaymentAmount = () => {
    const unitPrice = parseFloat(formData.unit_price) || 0
    const quantity = parseInt(formData.quantity) || 0
    if (unitPrice > 0 && quantity > 0) {
      formData.payment_amount = Number((unitPrice * quantity).toFixed(2))
      // 转换金额大写
      convertAmountToWords()
    }
  }

  /**
   * 转换金额为中文大写
   */
  const convertAmountToWords = () => {
    if (formData.payment_amount > 0) {
      formData.payment_amount_words = convertToChineseNumber(formData.payment_amount)
    }
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('office_procurement-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const { ApplicationApi } = await import('@/api/workflow/ApplicationApi')
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        console.log('采购表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      procurement_type: undefined,
      delivery_date: '',
      item_name: '',
      supplier_name: '',
      unit_price: '',
      quantity: '',
      payment_amount: 0,
      payment_amount_words: '',
      payee_name: '',
      bank_name: '',
      bank_account: '',
      payment_method: getDefaultPaymentMethod(),
      remark: '',
      attachment: [],
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('office_procurement-form setFormData called with:', data)
    Object.assign(formData, data)
  }

  /**
   * 保存表单
   */
  const handleSave = async () => {
    console.log('office_procurement-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备业务数据
      const businessData: OfficeProcurementFormData = {
        procurement_type: formData.procurement_type,
        delivery_date: formData.delivery_date,
        item_name: formData.item_name,
        supplier_name: formData.supplier_name,
        unit_price: parseFloat(formData.unit_price) || 0,
        quantity: parseInt(formData.quantity) || 0,
        payment_amount: formData.payment_amount,
        payment_amount_words: formData.payment_amount_words,
        payee_name: formData.payee_name,
        bank_name: formData.bank_name,
        bank_account: formData.bank_account,
        payment_method: formData.payment_method,
        remark: formData.remark,
        attachment: formData.attachment
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        businessData.id = formData.id
      }

      console.log('采购保存数据:', businessData)
      emit('save', businessData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备业务数据
      const businessData: OfficeProcurementFormData = {
        procurement_type: formData.procurement_type,
        delivery_date: formData.delivery_date,
        item_name: formData.item_name,
        supplier_name: formData.supplier_name,
        unit_price: parseFloat(formData.unit_price) || 0,
        quantity: parseInt(formData.quantity) || 0,
        payment_amount: formData.payment_amount,
        payment_amount_words: formData.payment_amount_words,
        payee_name: formData.payee_name,
        bank_name: formData.bank_name,
        bank_account: formData.bank_account,
        payment_method: formData.payment_method,
        remark: formData.remark,
        attachment: formData.attachment
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        businessData.id = formData.id
      }

      console.log('采购提交数据:', businessData)
      emit('submit', businessData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    emit('update:modelValue', false)
  }

  // ==================== 生命周期 ====================

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }
</style>
