# CRUD特性（Traits）使用指南

本文档介绍如何使用`CrudControllerTrait`和`CrudServiceTrait`来快速实现标准的CRUD操作。

## 特性概述

### CrudControllerTrait

`CrudControllerTrait`提供了标准的CRUD控制器方法实现，包括：

- `index()` - 获取列表
- `read($id)` - 获取详情
- `save()` - 新增数据
- `update($id)` - 更新数据
- `delete($id = null)` - 删除数据（支持单个和批量删除）
- `updateField()` - 更新字段值
- `options()` - 获取下拉选项

### CrudServiceTrait

`CrudServiceTrait`提供了标准的CRUD服务方法实现，包括：

- `getList()` - 获取列表
- `getPageList()` - 获取分页列表
- `getCount()` - 获取总数
- `getOne()` - 获取单条记录
- `add()` - 新增数据
- `edit()` - 编辑数据
- `delete()` - 删除数据
- `search()` - 通用搜索和排序
- `getSelectOptions()` - 获取下拉列表数据
- `updateField()` - 更新单个字段值

## 使用方法

### 在控制器中使用CrudControllerTrait

1. 在控制器类中引入trait：

```php
use app\common\core\crud\traits\CrudControllerTrait;

class YourController extends BaseController
{
    use CrudControllerTrait;
    
    protected function initialize()
    {
        parent::initialize();
        
        // 初始化服务实例
        $this->service = YourService::getInstance();
    }
}
```

2. 现在，您的控制器已经自动拥有了所有标准的CRUD操作方法。

3. 您可以根据需要重写这些方法，或添加自定义方法。

### 在服务类中使用CrudServiceTrait

1. 在服务类中引入trait：

```php
use app\common\core\crud\traits\CrudServiceTrait;use app\common\core\traits\TransactionTrait;

class YourService
{
    use CrudServiceTrait, TransactionTrait;
    
    // 单例模式实现
    private static array $instances = [];
    
    protected function __construct(?Model $model = null)
    {
        // 初始化CRUD服务
        $this->initCrudService($model);
        
        // 或者手动设置模型
        // $this->model = new YourModel();
        // $this->initCrudService();
        
        $this->initialize();
    }
    
    public static function getInstance(?Model $model = null): self
    {
        $class = static::class;
        
        if (!isset(self::$instances[$class])) {
            self::$instances[$class] = new $class($model);
        }
        
        return self::$instances[$class];
    }
    
    protected function initialize(): void
    {
        // 自定义初始化逻辑
    }
}
```

2. 现在，您的服务类已经自动拥有了所有标准的CRUD操作方法。

3. 您可以添加自定义业务方法，并在其中调用这些CRUD方法。

## 删除操作说明

删除方法(`delete`)现在支持两种模式：

1. **单个删除**：通过路由参数传递ID
   ```
   DELETE /your-controller/1
   ```

2. **批量删除**：通过POST请求体传递IDs数组
   ```
   POST /your-controller/delete
   {
     "ids": [1, 2, 3]
   }
   ```
   
   也支持传递单个ID进行删除：
   ```
   POST /your-controller/delete
   {
     "ids": 1
   }
   ```

## 示例

请参考以下示例文件：

- `app/common/core/examples/ExampleCrudController.php`
- `app/common/core/examples/ExampleCrudService.php`

## 注意事项

1. 使用`CrudControllerTrait`的控制器必须设置`$service`属性，指向一个实现了相应CRUD方法的服务实例。

2. 使用`CrudServiceTrait`的服务类必须初始化`$model`属性，或者在构造函数中调用`initCrudService()`方法。

3. 这些特性依赖于其他组件，如`ResponseTrait`、`TransactionTrait`等，请确保它们可用。

4. 这些特性提供的是通用实现，您可以根据具体业务需求重写相应方法。 