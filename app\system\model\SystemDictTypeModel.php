<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 字典类型表模型
 *
 * @property int $id 字典类型ID
 * @property string $name 字典类型名称
 * @property string $type 字典类型
 * @property int $status 状态：0禁用，1正常
 * @property string $remark 备注
 * @property int $creator_id 创建者
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $deleted_at 删除时间
 */
class SystemDictTypeModel extends BaseModel
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'system_dict_type';
    
    /**
     * 自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'created_at';
    
    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updated_at';
    
    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
                'id' => 'int',
        'status' => 'bool',
        'creator_id' => 'int',

    ];
    
    /**
     * 获取默认搜索字段
     *
     * @return array
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'id' => ['type' => 'eq'],
            'name' => ['type' => 'like'],
            'type' => ['type' => 'eq'],
            'status' => ['type' => 'eq'],
            'remark' => ['type' => 'like'],
            'creator_id' => ['type' => 'eq'],
            'created_at' => ['type' => 'date'],
            'updated_at' => ['type' => 'date'],
            'deleted_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 获取允许单字段编辑的字段
     *
     * @return array
     */
    public function getAllowUpdateFields(): array
    {
        return [
            'name',
            'type',
            'status',
            'remark',
            'created_at',
            'updated_at',
            'deleted_at',
        ];
    }
    
    /**
     * 获取允许排序的字段
     *
     * @return array
     */
    public function getAllowSortFields(): array
    {
        return [
            'id',
        ];
    }
} 