<?php
declare(strict_types=1);

namespace app\notice\service\interfaces;

/**
 * 消息模板服务接口
 */
interface TemplateServiceInterface
{
    /**
     * 根据模板编码获取模板
     *
     * @param string $code 模板编码
     * @return array|null 模板数据
     */
    public function getTemplateByCode(string $code): ?array;
    
    /**
     * 渲染模板内容
     *
     * @param string $template 模板内容
     * @param array $variables 模板变量
     * @return string 渲染后的内容
     */
    public function renderTemplate(string $template, array $variables): string;
    
    /**
     * 渲染模板标题和内容
     *
     * @param string $templateCode 模板编码
     * @param array $variables 模板变量
     * @return array 渲染后的标题和内容 ['title' => '标题', 'content' => '内容']
     */
    public function renderTemplateContent(string $templateCode, array $variables): array;
    
    /**
     * 创建模板
     *
     * @param array $data 模板数据
     * @return int|bool 成功返回模板ID，失败返回false
     */
    public function createTemplate(array $data): int|bool;
    
    /**
     * 更新模板
     *
     * @param int $id 模板ID
     * @param array $data 模板数据
     * @return bool 是否成功
     */
    public function updateTemplate(int $id, array $data): bool;
    
    /**
     * 删除模板
     *
     * @param int $id 模板ID
     * @return bool 是否成功
     */
    public function deleteTemplate(int $id): bool;
    
    /**
     * 获取模板列表
     *
     * @param array $filters 过滤条件
     * @param array $pagination 分页参数
     * @return array 模板列表
     */
    public function getTemplateList(array $filters = [], array $pagination = []): array;
    
    /**
     * 预览模板
     *
     * @param string $templateCode 模板编码
     * @param array $variables 模板变量
     * @return array 预览结果 ['title' => '标题', 'content' => '内容']
     */
    public function previewTemplate(string $templateCode, array $variables): array;
} 