<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmCustomer;
use app\crm\model\CrmContact;
use app\common\exception\BusinessException;
use think\facade\Db;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 客户表服务类
 */
class CrmCustomerService extends BaseService
{
	
	use ExportableTrait, ImportableTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmCustomer();
		parent::__construct();
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'customer_name' => ['type' => 'like'],
			
			'industry' => ['type' => 'eq'],
			
			'level' => ['type' => 'eq'],
			
			'source' => ['type' => 'eq'],
			
			'phone' => ['type' => 'like'],
			
			'region_province' => ['type' => 'eq'],
			
			'region_city' => ['type' => 'eq'],
			
			'owner_user_id' => ['type' => 'eq'],
			
			'status' => ['type' => 'eq'],
			
			'in_sea' => ['type' => 'eq'],
			
			'into_sea_time' => ['type' => 'date'],
			
			'sea_id' => ['type' => 'eq'],
			
			'credit_code' => ['type' => 'like'],
			
			'annual_revenue' => ['type' => 'between'],
			
			'employee_count' => ['type' => 'between'],
			
			'registered_capital' => ['type' => 'between'],
			
			'last_followed_at' => ['type' => 'date'],
			
			'next_followed_at' => ['type' => 'date'],
			
			'lock_status' => ['type' => 'eq'],
			
			'lock_expire_time' => ['type' => 'date'],
		
		];
	}
	
	/**
	 * 获取验证规则 - 基于crm_data.sql字段约束
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'customer_name'      => 'require|max:200',
			'industry'           => 'max:50',
			'level'              => 'integer|in:0,1,2,3',
			'source'             => 'max:50',
			'phone'              => 'max:20',
			'email'              => 'email|max:100',
			'website'            => 'url|max:200',
			'address'            => 'max:500',
			'region_province'    => 'max:50',
			'region_city'        => 'max:50',
			'region_district'    => 'max:50',
			'credit_code'        => 'max:50',
			'annual_revenue'     => 'float|egt:0',
			'employee_count'     => 'integer|egt:0',
			'registered_capital' => 'float|egt:0',
			'status'             => 'integer|in:0,1',
			'owner_user_id'      => 'require|integer|gt:0',
			'description'        => 'max:1000',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 预处理空字符串转null
	 *
	 * @param array $data 原始数据
	 * @return array 处理后的数据
	 */
	public function preprocessEmptyStrings(array $data): array
	{
		// 定义需要转换空字符串为null的日期时间字段
		$dateTimeFields = [
			'into_sea_time',
			'last_followed_at',
			'next_followed_at',
			'lock_expire_time'
		];
		
		// 处理日期时间字段的空字符串
		foreach ($dateTimeFields as $field) {
			if (isset($data[$field]) && $data[$field] === '') {
				$data[$field] = null;
			}
		}
		
		// credit_code 字段保持原样，不做特殊处理
		// 用户已修改数据库索引，允许 credit_code 为空
		
		return $data;
	}
	
	/**
	 * 重写添加方法，支持客户和联系人联合创建
	 *
	 * @param array $data 数据
	 * @return int 返回新增客户的ID
	 * @throws BusinessException
	 */
	public function add(array $data): int
	{
		// 分离客户数据和联系人数据
		$customerData = $this->extractCustomerData($data);
		$contactData  = $this->extractContactData($data);
		
		return $this->addCustomerWithContact($customerData, $contactData);
	}
	
	/**
	 * 从表单数据中提取客户数据
	 *
	 * @param array $data 表单数据
	 * @return array 客户数据
	 */
	public function extractCustomerData(array $data): array
	{
		// 排除联系人字段，保留客户字段
		$contactFields = [
			'id',
			'contact_name',
			'contact_gender',
			'contact_mobile',
			'contact_wechat',
			'contact_qq',
			'contact_position',
			'contact_email',
			'primary_contact_id'
		];
		
		$customerData = $data;
		foreach ($contactFields as $field) {
			unset($customerData[$field]);
		}
		
		return $this->preprocessEmptyStrings($customerData);
	}
	
	/**
	 * 从表单数据中提取联系人数据
	 *
	 * @param array $data 表单数据
	 * @return array 联系人数据
	 */
	public function extractContactData(array $data): array
	{
		return [
			'name'       => $data['contact_name'] ?? '',
			'gender'     => $data['contact_gender'] ?? 0,
			'mobile'     => $data['contact_mobile'] ?? '',
			'wechat'     => $data['contact_wechat'] ?? '',
			'qq'         => $data['contact_qq'] ?? '',
			'position'   => $data['contact_position'] ?? '',
			'email'      => $data['contact_email'] ?? '',
			'is_primary' => 1
		];
	}
	
	/**
	 * 创建客户并同时创建联系人
	 *
	 * @param array $customerData 客户数据
	 * @param array $contactData  联系人数据
	 * @return int 返回新增客户的ID
	 * @throws BusinessException
	 */
	protected function addCustomerWithContact(array $customerData, array $contactData): int
	{
		// 开启事务
		Db::startTrans();
		
		try {
			
			// 2. 处理负责人ID - 如果为0，设置为当前登录用户
			if (!isset($customerData['owner_user_id']) || $customerData['owner_user_id'] == 0) {
				$request                       = request();
				$customerData['owner_user_id'] = $request->adminId ?? 1;
			}
			
			// 3. 验证客户数据
			if (method_exists($this, 'validateData')) {
				$customerData = $this->validateData($customerData, 'add');
			}
			
			// 4. 过滤客户数据 - 直接使用客户数据，因为已经验证过
			$safeCustomerData = $customerData;
			
			$customerId = $this->model->saveByCreate($safeCustomerData);
			
			if (!$customerId) {
				throw new BusinessException('客户创建失败');
			}
			if (!empty($contactData)) {
				// 4. 准备联系人数据
				$contactData['customer_id'] = $customerId;
				$contactData['is_primary']  = 1; // 设为主要联系人
				
				// 5. 创建联系人
				$contactId = CrmContactService::getInstance()
				                              ->getModel()
				                              ->saveByCreate($contactData);
				
				if (!$contactId) {
					throw new BusinessException('联系人创建失败');
				}
			}
			
			// 提交事务
			Db::commit();
			
			return $customerId;
			
		}
		catch (\Exception $e) {
			// 回滚事务
			Db::rollback();
			throw new BusinessException('创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
}