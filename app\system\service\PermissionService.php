<?php

namespace app\system\service;

use app\common\core\constants\SystemConstant;
use app\system\model\RoleMenuModel;

class PermissionService
{
	public function getAdminPermissionByAdminId($adminId, $iaAll = false)
	{
		$permission = [];
		
		// 获取当前用户的角色ID
		$adminInfo = AdminService::getInstance()->getModel()->with(['roles'])
		                         ->where([
			                         [
				                         'id',
				                         '=',
				                         $adminId
			                         ],
			                         [
				                         'status',
				                         '=',
				                         1
			                         ],
		                         ])->findOrEmpty();
		
		if ($adminInfo->isEmpty()) {
			return $permission;
		}
		
		$where = [];
		
		if ($adminId != SystemConstant::SUPER_ADMIN_ID) {
			
			if ($adminInfo['roles']->isEmpty()) {
				return $permission;
			}
			
			$roleIds = $adminInfo['roles']->column('role_id');
			
			if (empty($roleIds)) {
				return $permission;
			}
			
			$menuIds = RoleMenuModel::where([
				[
					'role_id',
					'in',
					$roleIds
				],
			])
			                    ->column('menu_id');
			
			if (empty($menuIds)) {
				return $permission;
			}
			
			$where = [
				[
					'id',
					'in',
					array_unique($menuIds)
				],
				[
					'status',
					'=',
					1
				],
			];
			
		}
		
		$menuList = MenuService::getInstance()->getModel()->where($where)->order('sort desc')->select();
		
		return $iaAll
			? $menuList
			: $menuList->column('name');
	}
	
	
	
	/**
	 * 解析权限信息
	 *
	 * @param string $ruleName 请求的控制器方法标识
	 * @return array 包含模块、控制器和方法的关联数组
	 */
	public function parsePermissionInfo(string $ruleName): array
	{
		// 分割类路径和方法名
		[
			$classPath,
			$method
		] = explode('@', $ruleName);
		
		// 分割类路径为命名空间部分
		$parts = explode('\\', $classPath);
		
		// 提取模块名（倒数第二个命名空间部分）
		$module = strtolower($parts[count($parts) - 3]);
		
		// 提取控制器名（最后一个命名空间部分）
		$controller = strtolower($parts[count($parts) - 1]);
		
		// 如果$controller 包含 "controller" 则去掉
		$controller = str_replace('controller', '', $controller);
		// 返回解析后的信息
		return [
			$module,
			$controller,
			$method
		];
	}
	
	
}