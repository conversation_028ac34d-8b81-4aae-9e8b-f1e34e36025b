<?php
declare(strict_types=1);

namespace app\notice\model;

use app\common\core\base\BaseModel;

/**
 * 消息发送队列表模型
 *
 * @property int $id ID
 * @property int $message_id 消息ID
 * @property string $channel 发送通道：site,email,sms,wework,dingtalk
 * @property string $recipient_ids 接收人ID列表，JSON数组
 * @property int $status 状态：0待处理，1处理中，2已处理，3处理失败
 * @property int $retry_count 重试次数
 * @property string $error_message 错误信息
 * @property string $scheduled_time 计划发送时间
 * @property string $process_time 处理时间
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property int $tenant_id 租户ID
 */
class NoticeQueueModel extends BaseModel
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'notice_queue';
    
    /**
     * 自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'created_at';
    
    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updated_at';
    
    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
                'id' => 'integer',
        'message_id' => 'integer',
        'status' => 'integer',
        'retry_count' => 'integer',
        'scheduled_time' => 'datetime',
        'process_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'tenant_id' => 'integer',

    ];
    
    /**
     * 获取默认搜索字段
     *
     * @return array
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'recipient_ids' => ['type' => 'in'],
            'status' => ['type' => 'eq'],
            'scheduled_time' => ['type' => 'date'],
            'process_time' => ['type' => 'date'],
            'created_at' => ['type' => 'date'],
            'updated_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 获取允许单字段编辑的字段
     *
     * @return array
     */
    public function getAllowUpdateFields(): array
    {
        return [
            'message_id',
            'channel',
            'recipient_ids',
            'status',
            'retry_count',
            'error_message',
            'scheduled_time',
            'process_time',
        ];
    }
    
    /**
     * 获取允许排序的字段
     *
     * @return array
     */
    public function getAllowSortFields(): array
    {
        return [
            'id',
            'created_at',
            'updated_at',
            'scheduled_time',
            'process_time',
        ];
    }
} 