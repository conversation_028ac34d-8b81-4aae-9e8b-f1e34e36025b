# 工作流完整配置指南

## 📋 配置概述

**配置时间：** 2025-07-28  
**配置范围：** 9个业务审批表的完整工作流配置  
**配置内容：** WorkflowTypeService + workflow_type表 + workflow_definition表  

## ✅ 已完成配置

### 1. WorkflowTypeService类更新 ✅

**文件位置：** `app/workflow/service/WorkflowTypeService.php`

**更新内容：** 在`$businessCode`数组中新增9个业务类型

```php
protected array $businessCode = [
    // 原有业务类型
    'hr_leave'                        => '假勤',
    'hr_travel'                       => '出差',
    'crm_contract'                    => '合同审批',
    'crm_contract_receivable'         => '回款审批',
    'daily_price_order'               => '每日报价审批',
    
    // 新增业务类型（模式二通用页面集成）
    'ims_outbound_approval'           => '出库申请审批',
    'ims_inbound_approval'            => '入库申请审批',
    'ims_shipment_approval'           => '出货申请审批',
    'ims_purchase_approval'           => '采购申请审批',
    'finance_payment_approval'        => '付款申请审批',
    'finance_expense_reimbursement'   => '报销申请审批',
    'hr_business_trip'                => '出差申请审批',
    'hr_outing'                       => '外出申请审批',
    'office_sample_mail'              => '样品邮寄申请审批',
];
```

## 🔧 待执行配置

### 2. workflow_type表配置

**配置文件：** `docs/workflow_type_config.sql`

**执行顺序：** 第一步执行

**配置内容：**
- 为9个业务类型创建workflow_type记录
- 配置module_code和business_code映射
- 支持DynamicWorkflowFactory动态创建

**执行命令：**
```sql
source docs/workflow_type_config.sql;
```

### 3. workflow_definition表配置

**配置文件：** `docs/workflow_definition_simple_config.sql`

**执行顺序：** 第二步执行（在workflow_type配置完成后）

**配置内容：**
- 为9个业务类型创建workflow_definition记录
- 复用id=32的flow_config配置
- 创建标准的一级审批流程

**执行命令：**
```sql
source docs/workflow_definition_simple_config.sql;
```

## 📊 配置映射关系

### 业务类型到Service映射

| 业务代码 | 模块 | Service类 | 说明 |
|----------|------|-----------|------|
| `ims_outbound_approval` | ims | ImsOutboundApprovalService | 出库申请 |
| `ims_inbound_approval` | ims | ImsInboundApprovalService | 入库申请 |
| `ims_shipment_approval` | ims | ImsShipmentApprovalService | 出货申请 |
| `ims_purchase_approval` | ims | ImsPurchaseApprovalService | 采购申请 |
| `finance_payment_approval` | finance | FinancePaymentApprovalService | 付款申请 |
| `finance_expense_reimbursement` | finance | FinanceExpenseReimbursementService | 报销申请 |
| `hr_business_trip` | hr | HrBusinessTripService | 出差申请 |
| `hr_outing` | hr | HrOutingService | 外出申请 |
| `office_sample_mail` | office | OfficeSampleMailService | 样品邮寄 |

### DynamicWorkflowFactory映射验证

```php
// 映射算法验证
'ims_outbound_approval' → ImsOutboundApprovalService
'finance_payment_approval' → FinancePaymentApprovalService
'hr_business_trip' → HrBusinessTripService
'office_sample_mail' → OfficeSampleMailService
```

## 🎯 配置执行步骤

### 步骤1：更新WorkflowTypeService ✅
```bash
# 已完成，无需额外操作
```

### 步骤2：配置workflow_type表
```sql
-- 执行workflow_type配置
mysql -u root -p base_admin < docs/workflow_type_config.sql

-- 验证配置结果
SELECT business_code, name, module_code, status 
FROM workflow_type 
WHERE business_code LIKE '%approval%' OR business_code LIKE '%trip%' OR business_code LIKE '%outing%' OR business_code LIKE '%mail%';
```

### 步骤3：配置workflow_definition表
```sql
-- 执行workflow_definition配置
mysql -u root -p base_admin < docs/workflow_definition_simple_config.sql

-- 验证配置结果
SELECT wd.name, wt.business_code, wd.status 
FROM workflow_definition wd 
LEFT JOIN workflow_type wt ON wd.type_id = wt.id 
WHERE wt.business_code IN ('ims_outbound_approval', 'finance_payment_approval', 'hr_business_trip', 'office_sample_mail');
```

### 步骤4：验证完整配置
```sql
-- 验证完整的工作流配置链路
SELECT 
    wt.business_code,
    wt.name as type_name,
    wd.name as definition_name,
    wd.status as definition_status,
    CASE 
        WHEN wd.flow_config IS NOT NULL AND wd.flow_config != '' THEN '已配置'
        ELSE '未配置'
    END as flow_config_status
FROM workflow_type wt
LEFT JOIN workflow_definition wd ON wt.id = wd.type_id
WHERE wt.business_code IN (
    'ims_outbound_approval', 'ims_inbound_approval', 'ims_shipment_approval', 'ims_purchase_approval',
    'finance_payment_approval', 'finance_expense_reimbursement',
    'hr_business_trip', 'hr_outing',
    'office_sample_mail'
)
ORDER BY wt.module_code, wt.business_code;
```

## 🔍 配置验证清单

### WorkflowTypeService验证 ✅
- [x] businessCode数组包含9个新业务类型
- [x] getBusinessOptions()方法返回正确选项
- [x] 业务代码命名符合规范

### workflow_type表验证
- [ ] 9条记录成功插入
- [ ] business_code字段值正确
- [ ] module_code字段值正确
- [ ] status字段为1（启用状态）

### workflow_definition表验证
- [ ] 9条记录成功插入
- [ ] type_id正确关联workflow_type
- [ ] flow_config复制自id=32记录
- [ ] status字段为1（启用状态）

### 集成验证
- [ ] DynamicWorkflowFactory能正确创建Service实例
- [ ] ApplicationController支持新业务类型
- [ ] 前端"我的申请"页面显示新类型

## 🚨 注意事项

### 前置条件
1. **id=32记录存在**：确保workflow_definition表中id=32的记录存在且flow_config有效
2. **数据库权限**：确保有足够权限执行INSERT和SELECT操作
3. **备份数据**：建议在执行前备份workflow_type和workflow_definition表

### 执行顺序
1. **必须先执行** workflow_type_config.sql
2. **然后执行** workflow_definition_simple_config.sql
3. **最后验证** 配置结果

### 错误处理
- 如果workflow_type插入失败，检查business_code是否重复
- 如果workflow_definition插入失败，检查type_id关联是否正确
- 如果id=32不存在，需要先创建标准的flow_config模板

## 📞 技术支持

如遇到配置问题，请检查：
1. **数据库连接**：确保能正常连接数据库
2. **表结构**：确认workflow_type和workflow_definition表结构正确
3. **权限设置**：确认有足够的数据库操作权限
4. **依赖关系**：确认相关的Model和Service类已创建

---

**配置完成后，9个业务类型将完全支持工作流审批功能！**
