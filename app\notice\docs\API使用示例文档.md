# 消息中心API使用示例文档

## 📋 概述

本文档提供了消息中心各种API的详细使用示例，包括发送消息、查询消息、管理模板等功能。

**更新时间**: 2025-07-16  
**适用版本**: v2.0+  
**重要说明**: 代码传入必须使用英文键名，模板显示使用中文变量名

## 🚀 快速开始

### 基本发送消息
```php
use app\notice\service\NoticeDispatcherService;

// 发送工作流审批通知
$result = NoticeDispatcherService::getInstance()->send(
    'workflow',              // 模块名
    'task_approval',         // 动作名
    [                        // 数据 - 使用英文键名
        'title' => '张三的请假申请',
        'task_name' => '部门经理审批',
        'submitter_name' => '张三',
        'created_at' => date('Y-m-d H:i:s'),
        'detail_url' => '/workflow/task/detail?id=123'
    ],
    [1, 2, 3],              // 接收人ID数组
    [                        // 可选参数
        'creator_id' => 1,
        'priority' => 1
    ]
);

if ($result) {
    echo "消息发送成功，消息ID: {$result}";
} else {
    echo "消息发送失败";
}
```

## 📊 Workflow模块完整示例

### 1. 审批通知 (task_approval)
```php
// 场景：工作流流转到审批节点，通知审批人
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    [
        'task_name' => '部门经理审批',
        'title' => '张三的请假申请',
        'submitter_name' => '张三',
        'created_at' => '2025-07-16 15:30:00',
        'detail_url' => '/workflow/task/detail?id=123'
    ],
    [$approverId],
    [
        'creator_id' => $submitterId,
        'business_id' => $instanceId,
        'priority' => 1
    ]
);
```

### 2. 审批结果通知 (task_approved)
```php
// 场景：审批完成，通知提交人结果
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approved',
    [
        'title' => '张三的请假申请',
        'result' => '通过',  // 或 '拒绝'
        'approver_name' => '李四',
        'completed_at' => date('Y-m-d H:i:s'),
        'opinion' => '符合公司规定，同意请假'
    ],
    [$submitterId],
    [
        'creator_id' => $approverId,
        'business_id' => $instanceId
    ]
);
```

### 3. 抄送通知 (task_cc)
```php
// 场景：工作流抄送给相关人员
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_cc',
    [
        'title' => '张三的请假申请',
        'submitter_name' => '张三',
        'node_name' => '抄送',
        'cc_time' => date('Y-m-d H:i:s'),
        'detail_url' => '/workflow/detail?id=123'
    ],
    $ccUserIds,
    [
        'creator_id' => $submitterId,
        'send_channels' => 'site,email'
    ]
);
```

### 4. 催办通知 (task_urge)
```php
// 场景：催办超时未处理的任务
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_urge',
    [
        'title' => '张三的请假申请',
        'task_name' => '部门经理审批',
        'urger_name' => '王五',
        'created_at' => date('Y-m-d H:i:s'),
        'reason' => '任务超时未处理，请及时审批'
    ],
    [$approverId],
    [
        'creator_id' => $urgerId,
        'priority' => 2  // 高优先级
    ]
);
```

### 5. 转交通知 (task_transfer)
```php
// 场景：任务转交给其他人处理
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_transfer',
    [
        'title' => '张三的请假申请',
        'node_name' => '部门经理审批',
        'from_user' => '李四',
        'to_user' => '王五',
        'transfer_time' => date('Y-m-d H:i:s'),
        'detail_url' => '/workflow/task/detail?id=123'
    ],
    [$toUserId],
    [
        'creator_id' => $fromUserId,
        'business_id' => $instanceId
    ]
);
```

### 6. 终止通知 (task_terminated)
```php
// 场景：工作流被终止
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_terminated',
    [
        'title' => '张三的请假申请',
        'result' => '已终止',
        'submit_time' => '2025-07-16 15:30:00',
        'terminate_time' => date('Y-m-d H:i:s'),
        'terminate_by' => '系统管理员',
        'reason' => '申请人主动撤回',
        'detail_url' => '/workflow/detail?id=123'
    ],
    [$submitterId],
    [
        'creator_id' => $operatorId,
        'business_id' => $instanceId
    ]
);
```

### 7. 作废通知 (task_void) 🆕
```php
// 场景：工作流被作废
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_void',
    [
        'title' => '张三的请假申请',
        'result' => '已作废',
        'submit_time' => '2025-07-16 15:30:00',
        'void_time' => date('Y-m-d H:i:s'),
        'void_by' => '系统管理员',
        'reason' => '申请内容有误，需要重新提交',
        'detail_url' => '/workflow/detail?id=123'
    ],
    [$submitterId],
    [
        'creator_id' => $operatorId,
        'business_id' => $instanceId
    ]
);
```

## 🔧 变量映射说明

### 重要概念
- **代码传入**: 使用英文键名（如 `title`、`task_name`）
- **模板配置**: 字段路径使用英文（如 `title`、`task_name`）
- **模板内容**: 显示中文变量名（如 `${流程标题}`、`${任务名称}`）
- **自动映射**: 消息中心自动完成英文键名到中文变量名的映射

### 常用变量映射表
| 英文键名 | 中文变量名 | 说明 |
|----------|------------|------|
| `title` | `${流程标题}` | 工作流程标题 |
| `task_name` | `${任务名称}` | 审批任务名称 |
| `submitter_name` | `${提交人}` | 流程提交人姓名 |
| `approver_name` | `${审批人}` | 审批人姓名 |
| `created_at` | `${提交时间}` | 流程提交时间 |
| `completed_at` | `${审批时间}` | 审批完成时间 |
| `result` | `${审批结果}` | 审批结果 |
| `opinion` | `${审批意见}` | 审批意见 |
| `node_name` | `${节点名称}` | 当前节点名称 |
| `reason` | `${原因}` | 操作原因 |

## 🚨 错误处理

### 标准错误处理模式
```php
try {
    $result = NoticeDispatcherService::getInstance()->send(
        'workflow',
        'task_approval',
        $variables,
        $recipients
    );
    
    if ($result) {
        Log::info("消息发送成功，消息ID: {$result}");
        return true;
    } else {
        Log::error('消息发送失败，返回值为空');
        return false;
    }
} catch (\Exception $e) {
    Log::error('消息发送异常: ' . $e->getMessage());
    // 不影响主业务流程
    return false;
}
```

### 常见错误及解决方案
1. **缺少必填变量**: 检查是否提供了所有必填的英文键名
2. **模板不存在**: 确认模板已创建且状态为启用
3. **权限问题**: 确保在CLI环境下正确设置了creator_id和tenant_id

## 📋 高级用法

### 批量发送
```php
// 发送给多个用户
$recipients = [1, 2, 3, 4, 5];
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    $variables,
    $recipients  // 一次发送给多个用户
);
```

### 自定义发送渠道
```php
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    $variables,
    $recipients,
    [
        'send_channels' => 'site,email,sms',  // 多渠道发送
        'priority' => 2,                      // 高优先级
        'is_delayed' => true,                 // 延时发送
        'delay_minutes' => 30                 // 延时30分钟
    ]
);
```

---

**文档版本**: 2.0  
**更新时间**: 2025-07-16  
**维护人**: Augment Agent
