<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmWorkReportService;
use think\response\Json;

/**
 * 工作报告表控制器
 */
class CrmWorkReportController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CrmWorkReportService
	 */
	protected $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = CrmWorkReportService::getInstance();
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * @todo 暂时弃用
	 * 复制汇报
	 */
	public function copy(int $id): Json
	{
		try {
			$result = $this->service->copy($id);
			return $this->success('复制成功', $result);
		}
		catch (\Exception $e) {
			return $this->error('复制失败：' . $e->getMessage());
		}
	}
	
}