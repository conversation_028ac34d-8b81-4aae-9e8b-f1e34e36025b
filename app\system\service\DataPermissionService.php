<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\utils\DataPermissionCacheUtil;

/**
 * 数据权限服务
 */
class DataPermissionService
{
    /**
     * 获取用户数据权限
     *
     * @param $adminId
     * @param bool $forceRefresh 是否强制刷新
     * @return array 数据权限配置
     */
    public function getUserDataPermission($adminId, $tenantId,bool $forceRefresh = false): array
    {
        return DataPermissionCacheUtil::getUserDataPermission($adminId,$tenantId, $forceRefresh);
    }
    
    /**
     * 刷新用户数据权限
     *
     * @param $adminId
     * @return array 数据权限配置
     */
    public function refreshUserDataPermission($adminId,$tenantId): array
    {
        return DataPermissionCacheUtil::refreshUserDataPermission($adminId,$tenantId);
    }
    
    /**
     * 清除用户数据权限缓存
     *
     * @param $adminId
     * @return bool 是否成功
     */
    public function clearUserDataPermission($adminId,$tenantId): bool
    {
        return DataPermissionCacheUtil::clearUserDataPermission($adminId,$tenantId);
    }
    
    /**
     * 清除所有数据权限缓存
     * 
     * @return bool 是否成功
     */
    public function clearAllDataPermissions(): bool
    {
        return DataPermissionCacheUtil::clearAllDataPermissions();
    }
} 