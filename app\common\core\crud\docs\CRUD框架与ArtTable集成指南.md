# CRUD框架与ArtTable集成指南

## 一、概述

本文档介绍了CRUD框架与ArtTable组件的集成方案，通过这种集成，可以实现前后端一体化的CRUD操作，提高开发效率和代码质量。

### 1.1 集成目标

- 实现前端ArtTable组件与后端CRUD框架的无缝对接
- 自动生成符合ArtTable规范的前端代码
- 支持导入导出功能的前后端集成
- 提供统一的表单处理方案

### 1.2 技术栈

- 后端：ThinkPHP + CRUD框架
- 前端：Vue3 + ElementPlus + ArtTable组件

## 二、后端改进

### 2.1 CrudService集成导入导出功能

CrudService类已经集成了导入导出相关的Trait，包括：

- FormatableFieldsTrait：提供字段格式处理基础功能
- ExportableTrait：提供数据导出功能
- ImportableTrait：提供数据导入功能

```php
use app\common\core\crud\traits\FormatableFieldsTrait;
use app\common\core\crud\traits\ExportableTrait;
use app\common\core\crud\traits\ImportableTrait;
```

开发者可以根据需要在自己的Service类中引入这些Trait：

```php
class UserService extends CrudService 
{
    use ExportableTrait, ImportableTrait;
    
    // 其他代码...
}
```

### 2.2 CrudGenerator代码生成器优化

代码生成器已经优化，支持生成与ArtTable兼容的前端代码：

1. 生成API接口文件 (xxx.ts)
2. 生成ArtTable列表页面 (list.vue)
3. 生成表单组件：
   - 抽屉式表单 (form-drawer.vue)
   - 对话框式表单 (form-dialog.vue)
   - 表单帮助类 (form-helper.ts)

## 三、前端集成方案

### 3.1 ArtTable列表页面

ArtTable列表页面是一个基于ArtTable组件的Vue组件，它提供了以下功能：

- 数据列表展示
- 搜索表单
- 批量操作
- 排序筛选
- 导入导出

示例代码：

```vue
<template>
  <div class="app-container">
    <ArtTable
      :columns="columns"
      :fetch-data="fetchData"
      :search-form-items="searchFormItems"
      :operation-buttons="operationButtons"
      :batch-buttons="batchButtons"
      :show-pagination="true"
      :show-selection="true"
    >
      <template #operation="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
      </template>
    </ArtTable>
    
    <!-- 表单抽屉 -->
    <FormDrawer ref="formDrawerRef" @success="handleSuccess" />
  </div>
</template>
```

### 3.2 表单组件

为了适应不同的UI需求，提供了两种表单组件：

1. 抽屉式表单 (form-drawer.vue)：适用于复杂表单，可以展示更多字段
2. 对话框式表单 (form-dialog.vue)：适用于简单表单，展示在对话框中

这两种表单组件共享相同的表单配置和数据处理逻辑，通过表单帮助类 (form-helper.ts) 实现。

### 3.3 API接口文件

API接口文件提供了与后端CRUD接口对应的前端方法，包括：

- 获取列表数据
- 获取详情
- 添加记录
- 编辑记录
- 删除记录
- 导入导出功能

```typescript
// 获取列表
export function getList(params: QueryParams) {
  return request({
    url: '/api/system/users',
    method: 'get',
    params
  });
}

// 获取详情
export function getDetail(id: number) {
  return request({
    url: `/api/system/users/${id}`,
    method: 'get'
  });
}

// 添加
export function add(data: FormData) {
  return request({
    url: '/api/system/users',
    method: 'post',
    data
  });
}

// 编辑
export function update(data: FormData) {
  return request({
    url: `/api/system/users/${data.id}`,
    method: 'put',
    data
  });
}

// 删除
export function remove(id: number) {
  return request({
    url: `/api/system/users/${id}`,
    method: 'delete'
  });
}

// 导出
export function exportData(params: QueryParams) {
  return request({
    url: '/api/system/users/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 导入
export function importData(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/api/system/users/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
```

## 四、代码生成与使用

### 4.1 使用代码生成器

使用以下命令生成CRUD代码：

```bash
php think crud user --module=system --with-frontend
```

参数说明：
- `user`：表名
- `--module=system`：模块名称
- `--with-frontend`：生成前端代码

### 4.2 表注释配置

在数据库表注释中可以添加特殊标记来配置代码生成器的行为：

```sql
CREATE TABLE `system_user` (
  -- 字段定义
) COMMENT='用户表 @module:admin @exp:true @imp:true';
```

支持的标记：
- `@module:xxx`：指定模块名称
- `@exp:true`：启用导出功能
- `@imp:true`：启用导入功能
- `@exp_scene:field1,field2`：指定导出场景包含的字段
- `@imp_scene:field1,field2`：指定导入场景包含的字段

### 4.3 字段注释配置

在字段注释中可以添加特殊标记来配置字段的行为：

```sql
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用 | @s=eq @e @exp @imp @fmt=status',
```

支持的标记：
- `@s=xxx`：搜索类型 (eq, like, gt, lt, between, date等)
- `@e`：在编辑表单中显示
- `@exp`：包含在导出中
- `@imp`：包含在导入中
- `@fmt=xxx`：格式化方式 (datetime, date, status, enum等)
- `@opt=xxx`：选项数据来源 (静态选项或API接口)

## 五、ArtTable与CRUD框架的数据交互

### 5.1 列表数据获取

ArtTable组件通过fetchData属性获取列表数据，该方法应返回一个Promise，包含以下格式的数据：

```typescript
{
  total: number;      // 总记录数
  data: Array<any>;   // 数据列表
}
```

与后端CRUD框架的search方法返回格式一致：

```php
[
    'total' => 100,   // 总记录数
    'data' => [...]   // 数据列表
]
```

### 5.2 搜索条件映射

ArtTable的搜索表单项与后端CRUD框架的搜索字段配置相对应：

| ArtTable搜索类型 | CRUD框架搜索类型 |
|----------------|----------------|
| input          | like           |
| select         | eq             |
| date-picker    | date           |
| date-range     | between        |
| number-range   | between        |

### 5.3 导入导出功能

ArtTable组件提供了导入导出按钮，点击后调用对应的API接口：

```typescript
// 导出
const handleExport = async () => {
  const params = { ...searchForm };
  const res = await exportData(params);
  
  // 处理下载
  const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = '导出数据.xlsx';
  link.click();
};

// 导入
const handleImport = async (file: File) => {
  try {
    const res = await importData(file);
    ElMessage.success(`导入成功，成功${res.data.success}条，失败${res.data.fail}条`);
    fetchData();
  } catch (error) {
    ElMessage.error('导入失败');
  }
};
```

## 六、最佳实践

### 6.1 统一的字段命名

为了确保前后端字段名称一致，建议遵循以下命名规范：

- 使用下划线命名法 (snake_case)
- 避免使用特殊字符
- 使用有意义的字段名

### 6.2 字段格式化处理

对于需要格式化的字段，可以使用以下方式：

1. 后端：在字段注释中添加 `@fmt=xxx` 标记
2. 前端：在ArtTable的columns配置中设置format属性

```javascript
// 后端字段注释
// `created_at` datetime DEFAULT NULL COMMENT '创建时间 | @exp @fmt=datetime',

// 前端列配置
{ prop: 'created_at', label: '创建时间', type: 'datetime', format: 'YYYY-MM-DD HH:mm:ss' }
```

### 6.3 表单验证规则

表单验证规则可以从后端验证规则自动生成：

1. 后端：在字段注释中添加 `@val=xxx` 标记
2. 前端：在表单组件中设置rules属性

```javascript
// 后端字段注释
// `email` varchar(100) DEFAULT NULL COMMENT '邮箱 | @s=like @e @exp @imp @val=email',

// 前端表单验证规则
const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
};
```

## 七、常见问题解答

### 7.1 如何自定义表格列渲染？

可以使用ArtTable的插槽功能自定义列渲染：

```vue
<ArtTable :columns="columns">
  <template #status="{ row }">
    <el-tag :type="row.status === 1 ? 'success' : 'danger'">
      {{ row.status === 1 ? '启用' : '禁用' }}
    </el-tag>
  </template>
</ArtTable>
```

### 7.2 如何处理复杂的搜索条件？

对于复杂的搜索条件，可以在后端Service中重写search方法：

```php
public function search(array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null, string $scene = 'list')
{
    // 自定义搜索条件处理
    if (isset($params['keyword']) && !empty($params['keyword'])) {
        $params['custom_search'] = $params['keyword'];
        // 自定义搜索字段
        $searchFields['custom_search'] = [
            'type' => 'custom',
            'handler' => function($value) {
                return function($query) use($value) {
                    $query->where('username|mobile|email', 'like', "%{$value}%");
                };
            }
        ];
    }
    
    // 调用父类方法完成搜索
    return parent::search($params, $searchFields, $with, $applyDataPermission, $scene);
}
```

### 7.3 如何自定义表单组件？

可以修改生成的表单组件，添加自定义逻辑：

```vue
<template>
  <el-drawer
    v-model="visible"
    :title="formData.id ? '编辑' + title : '新增' + title"
    :size="500"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <!-- 自定义表单项 -->
      <el-form-item label="用户名" prop="username">
        <el-input v-model="formData.username" />
      </el-form-item>
      
      <!-- 动态表单项 -->
      <template v-if="formData.type === 'admin'">
        <el-form-item label="权限" prop="permissions">
          <el-tree-select
            v-model="formData.permissions"
            :data="permissionOptions"
            multiple
            node-key="id"
            show-checkbox
          />
        </el-form-item>
      </template>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>
```

## 八、总结

通过CRUD框架与ArtTable组件的集成，我们实现了前后端一体化的CRUD操作，大大提高了开发效率和代码质量。开发者可以通过简单的配置和代码生成器，快速构建功能完善的管理系统。

未来，我们将继续优化这套集成方案，提供更多的功能和更好的开发体验。 