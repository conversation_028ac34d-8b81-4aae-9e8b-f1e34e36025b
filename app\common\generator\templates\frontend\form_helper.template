<script setup lang="ts">
import { ref, defineExpose } from 'vue'
import { {{EntityName}}Api } from '@/api/{{moduleName}}/{{entityName}}'
import FormDrawer from './form-drawer.vue'
import FormDialog from './form-dialog.vue'

// 使用对话框还是抽屉
const useDrawer = {{useDrawer}} // true表示使用抽屉，false表示使用对话框

// 引用
const formRef = ref()

// 显示表单
const showForm = async (type: string, id?: number) => {
  if (useDrawer) {
    formRef.value?.showDrawer(type, id)
  } else {
    formRef.value?.showDialog(type, id)
  }
}

// 显示对话框（兼容表格组件的调用方式）
const showDialog = async (type: string, id?: number) => {
  showForm(type, id)
}

// 暴露方法给父组件
defineExpose({
  showForm,
  showDialog
})
</script>

<template>
  <FormDrawer v-if="useDrawer" ref="formRef" @success="$emit('success')" />
  <FormDialog v-else ref="formRef" @success="$emit('success')" />
</template>