<?php
declare(strict_types=1);

namespace app\common\utils;

/**
 * 支付方式处理工具类
 * 提供统一的支付方式常量、选项和转换方法
 */
class PaymentMethodHelper
{
    /**
     * 支付方式常量定义
     */
    public const BANK_TRANSFER = 1;    // 银行转账
    public const CASH = 2;            // 现金支付
    public const CHECK = 3;           // 支票
    public const ALIPAY = 4;          // 支付宝
    public const WECHAT = 5;          // 微信
    public const OTHER = 6;           // 其他
    
    /**
     * 支付方式标签映射
     */
    private static array $labels = [
        self::BANK_TRANSFER => '银行转账',
        self::CASH => '现金支付',
        self::CHECK => '支票',
        self::ALIPAY => '支付宝',
        self::WECHAT => '微信',
        self::OTHER => '其他'
    ];
    
    /**
     * 支付方式图标映射
     */
    private static array $icons = [
        self::BANK_TRANSFER => 'Bank',
        self::CASH => 'Money',
        self::CHECK => 'Document',
        self::ALIPAY => 'Wallet',
        self::WECHAT => 'ChatDotRound',
        self::OTHER => 'More'
    ];
    
    /**
     * 支付方式颜色映射
     */
    private static array $colors = [
        self::BANK_TRANSFER => '#409EFF',
        self::CASH => '#67C23A',
        self::CHECK => '#E6A23C',
        self::ALIPAY => '#1677FF',
        self::WECHAT => '#07C160',
        self::OTHER => '#909399'
    ];
    
    /**
     * 获取所有支付方式选项
     * 用于下拉选择器、单选框等组件
     * 
     * @return array 支付方式选项数组
     * 
     * @example
     * PaymentMethodHelper::getOptions()
     * // 返回:
     * // [
     * //   ['label' => '银行转账', 'value' => 1, 'icon' => 'Bank', 'color' => '#409EFF'],
     * //   ['label' => '现金支付', 'value' => 2, 'icon' => 'Money', 'color' => '#67C23A'],
     * //   ...
     * // ]
     */
    public static function getOptions(): array
    {
        $options = [];
        foreach (self::$labels as $value => $label) {
            $options[] = [
                'label' => $label,
                'value' => $value,
                'icon' => self::$icons[$value] ?? 'QuestionFilled',
                'color' => self::$colors[$value] ?? '#909399'
            ];
        }
        return $options;
    }
    
    /**
     * 根据支付方式值获取对应的标签文本
     * 
     * @param int|null $value 支付方式值
     * @return string 支付方式标签文本
     * 
     * @example
     * PaymentMethodHelper::getLabel(1); // 返回 "银行转账"
     * PaymentMethodHelper::getLabel(4); // 返回 "支付宝"
     * PaymentMethodHelper::getLabel(999); // 返回 "未知"
     */
    public static function getLabel(?int $value): string
    {
        if ($value === null) {
            return '-';
        }
        
        return self::$labels[$value] ?? '未知';
    }
    
    /**
     * 根据支付方式值获取对应的选项对象
     * 
     * @param int|null $value 支付方式值
     * @return array|null 支付方式选项对象或null
     * 
     * @example
     * PaymentMethodHelper::getOption(1)
     * // 返回: ['label' => '银行转账', 'value' => 1, 'icon' => 'Bank', 'color' => '#409EFF']
     */
    public static function getOption(?int $value): ?array
    {
        if ($value === null || !isset(self::$labels[$value])) {
            return null;
        }
        
        return [
            'label' => self::$labels[$value],
            'value' => $value,
            'icon' => self::$icons[$value] ?? 'QuestionFilled',
            'color' => self::$colors[$value] ?? '#909399'
        ];
    }
    
    /**
     * 根据支付方式值获取对应的图标
     * 
     * @param int|null $value 支付方式值
     * @return string 图标名称
     * 
     * @example
     * PaymentMethodHelper::getIcon(1); // 返回 "Bank"
     * PaymentMethodHelper::getIcon(4); // 返回 "Wallet"
     */
    public static function getIcon(?int $value): string
    {
        if ($value === null) {
            return 'QuestionFilled';
        }
        
        return self::$icons[$value] ?? 'QuestionFilled';
    }
    
    /**
     * 根据支付方式值获取对应的颜色
     * 
     * @param int|null $value 支付方式值
     * @return string 颜色值
     * 
     * @example
     * PaymentMethodHelper::getColor(1); // 返回 "#409EFF"
     * PaymentMethodHelper::getColor(5); // 返回 "#07C160"
     */
    public static function getColor(?int $value): string
    {
        if ($value === null) {
            return '#909399';
        }
        
        return self::$colors[$value] ?? '#909399';
    }
    
    /**
     * 验证支付方式值是否有效
     * 
     * @param mixed $value 支付方式值
     * @return bool 是否为有效的支付方式
     * 
     * @example
     * PaymentMethodHelper::isValid(1); // 返回 true
     * PaymentMethodHelper::isValid(999); // 返回 false
     */
    public static function isValid($value): bool
    {
        return isset(self::$labels[$value]);
    }
    
    /**
     * 获取默认支付方式（银行转账）
     * 
     * @return int 默认支付方式值
     */
    public static function getDefault(): int
    {
        return self::BANK_TRANSFER;
    }
    
    /**
     * 获取所有有效的支付方式值
     * 
     * @return array 支付方式值数组
     */
    public static function getValidValues(): array
    {
        return array_keys(self::$labels);
    }
    
    /**
     * 获取简化的选项数组（仅包含 label 和 value）
     * 用于简单的选择器组件
     * 
     * @return array 简化的选项数组
     */
    public static function getSimpleOptions(): array
    {
        $options = [];
        foreach (self::$labels as $value => $label) {
            $options[] = [
                'label' => $label,
                'value' => $value
            ];
        }
        return $options;
    }
    
    /**
     * 根据支付方式生成标签样式类型
     * 用于前端标签组件的样式
     * 
     * @param int|null $value 支付方式值
     * @return string 标签类型
     */
    public static function getTagType(?int $value): string
    {
        $typeMap = [
            self::BANK_TRANSFER => 'primary',
            self::CASH => 'success',
            self::CHECK => 'warning',
            self::ALIPAY => 'primary',
            self::WECHAT => 'success',
            self::OTHER => 'info'
        ];
        
        return $typeMap[$value] ?? 'info';
    }
    
    /**
     * 获取支付方式分组
     * 用于复杂的选择场景
     * 
     * @return array 分组数组
     */
    public static function getGroups(): array
    {
        return [
            [
                'label' => '电子支付',
                'options' => [
                    self::getOption(self::BANK_TRANSFER),
                    self::getOption(self::ALIPAY),
                    self::getOption(self::WECHAT)
                ]
            ],
            [
                'label' => '传统支付',
                'options' => [
                    self::getOption(self::CASH),
                    self::getOption(self::CHECK)
                ]
            ],
            [
                'label' => '其他方式',
                'options' => [
                    self::getOption(self::OTHER)
                ]
            ]
        ];
    }
    
    /**
     * 转换支付方式值为安全的整数
     * 
     * @param mixed $value 输入值
     * @param int $default 默认值
     * @return int 安全的支付方式值
     */
    public static function toSafeValue($value, int $default = self::BANK_TRANSFER): int
    {
        $intValue = is_numeric($value) ? intval($value) : $default;
        return self::isValid($intValue) ? $intValue : $default;
    }
}
