<template>
  <div class="test-page">
    <h2>回款凭证文件处理测试</h2>
    
    <div class="test-section">
      <h3>测试数据</h3>
      <div class="test-data">
        <div class="data-item">
          <h4>数组格式凭证：</h4>
          <pre>{{ JSON.stringify(testData.arrayFormat, null, 2) }}</pre>
          <div class="result">
            <p>解析结果：{{ parseVoucherFiles(testData.arrayFormat.voucher_files) }}</p>
            <p>是否有文件：{{ hasVoucherFiles(testData.arrayFormat) }}</p>
            <p>文件数量：{{ getVoucherFilesCount(testData.arrayFormat) }}</p>
          </div>
        </div>

        <div class="data-item">
          <h4>字符串格式凭证：</h4>
          <pre>{{ JSON.stringify(testData.stringFormat, null, 2) }}</pre>
          <div class="result">
            <p>解析结果：{{ parseVoucherFiles(testData.stringFormat.voucher_files) }}</p>
            <p>是否有文件：{{ hasVoucherFiles(testData.stringFormat) }}</p>
            <p>文件数量：{{ getVoucherFilesCount(testData.stringFormat) }}</p>
          </div>
        </div>

        <div class="data-item">
          <h4>空数组格式：</h4>
          <pre>{{ JSON.stringify(testData.emptyArray, null, 2) }}</pre>
          <div class="result">
            <p>解析结果：{{ parseVoucherFiles(testData.emptyArray.voucher_files) }}</p>
            <p>是否有文件：{{ hasVoucherFiles(testData.emptyArray) }}</p>
            <p>文件数量：{{ getVoucherFilesCount(testData.emptyArray) }}</p>
          </div>
        </div>

        <div class="data-item">
          <h4>null/undefined格式：</h4>
          <pre>{{ JSON.stringify(testData.nullFormat, null, 2) }}</pre>
          <div class="result">
            <p>解析结果：{{ parseVoucherFiles(testData.nullFormat.voucher_files) }}</p>
            <p>是否有文件：{{ hasVoucherFiles(testData.nullFormat) }}</p>
            <p>文件数量：{{ getVoucherFilesCount(testData.nullFormat) }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>预览测试</h3>
      <div class="preview-test">
        <el-button type="primary" @click="testPreview">测试凭证预览</el-button>
      </div>
    </div>

    <!-- 凭证预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="凭证预览测试"
      width="80%"
      destroy-on-close
    >
      <div class="voucher-preview-container">
        <div class="voucher-grid">
          <div 
            v-for="(file, index) in currentFiles" 
            :key="index" 
            class="voucher-grid-item"
          >
            <el-image
              :src="file"
              :preview-src-list="currentFiles"
              :initial-index="index"
              fit="cover"
              class="voucher-preview-image"
              preview-teleported
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  const previewVisible = ref(false)
  const currentFiles = ref<string[]>([])

  // 测试数据
  const testData = ref({
    arrayFormat: {
      voucher_files: [
        'https://example.com/voucher1.jpg',
        'https://example.com/voucher2.jpg',
        'https://example.com/voucher3.jpg'
      ]
    },
    stringFormat: {
      voucher_files: '["https://example.com/voucher1.jpg","https://example.com/voucher2.jpg"]'
    },
    emptyArray: {
      voucher_files: []
    },
    nullFormat: {
      voucher_files: null
    }
  })

  // 凭证文件处理方法（从回款列表页面复制）
  const parseVoucherFiles = (voucherFiles: any): string[] => {
    if (!voucherFiles) return []
    try {
      if (typeof voucherFiles === 'string') {
        const parsed = JSON.parse(voucherFiles)
        return Array.isArray(parsed) ? parsed : []
      }
      return Array.isArray(voucherFiles) ? voucherFiles : []
    } catch {
      return []
    }
  }

  const hasVoucherFiles = (row: any): boolean => {
    const files = parseVoucherFiles(row.voucher_files)
    return files.length > 0
  }

  const getVoucherFilesCount = (row: any): number => {
    const files = parseVoucherFiles(row.voucher_files)
    return files.length
  }

  const testPreview = () => {
    currentFiles.value = parseVoucherFiles(testData.value.arrayFormat.voucher_files)
    previewVisible.value = true
  }
</script>

<style scoped lang="scss">
  .test-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;

    h2 {
      color: #303133;
      margin-bottom: 20px;
    }

    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #dcdfe6;
      border-radius: 8px;

      h3 {
        color: #409eff;
        margin-bottom: 16px;
      }

      .test-data {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .data-item {
          padding: 16px;
          background: #f5f7fa;
          border-radius: 6px;

          h4 {
            color: #606266;
            margin-bottom: 12px;
          }

          pre {
            background: #fff;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            font-size: 12px;
            margin-bottom: 12px;
            overflow-x: auto;
          }

          .result {
            p {
              margin: 4px 0;
              font-size: 14px;
              color: #303133;
            }
          }
        }
      }

      .preview-test {
        text-align: center;
      }
    }

    /* 凭证预览样式 */
    .voucher-preview-container {
      .voucher-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;

        .voucher-grid-item {
          .voucher-preview-image {
            width: 100%;
            height: 200px;
            border-radius: 8px;
            border: 1px solid #dcdfe6;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              transform: scale(1.02);
            }
          }
        }
      }
    }
  }
</style>
