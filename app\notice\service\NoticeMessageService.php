<?php
declare(strict_types=1);

namespace app\notice\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\common\utils\CacheUtil;
use app\notice\model\NoticeMessageModel;
use app\notice\model\NoticeRecipientModel;
use app\notice\service\channel\ChannelFactory;
use app\notice\service\interfaces\NoticeServiceInterface;
use think\facade\Db;
use think\facade\Log;

/**
 * todo 不使用model操作，使用封装的CrudServiceTrait中的对应方法操作
 * 消息记录表服务类
 */
class NoticeMessageService extends BaseService implements NoticeServiceInterface
{
	
	use CrudServiceTrait;
	
	/**
	 * 未读消息缓存键前缀
	 */
	public const UNREAD_COUNT_CACHE_PREFIX = 'notice:unread:';
	
	/**
	 * 模板服务实例
	 */
	protected ?NoticeTemplateService $templateService = null;
	
	/**
	 * 队列服务实例
	 */
	protected ?NoticeQueueService $queueService = null;
	
	/**
	 * 租户模板配置服务实例
	 */
	protected ?TemplateConfigService $templateConfigService = null;
	
	/**
	 * 构造函数
	 */
	protected function __construct()
	{
		$this->model = new NoticeMessageModel();
		parent::__construct();
		$this->crudService->setEnableDataPermission(false);
		$this->templateService       = NoticeTemplateService::getInstance();
		$this->queueService          = NoticeQueueService::getInstance();
		$this->templateConfigService = TemplateConfigService::getInstance();
	}
	
	/**
	 * 发送消息
	 *
	 * @param string $templateCode     模板编码
	 * @param array  $variables        模板变量
	 * @param array  $recipients       接收人ID数组
	 * @param array  $options          选项参数，数据结构如下：
	 *                                 [
	 *                                 // === 业务相关 ===
	 *                                 'business_id'    => string,     // 业务ID，关联具体业务记录
	 *                                 'module_code'    => string,     // 模块代码，如 'workflow'，默认从模板获取
	 *                                 'sub_type'       => string,     // 子类型，用于细分消息类型
	 *
	 *                                 // === URL相关 ===
	 *                                 'detail_url'     => string,     // 详情页URL（PC端）
	 *                                 'mobile_url'     => string,     // 移动端URL
	 *
	 *                                 // === 发送控制 ===
	 *                                 'send_channels'  => string,     // 发送通道，逗号分隔，如 'site,email,sms'
	 *                                 'priority'       => int,        // 优先级：0=普通，1=重要，2=紧急
	 *                                 'delay_minutes'  => int,        // 延迟发送分钟数
	 *
	 *                                 // === 发送人信息 ===
	 *                                 'sender_id'      => int,        // 发送人ID
	 *                                 'sender_name'    => string,     // 发送人姓名
	 *                                 'creator_id'     => int,        // 创建人ID（用于权限控制）
	 *
	 *                                 // === 租户相关 ===
	 *                                 'tenant_id'      => int,        // 租户ID
	 *
	 *                                 // === 接收人相关 ===
	 *                                 'user_names'     => array,      // 接收人姓名数组，与recipients对应
	 *
	 *                                 // === 动作配置 ===
	 *                                 'action_config'  => array,      // 动作配置，如按钮配置等
	 *                             ]
	 * @return int 成功返回消息ID，失败返回0
	 */
	public function send(string $templateCode, array $variables, array $recipients, array $options = []): int
	{
		// 参数校验
		if (empty($templateCode) || empty($recipients)) {
			Log::error("发送消息失败: 模板编码或接收人为空");
			return 0;
		}
		
		$result = 0;
		// 开启事务
		Db::startTrans();
		
		try {
			// 获取模板
			$template = $this->templateService->getTemplateByCode($templateCode);
			if (!$template) {
				Log::error("发送消息失败: 模板不存在 code={$templateCode}");
				Db::rollback();
				return $result;
			}
			
			// 获取租户ID
			$tenantId = get_tenant_id() ?? 0;
			
			// 获取可用的通道列表
			$availableChannels = $tenantId > 0
				? $this->templateService->getTemplateChannels($templateCode, $tenantId)
				: explode(',', $template['send_channels']);
			
			// 如果指定了发送通道，则与可用通道求交集
			$specifiedChannels = !empty($options['send_channels'])
				? explode(',', $options['send_channels'])
				: [];
			$sendChannels      = !empty($specifiedChannels)
				? array_intersect($availableChannels, $specifiedChannels)
				: $availableChannels;
			
			// 如果没有可用通道，默认使用站内信
			$sendChannels = !empty($sendChannels)
				? $sendChannels
				: ['site'];
			
			// 渲染模板内容
			$renderedContent = $this->templateService->renderTemplateContent($templateCode, $variables);
			
			// 获取延迟发送信息
			$isDelayed    = !empty($options['delay_minutes']) && $options['delay_minutes'] > 0;
			$delayMinutes = $isDelayed
				? (int)$options['delay_minutes']
				: 0;
			
			// 计算计划发送时间
			$now           = time();
			$scheduledTime = $isDelayed
				? date('Y-m-d H:i:s', $now + $delayMinutes * 60)
				: date('Y-m-d H:i:s', $now);
			
			// 创建消息记录
			$messageData = [
				'template_id'   => $template['id'],
				'title'         => $renderedContent['title'],
				'content'       => $renderedContent['content'],
				'module_code'   => $options['module_code'] ?? $template['module_code'],
				'sub_type'      => $options['sub_type'] ?? '',
				'business_id'   => $options['business_id'] ?? '',
				'detail_url'    => $options['detail_url'] ?? '',
				'mobile_url'    => $options['mobile_url'] ?? '',
				//				'app_path'      => $options['app_path'] ?? '',
				//				'mini_app_path' => $options['mini_app_path'] ?? '',
				'action_config' => isset($options['action_config'])
					? json_encode($options['action_config'])
					: null,
				'sender_id'     => $options['sender_id'] ?? 0,
				'sender_name'   => $options['sender_name'] ?? '',
				'send_time'     => $scheduledTime,
				'priority'      => $options['priority'] ?? 0,
				'status'        => $isDelayed
					? 0
					: 1,
				// 延迟发送时状态为待发送，否则为已发送
				'is_delayed'    => $isDelayed
					? 1
					: 0,
				'delay_minutes' => $delayMinutes,
				'send_channels' => implode(',', $sendChannels),
			];
			
			// 插入消息记录
			$messageId = (new NoticeMessageModel())->saveByCreate($messageData);
			
			if (!$messageId) {
				Log::error("发送消息失败: 创建消息记录失败");
				Db::rollback();
				return $result;
			}
			
			// 创建批量插入接收人的数据
			$recipientData = $this->buildRecipientData($messageId, $recipients);
			
			// 批量插入接收人记录
			$recipientResult = (new NoticeRecipientModel())->batchSave($recipientData);
			
			if (!$recipientResult) {
				Log::error("发送消息失败: 创建接收人记录失败");
				Db::rollback();
				return $result;
			}
			
			// 添加到发送队列 - 只有非延迟消息立即添加到队列
			if (!$isDelayed) {
				$this->addMessageToQueue($messageId, $sendChannels, $recipients, $scheduledTime);
			}
			
			// 提交事务
			Db::commit();
			
			$result = $messageId;
		}
		catch (\Exception $e) {
			// 回滚事务
			Db::rollback();
			Log::error("发送消息异常: " . $e->getMessage());
		}
		
		return $result;
	}
	
	/**
	 * 构建接收人数据
	 *
	 * @param int|string $messageId  消息ID
	 * @param array      $recipients 接收人ID列表
	 * @return array 接收人数据
	 */
	protected function buildRecipientData(int|string $messageId, array $recipients): array
	{
		$recipientData = [];
		
		foreach ($recipients as $userId) {
			$recipientData[] = [
				'message_id'         => $messageId,
				'user_id'            => $userId,
				'read_status'        => 0,
				// 未读
				'site_delivered'     => 0,
				// 未投递
				'email_delivered'    => 0,
				'sms_delivered'      => 0,
				'wework_delivered'   => 0,
				'dingtalk_delivered' => 0,
			];
			
			// 清除用户未读消息缓存
			CacheUtil::delete(self::UNREAD_COUNT_CACHE_PREFIX . $userId);
		}
		
		return $recipientData;
	}
	
	/**
	 * 添加消息到队列
	 *
	 * @param int|string $messageId     消息ID
	 * @param array      $channels      通道列表
	 * @param array      $recipients    接收人列表
	 * @param string     $scheduledTime 计划发送时间
	 * @return void
	 */
	protected function addMessageToQueue(
		int|string $messageId, array $channels, array $recipients, string $scheduledTime
	): void
	{
		// 获取受支持的通道列表
		$supportedChannels = ChannelFactory::getSupportedChannels();
		
		// 添加到队列
		foreach ($channels as $channel) {
			$channel = trim($channel);
			if (!$channel || !in_array($channel, $supportedChannels)) {
				continue;
			}
			
			$this->queueService->addToQueue($messageId, $channel, $recipients, [
				'scheduled_time' => $scheduledTime
			]);
		}
	}
	
	/**
	 * 处理延迟消息
	 *
	 * @param int $limit 处理条数限制
	 * @return int 成功处理的消息数量
	 */
	public function processDelayedMessages(int $limit = 100): int
	{
		$now      = date('Y-m-d H:i:s');
		$messages = (new NoticeMessageModel())->where('is_delayed', 1)
		                                      ->where('status', 0)
		                                      ->where('send_time', '<=', $now)
		                                      ->limit($limit)
		                                      ->select();
		
		if ($messages->isEmpty()) {
			return 0;
		}
		
		$processedCount = 0;
		
		foreach ($messages as $message) {
			try {
				// 添加到队列
				$channels   = explode(',', $message->send_channels);
				$recipients = NoticeRecipientModel::where('message_id', $message->id)
				                                  ->column('user_id');
				
				if (empty($recipients)) {
					continue;
				}
				
				// 添加到发送队列
				$this->addMessageToQueue($message->id, $channels, $recipients, $now);
				
				// 更新消息状态为已发送
				$message->status = 1;
				$message->save();
				
				$processedCount++;
			}
			catch (\Exception $e) {
				Log::error("处理延迟消息失败: [ID={$message->id}] " . $e->getMessage());
			}
		}
		
		return $processedCount;
	}
	
	/**
	 * 获取用户未读消息数量
	 *
	 * @param int $userId 用户ID
	 * @return int 未读消息数量
	 */
	public function getUnreadCount(int $userId): int
	{
		// 先从缓存获取
		$cacheKey = self::UNREAD_COUNT_CACHE_PREFIX . $userId;
		$count    = CacheUtil::get($cacheKey);
		
		if ($count === null) {
			// 缓存未命中，从数据库获取
			$count = NoticeRecipientModel::where('user_id', $userId)
			                             ->where('read_status', 0)
			                             ->count();
			
			// 写入缓存，有效期1小时
			CacheUtil::set($cacheKey, $count, 3600);
		}
		
		return (int)$count;
	}
	
	/**
	 * 标记消息为已读
	 *
	 * @param int    $messageId 消息ID
	 * @param string $client    终端
	 * @return bool 是否成功
	 */
	public function markAsRead(int $messageId, string $client = 'pc'): bool
	{
		
		try {
			$userId = get_user_id();
			$info   = NoticeRecipientModel::where('id', $messageId)
			                              ->where('user_id', $userId)
			                              ->field('id')
			                              ->findOrEmpty();
			
			if ($info->isEmpty()) {
				throw new BusinessException('消息不存在');
			}
			
			$res = $info->saveByUpdate([
				'read_status' => 1,
				'read_time'   => date('Y-m-d H:i:s'),
				'read_client' => $client
				// 默认为PC端
			]);
			
			// 清除缓存
			$res && CacheUtil::delete(self::UNREAD_COUNT_CACHE_PREFIX . $userId);
			
			return $res;
		}
		catch (\Exception $e) {
			Log::error("标记消息已读异常: " . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 批量标记消息为已读
	 *
	 * @param array  $messageIds 消息ID数组
	 * @param string $client
	 * @return bool 是否成功
	 */
	public function batchMarkAsRead(array $messageIds, string $client = 'pc'): bool
	{
		if (empty($messageIds)) {
			return false;
		}
		
		try {
			$userId = get_user_id();
			$list   = NoticeRecipientModel::where('user_id', $userId)
			                              ->whereIn('id', $messageIds)
			                              ->field('id')
			                              ->select();
			$res    = true;
			if (!$list->isEmpty()) {
				$res = $list->update([
					'read_status' => 1,
					'read_time'   => date('Y-m-d H:i:s'),
					'read_client' => $client
				]);
				// 清除缓存
				CacheUtil::delete(self::UNREAD_COUNT_CACHE_PREFIX . $userId);
			}
			
			
			return $res;
		}
		catch (\Exception $e) {
			Log::error("批量标记消息已读异常: " . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 标记所有消息为已读
	 *
	 * @param int    $userId 用户ID
	 * @param string $client
	 * @return bool 是否成功
	 */
	public function markAllAsRead(int $userId, string $client = 'pc'): bool
	{
		if (!$userId) {
			return false;
		}
		
		try {
			NoticeRecipientModel::where('user_id', $userId)
			                    ->where('read_status', 0)
			                    ->update([
				                    'read_status' => 1,
				                    'read_time'   => date('Y-m-d H:i:s'),
				                    'read_client' => $client
			                    ]);
			
			// 清除缓存
			CacheUtil::delete(self::UNREAD_COUNT_CACHE_PREFIX . $userId);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error("标记所有消息已读异常: " . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 获取用户消息列表
	 *
	 * @param int   $userId     用户ID
	 * @param array $filters    过滤条件
	 * @param array $pagination 分页参数
	 * @return array 消息列表
	 */
	public function getUserMessages(int $userId, array $filters = [], array $pagination = []): array
	{
		try {
			$page     = intval($pagination['page']) ?? 1;
			$pageSize = intval($pagination['page_size']) ?? 15;
			
			// 构建查询 - 使用模型关联
			$query = NoticeRecipientModel::with([
				'message' => function ($query) {
					// 只查询需要的字段，提高性能
					$query->field('id,title,content,type,module_code,sub_type,sender_name,send_time,detail_url,mobile_url,priority');
				}
			])
			                             ->where('user_id', $userId);
			
			// 应用过滤条件
			if (isset($filters['read_status']) && $filters['read_status'] !== '') {
				$query->where('read_status', $filters['read_status']);
			}
			
			// 关联条件查询 - 消息类型
			if (!empty($filters['type'])) {
				$query->hasWhere('message', ['type' => $filters['type']]);
			}
			
			// 关联条件查询 - 业务模块
			if (!empty($filters['module_code'])) {
				$query->hasWhere('message', ['module_code' => $filters['module_code']]);
			}
			
			// 关联条件查询 - 消息子类型
			if (!empty($filters['sub_type'])) {
				$query->hasWhere('message', ['sub_type' => $filters['sub_type']]);
			}
			
			// 关联条件查询 - 优先级
			if (!empty($filters['priority'])) {
				$query->hasWhere('message', ['priority' => $filters['priority']]);
			}
			
			// 关联条件查询 - 关键词搜索
			if (!empty($filters['keyword'])) {
				$query->hasWhere('message', function ($q) use ($filters) {
					$q->where('title', 'like', "%{$filters['keyword']}%");
				});
			}
			
			// 日期范围过滤
			if (!empty($filters['start_time'])) {
				$query->hasWhere('message', function ($q) use ($filters) {
					$q->where('send_time', '>=', $filters['start_time']);
				});
			}
			if (!empty($filters['end_time'])) {
				$query->hasWhere('message', function ($q) use ($filters) {
					$q->where('send_time', '<=', $filters['end_time']);
				});
			}
			
			// 获取总数
			$total = $query->count();
			
			// 获取数据
			$list = $query->order('created_at', 'desc')
			              ->page($page, $pageSize)
			              ->select()
			              ->toArray();
			
			// 数据扁平化处理 - 将关联数据合并到主数据中
			foreach ($list as &$item) {
				if (!empty($item['message'])) {
					// 合并消息数据到主数据中
					$item['title']       = $item['message']['title'] ?? '';
					$item['content']     = $item['message']['content'] ?? '';
					$item['type']        = $item['message']['type'] ?? '';
					$item['module_code'] = $item['message']['module_code'] ?? '';
					$item['sub_type']    = $item['message']['sub_type'] ?? '';
					$item['sender_name'] = $item['message']['sender_name'] ?? '';
					$item['send_time']   = $item['message']['send_time'] ?? '';
					$item['detail_url']  = $item['message']['detail_url'] ?? '';
					$item['mobile_url']  = $item['message']['mobile_url'] ?? '';
					$item['priority']    = $item['message']['priority'] ?? 0;
					
					// 移除嵌套的message数据
					unset($item['message']);
				}
			}
			
			return [
				'list'      => $list,
				'total'     => $total,
				'page'      => $page,
				'page_size' => $pageSize
			];
		}
		catch (\Exception $e) {
			Log::error("获取用户消息列表异常: " . $e->getMessage());
			return [
				'list'      => [],
				'total'     => 0,
				'page'      => $pagination['page'] ?? 1,
				'page_size' => $pagination['page_size'] ?? 15
			];
		}
	}
	
	/**
	 * 删除用户消息
	 *
	 * @param int $messageId 消息ID
	 * @param int $userId    用户ID
	 * @return bool 是否成功
	 */
	public function deleteUserMessage(int $messageId, int $userId): bool
	{
		if (!$messageId || !$userId) {
			return false;
		}
		
		try {
			// 标记为已删除
			$result = NoticeRecipientModel::where('message_id', $messageId)
			                              ->where('user_id', $userId)
			                              ->field('id')
			                              ->findOrEmpty();
			if ($result->isEmpty()) {
				throw new BusinessException('消息不存在');
			}
			
			$res = $result->delete();
			$res && CacheUtil::delete(self::UNREAD_COUNT_CACHE_PREFIX . $userId);
			
			return $res;
		}
		catch (\Exception $e) {
			Log::error("删除用户消息异常: " . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 批量删除用户消息
	 *
	 * @param array $messageIds 消息ID数组
	 * @param int   $userId     用户ID
	 * @return bool 是否成功
	 */
	public function batchDeleteUserMessages(array $messageIds, int $userId): bool
	{
		if (empty($messageIds) || !$userId) {
			return false;
		}
		
		try {
			// 标记为已删除
			$result = NoticeRecipientModel::where('user_id', $userId)
			                              ->whereIn('message_id', $messageIds)
			                              ->field('id')
			                              ->select();
			
			if ($result->isEmpty()) {
				throw new BusinessException('消息不存在');
			}
			$res = $result->delete();
			// 清除缓存
			$res && CacheUtil::delete(self::UNREAD_COUNT_CACHE_PREFIX . $userId);
			
			return $res;
		}
		catch (\Exception $e) {
			Log::error("批量删除用户消息异常: " . $e->getMessage());
			return false;
		}
	}
} 