<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

/**
 * 排序处理相关功能
 */
trait OrderTrait
{
    /**
     * 应用排序
     */
    protected function applyOrder($query, $order)
    {
        // 安全过滤排序条件
        $safeOrder = $this->filterOrderCondition($order);
        
        if (!empty($safeOrder)) {
            $query->order($safeOrder);
        }
        else {
            $query->order($this->defaultOrder);
        }
        
        return $query;
    }
    
    /**
     * 过滤排序条件，防止SQL注入
     */
    protected function filterOrderCondition($order)
    {
        if (empty($order)) {
            return [];
        }
        
        // 如果是字符串形式
        if (is_string($order)) {
            return $this->filterStringOrderCondition($order);
        }
        
        // 如果是数组形式
        if (is_array($order)) {
            return $this->filterArrayOrderCondition($order);
        }
        
        return [];
    }
    
    /**
     * 过滤字符串形式的排序条件
     */
    protected function filterStringOrderCondition(string $order): string
    {
        $orderParts = explode(',', $order);
        $safeOrderParts = [];
        
        foreach ($orderParts as $part) {
            $part = trim($part);
            if (empty($part)) {
                continue;
            }
            
            $partDetails = explode(' ', $part);
            $field = $partDetails[0];
            $direction = isset($partDetails[1]) ? strtolower($partDetails[1]) : 'asc';
            
            // 验证字段和排序方向
            if ($this->isValidField($field) && ($direction === 'asc' || $direction === 'desc')) {
                // 检查是否在允许排序的字段列表中
                if (empty($this->allowSortFields) || in_array($field, $this->allowSortFields)) {
                    $safeOrderParts[] = $field . ' ' . $direction;
                }
            }
        }
        
        return implode(',', $safeOrderParts);
    }
    
    /**
     * 过滤数组形式的排序条件
     */
    protected function filterArrayOrderCondition(array $order): array
    {
        $safeOrder = [];
        
        foreach ($order as $field => $direction) {
            // 验证字段和排序方向
            if ($this->isValidField($field)) {
                $direction = strtolower($direction);
                if ($direction === 'asc' || $direction === 'desc') {
                    // 检查是否在允许排序的字段列表中
                    if (empty($this->allowSortFields) || in_array($field, $this->allowSortFields)) {
                        $safeOrder[$field] = $direction;
                    }
                }
            }
        }
        
        return $safeOrder;
    }
} 