<?php
declare (strict_types=1);

namespace app\system\validate;

use think\Validate;

class AdminLoginValidate extends Validate
{
	/**
	 * 定义验证规则
	 * 格式：'字段名' =>  ['规则1','规则2'...]
	 *
	 * @var array
	 */
	protected $rule = [
		'username' => 'require',
		'password' => 'require',
	];

	/**
	 * 定义错误信息
	 * 格式：'字段名.规则名' =>  '错误信息'
	 *
	 * @var array
	 */
	protected $message = [
		'username.require' => '请输入用户名',
		'password.require' => '请输入密码',
		'captcha.require' => '请输入验证码',
		'key.require' => '请输入验证码',
	];

	/**
	 * 验证场景
	 */
	protected $scene = [
		'login' => ['username', 'password'],
		'login_with_captcha' => ['username', 'password', 'captcha', 'key'],
	];

	/**
	 * 初始化验证器
	 */
	public function __construct()
	{
		parent::__construct();

		// 根据配置决定是否需要验证码
		if (env('CAPTCHA', 1) == 1) {
			$this->rule['captcha'] = 'require';
			$this->rule['key'] = 'require';
		}
	}
}
