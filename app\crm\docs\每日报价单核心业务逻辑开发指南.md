# 每日报价单核心业务逻辑开发指南

## 📋 开发前提
- ✅ 已执行 `daily_price_approval_system_v4.sql` 创建数据表
- ✅ 已使用CRUD生成器生成基础代码（模块：daily）
- ✅ 已配置工作流类型 `daily_price_order`

## 📁 模块结构
```
app/daily/                    # 每日报价模块
├── controller/
│   ├── DailyPriceOrderController.php
│   ├── DailyPriceItemController.php
│   └── DailyPriceHistoryController.php
├── model/
│   ├── DailyPriceOrder.php
│   ├── DailyPriceItem.php
│   └── DailyPriceHistory.php
├── service/
│   ├── DailyPriceOrderService.php
│   ├── DailyPriceItemService.php
│   ├── DailyPriceHistoryService.php
│   └── DailyPriceOrderWorkflowService.php  # 新增工作流服务
└── validate/
    ├── DailyPriceOrderValidate.php
    ├── DailyPriceItemValidate.php
    └── DailyPriceHistoryValidate.php
```

## 🎯 核心开发任务

### 1. 工作流服务开发

#### 创建文件：`app/daily/service/DailyPriceOrderWorkflowService.php`

```php
<?php
namespace app\daily\service;

use app\common\service\WorkflowableService;
use app\daily\model\DailyPriceOrder;
use app\daily\model\DailyPriceItem;
use app\daily\model\DailyPriceHistory;
use think\facade\Db;
use think\facade\Log;
use app\common\exception\BusinessException;

class DailyPriceOrderWorkflowService extends WorkflowableService
{
    protected function getBusinessCode(): string
    {
        return 'daily_price_order';
    }
    
    protected function getApprovalTitle(object $record): string
    {
        return "每日报价审批-{$record->order_number}({$record->price_date})";
    }
    
    protected function validateForApproval(object $record): void
    {
        parent::validateForApproval($record);
        
        // 验证基本信息
        if ($record->total_items <= 0) {
            throw new BusinessException('报价单必须包含产品明细');
        }
        
        // 验证重复提交
        $existsApproved = DailyPriceOrder::where('price_date', $record->price_date)
            ->where('approval_status', 2)
            ->where('id', '<>', $record->id)
            ->count();

        if ($existsApproved > 0) {
            throw new BusinessException('当日已存在已通过的报价单');
        }

        // 验证价格变动合理性
        $this->validatePriceChanges($record);
    }

    private function validatePriceChanges(object $record): void
    {
        $items = DailyPriceItem::where('order_id', $record->id)->select();
        
        foreach ($items as $item) {
            if (abs($item->change_rate) > 50) {
                throw new BusinessException("产品价格变动幅度过大({$item->change_rate}%)");
            }
            if ($item->unit_price < 0) {
                throw new BusinessException("产品价格不能为负数");
            }
        }
    }
    
    protected function afterApprovalComplete(object $record, int $status, string $opinion): void
    {
        switch ($status) {
            case 2: // 已通过
                $this->handleOrderApproved($record);
                break;
            case 3: // 已拒绝
                $this->handleOrderRejected($record, $opinion);
                break;
            case 4: // 已终止
                $this->handleOrderTerminated($record);
                break;
            case 6: // 已作废
                $this->handleOrderVoided($record);
                break;
        }
    }
    
    private function handleOrderApproved(object $order): void
    {
        Db::startTrans();
        try {
            // 生成价格历史记录
            $this->generatePriceHistory($order);
            
            // 清除当日其他草稿
            DailyPriceOrder::where('price_date', $order->price_date)
                ->where('approval_status', 0)
                ->where('id', '<>', $order->id)
                ->delete();

            Db::commit();
            Log::info('报价单审批通过', ['order_id' => $order->id]);
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    private function generatePriceHistory(object $order): void
    {
        $items = DailyPriceItem::where('order_id', $order->id)->select();

        foreach ($items as $item) {
            DailyPriceHistory::create([
                'tenant_id' => $order->tenant_id,
                'order_id' => $order->id,
                'item_id' => $item->id,
                'supplier_id' => $item->supplier_id,
                'product_id' => $item->product_id,
                'old_price' => $item->old_price,
                'new_price' => $item->unit_price,
                'price_change' => $item->price_change,
                'change_rate' => $item->change_rate,
                'change_date' => $order->price_date,
                'approval_time' => $order->approval_time,
                'creator_id' => $order->creator_id
            ]);
        }
    }
    
    private function handleOrderRejected(object $order, string $opinion): void
    {
        $order->remark = ($order->remark ? $order->remark . "\n" : '') . "审批拒绝：{$opinion}";
        $order->save();
    }
    
    private function handleOrderTerminated(object $order): void
    {
        DailyPriceItem::where('order_id', $order->id)->delete();
    }

    private function handleOrderVoided(object $order): void
    {
        DailyPriceItem::where('order_id', $order->id)->delete();
        $order->delete();
    }
}
```

### 2. 业务服务扩展

#### 扩展文件：`app/daily/service/DailyPriceOrderService.php`

在生成的基础服务类中添加以下方法：

```php
/**
 * 创建报价单（扩展方法）
 */
public function createOrderWithDefaults(array $data): array
{
    $data['approval_status'] = 0;
    $data['price_date'] = $data['price_date'] ?? date('Y-m-d');
    $data['title'] = $data['price_date'] . '报价'; // 自动生成标题
    $data['total_items'] = 0;

    return $this->create($data);
}

/**
 * 批量保存明细
 */
public function saveItems(int $orderId, array $items): array
{
    $order = DailyPriceOrder::find($orderId);
    if (!$order || !in_array($order->approval_status, [0, 3, 5])) {
        return $this->error('报价单当前状态不允许编辑');
    }
    
    Db::startTrans();
    try {
        DailyPriceItem::where('order_id', $orderId)->delete();

        $validItems = 0;
        foreach ($items as $index => $item) {
            $item['order_id'] = $orderId;
            $item['tenant_id'] = get_tenant_id();
            $item['sort_order'] = $index + 1;

            // 计算价格变动
            if (isset($item['unit_price']) && isset($item['old_price'])) {
                $item['price_change'] = $item['unit_price'] - $item['old_price'];
                $item['change_rate'] = $item['old_price'] > 0 ?
                    ($item['price_change'] / $item['old_price']) * 100 : 0;
            }

            DailyPriceItem::create($item);
            $validItems++;
        }
        
        $order->total_items = $validItems;
        $order->save();
        
        Db::commit();
        return $this->success('保存成功', ['total_items' => $validItems]);
    } catch (\Exception $e) {
        Db::rollback();
        return $this->error('保存失败：' . $e->getMessage());
    }
}

/**
 * 从昨日复制
 */
public function copyFromYesterday(string $targetDate): array
{
    $yesterdayDate = date('Y-m-d', strtotime($targetDate . ' -1 day'));

    $yesterdayOrder = DailyPriceOrder::where('price_date', $yesterdayDate)
        ->where('approval_status', 2)
        ->find();

    if (!$yesterdayOrder) {
        return $this->error('未找到昨日已通过的报价单');
    }

    $yesterdayItems = DailyPriceItem::where('order_id', $yesterdayOrder->id)->select();
    
    $newOrder = $this->createOrderWithDefaults([
        'price_date' => $targetDate,
        'remark' => "基于{$yesterdayDate}报价单复制"
    ]);
    
    if (!$newOrder['success']) {
        return $newOrder;
    }
    
    $newItems = [];
    foreach ($yesterdayItems as $item) {
        $newItems[] = [
            'supplier_id' => $item->supplier_id,
            'product_id' => $item->product_id,
            'unit_price' => $item->unit_price,
            'old_price' => $item->unit_price,
            'stock_price' => $item->stock_price,
            'stock_qty' => $item->stock_qty,
            'policy_remark' => $item->policy_remark,
            'is_manual_price' => 0
        ];
    }
    
    $result = $this->saveItems($newOrder['data']['id'], $newItems);
    
    return $this->success('复制成功', [
        'order' => $newOrder['data'],
        'items_count' => count($newItems)
    ]);
}

// 报价单编号生成方法已删除，不再需要
// 标题自动基于日期生成：YYYY-MM-DD + "报价"
```

### 3. 控制器扩展

#### 扩展文件：`app/daily/controller/DailyPriceOrderController.php`

在生成的基础控制器中添加以下方法：

```php
/**
 * 提交审批
 */
public function submitApproval(): Json
{
    $id = $this->request->post('id', 0, 'int');

    try {
        $workflowService = new DailyPriceOrderWorkflowService();
        $result = $workflowService->submitApproval($id);

        return $this->success('提交成功', $result);
    } catch (\Exception $e) {
        return $this->error('提交失败：' . $e->getMessage());
    }
}

/**
 * 撤回审批
 */
public function withdrawApproval(): Json
{
    $id = $this->request->post('id', 0, 'int');

    try {
        $workflowService = new DailyPriceOrderWorkflowService();
        $result = $workflowService->withdrawApproval($id);

        return $this->success('撤回成功', $result);
    } catch (\Exception $e) {
        return $this->error('撤回失败：' . $e->getMessage());
    }
}

/**
 * 保存明细
 */
public function saveItems(): Json
{
    $orderId = $this->request->post('order_id', 0, 'int');
    $items = $this->request->post('items', [], 'array');
    
    try {
        $service = new DailyPriceOrderService();
        $result = $service->saveItems($orderId, $items);

        return $result['success'] ?
            $this->success($result['message'], $result['data']) :
            $this->error($result['message']);
    } catch (\Exception $e) {
        return $this->error('保存失败：' . $e->getMessage());
    }
}

/**
 * 从昨日复制
 */
public function copyFromYesterday(): Json
{
    $targetDate = $this->request->post('target_date', date('Y-m-d'), 'string');

    try {
        $service = new DailyPriceOrderService();
        $result = $service->copyFromYesterday($targetDate);
        
        return $result['success'] ? 
            $this->success($result['message'], $result['data']) : 
            $this->error($result['message']);
    } catch (\Exception $e) {
        return $this->error('复制失败：' . $e->getMessage());
    }
}
```

## 🔧 路由配置

在 `route/daily.php` 中添加扩展路由：

```php
// 每日报价单扩展路由
Route::group('daily-price-order', function () {
    Route::post(':id/submit-approval', 'DailyPriceOrderController@submitApproval');
    Route::post(':id/withdraw-approval', 'DailyPriceOrderController@withdrawApproval');
    Route::post(':id/void', 'DailyPriceOrderController@voidOrder');
    Route::post(':id/items', 'DailyPriceOrderController@saveItems');
    Route::post('copy-yesterday', 'DailyPriceOrderController@copyFromYesterday');
    Route::get(':id/export', 'DailyPriceOrderController@export');
});
```

## 🧪 测试要点

### 1. 工作流测试
- [ ] 提交审批功能
- [ ] 审批通过后生成历史记录
- [ ] 审批拒绝后可重新编辑
- [ ] 撤回审批功能

### 2. 业务逻辑测试
- [ ] 报价单编号生成唯一性
- [ ] 明细保存和价格计算
- [ ] 从昨日复制功能
- [ ] 状态权限控制

### 3. 数据完整性测试
- [ ] 租户数据隔离
- [ ] 软删除功能
- [ ] 事务回滚机制
- [ ] 并发操作处理

## 📝 开发注意事项

1. **继承生成器代码**：扩展而不是重写生成的基础代码
2. **工作流集成**：确保正确继承 `WorkflowableService`
3. **事务处理**：关键操作使用数据库事务
4. **异常处理**：提供友好的错误提示
5. **日志记录**：记录关键操作日志
6. **权限控制**：基于审批状态控制操作权限

这个指南专注于核心业务逻辑的实现，基于CRUD生成器生成的基础代码进行扩展，大大简化了开发工作量。
