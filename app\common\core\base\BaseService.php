<?php
declare(strict_types=1);

namespace app\common\core\base;

use app\common\core\crud\CrudService;
use app\common\core\crud\factory\CrudFactory;
use think\Model;

/**
 * 服务基类
 * 重构后此类仅作为基础服务类，具体CRUD操作应使用CrudService
 */
abstract class BaseService
{
	
	/**
	 * 模型实例
	 *
	 * @var Model|null
	 */
	protected ?Model $model = null;
	
	/**
	 * CRUD服务实例
	 *
	 * @var CrudService
	 */
	protected CrudService $crudService;
	
	// 存储各个子类的单例实例
	private static array $instances = [];
	
	// 受保护的构造函数防止外部实例化
	protected function __construct()
	{
		if ($this->model !== null) {
			$this->crudService = CrudFactory::create($this->model);
		}
		else {
			throw new \RuntimeException(static::class . ' must have a model instance assigned.');
		}
		
		// 初始化（子类可重写）
		$this->initialize();
	}
	
	/**
	 * 获取单例实例
	 *
	 * @return static
	 */
	public static function getInstance(): self
	{
		$class = static::class; // 获取实际调用类名（支持子类）
		
		// 如果对应类的实例不存在则创建
		if (!isset(self::$instances[$class])) {
			self::$instances[$class] = new $class();
		}
		
		return self::$instances[$class];
	}
	
	/**
	 * 获取模型实例
	 *
	 * @return Model|null
	 */
	public function getModel(): ?Model
	{
		return $this->model;
	}
	
	/**
	 * 获取CRUD服务实例
	 *
	 * @return CrudService
	 */
	public function getCrudService(): CrudService
	{
		return $this->crudService;
	}
	
	/**
	 * 设置CRUD服务实例
	 *
	 * @param CrudService $crudService
	 * @return $this
	 */
	public function setCrudService(CrudService $crudService): self
	{
		$this->crudService = $crudService;
		return $this;
	}
	
	/**
	 * 初始化方法
	 * 子类可重写此方法进行初始化操作
	 *
	 * @return void
	 */
	protected function initialize(): void
	{
		// 子类可以重写此方法实现自定义初始化
	}
	
	/**
	 * 代理方法到CRUD服务
	 * 方便直接在Service中调用CRUD操作方法
	 *
	 * @param $name
	 * @param $arguments
	 * @return mixed
	 */
	public function __call($name, $arguments)
	{
		if (method_exists($this->crudService, $name)) {
			return call_user_func_array([
				$this->crudService,
				$name
			], $arguments);
		}
		
		throw new \BadMethodCallException("Method {$name} does not exist");
	}
} 