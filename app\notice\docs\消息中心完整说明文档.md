# 消息中心完整说明文档

## 📋 概述

消息中心是一个完整的多租户消息通知系统，支持多种通知渠道和模板化消息发送。系统采用模块化设计，支持站内信、邮件、短信、企业微信、钉钉等多种通知方式。

## 🏗️ 系统架构

### 核心组件架构
```
消息中心架构
├── 模板管理层 (NoticeTemplateService)
│   ├── 模板定义 (notice_template)
│   ├── 变量配置 (variables_config)
│   └── 租户配置 (notice_template_tenant_config)
├── 消息调度层 (NoticeDispatcherService)
│   ├── 模板解析
│   ├── 变量提取
│   └── 消息发送
├── 消息处理层 (NoticeMessageService)
│   ├── 消息记录 (notice_message)
│   ├── 接收人管理 (notice_recipient)
│   └── 发送队列 (notice_queue)
└── 渠道发送层 (ChannelFactory)
    ├── 站内信 (SiteChannelService)
    ├── 邮件 (EmailChannelService)
    ├── 短信 (SmsChannelService)
    ├── 企业微信 (WeworkChannelService)
    ├── 钉钉 (DingtalkChannelService)
    └── Webhook (WebhookChannelService)
```

### 数据库表结构
```sql
-- 通知模板表
notice_template
├── id (主键)
├── code (模板编码，唯一)
├── name (模板名称)
├── title (消息标题模板)
├── content (消息内容模板)
├── variables_config (变量配置JSON)
├── module_code (所属模块)
├── send_channels (发送渠道)
├── status (状态)
└── tenant_id (租户ID)

-- 消息记录表
notice_message
├── id (主键)
├── template_code (模板编码)
├── title (消息标题)
├── content (消息内容)
├── send_channels (发送渠道)
├── status (发送状态)
└── tenant_id (租户ID)

-- 消息接收人表
notice_recipient
├── id (主键)
├── message_id (消息ID)
├── user_id (接收人ID)
├── read_status (阅读状态)
└── read_time (阅读时间)

-- 发送队列表
notice_queue
├── id (主键)
├── message_id (消息ID)
├── channel (发送渠道)
├── recipient_ids (接收人ID列表)
├── status (处理状态)
└── retry_count (重试次数)
```

## 🔧 核心服务类

### 1. NoticeDispatcherService (消息调度服务)
**功能**: 统一的消息发送入口，负责模板解析和变量提取
**主要方法**:
- `send(string $module, string $action, array $data, array $recipients, array $options = [])`

### 2. NoticeTemplateService (模板管理服务)
**功能**: 模板的增删改查和缓存管理
**主要方法**:
- `getTemplateByCode(string $code)`: 根据编码获取模板
- `createTemplate(array $data)`: 创建模板
- `updateTemplate(int $id, array $data)`: 更新模板

### 3. NoticeMessageService (消息记录服务)
**功能**: 消息记录的创建和管理
**主要方法**:
- `send(string $templateCode, array $variables, array $recipients, array $options = [])`: 发送消息
- `getUserMessages(int $userId, array $filters = [])`: 获取用户消息
- `getUnreadCount(int $userId)`: 获取未读消息数

### 4. NoticeQueueService (队列处理服务)
**功能**: 消息队列的管理和处理
**主要方法**:
- `addToQueue(int $messageId, string $channel, array $recipientIds, array $options = [])`: 添加到队列
- `processQueue(int $limit = 100)`: 处理队列

## 📝 模板编码规范

### 编码格式
模板编码采用 `{模块}_{动作}` 格式：

**工作流模块 (workflow)**:
- `workflow_task_approval` - 任务审批通知
- `workflow_task_approved` - 审批结果通知
- `workflow_task_cc` - 抄送通知
- `workflow_task_urge` - 催办通知
- `workflow_task_transfer` - 转交通知
- `workflow_task_terminated` - 终止通知

**CRM模块 (crm)**:
- `crm_lead_convert` - 线索转化通知
- `crm_customer_assign` - 客户分配通知
- `crm_business_stage_change` - 商机阶段变更通知
- `crm_quotation_create` - 报价单创建通知
- `crm_contract_approval` - 合同审批通知

**系统模块 (system)**:
- `system_notice` - 系统通知

### 变量配置格式
```json
{
  "variables": [
    {
      "name": "任务名称",
      "code": "task_name",
      "field": "task_name",
      "required": true,
      "description": "审批任务名称"
    },
    {
      "name": "流程标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    }
  ]
}
```

## 🚀 使用指南

### 1. 基本调用方式

#### 方式一：通过调度器发送 (推荐)
```php
use app\notice\service\NoticeDispatcherService;

// 发送工作流审批通知
$result = NoticeDispatcherService::getInstance()->send(
    'workflow',           // 模块名称
    'task_approval',      // 动作名称
    [                     // 业务数据
        'task_name' => '合同审批',
        'title' => '销售合同审批申请',
        'submitter_name' => '张三',
        'created_at' => '2025-07-16 15:30:00'
    ],
    [1, 2, 3],           // 接收人ID数组
    [                     // 选项参数
        'creator_id' => 1,
        'priority' => 1
    ]
);
```

#### 方式二：直接调用消息服务
```php
use app\notice\service\NoticeMessageService;

$result = NoticeMessageService::getInstance()->send(
    'workflow_task_approval',  // 模板编码
    [                          // 模板变量
        'task_name' => '合同审批',
        'title' => '销售合同审批申请',
        'submitter_name' => '张三',
        'created_at' => '2025-07-16 15:30:00'
    ],
    [1, 2, 3],                // 接收人ID数组
    [                          // 选项参数
        'creator_id' => 1,
        'send_channels' => 'site,email'
    ]
);
```

### 2. 获取用户消息

#### 获取未读消息数
```php
$unreadCount = NoticeMessageService::getInstance()->getUnreadCount($userId);
```

#### 获取消息列表
```php
$messages = NoticeMessageService::getInstance()->getUserMessages($userId, [
    'read_status' => 0,     // 0=未读, 1=已读
    'module_code' => 'workflow',
    'page' => 1,
    'limit' => 10
]);
```

#### 标记消息为已读
```php
$result = NoticeMessageService::getInstance()->markAsRead($messageId, $userId);
```

### 3. 模板管理

#### 创建模板
```php
$templateData = [
    'code' => 'workflow_task_approval',
    'name' => '工作流任务审批通知',
    'title' => '您有一个待审批任务',
    'content' => '标题：${流程标题}\n当前环节：${任务名称}\n提交人：${提交人}',
    'variables_config' => json_encode([
        'variables' => [
            [
                'name' => '任务名称',
                'code' => 'task_name',
                'field' => 'task_name',
                'required' => true,
                'description' => '审批任务名称'
            ]
        ]
    ]),
    'module_code' => 'workflow',
    'send_channels' => 'site',
    'status' => 1
];

$templateId = NoticeTemplateService::getInstance()->createTemplate($templateData);
```

## 📊 支持的发送渠道

### 1. 站内信 (site)
- **服务类**: `SiteChannelService`
- **特点**: 系统内部消息，支持已读/未读状态
- **配置**: 无需额外配置

### 2. 邮件 (email)
- **服务类**: `EmailChannelService`
- **特点**: 支持HTML格式，可配置SMTP服务器
- **配置**: 需要配置邮件服务器信息

### 3. 短信 (sms)
- **服务类**: `SmsChannelService`
- **特点**: 支持多种短信服务商
- **配置**: 需要配置短信服务商API

### 4. 企业微信 (wework)
- **服务类**: `WeworkChannelService`
- **特点**: 企业内部通知
- **配置**: 需要配置企业微信应用信息

### 5. 钉钉 (dingtalk)
- **服务类**: `DingtalkChannelService`
- **特点**: 钉钉群组或个人消息
- **配置**: 需要配置钉钉应用信息

### 6. Webhook (webhook)
- **服务类**: `WebhookChannelService`
- **特点**: 自定义HTTP回调
- **配置**: 需要配置回调URL和认证信息

## ⚙️ 队列处理

### 启动队列处理器
```bash
# 处理消息队列
php think notice:process-queue

# 处理延迟消息
php think notice:process-delayed
```

### 队列配置
```php
// config/queue.php
return [
    'default' => 'redis',
    'connections' => [
        'redis' => [
            'driver' => 'redis',
            'queue' => 'notice',
            'retry_after' => 90,
        ],
    ],
];
```

## 🔍 错误处理和日志

### 日志记录
系统会自动记录以下日志：
- 消息发送成功/失败
- 模板解析错误
- 变量验证失败
- 队列处理异常

### 常见错误处理
1. **模板不存在**: 检查模板编码是否正确
2. **变量缺失**: 检查必填变量是否提供
3. **接收人为空**: 检查接收人ID数组是否有效
4. **渠道配置错误**: 检查对应渠道的配置信息

## 📈 性能优化

### 1. 模板缓存
- 模板数据自动缓存1小时
- 支持手动清除缓存

### 2. 批量处理
- 支持批量创建接收人记录
- 队列批量处理消息

### 3. 异步发送
- 支持延迟发送
- 队列异步处理

## 🔒 安全考虑

### 1. 数据权限
- 支持租户隔离
- 用户只能查看自己的消息

### 2. 输入验证
- 模板变量验证
- 接收人权限验证

### 3. 防重复发送
- 队列去重机制
- 发送状态跟踪
