<?php
/**
 * 测试出库和出货明细数据保存功能
 */

// 数据库连接配置
$host = '*************';
$port = '3306';
$database = 'www_bs_com';
$username = 'www_bs_com';
$password = 'PdadjMXmNy8Pn9tj';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 测试出库和出货明细数据保存功能 ===\n\n";
    
    // 1. 测试数字转中文大写功能
    echo "1. 测试数字转中文大写功能:\n";
    echo str_repeat("-", 50) . "\n";

    $testAmounts = [0, 123.45, 1000, 10000.50, 1234567.89];
    foreach ($testAmounts as $amount) {
        $chinese = convertToChineseNumber($amount);
        echo sprintf("%.2f => %s\n", $amount, $chinese);
    }

    echo "\n";

    // 2. 测试安全数学运算
    echo "2. 测试安全数学运算:\n";
    echo str_repeat("-", 50) . "\n";

    $quantity = 10.5;
    $unitPrice = 123.45;
    $totalAmount = safeMultiply($quantity, $unitPrice);
    echo sprintf("数量: %.2f, 单价: %.2f, 小计: %.2f\n", $quantity, $unitPrice, $totalAmount);

    $sum1 = safeAdd(100.1, 200.2);
    $sum2 = safeAdd($sum1, 300.3);
    echo sprintf("累计计算: 100.1 + 200.2 + 300.3 = %.2f\n", $sum2);
    
    echo "\n";
    
    // 3. 查询测试数据
    echo "3. 查询测试用的供应商和产品数据:\n";
    echo str_repeat("-", 50) . "\n";
    
    // 查询供应商
    $stmt = $pdo->query("
        SELECT id, name 
        FROM ims_supplier 
        WHERE deleted_at IS NULL AND status = 1 
        ORDER BY id 
        LIMIT 3
    ");
    $suppliers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "可用供应商:\n";
    foreach ($suppliers as $supplier) {
        echo "ID: {$supplier['id']}, 名称: {$supplier['name']}\n";
    }
    
    // 查询产品
    $stmt = $pdo->query("
        SELECT id, name, price, cost 
        FROM crm_product 
        WHERE deleted_at IS NULL AND status = 1 
        ORDER BY id 
        LIMIT 5
    ");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n可用产品:\n";
    foreach ($products as $product) {
        echo "ID: {$product['id']}, 名称: {$product['name']}, 价格: {$product['price']}\n";
    }
    
    echo "\n";
    
    // 4. 模拟出库申请明细数据验证
    echo "4. 模拟出库申请明细数据验证:\n";
    echo str_repeat("-", 50) . "\n";
    
    if (!empty($suppliers) && !empty($products)) {
        // 构造测试明细数据
        $outboundItems = [
            [
                'supplier_id' => $suppliers[0]['id'],
                'product_id' => $products[0]['id'],
                'product_name' => $products[0]['name'],
                'quantity' => 10,
                'unit_price' => 100.50,
                'product_unit' => '个'
            ],
            [
                'supplier_id' => $suppliers[1]['id'] ?? $suppliers[0]['id'],
                'product_id' => $products[1]['id'] ?? $products[0]['id'],
                'product_name' => $products[1]['name'] ?? $products[0]['name'],
                'quantity' => 5,
                'unit_price' => 200.75,
                'product_unit' => '台'
            ]
        ];
        
        echo "出库明细数据:\n";
        $totalAmount = 0;
        $totalQuantity = 0;
        
        foreach ($outboundItems as $index => $item) {
            $itemTotal = safeMultiply($item['quantity'], $item['unit_price']);
            $totalAmount = safeAdd($totalAmount, $itemTotal);
            $totalQuantity = safeAdd($totalQuantity, $item['quantity']);
            
            echo sprintf(
                "明细%d: 供应商ID=%d, 产品ID=%d, 数量=%.2f, 单价=%.2f, 小计=%.2f\n",
                $index + 1,
                $item['supplier_id'],
                $item['product_id'],
                $item['quantity'],
                $item['unit_price'],
                $itemTotal
            );
        }
        
        echo sprintf("\n总数量: %.2f\n", $totalQuantity);
        echo sprintf("总金额: %.2f\n", $totalAmount);
        echo sprintf("总金额大写: %s\n", convertToChineseNumber($totalAmount));
        
        // 5. 验证必填字段检查
        echo "\n5. 验证必填字段检查:\n";
        echo str_repeat("-", 50) . "\n";
        
        $invalidItems = [
            ['supplier_id' => '', 'product_id' => 1, 'quantity' => 10, 'unit_price' => 100],
            ['supplier_id' => 1, 'product_id' => '', 'quantity' => 10, 'unit_price' => 100],
            ['supplier_id' => 1, 'product_id' => 1, 'quantity' => 0, 'unit_price' => 100],
            ['supplier_id' => 1, 'product_id' => 1, 'quantity' => 10, 'unit_price' => 0],
        ];
        
        $errorMessages = [
            '供应商ID为必填项',
            '产品ID为必填项',
            '出库数量必须大于0',
            '单价必须大于0'
        ];
        
        foreach ($invalidItems as $index => $item) {
            try {
                validateItemData($item);
                echo "验证失败: 应该抛出异常\n";
            } catch (Exception $e) {
                echo "✓ 验证通过: {$errorMessages[$index]} - {$e->getMessage()}\n";
            }
        }
        
    } else {
        echo "警告: 没有找到测试用的供应商或产品数据\n";
    }
    
    echo "\n";
    
    // 6. 检查数据库表结构
    echo "6. 检查数据库表结构:\n";
    echo str_repeat("-", 50) . "\n";
    
    $tables = [
        'ims_outbound_approval' => ['total_amount', 'total_quantity', 'total_amount_chinese'],
        'ims_shipment_approval' => ['total_amount', 'total_quantity', 'total_amount_chinese'],
        'ims_outbound_item' => ['supplier_id', 'product_id', 'quantity', 'unit_price', 'total_amount'],
        'ims_shipment_item' => ['supplier_id', 'product_id', 'quantity', 'unit_price', 'total_amount']
    ];
    
    foreach ($tables as $tableName => $requiredFields) {
        echo "\n检查表 {$tableName}:\n";
        
        $stmt = $pdo->prepare("
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = ?
            AND COLUMN_NAME IN ('" . implode("','", $requiredFields) . "')
            ORDER BY ORDINAL_POSITION
        ");
        $stmt->execute([$tableName]);
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($columns) === count($requiredFields)) {
            echo "✓ 所有必需字段都存在\n";
            foreach ($columns as $column) {
                echo "  - {$column['COLUMN_NAME']}: {$column['DATA_TYPE']} ({$column['COLUMN_COMMENT']})\n";
            }
        } else {
            echo "✗ 缺少字段，需要执行迁移脚本\n";
            $existingFields = array_column($columns, 'COLUMN_NAME');
            $missingFields = array_diff($requiredFields, $existingFields);
            echo "  缺少字段: " . implode(', ', $missingFields) . "\n";
        }
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "测试完成！\n";
    echo "如果发现缺少字段，请执行: database/migrations/add_total_amount_chinese_fields.sql\n";
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "执行失败: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 验证明细项数据（模拟服务类中的验证逻辑）
 */
function validateItemData(array $item): void
{
    // 验证供应商ID
    if (empty($item['supplier_id'])) {
        throw new Exception('供应商ID为必填项');
    }

    // 验证产品ID
    if (empty($item['product_id'])) {
        throw new Exception('产品ID为必填项');
    }

    // 验证出库数量
    if (empty($item['quantity']) || floatval($item['quantity']) <= 0) {
        throw new Exception('出库数量必须大于0');
    }

    // 验证单价
    if (empty($item['unit_price']) || floatval($item['unit_price']) <= 0) {
        throw new Exception('单价必须大于0');
    }
}

/**
 * 数字转中文大写金额
 */
function convertToChineseNumber($num): string
{
    if (!is_numeric($num)) return '零元整';

    $num = floatval($num);
    if ($num == 0) return '零元整';

    $digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    $units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];
    $decimalUnits = ['角', '分'];

    $isNegative = $num < 0;
    $absNum = abs($num);

    $numStr = number_format($absNum, 2, '.', '');
    [$integerPart, $decimalPart] = explode('.', $numStr);

    $result = '';

    if (intval($integerPart) === 0) {
        $result = '零';
    } else {
        $integerArray = array_reverse(str_split($integerPart));
        $hasZero = false;

        for ($i = 0; $i < count($integerArray); $i++) {
            $digit = intval($integerArray[$i]);
            $unitIndex = $i % 9;

            if ($digit !== 0) {
                if ($hasZero && $unitIndex !== 4 && $unitIndex !== 8) {
                    $result = '零' . $result;
                }
                $result = $digits[$digit] . $units[$unitIndex] . $result;
                $hasZero = false;
            } else {
                if ($unitIndex === 4 || $unitIndex === 8) {
                    if ($result && !str_starts_with($result, $units[$unitIndex])) {
                        $result = $units[$unitIndex] . $result;
                    }
                }
                $hasZero = true;
            }
        }
    }

    $result .= '元';

    if (intval($decimalPart) > 0) {
        $jiao = intval($decimalPart[0]);
        $fen = intval($decimalPart[1]);

        if ($jiao > 0) {
            $result .= $digits[$jiao] . $decimalUnits[0];
        }
        if ($fen > 0) {
            $result .= $digits[$fen] . $decimalUnits[1];
        }
    } else {
        $result .= '整';
    }

    if ($isNegative) {
        $result = '负' . $result;
    }

    return $result;
}

/**
 * 安全的数字乘法运算
 */
function safeMultiply($a, $b): float
{
    if (!is_numeric($a) || !is_numeric($b)) return 0.0;

    $a = floatval($a);
    $b = floatval($b);

    return round($a * $b, 2);
}

/**
 * 安全的数字加法运算
 */
function safeAdd($a, $b): float
{
    if (!is_numeric($a) || !is_numeric($b)) return 0.0;

    $a = floatval($a);
    $b = floatval($b);

    return round($a + $b, 2);
}
