<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\crm\model\CrmLead;
use app\crm\model\CrmCustomer;
use app\crm\model\CrmContact;
use think\facade\Db;
use think\facade\Log;

/**
 * 线索转化业务服务类
 * 负责处理线索转客户的核心业务逻辑
 */
class LeadConversionService extends BaseService
{
    /**
     * 构造函数
     */
    protected function __construct()
    {
        $this->model = new CrmLead();
        parent::__construct();
    }
    /**
     * 线索转化为客户
     * 
     * @param int $leadId 线索ID
     * @param array $customerData 客户数据
     * @param array $contactData 联系人数据
     * @return array 转化结果
     * @throws BusinessException
     */
    public function convertToCustomer(int $leadId, array $customerData = [], array $contactData = []): array
    {
        // 开启数据库事务
        Db::startTrans();
        
        try {
            // 1. 验证线索状态和权限
            $lead = $this->validateLeadForConversion($leadId);
            
            // 2. 准备客户数据
            $preparedCustomerData = $this->prepareCustomerData($lead, $customerData);
            
            // 3. 创建客户记录
            $customer = $this->createCustomer($preparedCustomerData);
            
            // 4. 创建联系人记录
            $contact = $this->createContact($customer['id'], $lead, $contactData);
            
            // 5. 更新线索状态
            $this->updateLeadStatus($leadId, $customer['id']);
            
            // 6. 记录转化历史
            $this->recordConversionHistory($leadId, $customer['id'], $contact['id']);
            
            // 提交事务
            Db::commit();
            
            // 7. 发送转化通知 (异步)
            $this->sendConversionNotification($lead, $customer, $contact);
            
            return [
                'success' => true,
                'message' => '线索转化成功',
                'data' => [
                    'customer_id' => $customer['id'],
                    'contact_id' => $contact['id'],
                    'customer_name' => $customer['customer_name'],
                    'contact_name' => $contact['contact_name']
                ]
            ];
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            // 记录错误日志
            Log::error('线索转化失败', [
                'lead_id' => $leadId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new BusinessException('线索转化失败：' . $e->getMessage());
        }
    }
    
    /**
     * 验证线索是否可以转化
     * 
     * @param int $leadId 线索ID
     * @return array 线索数据
     * @throws BusinessException
     */
    private function validateLeadForConversion(int $leadId): array
    {
        $leadModel = new CrmLead();
        $lead = $leadModel->where('id', $leadId)
            ->where('tenant_id', $this->getTenantId())
            ->findOrEmpty();
        
        if ($lead->isEmpty()) {
            throw new BusinessException('线索不存在或无权限访问');
        }
        
        $leadData = $lead->toArray();
        
        // 检查线索状态
        if ($leadData['status'] == 0) {
            throw new BusinessException('线索已禁用，无法转化');
        }
        
        // 检查是否已转化
        if (!empty($leadData['customer_id'])) {
            throw new BusinessException('线索已转化为客户，无法重复转化');
        }
        
        // 检查必填字段
        $requiredFields = ['lead_name', 'phone'];
        foreach ($requiredFields as $field) {
            if (empty($leadData[$field])) {
                throw new BusinessException("线索信息不完整，缺少：{$field}");
            }
        }
        
        return $leadData;
    }
    
    /**
     * 准备客户数据
     * 
     * @param array $lead 线索数据
     * @param array $customerData 额外客户数据
     * @return array 准备好的客户数据
     */
    private function prepareCustomerData(array $lead, array $customerData): array
    {
        $defaultData = [
            'customer_name' => $lead['lead_name'],
            'customer_type' => $customerData['customer_type'] ?? 1, // 默认个人客户
            'industry' => $lead['industry'] ?? '',
            'source' => $lead['source'] ?? '',
            'phone' => $lead['phone'],
            'email' => $lead['email'] ?? '',
            'address' => $lead['address'] ?? '',
            'description' => $lead['description'] ?? '',
            'status' => 1, // 启用状态
            'owner_id' => $lead['owner_id'],
            'tenant_id' => $lead['tenant_id'],
            'created_id' => $this->getAdminId(),
            'updated_id' => $this->getAdminId()
        ];
        
        // 合并用户提供的额外数据
        return array_merge($defaultData, $customerData);
    }
    
    /**
     * 创建客户记录
     * 
     * @param array $customerData 客户数据
     * @return array 创建的客户数据
     * @throws BusinessException
     */
    private function createCustomer(array $customerData): array
    {
        // ✅ 优化：使用直接模型实例化，避免单例状态污染

        // 检查客户名称是否重复
        $existingCustomer = \app\crm\model\CrmCustomer::where('customer_name', $customerData['customer_name'])
            ->where('tenant_id', $this->getTenantId())
            ->findOrEmpty();

        if (!$existingCustomer->isEmpty()) {
            throw new BusinessException('客户名称已存在，请修改后重试');
        }

        // 创建客户 - 使用直接模型实例化
        $customerModel = new \app\crm\model\CrmCustomer();
        $customerId = $customerModel->saveByCreate($customerData);

        if (!$customerId) {
            throw new BusinessException('创建客户失败');
        }

        // 获取创建的客户详情
        $createdCustomer = \app\crm\model\CrmCustomer::where('id', $customerId)->findOrEmpty();
        if ($createdCustomer->isEmpty()) {
            throw new BusinessException('获取客户详情失败');
        }

        return $createdCustomer->toArray();
    }
    
    /**
     * 创建联系人记录
     * 
     * @param int $customerId 客户ID
     * @param array $lead 线索数据
     * @param array $contactData 额外联系人数据
     * @return array 创建的联系人数据
     */
    private function createContact(int $customerId, array $lead, array $contactData): array
    {
        // ✅ 优化：使用直接模型实例化，避免单例状态污染

        $defaultContactData = [
            'customer_id' => $customerId,
            'contact_name' => $lead['contact_name'] ?? $lead['lead_name'],
            'gender' => $contactData['gender'] ?? 1, // 默认男性
            'phone' => $lead['phone'],
            'email' => $lead['email'] ?? '',
            'wechat' => $lead['wechat'] ?? '',
            'qq' => $lead['qq'] ?? '',
            'position' => $contactData['position'] ?? '',
            'is_primary' => 1, // 设为主联系人
            'status' => 1,
            'tenant_id' => $lead['tenant_id'],
            'created_id' => $this->getAdminId(),
            'updated_id' => $this->getAdminId()
        ];

        // 合并用户提供的额外数据
        $finalContactData = array_merge($defaultContactData, $contactData);

        // 创建联系人 - 使用直接模型实例化
        $contactModel = new \app\crm\model\CrmContact();
        $contactId = $contactModel->saveByCreate($finalContactData);

        if (!$contactId) {
            throw new BusinessException('创建联系人失败');
        }

        // 获取创建的联系人详情
        $createdContact = \app\crm\model\CrmContact::where('id', $contactId)->findOrEmpty();
        if ($createdContact->isEmpty()) {
            throw new BusinessException('获取联系人详情失败');
        }

        return $createdContact->toArray();
    }
    
    /**
     * 更新线索状态
     * 
     * @param int $leadId 线索ID
     * @param int $customerId 客户ID
     */
    private function updateLeadStatus(int $leadId, int $customerId): void
    {
        // ✅ 优化：使用直接模型更新，避免单例状态污染

        $leadModel = \app\crm\model\CrmLead::where('id', $leadId)->findOrEmpty();
        if (!$leadModel->isEmpty()) {
            $leadModel->saveByUpdate([
                'customer_id' => $customerId,
                'is_converted' => 1,
                'converted_at' => date('Y-m-d H:i:s'),
                'updated_id' => $this->getAdminId()
            ]);
        }
    }
    
    /**
     * 记录转化历史
     * 
     * @param int $leadId 线索ID
     * @param int $customerId 客户ID
     * @param int $contactId 联系人ID
     */
    private function recordConversionHistory(int $leadId, int $customerId, int $contactId): void
    {
        // 这里可以创建一个转化历史表来记录详细信息
        // 暂时使用日志记录
        Log::info('线索转化成功', [
            'lead_id' => $leadId,
            'customer_id' => $customerId,
            'contact_id' => $contactId,
            'operator_id' => $this->getAdminId(),
            'tenant_id' => $this->getTenantId(),
            'converted_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 发送转化通知
     * 
     * @param array $lead 线索数据
     * @param array $customer 客户数据
     * @param array $contact 联系人数据
     */
    private function sendConversionNotification(array $lead, array $customer, array $contact): void
    {
        try {
            // 这里集成消息通知系统
            // 暂时使用日志记录，后续集成NotificationService
            Log::info('发送线索转化通知', [
                'lead_name' => $lead['lead_name'],
                'customer_name' => $customer['customer_name'],
                'contact_name' => $contact['contact_name'],
                'operator_id' => $this->getAdminId()
            ]);
        } catch (\Exception $e) {
            // 通知发送失败不影响主业务
            Log::error('线索转化通知发送失败', [
                'error' => $e->getMessage(),
                'lead_id' => $lead['id']
            ]);
        }
    }
    
    /**
     * 检查线索转化条件
     * 
     * @param int $leadId 线索ID
     * @return array 检查结果
     */
    public function checkConversionConditions(int $leadId): array
    {
        try {
            $lead = $this->validateLeadForConversion($leadId);
            
            return [
                'can_convert' => true,
                'message' => '线索可以转化',
                'lead_info' => [
                    'lead_name' => $lead['lead_name'],
                    'phone' => $lead['phone'],
                    'email' => $lead['email'] ?? '',
                    'source' => $lead['source'] ?? ''
                ]
            ];
            
        } catch (BusinessException $e) {
            return [
                'can_convert' => false,
                'message' => $e->getMessage(),
                'lead_info' => []
            ];
        }
    }
}
