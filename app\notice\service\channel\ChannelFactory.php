<?php
declare(strict_types=1);

namespace app\notice\service\channel;

use app\notice\service\interfaces\ChannelServiceInterface;
use think\facade\Log;

/**
 * 通道工厂类
 */
class ChannelFactory
{
    /**
     * 通道服务实例缓存
     * @var array
     */
    protected static array $instances = [];
    
    /**
     * 通道类型映射
     * @var array
     */
    protected static array $channelMap = [
        'site' => SiteChannelService::class,
        'email' => EmailChannelService::class,
        'sms' => SmsChannelService::class,
        'wework' => WeworkChannelService::class,
        'dingtalk' => DingtalkChannelService::class,
        'webhook' => WebhookChannelService::class,
    ];
    
    /**
     * 获取通道服务实例
     *
     * @param string $channel 通道类型
     * @return ChannelServiceInterface|null 通道服务实例
     */
    public static function getChannel(string $channel): ?ChannelServiceInterface
    {
        if (!isset(self::$channelMap[$channel])) {
            Log::error("未知的通道类型: {$channel}");
            return null;
        }
        
        if (!isset(self::$instances[$channel])) {
            $className = self::$channelMap[$channel];
            
            // 检查类是否存在
            if (!class_exists($className)) {
                Log::warning("通道类未实现: {$className}");
                return null;
            }
            
            self::$instances[$channel] = new $className();
        }
        
        return self::$instances[$channel];
    }
    
    /**
     * 获取所有支持的通道类型
     *
     * @return array 通道类型列表
     */
    public static function getSupportedChannels(): array
    {
        return array_keys(self::$channelMap);
    }
    
    /**
     * 获取通道名称映射
     * 
     * @return array 通道名称映射
     */
    public static function getChannelNames(): array
    {
        return [
            'site' => '站内信',
            'email' => '电子邮件',
            'sms' => '短信',
            'wework' => '企业微信',
            'dingtalk' => '钉钉',
            'webhook' => '自定义Webhook',
        ];
    }
} 