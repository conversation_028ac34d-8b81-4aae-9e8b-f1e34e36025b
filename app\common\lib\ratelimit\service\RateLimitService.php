<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\service;

use app\common\lib\ratelimit\RateLimiter;
use think\Service;

/**
 * 限流服务注册
 */
class RateLimitService extends Service
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register(): void
    {
        // 注册单例
        $this->app->bind('ratelimit', function () {
            return new RateLimiter();
        });
    }
    
    /**
     * 服务启动
     *
     * @return void
     */
    public function boot(): void
    {
        // 注册中间件
        $this->registerMiddleware();
    }
    
    /**
     * 注册中间件
     *
     * @return void
     */
    protected function registerMiddleware(): void
    {
        // 全局中间件注册，如果需要全局启用可以取消注释
        // $this->app->middleware->add(\app\common\lib\ratelimit\middleware\RateLimitMiddleware::class);
    }
} 