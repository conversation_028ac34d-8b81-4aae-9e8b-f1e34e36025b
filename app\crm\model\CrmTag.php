<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 标签表模型
 */
class CrmTag extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_tag';
	
	
	public function getAllowUpdateFields()
	{
		return [
			'sort'
		];
	}
	
	// 获取允许排序的字段
	public function getAllowSortFields()
	{
		return [
			'id',
			'created_at',
			'sort'
		];
	}
	
	// 获取验证规则
	public function getValidateRules()
	{
		return [
			'tag_name'  => 'require|length:3,30',
			'tag_type'  => 'require|in:customer,lead,contact,business',
			'tag_color' => 'length:7',
			'sort'      => 'number'
		];
	}
}