<?php
declare(strict_types=1);

namespace app\ims\model;

use app\common\core\base\BaseModel;

/**
 * 入库明细表模型
 */
class ImsInboundItem extends BaseModel
{
    // 设置表名
    protected $name = 'ims_inbound_item';
    
    // 设置主键
    protected $pk = 'id';
    
    // 字段类型转换
    protected $type = [
        'inbound_id' => 'integer',
        'product_id' => 'integer',
        'product_spec_id' => 'integer',
        'quantity' => 'float',
        'unit_price' => 'float',
        'total_amount' => 'float',
    ];
    
    /**
     * 获取默认搜索字段
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'inbound_id' => ['type' => 'eq'],
            'product_id' => ['type' => 'eq'],
            'product_spec_id' => ['type' => 'eq'],
            'quantity' => ['type' => 'between'],
            'unit_price' => ['type' => 'between'],
            'total_amount' => ['type' => 'between'],
            'created_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 关联入库申请
     */
    public function inbound()
    {
        return $this->belongsTo(ImsInboundApproval::class, 'inbound_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo(\app\ims\model\ImsProduct::class, 'product_id', 'id')->bind([
            'product_name' => 'name',
            'product_code' => 'code',
            'product_unit' => 'unit'
        ]);
    }
    
    /**
     * 关联产品规格
     */
    public function productSpec()
    {
        return $this->belongsTo(\app\ims\model\ImsProductSpec::class, 'product_spec_id', 'id')->bind([
            'spec_name' => 'name'
        ]);
    }
}
