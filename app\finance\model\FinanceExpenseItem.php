<?php
declare(strict_types=1);

namespace app\finance\model;

use app\common\core\base\BaseModel;

/**
 * 报销明细表模型
 */
class FinanceExpenseItem extends BaseModel
{
    // 设置表名
    protected $name = 'finance_expense_item';
    
    // 设置主键
    protected $pk = 'id';
    
    // 字段类型转换
    protected $type = [
        'reimbursement_id' => 'integer',
    ];
    
    /**
     * 获取默认搜索字段
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'reimbursement_id' => ['type' => 'eq'],
            'description' => ['type' => 'like'],
            'created_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 关联报销申请
     */
    public function reimbursement()
    {
        return $this->belongsTo(FinanceExpenseReimbursement::class, 'reimbursement_id', 'id');
    }
}
