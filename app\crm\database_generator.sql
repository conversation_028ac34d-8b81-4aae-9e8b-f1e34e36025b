-- CRM系统数据库设计 - 生成器可用版本
-- 基于多租户架构，优化版本：添加软删除、优化字段类型、完善索引、添加外键约束
-- 所有代码生成在 app/crm 目录内
--
-- 生成器使用说明：
-- 1. 执行SQL创建表结构
-- 2. 按顺序执行生成器命令：php think generator:crud 表名 --module=crm --frontend --overwrite
-- 3. 在system_menu表中添加相应的菜单路由
-- 4. 使用MCP测试功能验证生成的代码
-- 5. 确认无误后继续下一个表的生成

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 标签表 - 支持客户、线索、联系人、商机标签管理
-- 生成器命令: php think generator:crud crm_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_tag`;
CREATE TABLE IF NOT EXISTS `crm_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `tag_name`   varchar(30)         NOT NULL DEFAULT '' COMMENT '标签名称 @required @max:30 @search:like',
    `tag_type`   varchar(20)         NOT NULL DEFAULT '' COMMENT '标签类型:customer=客户标签,lead=线索标签,contact=联系人标签,business=商机标签 @required @search:eq @component:tag',
    `tag_color`  varchar(20)         NOT NULL DEFAULT '' COMMENT '标签颜色 @component:color',
    `sort`       int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序 @number',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_type` (`tenant_id`, `tag_type`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='标签表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 线索表 - 线索管理核心表
-- 生成器命令: php think generator:crud crm_lead --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead`;
CREATE TABLE IF NOT EXISTS `crm_lead`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '线索ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `lead_name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '线索姓名 @required @max:50 @search:like @exp @imp',
    `company`          varchar(200)        NOT NULL DEFAULT '' COMMENT '公司名称 @max:200 @search:like @exp @imp',
    `position`         varchar(50)         NOT NULL DEFAULT '' COMMENT '职位 @max:50 @exp @imp',
    `mobile`           varchar(20)         NOT NULL DEFAULT '' COMMENT '手机号 @max:20 @search:like @exp @imp',
    `phone`            varchar(20)         NOT NULL DEFAULT '' COMMENT '电话 @max:20 @exp @imp',
    `email`            varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱 @email @max:100 @search:like @exp @imp',
    `status`           tinyint(1)          NOT NULL DEFAULT 1 COMMENT '状态:0=无效,1=未跟进,2=跟进中,3=已转化,4=已失效 @required @search:eq @component:tag @exp @imp',
    `source`           varchar(50)         NOT NULL DEFAULT '' COMMENT '线索来源 @max:50 @search:eq @exp @imp',
    `level`            tinyint(1)          NOT NULL DEFAULT 0 COMMENT '线索级别:0=未知,1=低,2=中,3=高 @search:eq @component:tag @exp @imp',
    `industry`         varchar(50)         NOT NULL DEFAULT '' COMMENT '所属行业 @max:50 @search:eq @exp @imp',
    `address`          text COMMENT '地址 @form:textarea @exp @imp',
    `remark`           text COMMENT '备注 @form:textarea',
    `is_transformed`   tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已转化:0=未转化,1=已转化 @component:switch @search:eq @exp',
    `transformed_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '转化后的客户ID',
    `transformed_time` datetime                     DEFAULT NULL COMMENT '转化时间 @search:date @exp',
    `owner_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `last_followed_at` datetime                     DEFAULT NULL COMMENT '最后跟进时间 @search:date @exp',
    `next_followed_at` datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date @exp',
    `in_pool`          tinyint(1)          NOT NULL DEFAULT 1 COMMENT '是否在线索池:0=否,1=是 @component:switch @search:eq',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_pool` (`tenant_id`, `in_pool`),
    KEY `idx_follow_time` (`tenant_id`, `next_followed_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 线索与标签关联表
-- 生成器命令: php think generator:crud crm_lead_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_tag`;
CREATE TABLE IF NOT EXISTS `crm_lead_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `lead_id`    bigint(20) UNSIGNED NOT NULL COMMENT '线索ID @required @search:eq',
    `tag_id`     bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_lead_tag` (`lead_id`, `tag_id`),
    KEY `idx_tenant_lead` (`tenant_id`, `lead_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 线索跟进记录表
-- 生成器命令: php think generator:crud crm_lead_follow --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_follow`;
CREATE TABLE IF NOT EXISTS `crm_lead_follow`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `lead_id`          bigint(20) UNSIGNED NOT NULL COMMENT '线索ID @required @search:eq',
    `content`          text COMMENT '跟进内容 @required @form:textarea',
    `follow_type`      varchar(20)         NOT NULL DEFAULT '' COMMENT '跟进方式:phone=电话,email=邮件,visit=拜访,wechat=微信 @search:eq @component:tag',
    `follow_result`    text COMMENT '跟进结果 @max:255 @form:textarea',
    `next_follow_time` datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_lead` (`tenant_id`, `lead_id`),
    KEY `idx_tenant_created` (`tenant_id`, `created_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索跟进记录表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 线索分配记录表
-- 生成器命令: php think generator:crud crm_lead_assignment --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_assignment`;
CREATE TABLE IF NOT EXISTS `crm_lead_assignment`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `lead_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '线索ID @required @search:eq',
    `from_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '原负责人ID @search:eq',
    `to_user_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '新负责人ID @required @search:eq',
    `assignment_type` tinyint(1) UNSIGNED NOT NULL COMMENT '分配类型:1=手动分配,2=自动分配,3=认领,4=抢占 @required @search:eq @component:tag',
    `reason`          text COMMENT '分配原因 @max:255 @form:textarea',
    `created_by`      bigint(20) UNSIGNED NOT NULL COMMENT '操作人ID @required',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_lead` (`tenant_id`, `lead_id`),
    KEY `idx_tenant_to_user` (`tenant_id`, `to_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索分配记录表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户表 - 客户管理核心表
-- 生成器命令: php think generator:crud crm_customer --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer`;
CREATE TABLE IF NOT EXISTS `crm_customer`
(
    `id`                 bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '客户ID',
    `tenant_id`          bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_name`      varchar(200)            NOT NULL DEFAULT '' COMMENT '客户名称/公司名称 @required @max:200 @search:like @exp @imp',
    `industry`           varchar(50)             NOT NULL DEFAULT '' COMMENT '所属行业 @max:50 @search:eq @exp @imp',
    `level`              tinyint(1)              NOT NULL DEFAULT 1 COMMENT '客户级别:1=普通,2=重要,3=战略 @search:eq @component:tag @exp @imp',
    `source`             varchar(50)             NOT NULL DEFAULT '' COMMENT '客户来源 @max:50 @search:eq @exp @imp',
    `phone`              varchar(20)             NOT NULL DEFAULT '' COMMENT '电话 @max:20 @search:like @exp @imp',
    `website`            varchar(100)            NOT NULL DEFAULT '' COMMENT '网站 @url @max:100 @component:link @exp @imp',
    `region_province`    varchar(30)             NOT NULL DEFAULT '' COMMENT '省份 @max:30 @search:eq @exp @imp',
    `region_city`        varchar(30)             NOT NULL DEFAULT '' COMMENT '城市 @max:30 @search:eq @exp @imp',
    `region_district`    varchar(30)             NOT NULL DEFAULT '' COMMENT '区/县 @max:30 @exp @imp',
    `address`            text COMMENT '详细地址 @form:textarea @exp @imp',
    `zip_code`           varchar(20)             NOT NULL DEFAULT '' COMMENT '邮政编码 @max:20 @exp @imp',
    `remark`             text COMMENT '备注 @form:textarea',
    `owner_user_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `status`             tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '客户状态:0=停用,1=正常 @component:switch @search:eq @exp @imp',
    `in_sea`             tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '是否在公海:0=否,1=是 @component:switch @search:eq @exp',
    `into_sea_time`      datetime                         DEFAULT NULL COMMENT '进入公海时间 @search:date @exp',
    `sea_id`             bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '所属公海ID @search:eq',
    `credit_code`        varchar(50)             NOT NULL DEFAULT '' COMMENT '统一社会信用代码 @max:50 @search:like @exp @imp',
    `annual_revenue`     decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '年营业额 @number @component:currency @search:between @exp @imp',
    `employee_count`     int(11)                 NOT NULL DEFAULT 0 COMMENT '员工人数 @number @search:between @exp @imp',
    `registered_capital` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '注册资本 @number @component:currency @search:between @exp @imp',
    `last_followed_at`   datetime                         DEFAULT NULL COMMENT '最后跟进时间 @search:date @exp',
    `next_followed_at`   datetime                         DEFAULT NULL COMMENT '下次跟进时间 @search:date @exp',
    `creator_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`         datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`         datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `lock_status`        tinyint(1)              NOT NULL DEFAULT 0 COMMENT '锁定状态:0=未锁定,1=已锁定 @component:switch @search:eq',
    `lock_expire_time`   datetime                         DEFAULT NULL COMMENT '锁定到期时间 @search:date',
    `deleted_at`         datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`, `in_sea`),
    KEY `idx_tenant_sea` (`tenant_id`, `sea_id`),
    KEY `idx_follow_time` (`tenant_id`, `next_followed_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户与标签关联表
-- 生成器命令: php think generator:crud crm_customer_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_tag`;
CREATE TABLE IF NOT EXISTS `crm_customer_tag`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL COMMENT '客户ID @required @search:eq',
    `tag_id`      bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_customer_tag` (`customer_id`, `tag_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 联系人表 - 客户联系人管理
-- 生成器命令: php think generator:crud crm_contact --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact`;
CREATE TABLE IF NOT EXISTS `crm_contact`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '联系人姓名 @required @max:50 @search:like @exp @imp',
    `gender`      tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别:0=未知,1=男,2=女 @search:eq @component:tag @exp @imp',
    `position`    varchar(50)         NOT NULL DEFAULT '' COMMENT '职位 @max:50 @search:like @exp @imp',
    `department`  varchar(50)         NOT NULL DEFAULT '' COMMENT '部门 @max:50 @search:like @exp @imp',
    `mobile`      varchar(20)         NOT NULL DEFAULT '' COMMENT '手机号 @max:20 @search:like @exp @imp',
    `phone`       varchar(20)         NOT NULL DEFAULT '' COMMENT '电话 @max:20 @exp @imp',
    `email`       varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱 @email @max:100 @search:like @exp @imp',
    `wechat`      varchar(50)         NOT NULL DEFAULT '' COMMENT '微信 @max:50 @exp @imp',
    `qq`          varchar(20)         NOT NULL DEFAULT '' COMMENT 'QQ @max:20 @exp @imp',
    `importance`  tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '重要程度:0=普通,1=重要,2=核心 @search:eq @component:tag @exp @imp',
    `role_type`   varchar(20)         NOT NULL DEFAULT '' COMMENT '角色类型:decision=决策者,user=使用者,influence=影响者 @search:eq @component:tag @exp @imp',
    `birthday`    date                         DEFAULT NULL COMMENT '生日 @search:date @exp @imp',
    `address`     text COMMENT '地址 @form:textarea @exp @imp',
    `remark`      text COMMENT '备注 @form:textarea',
    `is_primary`  tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为主要联系人:0=否,1=是 @component:switch @search:eq @exp @imp',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_mobile` (`tenant_id`, `mobile`),
    KEY `idx_tenant_primary` (`tenant_id`, `is_primary`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='联系人表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 联系人与标签关联表
-- 生成器命令: php think generator:crud crm_contact_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact_tag`;
CREATE TABLE IF NOT EXISTS `crm_contact_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `contact_id` bigint(20) UNSIGNED NOT NULL COMMENT '联系人ID @required @search:eq',
    `tag_id`     bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_contact_tag` (`contact_id`, `tag_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_tag_id` (`tag_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='联系人与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户跟进记录表
-- 生成器命令: php think generator:crud crm_customer_follow --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_follow`;
CREATE TABLE IF NOT EXISTS `crm_customer_follow`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq',
    `contact_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '联系人ID @search:eq',
    `content`          text COMMENT '跟进内容 @required @form:textarea',
    `follow_type`      varchar(20)         NOT NULL DEFAULT '' COMMENT '跟进方式:phone=电话,email=邮件,visit=拜访,wechat=微信 @search:eq @component:tag',
    `follow_result`    varchar(255)        NOT NULL DEFAULT '' COMMENT '跟进结果 @max:255 @form:textarea',
    `next_follow_time` datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date',
    `images`           text COMMENT '图片附件（JSON格式，存储路径） @component:image',
    `files`            text COMMENT '其他附件（JSON格式，存储路径和说明） @component:file',
    `mentioned_users`  text COMMENT '@提及的用户（JSON格式，用户ID列表）',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_contact_id` (`contact_id`),
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户跟进记录表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户分配记录表
-- 生成器命令: php think generator:crud crm_customer_assignment --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_assignment`;
CREATE TABLE IF NOT EXISTS `crm_customer_assignment`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq',
    `from_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '原负责人ID @search:eq',
    `to_user_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '新负责人ID @required @search:eq',
    `assignment_type` tinyint(1) UNSIGNED NOT NULL COMMENT '分配类型:1=手动分配,2=自动分配,3=认领,4=抢占 @required @search:eq @component:tag',
    `reason`          text COMMENT '分配原因 @max:255 @form:textarea',
    `created_by`      bigint(20) UNSIGNED NOT NULL COMMENT '操作人ID @required',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_to_user_id` (`to_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户分配记录表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户共享表
-- 生成器命令: php think generator:crud crm_customer_share --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_share`;
CREATE TABLE IF NOT EXISTS `crm_customer_share`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq',
    `owner_user_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户主负责人ID @required @search:eq',
    `user_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '被共享用户ID @required @search:eq',
    `share_scope`    tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '共享范围:1=只读,2=读写 @search:eq @component:tag',
    `share_deadline` date                         DEFAULT NULL COMMENT '共享截止日期 @search:date',
    `creator_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `deleted_at`     datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户共享表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户公海表
-- 生成器命令: php think generator:crud crm_customer_sea --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_sea`;
CREATE TABLE IF NOT EXISTS `crm_customer_sea`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '公海ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `sea_name`    varchar(50)         NOT NULL DEFAULT '' COMMENT '公海名称 @required @max:50 @search:like',
    `description` varchar(255)        NOT NULL DEFAULT '' COMMENT '公海描述 @max:255 @form:textarea',
    `dept_scope`  text COMMENT '部门适用范围（JSON格式，部门ID列表）',
    `role_scope`  text COMMENT '角色适用范围（JSON格式，角色ID列表）',
    `status`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户公海表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 公海规则表
-- 生成器命令: php think generator:crud crm_sea_rule --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_sea_rule`;
CREATE TABLE IF NOT EXISTS `crm_sea_rule`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规则ID',
    `tenant_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `sea_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '公海ID @required @search:eq',
    `rule_name`      varchar(50)         NOT NULL DEFAULT '' COMMENT '规则名称 @required @max:50 @search:like',
    `rule_type`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '规则类型:1=未跟进,2=未成交 @required @search:eq @component:tag',
    `follow_days`    int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '未跟进天数 @number',
    `deal_days`      int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '未成交天数 @number',
    `customer_level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '适用的客户级别:0=全部,1=普通,2=重要,3=战略 @search:eq @component:tag',
    `enable_notify`  tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用提醒:0=否,1=是 @component:switch',
    `notify_days`    int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '提前提醒天数 @number',
    `status`         tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq',
    `creator_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_sea_id` (`sea_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='公海规则表 @module:crm @exp:true @imp:true';

-- -------------------------------------------------------------------------
-- 商机管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 商机表 - 商机管理核心表
-- 生成器命令: php think generator:crud crm_business --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business`;
CREATE TABLE IF NOT EXISTS `crm_business`
(
    `id`               bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '商机ID',
    `tenant_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `business_name`    varchar(200)            NOT NULL DEFAULT '' COMMENT '商机名称 @required @max:200 @search:like @exp @imp',
    `customer_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `contact_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '联系人ID @search:eq @exp @imp',
    `amount`           decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商机金额 @number @component:currency @search:between @exp @imp',
    `discount_rate`    decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%) @number @search:between @exp @imp',
    `status`           tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '状态:1=跟进中,2=赢单,3=输单,4=无效 @required @search:eq @component:tag @exp @imp',
    `stage_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '当前阶段ID @search:eq @exp @imp',
    `source`           varchar(50)             NOT NULL DEFAULT '' COMMENT '商机来源 @max:50 @search:eq @exp @imp',
    `type`             varchar(50)             NOT NULL DEFAULT '' COMMENT '商机类型 @max:50 @search:eq @exp @imp',
    `priority`         tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '优先级:0=普通,1=重要,2=紧急 @search:eq @component:tag @exp @imp',
    `expect_date`      date                             DEFAULT NULL COMMENT '预计成交日期 @search:date @exp @imp',
    `closed_date`      date                             DEFAULT NULL COMMENT '实际成交日期 @search:date @exp @imp',
    `lost_reason`      varchar(255)            NOT NULL DEFAULT '' COMMENT '输单原因 @max:255 @form:textarea @exp',
    `competitor`       varchar(100)            NOT NULL DEFAULT '' COMMENT '主要竞争对手 @max:100 @search:like @exp @imp',
    `remark`           text COMMENT '备注 @form:textarea',
    `owner_user_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `last_followed_at` datetime                         DEFAULT NULL COMMENT '最后跟进时间 @search:date @exp',
    `next_followed_at` datetime                         DEFAULT NULL COMMENT '下次跟进时间 @search:date @exp',
    `creator_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`       datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_status_stage` (`tenant_id`, `status`, `stage_id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_expect_date` (`tenant_id`, `expect_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机与标签关联表
-- 生成器命令: php think generator:crud crm_business_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_tag`;
CREATE TABLE IF NOT EXISTS `crm_business_tag`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `business_id` bigint(20) UNSIGNED NOT NULL COMMENT '商机ID @required @search:eq',
    `tag_id`      bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_business_tag` (`business_id`, `tag_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_tag_id` (`tag_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机阶段表
-- 生成器命令: php think generator:crud crm_business_stage --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_stage`;
CREATE TABLE IF NOT EXISTS `crm_business_stage`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '阶段ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `stage_name`  varchar(50)         NOT NULL DEFAULT '' COMMENT '阶段名称 @required @max:50 @search:like',
    `order_num`   int(11) UNSIGNED    NOT NULL COMMENT '排序号 @required @number',
    `win_rate`    decimal(5, 2)       NOT NULL DEFAULT 0 COMMENT '赢单率(%) @number',
    `description` text COMMENT '描述 @max:255 @form:textarea',
    `status`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机阶段表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机阶段记录表
-- 生成器命令: php think generator:crud crm_business_stage_record --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_stage_record`;
CREATE TABLE IF NOT EXISTS `crm_business_stage_record`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `business_id` bigint(20) UNSIGNED NOT NULL COMMENT '商机ID @required @search:eq',
    `stage_id`    bigint(20) UNSIGNED NOT NULL COMMENT '阶段ID @required @search:eq',
    `remark`      text COMMENT '备注 @max:255 @form:textarea',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_stage_id` (`stage_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机阶段记录表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机产品关联表
-- 生成器命令: php think generator:crud crm_business_product --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_product`;
CREATE TABLE IF NOT EXISTS `crm_business_product`
(
    `id`            bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `business_id`   bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '商机ID @required @search:eq',
    `product_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq',
    `price`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价 @number @component:currency',
    `quantity`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '数量 @number',
    `discount`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣 @number @component:currency',
    `discount_rate` decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%) @number',
    `subtotal`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额 @number @component:currency',
    `order_num`     int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '排序 @number',
    `remark`        text COMMENT '备注 @max:255 @form:textarea',
    `creator_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`    datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`    datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机产品关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机跟进记录表
-- 生成器命令: php think generator:crud crm_business_follow --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_follow`;
CREATE TABLE IF NOT EXISTS `crm_business_follow`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `business_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商机ID @required @search:eq',
    `content`          text COMMENT '跟进内容 @required @form:textarea',
    `follow_type`      varchar(20)         NOT NULL DEFAULT '' COMMENT '跟进方式（如电话、邮件、拜访等） @max:20 @search:eq',
    `follow_result`    text COMMENT '跟进结果 @max:255 @form:textarea',
    `next_follow_time` datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date',
    `images`           text COMMENT '图片附件（JSON格式，存储路径） @component:upload',
    `files`            text COMMENT '其他附件（JSON格式，存储路径和说明） @component:upload',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机跟进记录表 @module:crm @exp:true @imp:true';

-- -------------------------------------------------------------------------
-- 报价单管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 报价单表
-- 生成器命令: php think generator:crud crm_quotation --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_quotation`;
CREATE TABLE IF NOT EXISTS `crm_quotation`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '报价单ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `quotation_no`    varchar(30)             NOT NULL DEFAULT '' COMMENT '报价单编号 @required @max:30 @search:like @exp @imp',
    `title`           varchar(100)            NOT NULL DEFAULT '' COMMENT '报价单标题 @required @max:100 @search:like @exp @imp',
    `customer_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `contact_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '联系人ID @search:eq @exp @imp',
    `business_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联商机ID @search:eq @exp @imp',
    `total_amount`    decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '报价总金额 @number @component:currency @search:between @exp @imp',
    `discount_rate`   decimal(5, 2) UNSIGNED           DEFAULT 100.00 COMMENT '整单折扣率(%) @number @search:between @exp @imp',
    `discount_amount` decimal(15, 2) UNSIGNED          DEFAULT 0.00 COMMENT '折扣金额 @number @component:currency @search:between @exp @imp',
    `tax_rate`        decimal(5, 2) UNSIGNED           DEFAULT 0.00 COMMENT '税率(%) @number @search:between @exp @imp',
    `tax_amount`      decimal(15, 2) UNSIGNED          DEFAULT 0.00 COMMENT '税额 @number @component:currency @search:between @exp @imp',
    `final_amount`    decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最终金额 @number @component:currency @search:between @exp @imp',
    `owner_user_id`   bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `status`          tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '状态:0=草稿,1=审批中,2=已审批,3=已发送,4=已转化,5=已作废 @search:eq @component:tag @exp @imp',
    `valid_days`      int(11) UNSIGNED        NOT NULL DEFAULT 30 COMMENT '有效天数 @number @exp @imp',
    `expire_date`     date                             DEFAULT NULL COMMENT '过期日期 @search:date @exp @imp',
    `remark`          text COMMENT '备注 @form:textarea',
    `send_time`       datetime                         DEFAULT NULL COMMENT '发送时间 @search:date @exp',
    `viewed_time`     datetime                         DEFAULT NULL COMMENT '客户查看时间 @search:date @exp',
    `viewed_count`    int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '客户查看次数 @number @exp',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_quotation_no` (`quotation_no`, `tenant_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_expire_date` (`tenant_id`, `expire_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 报价单产品明细表
-- 生成器命令: php think generator:crud crm_quotation_product --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_quotation_product`;
CREATE TABLE IF NOT EXISTS `crm_quotation_product`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `quotation_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '报价单ID @required @search:eq @exp @imp',
    `product_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    `price`           decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价 @required @number @component:currency @search:between @exp @imp',
    `quantity`        decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '数量 @required @number @search:between @exp @imp',
    `discount`        decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣 @number @component:currency @search:between @exp @imp',
    `discount_rate`   decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%) @number @search:between @exp @imp',
    `tax_rate`        decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 0.00 COMMENT '税率(%) @number @search:between @exp @imp',
    `subtotal`        decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额（不含税） @number @component:currency @search:between @exp @imp',
    `tax_amount`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '税额 @number @component:currency @search:between @exp @imp',
    `amount_with_tax` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '含税金额 @number @component:currency @search:between @exp @imp',
    `remark`          text COMMENT '备注 @max:255 @form:textarea',
    `order_num`       int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '排序 @number @exp @imp',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_quotation_id` (`quotation_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单产品明细表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 报价单模板表
-- 生成器命令: php think generator:crud crm_quotation_template --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_quotation_template`;
CREATE TABLE IF NOT EXISTS `crm_quotation_template`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `tenant_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `template_name` varchar(50)         NOT NULL DEFAULT '' COMMENT '模板名称 @required @max:50 @search:like @exp @imp',
    `template_code` varchar(30)         NOT NULL DEFAULT '' COMMENT '模板编码 @max:30 @search:like @exp @imp',
    `content`       text COMMENT '模板内容（HTML格式） @form:editor',
    `type`          tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '模板类型:0=普通,1=系统 @search:eq @component:tag @exp @imp',
    `status`        tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `remark`        text COMMENT '备注 @max:255 @form:textarea',
    `creator_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单模板表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 报价单审批记录表
-- 生成器命令: php think generator:crud crm_quotation_approval --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_quotation_approval`;
CREATE TABLE IF NOT EXISTS `crm_quotation_approval`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `quotation_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '报价单ID @required @search:eq @exp @imp',
    `node_name`       varchar(50)         NOT NULL DEFAULT '' COMMENT '审批节点名称 @max:50 @search:like @exp @imp',
    `approve_user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审批人ID @required @search:eq @exp',
    `status`          tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态:0=待审批,1=通过,2=驳回 @required @search:eq @component:tag @exp @imp',
    `opinion`         TEXT COMMENT '审批意见 @max:255 @form:textarea @exp',
    `approval_time`   datetime                     DEFAULT NULL COMMENT '审批时间 @search:date @exp',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_quotation_id` (`quotation_id`),
    KEY `idx_approve_user_id` (`approve_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单审批记录表 @module:crm @exp:true @imp:true';

-- -------------------------------------------------------------------------
-- 合同管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 合同表
-- 生成器命令: php think generator:crud crm_contract --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_contract`;
CREATE TABLE IF NOT EXISTS `crm_contract`
(
    `id`                bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '合同ID',
    `tenant_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_no`       varchar(30)             NOT NULL DEFAULT '' COMMENT '合同编号 @required @max:30 @search:like @exp @imp',
    `contract_name`     varchar(100)            NOT NULL DEFAULT '' COMMENT '合同名称 @required @max:100 @search:like @exp @imp',
    `customer_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `contact_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '联系人ID @search:eq @exp @imp',
    `business_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联商机ID @search:eq @exp @imp',
    `quotation_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联报价单ID @search:eq @exp @imp',
    `contract_amount`   decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '合同金额 @required @number @component:currency @search:between @exp @imp',
    `received_amount`   decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '已收款金额 @number @component:currency @search:between @exp',
    `unreceived_amount` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '未收款金额 @number @component:currency @search:between @exp',
    `owner_user_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `status`            tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '状态:0=草稿,1=审批中,2=已审批,3=执行中,4=已完成,5=已作废 @search:eq @component:tag @exp @imp',
    `contract_type`     varchar(30)             NOT NULL DEFAULT '' COMMENT '合同类型 @max:30 @search:eq @exp @imp',
    `start_date`        date                             DEFAULT NULL COMMENT '合同开始日期 @search:date @exp @imp',
    `end_date`          date                             DEFAULT NULL COMMENT '合同结束日期 @search:date @exp @imp',
    `sign_date`         date                             DEFAULT NULL COMMENT '签约日期 @search:date @exp @imp',
    `payment_terms`     text COMMENT '付款条件 @form:textarea @exp',
    `delivery_terms`    text COMMENT '交货条件 @form:textarea @exp',
    `contract_content`  text COMMENT '合同内容 @form:editor',
    `remark`            text COMMENT '备注 @form:textarea',
    `creator_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`        datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`        datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_contract_no` (`contract_no`, `tenant_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 合同产品明细表
-- 生成器命令: php think generator:crud crm_contract_product --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_contract_product`;
CREATE TABLE IF NOT EXISTS `crm_contract_product`
(
    `id`            bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id`   bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '合同ID @required @search:eq @exp @imp',
    `product_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    `price`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价 @required @number @component:currency @search:between @exp @imp',
    `quantity`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '数量 @required @number @search:between @exp @imp',
    `discount`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣 @number @component:currency @search:between @exp @imp',
    `discount_rate` decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%) @number @search:between @exp @imp',
    `subtotal`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额 @number @component:currency @search:between @exp @imp',
    `order_num`     int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '排序 @number @exp @imp',
    `remark`        text COMMENT '备注 @max:255 @form:textarea',
    `creator_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`    datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`    datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同产品明细表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 合同附件表
-- 生成器命令: php think generator:crud crm_contract_attachment --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_contract_attachment`;
CREATE TABLE IF NOT EXISTS `crm_contract_attachment`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合同ID @required @search:eq @exp @imp',
    `file_name`   varchar(100)        NOT NULL DEFAULT '' COMMENT '文件名 @required @max:100 @search:like @exp @imp',
    `file_path`   varchar(255)        NOT NULL DEFAULT '' COMMENT '文件路径 @required @max:255 @component:document @exp',
    `file_size`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小（字节） @number @exp',
    `file_type`   varchar(50)         NOT NULL DEFAULT '' COMMENT '文件类型 @max:50 @search:eq @exp @imp',
    `remark`      text COMMENT '备注 @max:255 @form:textarea',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同附件表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 合同审批记录表
-- 生成器命令: php think generator:crud crm_contract_approval --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_contract_approval`;
CREATE TABLE IF NOT EXISTS `crm_contract_approval`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合同ID @required @search:eq @exp @imp',
    `node_name`       varchar(50)         NOT NULL DEFAULT '' COMMENT '审批节点名称 @max:50 @search:like @exp @imp',
    `approve_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '审批人ID @required @search:eq @exp',
    `status`          tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态:0=待审批,1=通过,2=驳回 @required @search:eq @component:tag @exp @imp',
    `opinion`         varchar(255)                 DEFAULT NULL COMMENT '审批意见 @max:255 @form:textarea @exp',
    `approval_time`   datetime                     DEFAULT NULL COMMENT '审批时间 @search:date @exp',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_approve_user_id` (`approve_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同审批记录表 @module:crm @exp:true @imp:true';

-- -------------------------------------------------------------------------
-- 回款管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 回款计划表
-- 生成器命令: php think generator:crud crm_payment_plan --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_payment_plan`;
CREATE TABLE IF NOT EXISTS `crm_payment_plan`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '计划ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '合同ID @required @search:eq @exp @imp',
    `customer_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `stage`           varchar(50)             NOT NULL DEFAULT '' COMMENT '期次（如第一期、第二期） @max:50 @search:like @exp @imp',
    `expect_amount`   decimal(15, 2)          NOT NULL DEFAULT 0.00 COMMENT '计划回款金额（支持退款负数） @number @component:currency @search:between @exp @imp',
    `expect_date`     date                             DEFAULT NULL COMMENT '计划回款日期 @required @search:date @exp @imp',
    `remark`          text COMMENT '备注 @max:255 @form:textarea',
    `received_amount` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '已回款金额 @number @component:currency @search:between @exp',
    `status`          tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '状态:0=未回款,1=部分回款,2=已回款,3=逾期 @search:eq @component:tag @exp @imp',
    `remind_days`     int(11) UNSIGNED        NOT NULL DEFAULT 3 COMMENT '提前提醒天数 @number @exp @imp',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_expect_date` (`expect_date`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='回款计划表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 回款记录表
-- 生成器命令: php think generator:crud crm_payment_record --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_payment_record`;
CREATE TABLE IF NOT EXISTS `crm_payment_record`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `payment_no`      varchar(30)             NOT NULL DEFAULT '' COMMENT '回款编号 @required @max:30 @search:like @exp @imp',
    `contract_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '合同ID @required @search:eq @exp @imp',
    `customer_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `plan_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联的回款计划ID @search:eq @exp @imp',
    `amount`          decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '回款金额 @required @number @component:currency @search:between @exp @imp',
    `payment_date`    date                             DEFAULT NULL COMMENT '回款日期 @required @search:date @exp @imp',
    `payment_method`  varchar(30)             NOT NULL DEFAULT '' COMMENT '回款方式 @max:30 @search:eq @exp @imp',
    `voucher_path`    text COMMENT '回款凭证路径 @max:255 @component:document @exp',
    `bank_name`       varchar(100)            NOT NULL DEFAULT '' COMMENT '收款银行名称 @max:100 @search:like @exp @imp',
    `bank_account`    varchar(50)             NOT NULL DEFAULT '' COMMENT '收款银行账号 @max:50 @search:like @exp @imp',
    `remark`          text COMMENT '备注 @form:textarea',
    `status`          tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '状态:0=未确认,1=已确认,2=已作废 @search:eq @component:tag @exp @imp',
    `confirm_user_id` bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '确认人ID @search:eq @exp',
    `confirm_time`    datetime                         DEFAULT NULL COMMENT '确认时间 @search:date @exp',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_payment_no` (`payment_no`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_payment_date` (`payment_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='回款记录表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 回款附件表
-- 生成器命令: php think generator:crud crm_payment_attachment --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_payment_attachment`;
CREATE TABLE IF NOT EXISTS `crm_payment_attachment`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `record_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '回款记录ID @required @search:eq @exp @imp',
    `file_name`  varchar(100)        NOT NULL DEFAULT '' COMMENT '文件名 @required @max:100 @search:like @exp @imp',
    `file_path`  varchar(255)        NOT NULL DEFAULT '' COMMENT '文件路径 @required @max:255 @component:document @exp',
    `file_size`  bigint(20)          NOT NULL DEFAULT 0 COMMENT '文件大小（字节） @number @exp',
    `file_type`  text COMMENT '文件类型 @max:50 @search:eq @exp @imp',
    `remark`     text COMMENT '备注 @max:255 @form:textarea',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_record_id` (`record_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='回款附件表 @module:crm @exp:true @imp:true';

-- -------------------------------------------------------------------------
-- 产品管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 产品表
-- 生成器命令: php think generator:crud crm_product --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_product`;
CREATE TABLE IF NOT EXISTS `crm_product`
(
    `id`            bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '产品ID',
    `tenant_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `product_name`  varchar(100)            NOT NULL DEFAULT '' COMMENT '产品名称 @required @max:100 @search:like @exp @imp',
    `product_code`  varchar(50)             NOT NULL DEFAULT '' COMMENT '产品编码 @max:50 @search:like @exp @imp',
    `category_id`   bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品分类ID @search:eq @exp @imp',
    `unit_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '单位ID @search:eq @exp @imp',
    `price`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '标准价格 @number @component:currency @search:between @exp @imp',
    `cost`          decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价格 @number @component:currency @search:between @exp @imp',
    `description`   text COMMENT '产品描述 @form:textarea @exp @imp',
    `specification` text COMMENT '产品规格 @form:textarea @exp @imp',
    `status`        tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`    datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`    datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_code` (`tenant_id`, `product_code`),
    KEY `idx_tenant_category` (`tenant_id`, `category_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 产品分类表
-- 生成器命令: php think generator:crud crm_product_category --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_product_category`;
CREATE TABLE IF NOT EXISTS `crm_product_category`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `tenant_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `parent_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级分类ID @search:eq @exp @imp',
    `category_name` varchar(50)         NOT NULL DEFAULT '' COMMENT '分类名称 @required @max:50 @search:like @exp @imp',
    `category_code` varchar(30)         NOT NULL DEFAULT '' COMMENT '分类编码 @max:30 @search:like @exp @imp',
    `description`   text COMMENT '分类描述 @max:255 @form:textarea @exp @imp',
    `sort`          int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序 @number @exp @imp',
    `status`        tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_parent` (`tenant_id`, `parent_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品分类表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 产品单位表
-- 生成器命令: php think generator:crud crm_product_unit --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_product_unit`;
CREATE TABLE IF NOT EXISTS `crm_product_unit`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '单位ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `unit_name`  varchar(20)         NOT NULL DEFAULT '' COMMENT '单位名称 @required @max:20 @search:like @exp @imp',
    `unit_code`  varchar(10)         NOT NULL DEFAULT '' COMMENT '单位编码 @max:10 @search:like @exp @imp',
    `remark`     text COMMENT '备注 @max:255 @form:textarea',
    `status`     tinyint(1)          NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品单位表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 产品价格策略表
-- 生成器命令: php think generator:crud crm_product_price --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_product_price`;
CREATE TABLE IF NOT EXISTS `crm_product_price`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '价格策略ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `product_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    `strategy_name`   varchar(50)             NOT NULL DEFAULT '' COMMENT '策略名称 @required @max:50 @search:like @exp @imp',
    `price`           decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格 @required @number @component:currency @search:between @exp @imp',
    `strategy_type`   tinyint(1)              NOT NULL DEFAULT 1 COMMENT '策略类型:1=客户等级,2=客户分类,3=客户标签,4=特定客户,5=区域,6=季节 @required @search:eq @component:select @exp @imp',
    `strategy_target` varchar(50)             NOT NULL DEFAULT '' COMMENT '策略目标 @max:50 @search:like @exp @imp',
    `target_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '目标ID @number @search:eq @exp @imp',
    `status`          tinyint(1)              NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @required @search:eq @component:switch @exp @imp',
    `start_date`      date                             DEFAULT NULL COMMENT '生效开始日期 @component:date @search:between @exp @imp',
    `end_date`        date                             DEFAULT NULL COMMENT '生效结束日期 @component:date @search:between @exp @imp',
    `remark`          text COMMENT '备注 @max:255 @component:textarea @exp @imp',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_product` (`tenant_id`, `product_id`),
    KEY `idx_tenant_strategy` (`tenant_id`, `strategy_type`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_date_range` (`start_date`, `end_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品价格策略表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 产品规格表
-- 生成器命令: php think generator:crud crm_product_spec --module=crm --frontend --overwrite
-- ----------------------------
drop table IF EXISTS `crm_product_spec`;
CREATE TABLE IF NOT EXISTS `crm_product_spec`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规格ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `product_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    `spec_name`        varchar(50)         NOT NULL COMMENT '规格名称 @required @max:50 @search:like @exp @imp',
    `spec_value`       varchar(100)        NOT NULL COMMENT '规格值 @required @max:100 @search:like @exp @imp',
    `spec_code`        varchar(50)         NOT NULL DEFAULT '' COMMENT '规格编码 @max:50 @search:like @exp @imp',
    `price_adjustment` decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '价格调整（支持负数降价） @number @component:currency @search:between @exp @imp',
    `adjustment_type`  tinyint(1)          NOT NULL DEFAULT 0 COMMENT '调整类型:0=固定值,1=百分比 @required @search:eq @component:radio @exp @imp',
    `inventory`        int(11)             NOT NULL DEFAULT 0 COMMENT '库存 @number @search:between @exp @imp',
    `images`           text COMMENT '规格图片 @component:image @exp @imp',
    `order_num`        int(11)             NOT NULL DEFAULT 0 COMMENT '排序号 @number @search:between @exp @imp',
    `status`           tinyint(1)          NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @required @search:eq @component:switch @exp @imp',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_product` (`tenant_id`, `product_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_spec_code` (`spec_code`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品规格表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 外键约束设置
-- ----------------------------
SET FOREIGN_KEY_CHECKS = 1;

-- 添加外键约束（可选，根据需要启用）
-- ALTER TABLE `crm_lead_tag` ADD CONSTRAINT `fk_lead_tag_lead` FOREIGN KEY (`lead_id`) REFERENCES `crm_lead` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_lead_tag` ADD CONSTRAINT `fk_lead_tag_tag` FOREIGN KEY (`tag_id`) REFERENCES `crm_tag` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_customer_tag` ADD CONSTRAINT `fk_customer_tag_customer` FOREIGN KEY (`customer_id`) REFERENCES `crm_customer` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_customer_tag` ADD CONSTRAINT `fk_customer_tag_tag` FOREIGN KEY (`tag_id`) REFERENCES `crm_tag` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_contact` ADD CONSTRAINT `fk_contact_customer` FOREIGN KEY (`customer_id`) REFERENCES `crm_customer` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_business` ADD CONSTRAINT `fk_business_customer` FOREIGN KEY (`customer_id`) REFERENCES `crm_customer` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_quotation` ADD CONSTRAINT `fk_quotation_customer` FOREIGN KEY (`customer_id`) REFERENCES `crm_customer` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_contract` ADD CONSTRAINT `fk_contract_customer` FOREIGN KEY (`customer_id`) REFERENCES `crm_customer` (`id`) ON DELETE CASCADE;

-- 生成器使用说明：
-- 1. 执行以上SQL语句创建数据表
-- 2. 使用生成器命令生成对应的控制器、模型和视图文件
-- 3. 在菜单数据表中添加相应的路由配置
-- 4. 使用MCP测试功能验证生成的代码
-- 5. 确认无误后继续下一个模块的生成

-- 生成器命令示例（按顺序执行）：
-- php think generator:crud crm_tag --module=crm --frontend --overwrite
-- php think generator:crud crm_lead --module=crm --frontend --overwrite
-- php think generator:crud crm_lead_tag --module=crm --frontend --overwrite
-- php think generator:crud crm_lead_follow --module=crm --frontend --overwrite
-- php think generator:crud crm_lead_assignment --module=crm --frontend --overwrite
-- php think generator:crud crm_customer --module=crm --frontend --overwrite
-- php think generator:crud crm_customer_tag --module=crm --frontend --overwrite
-- php think generator:crud crm_contact --module=crm --frontend --overwrite
-- php think generator:crud crm_contact_tag --module=crm --frontend --overwrite
-- php think generator:crud crm_customer_follow --module=crm --frontend --overwrite
-- php think generator:crud crm_customer_assignment --module=crm --frontend --overwrite
-- php think generator:crud crm_customer_share --module=crm --frontend --overwrite
-- php think generator:crud crm_customer_sea --module=crm --frontend --overwrite
-- php think generator:crud crm_sea_rule --module=crm --frontend --overwrite
-- php think generator:crud crm_business --module=crm --frontend --overwrite
-- php think generator:crud crm_business_tag --module=crm --frontend --overwrite
-- php think generator:crud crm_business_stage --module=crm --frontend --overwrite
-- php think generator:crud crm_business_stage_record --module=crm --frontend --overwrite
-- php think generator:crud crm_business_product --module=crm --frontend --overwrite
-- php think generator:crud crm_business_follow --module=crm --frontend --overwrite
-- php think generator:crud crm_quotation --module=crm --frontend --overwrite
-- php think generator:crud crm_quotation_product --module=crm --frontend --overwrite
-- php think generator:crud crm_quotation_template --module=crm --frontend --overwrite
-- php think generator:crud crm_quotation_approval --module=crm --frontend --overwrite
-- php think generator:crud crm_contract --module=crm --frontend --overwrite
-- php think generator:crud crm_contract_product --module=crm --frontend --overwrite
-- php think generator:crud crm_contract_attachment --module=crm --frontend --overwrite
-- php think generator:crud crm_contract_approval --module=crm --frontend --overwrite
-- php think generator:crud crm_payment_plan --module=crm --frontend --overwrite
-- php think generator:crud crm_payment_record --module=crm --frontend --overwrite
-- php think generator:crud crm_payment_attachment --module=crm --frontend --overwrite
-- php think generator:crud crm_product --module=crm --frontend --overwrite
-- php think generator:crud crm_product_category --module=crm --frontend --overwrite
-- php think generator:crud crm_product_unit --module=crm --frontend --overwrite
-- php think generator:crud crm_product_price --module=crm --frontend --overwrite
-- php think generator:crud crm_product_spec --module=crm --frontend --overwrite

-- 注意：
-- 1. 所有表都已添加了完整的CRUD生成器注释
-- 2. 包含了报价单模板、审批记录、合同附件、回款记录等完整功能
-- 3. 字段类型已优化：decimal(15,2)用于金额，varchar长度合理化
-- 4. 添加了软删除支持和完整的索引优化
-- 5. 支持多租户架构，所有表都包含tenant_id字段
-- 6. 金额字段类型说明：
--    - UNSIGNED类型：用于不会为负数的金额（如商机金额、合同金额、回款金额等）
--    - 非UNSIGNED类型：用于可能为负数的金额（如价格调整、计划回款等）
