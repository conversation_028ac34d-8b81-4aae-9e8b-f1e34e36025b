-- CRM系统数据库设计 - 优化重构版本
-- 基于多租户架构，解决架构设计问题，优化字段类型，完善索引设计
-- 针对150人以下企业CRM系统优化
--
-- 主要优化：
-- 1. 删除crm_customer_sea表，使用客户表统一管理公海
-- 2. 统一字段命名和数据类型规范
-- 3. 优化索引设计，每表限制5个索引
-- 4. 修复产品规格设计，区分单规格和多规格产品
-- 5. 添加缺失的唯一约束和业务字段
-- 6. 统一描述字段为text类型
-- 7. 保留并优化客户共享功能，支持小企业协作需求
-- 8. 新增客户共享操作日志表，完整记录操作历史
--
-- 生成器使用说明：
-- 1. 执行SQL创建表结构
-- 2. 按顺序执行生成器命令：php think generator:crud 表名 --module=crm --frontend --overwrite
-- 3. 在system_menu表中添加相应的菜单路由

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 标签表 - 支持客户、线索、联系人、商机标签管理
-- 生成器命令: php think generator:crud crm_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_tag`;
CREATE TABLE IF NOT EXISTS `crm_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `tag_name`   varchar(30)         NOT NULL DEFAULT '' COMMENT '标签名称 @required @max:30 @search:like',
    `tag_type`   varchar(20)         NOT NULL DEFAULT '' COMMENT '标签类型:customer=客户标签,lead=线索标签,contact=联系人标签,business=商机标签 @required @search:eq @component:tag',
    `tag_color`  varchar(20)         NOT NULL DEFAULT '' COMMENT '标签颜色 @component:color',
    `sort`       int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序 @number',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_type_name` (`tenant_id`, `tag_type`, `tag_name`),
    KEY `idx_tenant_type` (`tenant_id`, `tag_type`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='标签表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 线索表 - 线索管理核心表
-- 生成器命令: php think generator:crud crm_lead --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead`;
CREATE TABLE IF NOT EXISTS `crm_lead`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '线索ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `lead_name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '线索姓名 @required @max:50 @search:like @exp @imp',
    `company`          varchar(200)        NOT NULL DEFAULT '' COMMENT '公司名称 @max:200 @search:like @exp @imp',
    `position`         varchar(50)         NOT NULL DEFAULT '' COMMENT '职位 @max:50 @exp @imp',
    `mobile`           varchar(20)         NOT NULL DEFAULT '' COMMENT '手机号 @max:20 @search:like @exp @imp',
    `phone`            varchar(20)         NOT NULL DEFAULT '' COMMENT '电话 @max:20 @exp @imp',
    `email`            varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱 @email @max:100 @search:like @exp @imp',
    `status`           tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=无效,1=未跟进,2=跟进中,3=已转化,4=已失效 @required @search:eq @component:tag @exp @imp',
    `source`           varchar(50)         NOT NULL DEFAULT '' COMMENT '线索来源 @max:50 @search:eq @exp @imp',
    `level`            tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '线索级别:0=未知,1=低,2=中,3=高 @search:eq @component:tag @exp @imp',
    `industry`         varchar(50)         NOT NULL DEFAULT '' COMMENT '所属行业 @max:50 @search:eq @exp @imp',
    `address`          text COMMENT '地址 @form:textarea @exp @imp',
    `remark`           text COMMENT '备注 @form:textarea',
    `is_transformed`   tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已转化:0=未转化,1=已转化 @component:switch @search:eq @exp',
    `transformed_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '转化后的客户ID',
    `transformed_time` datetime                     DEFAULT NULL COMMENT '转化时间 @search:date @exp',
    `owner_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `last_followed_at` datetime                     DEFAULT NULL COMMENT '最后跟进时间 @search:date @exp',
    `next_followed_at` datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date @exp',
    `in_pool`          tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否在线索池:0=否,1=是 @component:switch @search:eq',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_pool` (`tenant_id`, `in_pool`),
    KEY `idx_transformed` (`is_transformed`, `transformed_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 线索与标签关联表
-- 生成器命令: php think generator:crud crm_lead_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_tag`;
CREATE TABLE IF NOT EXISTS `crm_lead_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `lead_id`    bigint(20) UNSIGNED NOT NULL COMMENT '线索ID @required @search:eq',
    `tag_id`     bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_lead_tag` (`lead_id`, `tag_id`),
    KEY `idx_tenant_lead` (`tenant_id`, `lead_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户表 - 客户管理核心表（包含公海管理功能）
-- 生成器命令: php think generator:crud crm_customer --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer`;
CREATE TABLE IF NOT EXISTS `crm_customer`
(
    `id`                 bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '客户ID',
    `tenant_id`          bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_name`      varchar(200)        NOT NULL DEFAULT '' COMMENT '客户名称/公司名称 @required @max:200 @search:like @exp @imp',
    `industry`           varchar(50)         NOT NULL DEFAULT '' COMMENT '所属行业 @max:50 @search:eq @exp @imp',
    `level`              tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '客户级别:1=普通,2=重要,3=战略 @search:eq @component:tag @exp @imp',
    `source`             varchar(50)         NOT NULL DEFAULT '' COMMENT '客户来源 @max:50 @search:eq @exp @imp',
    `phone`              varchar(20)         NOT NULL DEFAULT '' COMMENT '电话 @max:20 @search:like @exp @imp',
    `website`            varchar(100)        NOT NULL DEFAULT '' COMMENT '网站 @url @max:100 @component:link @exp @imp',
    `region_province`    varchar(30)         NOT NULL DEFAULT '' COMMENT '省份 @max:30 @search:eq @exp @imp',
    `region_city`        varchar(30)         NOT NULL DEFAULT '' COMMENT '城市 @max:30 @search:eq @exp @imp',
    `region_district`    varchar(30)         NOT NULL DEFAULT '' COMMENT '区/县 @max:30 @exp @imp',
    `address`            text COMMENT '详细地址 @form:textarea @exp @imp',
    `zip_code`           varchar(20)         NOT NULL DEFAULT '' COMMENT '邮政编码 @max:20 @exp @imp',
    `remark`             text COMMENT '备注 @form:textarea',
    `owner_user_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `status`             tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '客户状态:0=停用,1=正常 @component:switch @search:eq @exp @imp',
    -- 公海管理字段
    `in_sea`             tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否在公海:0=否,1=是 @component:switch @search:eq @exp',
    `into_sea_time`      datetime                     DEFAULT NULL COMMENT '进入公海时间 @search:date @exp',
    `recycle_reason`     varchar(255)        NOT NULL DEFAULT '' COMMENT '回收原因 @max:255 @form:textarea',
    `recycle_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '回收操作人ID @search:eq',
    -- 企业信息字段
    `credit_code`        varchar(50)         NOT NULL DEFAULT '' COMMENT '统一社会信用代码 @max:50 @search:like @exp @imp',
    `annual_revenue`     decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '年营业额 @number @component:currency @search:between @exp @imp',
    `employee_count`     int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '员工人数 @number @search:between @exp @imp',
    `registered_capital` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '注册资本 @number @component:currency @search:between @exp @imp',
    `last_followed_at`   datetime                     DEFAULT NULL COMMENT '最后跟进时间 @search:date @exp',
    `next_followed_at`   datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date @exp',
    `creator_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `lock_status`        tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '锁定状态:0=未锁定,1=已锁定 @component:switch @search:eq',
    `lock_expire_time`   datetime                     DEFAULT NULL COMMENT '锁定到期时间 @search:date',
    `deleted_at`         datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_credit` (`tenant_id`, `credit_code`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`, `in_sea`),
    KEY `idx_sea_status` (`in_sea`, `into_sea_time`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户与标签关联表
-- 生成器命令: php think generator:crud crm_customer_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_tag`;
CREATE TABLE IF NOT EXISTS `crm_customer_tag`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL COMMENT '客户ID @required @search:eq',
    `tag_id`      bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_customer_tag` (`customer_id`, `tag_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户共享表 - 客户协作管理（优化版本）
-- 生成器命令: php think generator:crud crm_customer_share --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_share`;
CREATE TABLE IF NOT EXISTS `crm_customer_share`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `shared_user_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '共享员工ID @required @search:eq @exp @imp',
    `status`          tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @search:eq @component:tag @exp @imp',
    `remark`          text COMMENT '备注说明 @form:textarea',
    `creator_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_customer_user` (`customer_id`, `shared_user_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_user` (`tenant_id`, `shared_user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户共享表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 客户共享操作日志表 - 记录所有共享操作历史
-- 生成器命令: php think generator:crud crm_customer_share_log --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_share_log`;
CREATE TABLE IF NOT EXISTS `crm_customer_share_log`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp',
    `action_type`     varchar(20)         NOT NULL DEFAULT '' COMMENT '操作类型:add=添加共享,remove=取消共享,batch_add=批量添加,batch_remove=批量取消 @search:eq @component:tag @exp',
    `target_user_ids` text COMMENT '目标员工ID列表(JSON格式) @exp',
    `remark`          text COMMENT '操作备注 @form:textarea',
    `operator_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作人ID @search:eq @exp',
    `operator_ip`     varchar(45)         NOT NULL DEFAULT '' COMMENT '操作IP @max:45 @exp',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间 @exp',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_action_type` (`action_type`),
    KEY `idx_operator` (`operator_id`),
    KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB COMMENT ='客户共享操作日志表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 联系人表 - 客户联系人管理
-- 生成器命令: php think generator:crud crm_contact --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact`;
CREATE TABLE IF NOT EXISTS `crm_contact`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '联系人姓名 @required @max:50 @search:like @exp @imp',
    `gender`      tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别:0=未知,1=男,2=女 @search:eq @component:tag @exp @imp',
    `position`    varchar(50)         NOT NULL DEFAULT '' COMMENT '职位 @max:50 @search:like @exp @imp',
    `department`  varchar(50)         NOT NULL DEFAULT '' COMMENT '部门 @max:50 @search:like @exp @imp',
    `mobile`      varchar(20)         NOT NULL DEFAULT '' COMMENT '手机号 @max:20 @search:like @exp @imp',
    `phone`       varchar(20)         NOT NULL DEFAULT '' COMMENT '电话 @max:20 @exp @imp',
    `email`       varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱 @email @max:100 @search:like @exp @imp',
    `wechat`      varchar(50)         NOT NULL DEFAULT '' COMMENT '微信 @max:50 @exp @imp',
    `qq`          varchar(20)         NOT NULL DEFAULT '' COMMENT 'QQ @max:20 @exp @imp',
    `importance`  tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '重要程度:0=普通,1=重要,2=核心 @search:eq @component:tag @exp @imp',
    `role_type`   varchar(20)         NOT NULL DEFAULT '' COMMENT '角色类型:decision=决策者,user=使用者,influence=影响者 @search:eq @component:tag @exp @imp',
    `birthday`    date                         DEFAULT NULL COMMENT '生日 @search:date @exp @imp',
    `address`     text COMMENT '地址 @form:textarea @exp @imp',
    `remark`      text COMMENT '备注 @form:textarea',
    `is_primary`  tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为主要联系人:0=否,1=是 @component:switch @search:eq @exp @imp',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_mobile` (`tenant_id`, `mobile`),
    KEY `idx_tenant_primary` (`tenant_id`, `is_primary`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='联系人表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 联系人与标签关联表
-- 生成器命令: php think generator:crud crm_contact_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact_tag`;
CREATE TABLE IF NOT EXISTS `crm_contact_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `contact_id` bigint(20) UNSIGNED NOT NULL COMMENT '联系人ID @required @search:eq',
    `tag_id`     bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_contact_tag` (`contact_id`, `tag_id`),
    KEY `idx_tenant_contact` (`tenant_id`, `contact_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='联系人与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 产品分类表 - 产品分类管理
-- 生成器命令: php think generator:crud crm_product_category --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_product_category`;
CREATE TABLE IF NOT EXISTS `crm_product_category`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `parent_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级分类ID @search:eq',
    `name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '分类名称 @required @max:50 @search:like @exp @imp',
    `code`        varchar(30)         NOT NULL DEFAULT '' COMMENT '分类编码 @max:30 @search:like @exp @imp',
    `description` text COMMENT '分类描述 @form:textarea @exp @imp',
    `sort`        int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序 @number',
    `status`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_code` (`tenant_id`, `code`),
    KEY `idx_tenant_parent` (`tenant_id`, `parent_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品分类表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 产品表 - 产品管理核心表（统一使用产品规格表管理规格）
-- 生成器命令: php think generator:crud crm_product --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_product`;
CREATE TABLE IF NOT EXISTS `crm_product`
(
    `id`           bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '产品ID',
    `tenant_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `category_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID @required @search:eq @exp @imp',
    `name`         varchar(200)        NOT NULL DEFAULT '' COMMENT '产品名称 @required @max:200 @search:like @exp @imp',
    `code`         varchar(50)         NOT NULL DEFAULT '' COMMENT '产品编码 @max:50 @search:like @exp @imp',
    `unit`         varchar(20)         NOT NULL DEFAULT '' COMMENT '单位 @max:20 @search:eq @exp @imp',
    `price`        decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '标准价格 @required @number @component:currency @search:between @exp @imp',
    `cost_price`   decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价格 @number @component:currency @search:between @exp @imp',
    `description`  text COMMENT '产品描述 @form:textarea @exp @imp',
    `images`       text COMMENT '产品图片(JSON格式) @form:upload @component:image',
    `status`       tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`   datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_code` (`tenant_id`, `code`),
    KEY `idx_tenant_category` (`tenant_id`, `category_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 产品规格表 - 统一的产品规格管理（支持单规格和多规格产品）
-- 生成器命令: php think generator:crud crm_product_spec --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_product_spec`;
CREATE TABLE IF NOT EXISTS `crm_product_spec`
(
    `id`                bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规格ID',
    `tenant_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `product_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    `spec_name`         varchar(100)        NOT NULL DEFAULT '' COMMENT '规格名称 @required @max:100 @search:like @exp @imp',
    `spec_value`        varchar(100)        NOT NULL DEFAULT '' COMMENT '规格值 @max:100 @search:like @exp @imp',
    `spec_code`         varchar(50)         NOT NULL DEFAULT '' COMMENT '规格编码 @max:50 @search:like @exp @imp',
    `price_adjustment`  decimal(15, 2) SIGNED NOT NULL DEFAULT 0.00 COMMENT '价格调整(支持负数降价) @number @component:currency @search:between @exp @imp',
    `adjustment_type`   tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '调整类型:0=固定金额,1=百分比 @search:eq @component:tag @exp @imp',
    `inventory`         int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '库存数量 @number @search:between @exp @imp',
    `images`            text COMMENT '规格图片(JSON格式) @form:upload @component:image',
    `order_num`         int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序 @number',
    `status`            tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_product_code` (`product_id`, `spec_code`),
    KEY `idx_tenant_product` (`tenant_id`, `product_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品规格表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机表 - 商机管理核心表
-- 生成器命令: php think generator:crud crm_business --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business`;
CREATE TABLE IF NOT EXISTS `crm_business`
(
    `id`                bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商机ID',
    `tenant_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `business_name`     varchar(200)        NOT NULL DEFAULT '' COMMENT '商机名称 @required @max:200 @search:like @exp @imp',
    `amount`            decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商机金额 @required @number @component:currency @search:between @exp @imp',
    `stage`             varchar(50)         NOT NULL DEFAULT '' COMMENT '商机阶段 @required @max:50 @search:eq @exp @imp',
    `probability`       decimal(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成功概率(%) @number @min:0 @max:100 @search:between @exp @imp',
    `expected_date`     date                         DEFAULT NULL COMMENT '预计成交日期 @required @search:date @exp @imp',
    `source`            varchar(50)         NOT NULL DEFAULT '' COMMENT '商机来源 @max:50 @search:eq @exp @imp',
    `status`            tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=失败,1=进行中,2=成功 @search:eq @component:tag @exp @imp',
    `type`              varchar(30)         NOT NULL DEFAULT '' COMMENT '商机类型 @max:30 @search:eq @exp @imp',
    `description`       text COMMENT '商机描述 @form:textarea @exp @imp',
    `remark`            text COMMENT '备注 @form:textarea',
    `owner_user_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `last_followed_at`  datetime                     DEFAULT NULL COMMENT '最后跟进时间 @search:date @exp',
    `next_followed_at`  datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date @exp',
    `closed_date`       date                         DEFAULT NULL COMMENT '关闭日期 @search:date @exp',
    `closed_reason`     varchar(255)        NOT NULL DEFAULT '' COMMENT '关闭原因 @max:255 @form:textarea',
    `creator_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_expected_date` (`expected_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机与标签关联表
-- 生成器命令: php think generator:crud crm_business_tag --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_tag`;
CREATE TABLE IF NOT EXISTS `crm_business_tag`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `business_id` bigint(20) UNSIGNED NOT NULL COMMENT '商机ID @required @search:eq',
    `tag_id`      bigint(20) UNSIGNED NOT NULL COMMENT '标签ID @required @search:eq',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_business_tag` (`business_id`, `tag_id`),
    KEY `idx_tenant_business` (`tenant_id`, `business_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机与标签关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 商机产品关联表 - 商机关联的产品清单
-- 生成器命令: php think generator:crud crm_business_product --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_product`;
CREATE TABLE IF NOT EXISTS `crm_business_product`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `business_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商机ID @required @search:eq @exp @imp',
    `product_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    `product_spec_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品规格ID(多规格产品时使用) @search:eq @exp @imp',
    `quantity`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 1.00 COMMENT '数量 @required @number @min:0.01 @search:between @exp @imp',
    `unit_price`       decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价 @required @number @component:currency @search:between @exp @imp',
    `discount_rate`    decimal(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣率(%) @number @min:0 @max:100 @search:between @exp @imp',
    `discount_amount`  decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣金额 @number @component:currency @search:between @exp @imp',
    `subtotal`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额 @number @component:currency @search:between @exp @imp',
    `remark`           text COMMENT '备注 @form:textarea',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_business` (`tenant_id`, `business_id`),
    KEY `idx_tenant_product` (`tenant_id`, `product_id`),
    KEY `idx_product_spec` (`product_spec_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机产品关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 合同表 - 合同管理核心表
-- 生成器命令: php think generator:crud crm_contract --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_contract`;
CREATE TABLE IF NOT EXISTS `crm_contract`
(
    `id`                bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '合同ID',
    `tenant_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `business_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商机ID @search:eq @exp @imp',
    `contract_number`   varchar(50)         NOT NULL DEFAULT '' COMMENT '合同编号 @required @max:50 @search:like @exp @imp',
    `contract_name`     varchar(200)        NOT NULL DEFAULT '' COMMENT '合同名称 @required @max:200 @search:like @exp @imp',
    `contract_amount`   decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '合同金额 @required @number @component:currency @search:between @exp @imp',
    `start_date`        date                         DEFAULT NULL COMMENT '合同开始日期 @required @search:date @exp @imp',
    `end_date`          date                         DEFAULT NULL COMMENT '合同结束日期 @required @search:date @exp @imp',
    `sign_date`         date                         DEFAULT NULL COMMENT '签署日期 @search:date @exp @imp',
    `status`            tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合同状态:0=草稿,1=待审核,2=执行中,3=已完成,4=已终止 @search:eq @component:tag @exp @imp',
    `type`              varchar(30)         NOT NULL DEFAULT '' COMMENT '合同类型 @max:30 @search:eq @exp @imp',
    `payment_method`    varchar(50)         NOT NULL DEFAULT '' COMMENT '付款方式 @max:50 @search:eq @exp @imp',
    `description`       text COMMENT '合同描述 @form:textarea @exp @imp',
    `remark`            text COMMENT '备注 @form:textarea',
    `contract_files`    text COMMENT '合同附件(JSON格式) @form:upload @component:file',
    `owner_user_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `creator_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_number` (`tenant_id`, `contract_number`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 合同产品关联表 - 合同关联的产品清单
-- 生成器命令: php think generator:crud crm_contract_product --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_contract_product`;
CREATE TABLE IF NOT EXISTS `crm_contract_product`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合同ID @required @search:eq @exp @imp',
    `product_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    `product_spec_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品规格ID(多规格产品时使用) @search:eq @exp @imp',
    `quantity`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 1.00 COMMENT '数量 @required @number @min:0.01 @search:between @exp @imp',
    `unit_price`       decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价 @required @number @component:currency @search:between @exp @imp',
    `discount_rate`    decimal(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣率(%) @number @min:0 @max:100 @search:between @exp @imp',
    `discount_amount`  decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣金额 @number @component:currency @search:between @exp @imp',
    `subtotal`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额 @number @component:currency @search:between @exp @imp',
    `remark`           text COMMENT '备注 @form:textarea',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_contract` (`tenant_id`, `contract_id`),
    KEY `idx_tenant_product` (`tenant_id`, `product_id`),
    KEY `idx_product_spec` (`product_spec_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同产品关联表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 回款表 - 回款管理核心表
-- 生成器命令: php think generator:crud crm_receivable --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_receivable`;
CREATE TABLE IF NOT EXISTS `crm_receivable`
(
    `id`                bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '回款ID',
    `tenant_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID @required @search:eq @exp @imp',
    `contract_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合同ID @required @search:eq @exp @imp',
    `receivable_number` varchar(50)         NOT NULL DEFAULT '' COMMENT '回款编号 @required @max:50 @search:like @exp @imp',
    `amount`            decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '回款金额 @required @number @component:currency @search:between @exp @imp',
    `received_date`     date                         DEFAULT NULL COMMENT '回款日期 @required @search:date @exp @imp',
    `payment_method`    varchar(50)         NOT NULL DEFAULT '' COMMENT '付款方式 @max:50 @search:eq @exp @imp',
    `status`            tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=待确认,1=已确认,2=已取消 @search:eq @component:tag @exp @imp',
    `voucher_files`     text COMMENT '回款凭证附件(JSON格式) @form:upload @component:file',
    `remark`            text COMMENT '备注 @form:textarea',
    `owner_user_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID @search:eq @exp',
    `creator_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_number` (`tenant_id`, `receivable_number`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_contract` (`tenant_id`, `contract_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='回款表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 跟进记录表 - 统一的跟进记录管理
-- 生成器命令: php think generator:crud crm_follow_record --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_follow_record`;
CREATE TABLE IF NOT EXISTS `crm_follow_record`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `tenant_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `related_type`  varchar(20)         NOT NULL DEFAULT '' COMMENT '关联类型:lead=线索,customer=客户,business=商机 @required @search:eq @exp @imp',
    `related_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID @required @search:eq @exp @imp',
    `follow_type`   varchar(30)         NOT NULL DEFAULT '' COMMENT '跟进方式:phone=电话,visit=拜访,email=邮件,wechat=微信,other=其他 @required @search:eq @exp @imp',
    `content`       text COMMENT '跟进内容 @required @form:textarea @exp @imp',
    `follow_date`   datetime                     DEFAULT NULL COMMENT '跟进时间 @required @search:date @exp @imp',
    `next_plan`     text COMMENT '下次跟进计划 @form:textarea @exp @imp',
    `next_date`     datetime                     DEFAULT NULL COMMENT '下次跟进时间 @search:date @exp @imp',
    `attachments`   text COMMENT '附件(JSON格式) @form:upload @component:file',
    `creator_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_related` (`tenant_id`, `related_type`, `related_id`),
    KEY `idx_tenant_creator` (`tenant_id`, `creator_id`),
    KEY `idx_follow_date` (`follow_date`),
    KEY `idx_next_date` (`next_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='跟进记录表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 注意：审批功能使用现有的workflow系统
-- workflow_history表已提供完整的审批记录功能，无需重复创建审批表
-- 复杂审批流程：使用workflow_definition + workflow_instance + workflow_history
-- 简单审批场景：使用现有的crm_contract_approval、crm_quotation_approval等表
-- ----------------------------

-- ----------------------------
-- 工作报告表 - 工作报告管理
-- 生成器命令: php think generator:crud crm_work_report --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_work_report`;
CREATE TABLE IF NOT EXISTS `crm_work_report`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '报告ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `title`       varchar(200)        NOT NULL DEFAULT '' COMMENT '报告标题 @required @max:200 @search:like @exp @imp',
    `type`        varchar(20)         NOT NULL DEFAULT '' COMMENT '报告类型:daily=日报,weekly=周报,monthly=月报 @required @search:eq @exp @imp',
    `report_date` date                         DEFAULT NULL COMMENT '报告日期 @required @search:date @exp @imp',
    `content`     text COMMENT '报告内容 @required @form:textarea @exp @imp',
    `summary`     text COMMENT '工作总结 @form:textarea @exp @imp',
    `plan`        text COMMENT '下期计划 @form:textarea @exp @imp',
    `attachments` text COMMENT '附件(JSON格式) @form:upload @component:file',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_creator` (`tenant_id`, `creator_id`),
    KEY `idx_tenant_type` (`tenant_id`, `type`),
    KEY `idx_report_date` (`report_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='工作报告表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 数据统计表 - 数据统计缓存
-- 生成器命令: php think generator:crud crm_statistics --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_statistics`;
CREATE TABLE IF NOT EXISTS `crm_statistics`
(
    `id`           bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
    `tenant_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `user_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID @search:eq @exp @imp',
    `stat_type`    varchar(30)         NOT NULL DEFAULT '' COMMENT '统计类型:lead=线索,customer=客户,business=商机,contract=合同,receivable=回款 @required @search:eq @exp @imp',
    `stat_period`  varchar(20)         NOT NULL DEFAULT '' COMMENT '统计周期:today=今日,week=本周,month=本月,quarter=本季度,year=本年 @required @search:eq @exp @imp',
    `stat_date`    date                         DEFAULT NULL COMMENT '统计日期 @required @search:date @exp @imp',
    `total_count`  int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '总数量 @number @search:between @exp @imp',
    `total_amount` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总金额 @number @component:currency @search:between @exp @imp',
    `data_detail`  text COMMENT '详细数据(JSON格式) @form:textarea',
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_user_type_period_date` (`tenant_id`, `user_id`, `stat_type`, `stat_period`, `stat_date`),
    KEY `idx_tenant_type` (`tenant_id`, `stat_type`),
    KEY `idx_stat_date` (`stat_date`)
) ENGINE = InnoDB COMMENT ='数据统计表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 系统配置表 - CRM系统配置
-- 生成器命令: php think generator:crud crm_config --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_config`;
CREATE TABLE IF NOT EXISTS `crm_config`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `config_key`  varchar(50)         NOT NULL DEFAULT '' COMMENT '配置键 @required @max:50 @search:like @exp @imp',
    `config_name` varchar(100)        NOT NULL DEFAULT '' COMMENT '配置名称 @required @max:100 @search:like @exp @imp',
    `config_value` text COMMENT '配置值 @form:textarea @exp @imp',
    `config_type` varchar(20)         NOT NULL DEFAULT '' COMMENT '配置类型:string=字符串,number=数字,boolean=布尔,json=JSON @required @search:eq @exp @imp',
    `description` text COMMENT '配置描述 @form:textarea @exp @imp',
    `sort`        int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序 @number',
    `status`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_key` (`tenant_id`, `config_key`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='系统配置表 @module:crm @exp:true @imp:true';

-- ----------------------------
-- 公海规则表 - 客户公海回收规则管理（保留核心业务功能）
-- 生成器命令: php think generator:crud crm_sea_rule --module=crm --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `crm_sea_rule`;
CREATE TABLE IF NOT EXISTS `crm_sea_rule`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规则ID',
    `tenant_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `rule_name`      varchar(50)         NOT NULL DEFAULT '' COMMENT '规则名称 @required @max:50 @search:like @exp @imp',
    `rule_type`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '规则类型:1=未跟进,2=未成交 @required @search:eq @component:select @exp @imp',
    `follow_days`    int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '未跟进天数 @number @search:between @exp @imp',
    `deal_days`      int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '未成交天数 @number @search:between @exp @imp',
    `customer_level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '适用的客户级别:0=全部,1=普通,2=重要,3=战略 @search:eq @component:select @exp @imp',
    `enable_notify`  tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用提醒:0=否,1=是 @component:switch @search:eq @exp @imp',
    `notify_days`    int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '提前提醒天数 @number @search:between @exp @imp',
    `status`         tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @component:switch @search:eq @exp @imp',
    `creator_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_rule_type` (`rule_type`),
    KEY `idx_customer_level` (`customer_level`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='公海规则表 @module:crm @exp:true @imp:true';

SET FOREIGN_KEY_CHECKS = 1;
