<?php
declare(strict_types=1);

namespace app\notice\service\channel;

use app\notice\model\NoticeRecipientModel;
use think\facade\Log;

/**
 * 站内信通道服务
 */
class SiteChannelService extends BaseChannelService
{
    /**
     * 通道编码
     * @var string
     */
    protected string $channelCode = 'site';
    
    /**
     * 发送站内信消息
     *
     * @param array $message 消息数据
     * @param array $recipients 接收人列表
     * @param array $options 选项参数
     * @return bool 是否发送成功
     */
    public function send(array $message, array $recipients, array $options = []): bool
    {
        // 参数校验
        $messageId = $message['id'] ?? 0;
        if (!$messageId || empty($recipients)) {
            Log::error('站内信发送失败: 消息ID为空或接收人为空');
            return false;
        }
        
        try {
            // 记录日志
            $tenantId = $options['tenant_id'] ?? 0;
            $this->log('send', [
                'message_id' => $messageId,
                'recipients_count' => count($recipients)
            ], $tenantId);
            
            // 批量更新接收人的站内信投递状态
            $result = NoticeRecipientModel::where('message_id', $messageId)
                ->whereIn('user_id', $recipients)
                ->update(['site_delivered' => 1]);
            
            return $result !== false;
        } catch (\Exception $e) {
            Log::error('站内信发送异常: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量发送站内信
     *
     * @param array $messageIds 消息ID列表
     * @param array $userIds 用户ID列表
     * @return bool 是否发送成功
     */
    public function batchSend(array $messageIds, array $userIds): bool
    {
        if (empty($messageIds) || empty($userIds)) {
            return false;
        }
        
        try {
            $result = NoticeRecipientModel::whereIn('message_id', $messageIds)
                ->whereIn('user_id', $userIds)
                ->update(['site_delivered' => 1]);
                
            return $result !== false;
        } catch (\Exception $e) {
            Log::error('站内信批量发送异常: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 测试站内信通道配置
     *
     * @param string $channel 通道编码
     * @param array $config 通道配置
     * @return array 测试结果
     */
    public function testChannelConfig(string $channel, array $config): array
    {
        // 站内信通道不需要特殊配置，直接返回成功
        return [
            'success' => true,
            'message' => '站内信通道配置测试成功'
        ];
    }
} 