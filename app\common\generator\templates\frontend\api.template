import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * {{comment}}相关接口
 */
export class {{EntityName}}Api {
  /**
   * 获取{{comment}}列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/{{moduleName}}/{{tableName}}/index',
      params
    })
  }

  /**
   * 获取{{comment}}详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/{{moduleName}}/{{tableName}}/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加{{comment}}
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/{{moduleName}}/{{tableName}}/add',
      data
    })
  }

  /**
   * 更新{{comment}}
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/{{moduleName}}/{{tableName}}/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除{{comment}}
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/{{moduleName}}/{{tableName}}/delete/${id}`
    })
  }

  /**
   * 批量删除{{comment}}
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/{{moduleName}}/{{tableName}}/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/{{moduleName}}/{{tableName}}/updateField',
      data
    })
  }

  {{#hasStatusField}}
  /**
   * 修改{{comment}}状态
   * @param data 状态数据
   */
  static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/{{moduleName}}/{{tableName}}/status/${data.id}`,
      data
    })
  }
  {{/hasStatusField}}

  {{#hasExport}}
  /**
   * 导出{{comment}}数据
   * @param params 导出参数
   */
  static export(params: any) {
    return request.get({
      url: '/{{moduleName}}/{{tableName}}/export',
      params,
      responseType: 'blob'
    })
  }
  {{/hasExport}}


  /**
   * 导入{{comment}}数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/{{moduleName}}/{{tableName}}/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/{{moduleName}}/{{tableName}}/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/{{moduleName}}/{{tableName}}/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}