<?php
declare(strict_types=1);

namespace app\common\utils;

/**
 * 树形结构工具类
 */
class Tree
{
    /**
     * 主键
     * @var string
     */
    protected $pk = 'id';
    
    /**
     * 父级ID键
     * @var string
     */
    protected $parentKey = 'parent_id';
    
    /**
     * 子级键
     * @var string
     */
    protected $childrenKey = 'children';
    
    /**
     * 根节点值
     * @var int
     */
    protected $root = 0;
    
    /**
     * 设置主键
     * @param string $pk 主键
     * @return $this
     */
    public function setPk(string $pk)
    {
        $this->pk = $pk;
        return $this;
    }
    
    /**
     * 设置父级ID键
     * @param string $parentKey 父级ID键
     * @return $this
     */
    public function setParentKey(string $parentKey)
    {
        $this->parentKey = $parentKey;
        return $this;
    }
    
    /**
     * 设置子级键
     * @param string $childrenKey 子级键
     * @return $this
     */
    public function setChildrenKey(string $childrenKey)
    {
        $this->childrenKey = $childrenKey;
        return $this;
    }
    
    /**
     * 设置根节点值
     * @param int $root 根节点值
     * @return $this
     */
    public function setRoot(int $root)
    {
        $this->root = $root;
        return $this;
    }
    
    /**
     * 构建树形结构
     * @param array $list 数据列表
     * @return array
     */
    public function build(array $list): array
    {
        if (empty($list)) {
            return [];
        }
        
        $tree = [];
        $refer = [];
        
        // 创建引用
        foreach ($list as $key => $item) {
            $refer[$item[$this->pk]] = &$list[$key];
        }
        
        // 构建树结构
        foreach ($list as $key => $item) {
            $parentId = $item[$this->parentKey];
            
            if ($parentId == $this->root) {
                // 根节点
                $tree[] = &$list[$key];
            } else {
                // 非根节点
                if (isset($refer[$parentId])) {
                    $parent = &$refer[$parentId];
                    $parent[$this->childrenKey][] = &$list[$key];
                }
            }
        }
        
        return $tree;
    }
    
    /**
     * 构建树形下拉选项
     * @param array $list 数据列表
     * @param string $labelKey 标签键名
     * @param string $disabledKey 禁用键名
     * @return array
     */
    public function buildOptions(array $list, string $labelKey = 'name', string $disabledKey = 'disabled'): array
    {
        // 先构建树
        $tree = $this->build($list);
        
        // 转为选项格式
        return $this->formatOptions($tree, $labelKey, $disabledKey);
    }
    
    /**
     * 格式化选项
     * @param array $tree 树形结构
     * @param string $labelKey 标签键名
     * @param string $disabledKey 禁用键名
     * @param int $level 层级
     * @return array
     */
    protected function formatOptions(array $tree, string $labelKey, string $disabledKey, int $level = 0): array
    {
        $options = [];
        
        foreach ($tree as $node) {
            // 生成缩进前缀
            $prefix = str_repeat('　', $level);
            
            // 构建选项
            $option = [
                'id'       => $node[$this->pk],
                'name'     => $prefix . $node[$labelKey],
                'disabled' => $node[$disabledKey] ?? false,
            ];
            
            $options[] = $option;
            
            // 处理子选项
            if (!empty($node[$this->childrenKey])) {
                $childOptions = $this->formatOptions(
                    $node[$this->childrenKey],
                    $labelKey,
                    $disabledKey,
                    $level + 1
                );
                
                $options = array_merge($options, $childOptions);
            }
        }
        
        return $options;
    }
    
    /**
     * 查找指定节点的所有子节点ID
     * @param array $list 数据列表
     * @param int|string $id 节点ID
     * @return array
     */
    public function getChildrenIds(array $list, $id): array
    {
        $ids = [];
        
        foreach ($list as $item) {
            if ($item[$this->parentKey] == $id) {
                $ids[] = $item[$this->pk];
                $childIds = $this->getChildrenIds($list, $item[$this->pk]);
                $ids = array_merge($ids, $childIds);
            }
        }
        
        return $ids;
    }
    
    /**
     * 查找指定节点的所有父节点ID
     * @param array $list 数据列表
     * @param int|string $id 节点ID
     * @return array
     */
    public function getParentIds(array $list, $id): array
    {
        $ids = [];
        $parentId = $this->getParentId($list, $id);
        
        if ($parentId && $parentId != $this->root) {
            $ids[] = $parentId;
            $parentIds = $this->getParentIds($list, $parentId);
            $ids = array_merge($ids, $parentIds);
        }
        
        return $ids;
    }
    
    /**
     * 获取父节点ID
     * @param array $list 数据列表
     * @param int|string $id 节点ID
     * @return int|string|null
     */
    protected function getParentId(array $list, $id)
    {
        foreach ($list as $item) {
            if ($item[$this->pk] == $id) {
                return $item[$this->parentKey];
            }
        }
        
        return null;
    }
    
    /**
     * 创建实例
     * @return static
     */
    public static function instance()
    {
        return new static();
    }
} 