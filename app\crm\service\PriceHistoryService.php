<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 价格历史记录服务
 * 负责记录价格变更历史，支持价格审计和回溯查询
 */
class PriceHistoryService extends BaseService
{
    /**
     * 缓存键前缀
     */
    const CACHE_PREFIX = 'crm:price_history:';
    const CACHE_TTL = 3600; // 1小时
    
    /**
     * 操作类型常量
     */
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_STATUS_CHANGE = 'status_change';
    const ACTION_CALCULATE = 'calculate';
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * 记录价格策略变更历史
     * 
     * @param string $action 操作类型
     * @param array $strategyData 策略数据
     * @param array $oldData 旧数据（更新时）
     * @param array $context 上下文信息
     * @return bool 记录结果
     */
    public function recordStrategyChange(string $action, array $strategyData, array $oldData = [], array $context = []): bool
    {
        try {
            $historyData = [
                'tenant_id' => $this->getTenantId(),
                'action_type' => $action,
                'strategy_id' => $strategyData['id'] ?? 0,
                'product_id' => $strategyData['product_id'] ?? 0,
                'strategy_type' => $strategyData['strategy_type'] ?? 0,
                'strategy_name' => $strategyData['strategy_name'] ?? '',
                'old_price' => $oldData['price'] ?? null,
                'new_price' => $strategyData['price'] ?? null,
                'old_data' => json_encode($oldData, JSON_UNESCAPED_UNICODE),
                'new_data' => json_encode($strategyData, JSON_UNESCAPED_UNICODE),
                'context' => json_encode($context, JSON_UNESCAPED_UNICODE),
                'operator_id' => $this->getAdminId(),
                'operator_name' => $context['operator_name'] ?? '',
                'ip_address' => request()->ip(),
                'user_agent' => request()->header('user-agent', ''),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            // 插入历史记录（这里假设有一个价格历史表，实际项目中需要创建）
            $result = Db::name('crm_price_history')->insert($historyData);
            
            Log::info('记录价格策略变更历史', $historyData);
            
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('记录价格策略变更历史失败', [
                'action' => $action,
                'strategy_data' => $strategyData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 记录价格计算历史
     * 
     * @param array $calculationData 计算数据
     * @return bool 记录结果
     */
    public function recordPriceCalculation(array $calculationData): bool
    {
        try {
            $historyData = [
                'tenant_id' => $this->getTenantId(),
                'action_type' => self::ACTION_CALCULATE,
                'product_id' => $calculationData['product_id'] ?? 0,
                'customer_id' => $calculationData['customer_id'] ?? 0,
                'quantity' => $calculationData['quantity'] ?? 1.0,
                'base_price' => $calculationData['base_price'] ?? 0,
                'final_price' => $calculationData['final_price'] ?? 0,
                'strategy_used' => $calculationData['strategy_used'] ?? '',
                'discount_amount' => $calculationData['discount_amount'] ?? 0,
                'discount_rate' => $calculationData['discount_rate'] ?? 0,
                'calculation_details' => json_encode($calculationData['calculation_details'] ?? [], JSON_UNESCAPED_UNICODE),
                'operator_id' => $this->getAdminId(),
                'ip_address' => request()->ip(),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $result = Db::name('crm_price_calculation_history')->insert($historyData);
            
            return $result !== false;
            
        } catch (\Exception $e) {
            Log::error('记录价格计算历史失败', [
                'calculation_data' => $calculationData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取策略变更历史
     * 
     * @param array $params 查询参数
     * @return array 历史记录
     */
    public function getStrategyHistory(array $params = []): array
    {
        $query = Db::name('crm_price_history')
            ->where('tenant_id', $this->getTenantId());
        
        // 按策略ID筛选
        if (!empty($params['strategy_id'])) {
            $query->where('strategy_id', $params['strategy_id']);
        }
        
        // 按产品ID筛选
        if (!empty($params['product_id'])) {
            $query->where('product_id', $params['product_id']);
        }
        
        // 按操作类型筛选
        if (!empty($params['action_type'])) {
            $query->where('action_type', $params['action_type']);
        }
        
        // 按时间范围筛选
        if (!empty($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date']);
        }
        if (!empty($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date']);
        }
        
        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 20);
        
        $total = $query->count();
        $list = $query->order('created_at desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
        
        // 解析JSON数据
        foreach ($list as &$item) {
            $item['old_data'] = json_decode($item['old_data'], true);
            $item['new_data'] = json_decode($item['new_data'], true);
            $item['context'] = json_decode($item['context'], true);
        }
        
        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 获取价格计算历史
     * 
     * @param array $params 查询参数
     * @return array 计算历史
     */
    public function getCalculationHistory(array $params = []): array
    {
        $query = Db::name('crm_price_calculation_history')
            ->where('tenant_id', $this->getTenantId());
        
        // 按产品ID筛选
        if (!empty($params['product_id'])) {
            $query->where('product_id', $params['product_id']);
        }
        
        // 按客户ID筛选
        if (!empty($params['customer_id'])) {
            $query->where('customer_id', $params['customer_id']);
        }
        
        // 按时间范围筛选
        if (!empty($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date']);
        }
        if (!empty($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date']);
        }
        
        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 20);
        
        $total = $query->count();
        $list = $query->order('created_at desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
        
        // 解析JSON数据
        foreach ($list as &$item) {
            $item['calculation_details'] = json_decode($item['calculation_details'], true);
        }
        
        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 获取价格趋势分析
     * 
     * @param int $productId 产品ID
     * @param string $period 时间周期 day/week/month
     * @param int $days 天数
     * @return array 趋势数据
     */
    public function getPriceTrend(int $productId, string $period = 'day', int $days = 30): array
    {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        $endDate = date('Y-m-d');
        
        // 获取价格变更记录
        $changes = Db::name('crm_price_history')
            ->where('tenant_id', $this->getTenantId())
            ->where('product_id', $productId)
            ->where('created_at', 'between', [$startDate, $endDate])
            ->where('action_type', 'in', [self::ACTION_CREATE, self::ACTION_UPDATE])
            ->order('created_at asc')
            ->select()
            ->toArray();
        
        // 获取价格计算记录
        $calculations = Db::name('crm_price_calculation_history')
            ->where('tenant_id', $this->getTenantId())
            ->where('product_id', $productId)
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('DATE(created_at) as date, AVG(final_price) as avg_price, COUNT(*) as calc_count')
            ->group('DATE(created_at)')
            ->order('date asc')
            ->select()
            ->toArray();
        
        return [
            'product_id' => $productId,
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'price_changes' => $changes,
            'calculation_stats' => $calculations
        ];
    }
    
    /**
     * 价格审计报告
     * 
     * @param array $params 查询参数
     * @return array 审计报告
     */
    public function generateAuditReport(array $params = []): array
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');
        
        // 策略变更统计
        $strategyChanges = Db::name('crm_price_history')
            ->where('tenant_id', $this->getTenantId())
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('action_type, COUNT(*) as count')
            ->group('action_type')
            ->select()
            ->toArray();
        
        // 价格计算统计
        $calculationStats = Db::name('crm_price_calculation_history')
            ->where('tenant_id', $this->getTenantId())
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('COUNT(*) as total_calculations, AVG(discount_rate) as avg_discount_rate, SUM(discount_amount) as total_discount')
            ->find();
        
        // 最活跃的操作员
        $activeOperators = Db::name('crm_price_history')
            ->where('tenant_id', $this->getTenantId())
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('operator_id, operator_name, COUNT(*) as operation_count')
            ->group('operator_id')
            ->order('operation_count desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        return [
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'strategy_changes' => $strategyChanges,
            'calculation_stats' => $calculationStats,
            'active_operators' => $activeOperators,
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 获取租户ID
     */
    private function getTenantId(): int
    {
        return request()->header('tenant-id', 0);
    }
    
    /**
     * 获取管理员ID
     */
    private function getAdminId(): int
    {
        return request()->header('admin-id', 0);
    }
}
