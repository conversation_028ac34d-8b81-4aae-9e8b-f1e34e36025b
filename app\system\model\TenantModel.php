<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 租户表模型
 *
 * @property int    $id              租户ID
 * @property string $name            租户名称
 * @property string $code            租户编码
 * @property string $domain          租户域名
 * @property string $logo            租户Logo
 * @property string $contact_name    联系人
 * @property string $contact_phone   联系电话
 * @property string $contact_email   联系邮箱
 * @property int    $package_id      租户套餐编号
 * @property string $expired_at      过期时间
 * @property int    $max_admin_count 最大用户数，0表示不限制
 * @property int    $upload_limit    上传容量限制(字节)，0表示不限制
 * @property int    $status          状态：0禁用，1正常
 * @property string $remark          备注
 * @property int    $creator_id      创建者
 * @property string $created_at      创建时间
 * @property string $updated_at      更新时间
 * @property string $deleted_at      删除时间
 */
class TenantModel extends BaseModel
{
	/**
	 * 数据表名称
	 *
	 * @var string
	 */
	protected $name = 'system_tenant';
	
	// 设置字段信息
	protected $schema = [
		'id'             => 'int',
		'name'           => 'string',
		'code'           => 'string',
		'domain'         => 'string',
		'logo'           => 'string',
		'contact_name'   => 'string',
		'contact_phone'  => 'string',
		'contact_email'  => 'string',
		'package_id'     => 'int',
		'expired_at'     => 'datetime',
		'max_user_count' => 'int',
		'upload_limit'   => 'int',
		'status'         => 'int',
		'remark'         => 'string',
		'creator_id'     => 'int',
		'updater_id'     => 'int',
		'created_at'     => 'datetime',
		'updated_at'     => 'datetime',
		'deleted_at'     => 'datetime',
	];
	
	/**
	 * 字段类型转换
	 *
	 * @var array
	 */
	protected $type = [
		'id'              => 'int',
		'package_id'      => 'int',
		'max_admin_count' => 'int',
		'upload_limit'    => 'int',
		'status'          => 'bool',
		'creator_id'      => 'int',
	];
	
	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'id'              => ['type' => 'eq'],
			'name'            => ['type' => 'like'],
			'code'            => ['type' => 'like'],
			'domain'          => ['type' => 'like'],
			'logo'            => ['type' => 'like'],
			'contact_name'    => ['type' => 'like'],
			'contact_phone'   => ['type' => 'like'],
			'contact_email'   => ['type' => 'like'],
			'package_id'      => ['type' => 'eq'],
			'expired_at'      => ['type' => 'date'],
			'max_admin_count' => ['type' => 'eq'],
			'upload_limit'    => ['type' => 'eq'],
			'status'          => ['type' => 'eq'],
			'remark'          => ['type' => 'like'],
			'created_at'      => ['type' => 'date'],
			'updated_at'      => ['type' => 'date'],
			'deleted_at'      => ['type' => 'date'],
		];
	}
	
	/**
	 * 获取允许单字段编辑的字段
	 *
	 * @return array
	 */
	public function getAllowUpdateFields(): array
	{
		return [
			'name',
			'code',
			'domain',
			'logo',
			'contact_name',
			'contact_phone',
			'contact_email',
			'package_id',
			'expired_at',
			'max_admin_count',
			'upload_limit',
			'status',
			'remark',
		];
	}
	
	/**
	 * 获取允许排序的字段
	 *
	 * @return array
	 */
	public function getAllowSortFields(): array
	{
		return [
			'id',
		];
	}
	
	/**
	 * 套餐关联
	 * @return BelongsTo
	 */
	public function package()
	{
		return $this->belongsTo(TenantPackageModel::class, 'package_id', 'id');
	}
	
	/**
	 * 管理员关联
	 * @return HasMany
	 */
	public function admins()
	{
		return $this->hasMany(AdminModel::class, 'tenant_id', 'id');
	}
	
	// 获取上传限制（MB单位）
	public function getUploadLimitMbAttr()
	{
		if (empty($this->upload_limit) || $this->upload_limit <= 0) {
			return 0; // 0表示不限制
		}
		
		// 转换为MB，向上取整
		return ceil($this->upload_limit / (1024 * 1024));
	}
	
	// 设置上传限制（MB转换为字节）
	public function setUploadLimitMbAttr($value)
	{
		if (empty($value) || $value <= 0) {
			$this->upload_limit = 0; // 0表示不限制
			return;
		}
		
		// 将MB转换为字节
		$this->upload_limit = $value * 1024 * 1024;
	}
	
	// 检查租户上传限制
	public function checkUploadLimit($fileSize): bool
	{
		// 如果限制为0，表示不限制
		if (empty($this->upload_limit) || $this->upload_limit <= 0) {
			return true;
		}
		
		// 查询租户已使用的容量
		$usedSpace = $this->getUsedSpace();
		
		// 检查是否超出限制
		return ($usedSpace + $fileSize) <= $this->upload_limit;
	}
	
	// 获取租户已使用的存储空间
	public function getUsedSpace(): int
	{
		// 通过附件表统计租户已使用的空间
		$attachmentModel = new \app\system\model\AttachmentModel();
		$usedSpace = $attachmentModel->where('tenant_id', $this->id)
		                             ->where('deleted_at', null)
		                             ->sum('size');
		
		return intval($usedSpace);
	}
	
	// 获取租户使用空间百分比
	public function getSpaceUsagePercent(): int
	{
		if (empty($this->upload_limit) || $this->upload_limit <= 0) {
			return 0; // 不限制时返回0%
		}
		
		$usedSpace = $this->getUsedSpace();
		
		return intval(($usedSpace / $this->upload_limit) * 100);
	}
} 