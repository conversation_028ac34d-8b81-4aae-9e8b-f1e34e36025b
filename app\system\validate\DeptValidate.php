<?php
declare (strict_types=1);

namespace app\system\validate;

use think\Validate;


/*
 * 管理员验证类
 * */

class DeptValidate extends Validate
{
	/**
	 * 定义验证规则
	 * 格式：'字段名' =>  ['规则1','规则2'...]
	 *
	 * @var array
	 */
	protected $rule = [
		'name'        => 'require|length:2,20',
		'code'        => 'length:2,20',
		'phone'       => 'mobile|max:20',
		'email'       => 'email|max:50',
		'leader_name' => 'length:2,20',
		'status'      => 'in:0,1',
		'remark'      => 'max:255',
	];
	
	/**
	 * 定义错误信息
	 * 格式：'字段名.规则名' =>  '错误信息'
	 *
	 * @var array
	 */
	protected $message = [
		'name.require'   => '请输入部门名称',
		'name.length'    => '部门名称长度必须在2-20个字符之间',
		'code.length'    => '密码长度必须在2-20个字符之间',
		'phone.mobile'   => '手机号格式不正确',
		'phone.max'      => '手机号最多不能超过20个字符',
		'email.email'    => '邮箱格式不正确',
		'email.max'      => '邮箱最多不能超过50个字符',
		'leader_name.length' => '部门领导名称长度必须在2-20个字符之间',
		'status.in'      => '状态值不正确',
		'remark.max'     => '备注最多不能超过255个字符',
	];
}
