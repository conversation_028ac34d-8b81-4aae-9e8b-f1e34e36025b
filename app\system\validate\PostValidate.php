<?php
declare (strict_types=1);

namespace app\system\validate;

use think\Validate;


/*
 * 岗位验证类
 * */

class PostValidate extends Validate
{
	/**
	 * 定义验证规则
	 * 格式：'字段名' =>  ['规则1','规则2'...]
	 *
	 * @var array
	 */
	protected $rule = [
		'name'   => 'require|length:1,50',
		'status' => 'in:0,1',
		'remark' => 'max:255',
	];
	
	/**
	 * 定义错误信息
	 * 格式：'字段名.规则名' =>  '错误信息'
	 *
	 * @var array
	 */
	protected $message = [
		'name.require' => '请输入岗位名称',
		'name.length'  => '岗位名称长度必须在1-50个字符之间',
		'status.in'    => '状态值不正确',
		'remark.max'   => '备注最多不能超过255个字符',
	];
}
