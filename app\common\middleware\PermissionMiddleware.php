<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\common\exception\AuthException;
use app\common\exception\PermissionException;
use app\system\service\PermissionService;
use Closure;
use think\facade\Cache;
use think\Request;
use think\Response;

/**
 * 系统权限验证中间件
 */
class PermissionMiddleware
{
	/**
	 * 权限验证中间件
	 *
	 * @param Request $request
	 * @param Closure $next
	 * @return Response
	 * @throws AuthException
	 * @throws PermissionException
	 */
	public function handle(Request $request, Closure $next): Response
	{
		// 从请求中获取用户信息
		$adminInfo = $request->adminInfo ?? [];
		$adminId   = $adminInfo['admin_id'] ?? 0;
		$adminData = $adminInfo['data'] ?? [];
		$tenantId  = $adminData['tenant_id'] ?? 0;
		
		// 如果未成功获取用户信息，说明用户未登录或登录已过期
		if (empty($adminId)) {
			throw new AuthException();
		}
		
		// 系统超级管理员
		if (is_super_admin()) {
			return $next($request);
		}
		
		// 获取当前请求的模块、控制器和方法
		$ruleName = $request->rule()
		                    ->getName();
		
		$permissionService = app(PermissionService::class);
		
		[
			$module,
			$controller,
			$action
		] = $permissionService->parsePermissionInfo($ruleName);
		// 组合权限标识: 模块:控制器:方法
		$permission = strtolower("{$module}:{$controller}:{$action}");
		
		// 从缓存获取用户权限列表
		$cacheKey         = "admin:permissions:{$adminId}";
//		$adminPermissions = Cache::get($cacheKey);
		$adminPermissions = [];
		
		// 如果缓存中没有，则查询数据库获取用户权限列表
		if (empty($adminPermissions)) {
			
			$adminPermissions = $permissionService->getAdminPermissionByAdminId($adminId, $tenantId);
			
			// 将权限列表缓存起来，设置1小时过期时间
			Cache::tag('menu')
			     ->set($cacheKey, $adminPermissions, 3600);
		}
		
		// 将用户权限列表添加到请求中，供后续处理使用
		$request->permissions = $adminPermissions;

		// 判断用户是否有权限访问当前资源
		if (empty($adminPermissions) || !in_array($permission, $adminPermissions)) {
			throw new PermissionException();
		}

		return $next($request);
	}

	/**
	 * 验证CRM客户数据权限
	 *
	 * @param Request $request
	 * @param int $adminId
	 * @throws PermissionException
	 */
	private function validateCustomerDataPermission(Request $request, int $adminId): void
	{
		// 获取客户ID（可能来自不同的参数）
		$customerId = $request->param('id')
			?? $request->param('customer_id')
			?? $request->param('customerId');

		// 如果没有客户ID，可能是列表操作，跳过验证
		if (!$customerId) {
			return;
		}

		// 使用客户权限服务验证
		$customerPermissionService = app(\app\crm\service\CustomerPermissionService::class);

		if (!$customerPermissionService->validateCustomerAccess((int)$customerId, $adminId)) {
			throw new PermissionException('无权限访问此客户数据');
		}
	}

}