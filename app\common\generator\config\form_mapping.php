<?php
/**
 * 表单组件映射配置
 */
return [
    // 字段名称模式映射
    'field_name_patterns' => [
        'password' => 'password',
        'email' => 'email',
        'phone' => 'phone',
        'mobile' => 'phone',
        'tel' => 'phone',
        'url' => 'url',
        'link' => 'url',
        'website' => 'url',
        'image' => 'image',
        'photo' => 'image',
        'avatar' => 'image',
        'picture' => 'image',
        'file' => 'upload',
        'attachment' => 'upload',
        'document' => 'upload',
        'video' => 'video',
        'audio' => 'audio',
        'media' => 'media',
        'color' => 'color',
        'status' => 'radio',
        'type' => 'radio',
        'gender' => 'radio',
        'sex' => 'radio',
        'tags' => 'tags',
        'keywords' => 'tags',
        'label' => 'tags',
        'address' => 'textarea',
        'content' => 'editor',
        'description' => 'textarea',
        'remark' => 'textarea',
        'detail' => 'textarea',
        'message' => 'textarea',
        'enable' => 'switch',
        'active' => 'switch',
        'visible' => 'switch',
        'published' => 'switch',
        'recommend' => 'switch',
        'featured' => 'switch'
    ],
    
    // 字段类型模式映射
    'field_type_patterns' => [
        'tinyint(1)' => 'switch',
        'tinyint' => 'number',
        'smallint' => 'number',
        'mediumint' => 'number',
        'int' => 'number',
        'bigint' => 'number',
        'float' => 'number',
        'double' => 'number',
        'decimal' => 'number',
        'char' => 'input',
        'varchar' => 'input',
        'text' => 'textarea',
        'mediumtext' => 'editor',
        'longtext' => 'editor',
        'date' => 'date',
        'time' => 'time',
        'year' => 'year',
        'datetime' => 'datetime',
        'timestamp' => 'datetime',
        'enum' => 'select',
        'set' => 'select'
    ],
    
    // 组件配置
    'input' => [
        'component' => 'el-input',
        'props' => []
    ],
    
    'password' => [
        'component' => 'el-input',
        'props' => [
            'type' => 'password',
            'show-password' => true
        ]
    ],
    
    'textarea' => [
        'component' => 'el-input',
        'props' => [
            'type' => 'textarea',
            'rows' => 3
        ]
    ],
    
    'number' => [
        'component' => 'el-input-number',
        'props' => [
            'controls-position' => 'right',
            'style' => 'width: 100%'
        ]
    ],
    
    'editor' => [
        'component' => 'rich-editor',
        'props' => [
            'height' => 300
        ]
    ],
    
    'select' => [
        'component' => 'el-select',
        'props' => [
            'style' => 'width: 100%'
        ],
        'needOptions' => true
    ],
    
    'radio' => [
        'component' => 'el-radio-group',
        'props' => [],
        'needOptions' => true,
        'optionComponent' => 'el-radio'
    ],
    
    'checkbox' => [
        'component' => 'el-checkbox-group',
        'props' => [],
        'needOptions' => true,
        'optionComponent' => 'el-checkbox'
    ],
    
    'switch' => [
        'component' => 'el-switch',
        'props' => [
            'active-value' => 1,
            'inactive-value' => 0
        ]
    ],
    
    'date' => [
        'component' => 'el-date-picker',
        'props' => [
            'type' => 'date',
            'style' => 'width: 100%',
            'value-format' => 'YYYY-MM-DD'
        ]
    ],
    
    'datetime' => [
        'component' => 'el-date-picker',
        'props' => [
            'type' => 'datetime',
            'style' => 'width: 100%',
            'value-format' => 'YYYY-MM-DD HH:mm:ss'
        ]
    ],
    
    'time' => [
        'component' => 'el-time-picker',
        'props' => [
            'style' => 'width: 100%',
            'value-format' => 'HH:mm:ss'
        ]
    ],
    
    'year' => [
        'component' => 'el-date-picker',
        'props' => [
            'type' => 'year',
            'style' => 'width: 100%',
            'value-format' => 'YYYY'
        ]
    ],
    
    'image' => [
        'component' => 'el-upload',
        'props' => [
            'list-type' => 'picture-card',
            'auto-upload' => false,
            'limit' => 1
        ]
    ],
    
    'upload' => [
        'component' => 'el-upload',
        'props' => [
            'auto-upload' => false
        ]
    ],
    
    'tags' => [
        'component' => 'el-select',
        'props' => [
            'multiple' => true,
            'allow-create' => true,
            'filterable' => true,
            'default-first-option' => true,
            'style' => 'width: 100%'
        ]
    ],
    
    'color' => [
        'component' => 'el-color-picker',
        'props' => [
            'show-alpha' => true
        ]
    ],
    
    'rate' => [
        'component' => 'el-rate',
        'props' => []
    ],
    
    'slider' => [
        'component' => 'el-slider',
        'props' => []
    ],
    
    'cascader' => [
        'component' => 'el-cascader',
        'props' => [
            'style' => 'width: 100%'
        ]
    ]
]; 