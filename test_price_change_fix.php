<?php
/**
 * 测试价格变动字段保存修复
 * 运行命令: php test_price_change_fix.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use app\daily\model\DailyPriceOrder;
use app\daily\model\DailyPriceItem;

try {
    echo "=== 价格变动字段保存测试 ===\n\n";
    
    // 1. 创建测试报价单
    echo "1. 创建测试报价单...\n";
    $orderData = [
        'tenant_id' => 0,
        'price_date' => date('Y-m-d'),
        'total_items' => 2,
        'approval_status' => 0,
        'creator_id' => 1
    ];
    
    $order = new DailyPriceOrder();
    $orderId = $order->saveByCreate($orderData);
    echo "报价单创建成功，ID: {$orderId}\n";
    
    // 2. 创建测试明细数据（模拟前端传递的完整数据）
    echo "\n2. 创建测试明细数据...\n";
    $itemsData = [
        [
            'order_id' => $orderId,
            'supplier_id' => 20,
            'product_id' => 9,
            'unit_price' => 105.50,
            'old_price' => 100.00,           // 昨日价格
            'price_change' => 5.50,          // 价格变动
            'change_rate' => 5.50,           // 变动比例
            'stock_price' => 95.00,
            'stock_qty' => 50,
            'policy_remark' => '测试涨价',
            'is_manual_price' => 1,
            'sort_order' => 1,
            'status' => 1
        ],
        [
            'order_id' => $orderId,
            'supplier_id' => 20,
            'product_id' => 8,
            'unit_price' => 190.00,
            'old_price' => 200.00,           // 昨日价格
            'price_change' => -10.00,        // 价格变动
            'change_rate' => -5.00,          // 变动比例
            'stock_price' => 180.00,
            'stock_qty' => 30,
            'policy_remark' => '测试降价',
            'is_manual_price' => 1,
            'sort_order' => 2,
            'status' => 1
        ]
    ];
    
    // 3. 保存明细数据
    foreach ($itemsData as $index => $itemData) {
        $item = new DailyPriceItem();
        $result = $item->saveByCreate($itemData);
        if ($result) {
            echo "明细 " . ($index + 1) . " 保存成功，ID: {$result}\n";
        } else {
            echo "明细 " . ($index + 1) . " 保存失败\n";
        }
    }
    
    // 4. 验证保存结果
    echo "\n3. 验证保存结果...\n";
    $savedItems = DailyPriceItem::where('order_id', $orderId)
                                ->with(['supplier', 'product'])
                                ->select();
    
    foreach ($savedItems as $item) {
        echo "--- 明细 ID: {$item->id} ---\n";
        echo "供应商: " . ($item->supplier->name ?? '未知') . "\n";
        echo "产品: " . ($item->product->name ?? '未知') . "\n";
        echo "当前单价: {$item->unit_price}\n";
        echo "昨日价格: {$item->old_price}\n";
        echo "价格变动: {$item->price_change}\n";
        echo "变动比例: {$item->change_rate}%\n";
        echo "格式化显示: " . $item->price_change_format . "\n";
        echo "变动类型: " . $item->price_change_type . "\n";
        echo "样式类: " . $item->price_change_class . "\n";
        echo "\n";
    }
    
    // 5. 测试获取器方法
    echo "4. 测试获取器方法...\n";
    $firstItem = $savedItems[0];
    echo "价格变动格式: " . $firstItem->price_change_format . "\n";
    echo "变动百分比: " . $firstItem->change_rate_percent . "\n";
    echo "变动类型: " . $firstItem->price_change_type . "\n";
    echo "样式类: " . $firstItem->price_change_class . "\n";
    
    // 6. 清理测试数据
    echo "\n5. 清理测试数据...\n";
    DailyPriceItem::where('order_id', $orderId)->delete();
    DailyPriceOrder::where('id', $orderId)->delete();
    echo "测试数据清理完成\n";
    
    echo "\n=== 测试完成 ===\n";
    echo "✅ 价格变动字段保存功能正常\n";
    echo "✅ 获取器方法工作正常\n";
    echo "✅ 数据格式化显示正确\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
    
    // 清理可能的残留数据
    if (isset($orderId)) {
        try {
            DailyPriceItem::where('order_id', $orderId)->delete();
            DailyPriceOrder::where('id', $orderId)->delete();
            echo "已清理残留测试数据\n";
        } catch (\Exception $cleanupError) {
            echo "清理数据时出错: " . $cleanupError->getMessage() . "\n";
        }
    }
}
