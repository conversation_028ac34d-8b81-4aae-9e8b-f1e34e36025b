<?php

namespace app\common\lib\upload\driver;


use app\common\lib\upload\UploadStorageInterface;

class LocalDriver implements UploadStorageInterface
{
	public function getUploadToken(array $config, array $params = []): string|array
	{
		// 本地存储不需要token
		return '';
	}
	
	public function upload(array $file, array $config): array
	{
		$saveFilePath = '/uploads/' . date('Y/m/d');
		// 实现本地文件上传逻辑
		$savePath = public_path() . $saveFilePath;
		$fileName = md5(microtime(true) . mt_rand(1000, 9999)) . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
		$saveName = $saveFilePath . '/' . $fileName;
		
		if (!is_dir($savePath)) {
			mkdir($savePath, 0755, true);
		}
		
		move_uploaded_file($file['tmp_name'], $savePath . '/' . $fileName);
		
		return [
			'name'       => $fileName,
			'real_name'  => $file['name'],
			'path'       => $saveName,
			'extension'  => pathinfo($file['name'], PATHINFO_EXTENSION),
			'size'       => $file['size'],
			'mime_type'  => $file['type'],
			'storage'    => 'local',
			'storage_id' => '',
		];
	}
	
	public function delete(string $filePath, array $config): bool
	{
		$realPath = public_path() . $filePath;
		if (!file_exists($realPath)) {
			return true;
		}
		return unlink($realPath);
	}
	
	public function callback(array $params, array $config): array
	{
		// 本地存储不需要回调
		return [];
	}
}