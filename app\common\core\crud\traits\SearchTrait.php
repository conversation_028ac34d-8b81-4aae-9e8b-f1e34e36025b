<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

/**
 * 搜索条件构建相关功能
 */
trait SearchTrait
{
	/**
	 * 构建搜索条件
	 */
	protected function buildSearchConditions(array $params, array $searchFields): array
	{
		$where = [];
		
		foreach ($searchFields as $field => $config) {
			// 检查参数是否有效
			if (!$this->isValidSearchParam($params, $field)) {
				continue;
			}
			
			$value = $params[$field];
			$type  = $config['type'] ?? 'eq';
			
			// 获取并检查实际搜索字段
			$searchField = $config['field'] ?? $field;
			if (!$this->isValidField($searchField)) {
				continue;
			}
			
			$where = $this->addSearchCondition($where, $searchField, $type, $value, $params, $config);
		}
		
		return $where;
	}
	
	/**
	 * 检查搜索参数是否有效
	 */
	protected function isValidSearchParam(array $params, string $field): bool
	{
		// 检查参数是否存在且非空
		if (!isset($params[$field]) || $params[$field] === '') {
			return false;
		}
		
		// 检查字段名是否有效
		return $this->isValidField($field);
	}
	
	/**
	 * 添加搜索条件
	 */
	protected function addSearchCondition(
		array $where, string $field, string $type, $value, array $params, array $config
	): array
	{
		$conditionMethod = 'buildCondition' . ucfirst(strtolower($type));
		
		// 使用动态方法构建条件
		if (method_exists($this, $conditionMethod)) {
			$condition = $this->$conditionMethod($field, $value, $params, $config);
			if ($condition) {
				if (is_array($condition[0] ?? null)) {
					// 多条件数组
					$where = array_merge($where, $condition);
				}
				else {
					// 单条件数组
					$where[] = $condition;
				}
			}
			return $where;
		}
		
		// 默认处理
		$value   = $this->filterValue($value);
		$where[] = [
			$field,
			$type,
			$value
		];
		
		return $where;
	}
	
	/**
	 * 构建LIKE条件
	 */
	protected function buildConditionLike(string $field, $value, array $params, array $config): array
	{
		$value = $this->filterString($value);
		return [
			$field,
			'like',
			"%{$value}%"
		];
	}
	
	/**
	 * 构建BETWEEN条件
	 */
	protected function buildConditionBetween(string $field, $value, array $params, array $config): ?array
	{
		if (!is_array($value) || count($value) !== 2) {
			return null;
		}
		
		$value[0] = $this->filterValue($value[0]);
		$value[1] = $this->filterValue($value[1]);
		return [
			$field,
			'between',
			$value
		];
	}
	
	/**
	 * 构建IN条件
	 */
	protected function buildConditionIn(string $field, $value, array $params, array $config): array
	{
		if (!is_array($value)) {
			if (is_string($value) && strpos($value, ',') !== false) {
				$value = explode(',', $value);
			}
			else {
				$value = [$value];
			}
		}
		
		$value = $this->filterValue($value);
		return [
			$field,
			'in',
			$value
		];
	}
	
	/**
	 * 构建日期条件
	 */
	protected function buildConditionDate(string $field, $value, array $params, array $config): ?array
	{
		if (!is_array($value) || count($value) !== 2) {
			return null;
		}
		
		$value[0] = $this->filterValue($value[0]);
		$value[1] = $this->filterValue($value[1]);
		return [
			$field,
			'between time',
			$value
		];
	}
	
	/**
	 * 构建自定义条件
	 */
	protected function buildConditionCustom(string $field, $value, array $params, array $config): ?array
	{
		if (!isset($config['handler']) || !is_callable($config['handler'])) {
			return null;
		}
		
		return call_user_func($config['handler'], $value, $params);
	}
	
	/**
	 * 构建相等条件
	 */
	protected function buildConditionEq(string $field, $value, array $params, array $config): array
	{
		$value = $this->filterValue($value);
		return [
			$field,
			'=',
			$value
		];
	}
	
	/**
	 * 构建搜索排序
	 */
	protected function buildSearchOrder(array $params): array
	{
		$order = [];
		
		if (empty($params['sort_field'])) {
			return ['id' => 'desc'];
		}
		
		$sortField = $params['sort_field'];
		$sortOrder = $params['sort_order'] ?? 'desc';
		
		// 验证排序字段和方向
		if (!$this->isValidField($sortField)) {
			return $order;
		}
		
		$sortOrder = strtolower($sortOrder) === 'desc'
			? 'desc'
			: 'asc';
		
		// 检查排序字段是否在白名单中
		$allowedSortFields = $params['allowed_sort_fields'] ?? $this->allowSortFields;
		if (empty($allowedSortFields) || in_array($sortField, $allowedSortFields)) {
			$order = [$sortField => $sortOrder];
		}
		
		return $order;
	}
} 