<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 租户配置表模型
 *
 * @property int    $id         配置ID
 * @property string $group      分组
 * @property string $item_key   配置项Key
 * @property string $item_value 配置项Value
 * @property string $remark     配置说明
 * @property string $version    配置版本号
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $deleted_at 删除时间
 * @property int    $tenant_id  租户ID
 */
class TenantConfigModel extends BaseModel
{
	/**
	 * 数据表名称
	 *
	 * @var string
	 */
	protected $name = 'system_tenant_config';
	
	
	/**
	 * 字段类型转换
	 *
	 * @var array
	 */
	protected $type = [
		'id'        => 'int',
		'tenant_id' => 'int',
	];
} 