<?php
declare(strict_types=1);

namespace app\common\core\crud\strategy;

use app\common\core\crud\CrudService;

/**
 * 树形结构CRUD服务
 * 用于处理有层级关系的数据
 */
class TreeCrudService extends CrudService
{
	/**
	 * 父ID字段名
	 *
	 * @var string
	 */
	protected string $parentField = 'parent_id';
	
	/**
	 * 子节点列表字段名
	 *
	 * @var string
	 */
	protected string $childrenField = 'children';
	
	
	/**
	 * @param array     $params
	 * @param array     $searchFields
	 * @param array     $with
	 * @param bool|null $applyDataPermission
	 * @return array
	 */
	public function searchTree(
		array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null
	): array
	{
		$this->checkModel();
		
		// 使用默认搜索字段配置
		if (empty($searchFields) && !empty($this->defaultSearchFields)) {
			$searchFields = $this->defaultSearchFields;
		}
		
		// 处理搜索条件
		$where = $this->buildSearchConditions($params, $searchFields);
		
		// 处理排序
		$order = $this->buildSearchOrder($params);
		
		return $this->getTree($where, $order, $with);
	}
	
	/**
	 * 获取树形结构数据
	 *
	 * @param array        $where    查询条件
	 * @param array|string $order    排序
	 * @param array        $with     关联数据
	 * @param int          $parentId 父ID，默认为0表示获取所有
	 * @param null         $applyDataPermission
	 * @return array 树形结构数据
	 */
	public function getTree(
		array $where = [], array|string $order = [], array $with = [], int $parentId = 0, $applyDataPermission = null
	): array
	{
		// 获取所有数据
		$list = $this->getList($where, $order, $with, $applyDataPermission)
		             ->toArray();
		
		// 生成树形结构
		return $this->buildTree($list, $parentId);
	}
	
	/**
	 * 构建树形结构
	 *
	 * @param array $list     扁平数据列表
	 * @param int   $parentId 父ID
	 * @return array 树形结构
	 */
	protected function buildTree(array $list, int $parentId): array
	{
		return list_to_tree($list, 'id', $this->parentField, $this->childrenField, $parentId);
	}
	
	/**
	 * 获取父子关系路径
	 *
	 * @param int $id 当前ID
	 * @return array 路径ID列表，从顶级到当前
	 */
	public function getPath(int $id): array
	{
		$path = [];
		$this->findPath($id, $path);
		return array_reverse($path);
	}
	
	/**
	 * 递归查找路径
	 *
	 * @param int    $id   当前ID
	 * @param array &$path 路径引用
	 * @return bool 是否找到节点
	 */
	protected function findPath(int $id, array &$path): bool
	{
		$item = $this->model->find($id);
		if (empty($item)) {
			return false;
		}
		
		$path[] = $id;
		
		if ($item[$this->parentField] > 0) {
			return $this->findPath($item[$this->parentField], $path);
		}
		
		return true;
	}
} 