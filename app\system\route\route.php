<?php

use think\facade\Route;

// system_dict_type 表路由
Route::group('api/system-dict-type', function () {
    // 标准CRUD路由
    Route::get('list', 'SystemDictTypeController/index');
    Route::get('detail/:id', 'SystemDictTypeController/detail');
    Route::post('add', 'SystemDictTypeController/add');
    Route::put('update/:id', 'SystemDictTypeController/edit');
    Route::post('delete', 'SystemDictTypeController/delete');
    Route::post('update-field', 'SystemDictTypeController/updateField');
    Route::get('options', 'SystemDictTypeController/options');
    
    // 自定义业务路由
    Route::get('custom-action', 'SystemDictTypeController/customAction');
}); 