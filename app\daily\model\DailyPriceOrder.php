<?php
declare(strict_types=1);

namespace app\daily\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\system\model\SystemAdmin;
use app\workflow\model\WorkflowInstance;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 每日报价单表模型
 */
class DailyPriceOrder extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'daily_price_order';
	
	protected $append = [
		'items'
	];
	
	/**
	 * 搜索字段配置
	 *
	 * @return array
	 */
	protected array $defaultSearchFields = [
		
		'order_number' => ['type' => 'like'],
		
		'title' => ['type' => 'like'],
		
		'price_date' => ['type' => 'date'],
		
		'total_items' => ['type' => 'between'],
		
		'status' => ['type' => 'eq'],
		
		'approval_status' => ['type' => 'eq'],
		
		'workflow_instance_id' => ['type' => 'eq'],
		
		'submit_time' => ['type' => 'datetime'],
		
		'approval_time' => ['type' => 'datetime'],
		
		'submitter_id' => ['type' => 'eq'],
		
		'void_time' => ['type' => 'datetime'],
		
		'void_user_id' => ['type' => 'eq'],
	
	];
	
	// 类型转换
	protected $type = [
		'price_date'           => 'date',
		'total_items'          => 'integer',
		'approval_status'      => 'integer',
		'workflow_instance_id' => 'integer',
		'submitter_id'         => 'integer',
		'void_user_id'         => 'integer',
		'creator_id'           => 'integer',
		'updated_id'           => 'integer',
		'submit_time'          => 'datetime',
		'approval_time'        => 'datetime',
		'void_time'            => 'datetime',
	];
	
	// 审批状态常量
	const STATUS_DRAFT      = 0;      // 草稿
	const STATUS_PENDING    = 1;      // 审批中
	const STATUS_APPROVED   = 2;      // 已通过
	const STATUS_REJECTED   = 3;      // 已拒绝
	const STATUS_TERMINATED = 4;      // 已终止
	const STATUS_RECALLED   = 5;      // 已撤回
	const STATUS_VOIDED     = 6;      // 已作废
	
	/**
	 * 获取审批状态文本
	 */
	public function getApprovalStatusTextAttr($value, $data)
	{
		$statusMap = [
			self::STATUS_DRAFT      => '草稿',
			self::STATUS_PENDING    => '审批中',
			self::STATUS_APPROVED   => '已通过',
			self::STATUS_REJECTED   => '已拒绝',
			self::STATUS_TERMINATED => '已终止',
			self::STATUS_RECALLED   => '已撤回',
			self::STATUS_VOIDED     => '已作废'
		];
		return $statusMap[$data['approval_status']] ?? '未知';
	}
	
	/**
	 * 获取审批状态样式类
	 */
	public function getApprovalStatusClassAttr($value, $data)
	{
		$classMap = [
			self::STATUS_DRAFT      => 'status-draft',
			self::STATUS_PENDING    => 'status-pending',
			self::STATUS_APPROVED   => 'status-approved',
			self::STATUS_REJECTED   => 'status-rejected',
			self::STATUS_TERMINATED => 'status-terminated',
			self::STATUS_RECALLED   => 'status-recalled',
			self::STATUS_VOIDED     => 'status-voided'
		];
		return $classMap[$data['approval_status']] ?? 'status-unknown';
	}
	
	/**
	 * 检查是否可以编辑
	 */
	public function getCanEditAttr($value, $data)
	{
		return in_array($data['approval_status'], [
			self::STATUS_DRAFT,
			self::STATUS_REJECTED,
			self::STATUS_RECALLED
		]);
	}
	
	/**
	 * 检查是否可以提交审批
	 */
	public function getCanSubmitAttr($value, $data)
	{
		return $data['approval_status'] == self::STATUS_DRAFT && $data['total_items'] > 0;
	}
	
	/**
	 * 检查是否可以撤回
	 */
	public function getCanRecallAttr($value, $data)
	{
		return $data['approval_status'] == self::STATUS_PENDING;
	}
	
	/**
	 * 检查是否可以作废
	 */
	public function getCanVoidAttr($value, $data)
	{
		return in_array($data['approval_status'], [
			self::STATUS_APPROVED,
			self::STATUS_REJECTED,
			self::STATUS_TERMINATED,
			self::STATUS_RECALLED
		]);
	}
	
	/**
	 * 关联报价明细
	 */
	public function items(): HasMany
	{
		return $this->hasMany(DailyPriceItem::class, 'order_id', 'id')
		            ->with([
			            'supplier',
			            'product'
		            ]);
	}
	
	/**
	 * 关联价格历史记录
	 */
	//	public function histories(): HasMany
	//	{
	//		return $this->hasMany(DailyPriceHistory::class, 'order_id', 'id');
	//	}
	
	/**
	 * 关联工作流实例
	 */
	public function workflow(): BelongsTo
	{
		return $this->belongsTo(WorkflowInstance::class, 'workflow_instance_id', 'id');
	}
	
	/**
	 * 关联提交人
	 */
	public function submitter(): BelongsTo
	{
		return $this->belongsTo(SystemAdmin::class, 'submitter_id', 'id');
	}
	
	/**
	 * 关联作废人
	 */
	public function voidUser(): BelongsTo
	{
		return $this->belongsTo(SystemAdmin::class, 'void_user_id', 'id');
	}
	
	public function getDefaultSearchFields(): array
	{
		return $this->defaultSearchFields;
	}
	
	public function setDefaultSearchFields(array $defaultSearchFields): void
	{
		$this->defaultSearchFields = $defaultSearchFields;
	}
}