<?php
declare(strict_types=1);

namespace app\common\core\contracts;

/**
 * 插件接口
 */
interface PluginInterface
{
    /**
     * 获取插件信息
     * @return array
     */
    public function getInfo(): array;
    
    /**
     * 安装插件
     * @return bool
     */
    public function install(): bool;
    
    /**
     * 卸载插件
     * @return bool
     */
    public function uninstall(): bool;
    
    /**
     * 启用插件
     * @return bool
     */
    public function enable(): bool;
    
    /**
     * 禁用插件
     * @return bool
     */
    public function disable(): bool;
    
    /**
     * 升级插件
     * @param string $version 目标版本
     * @return bool
     */
    public function upgrade(string $version): bool;
    
    /**
     * 初始化插件
     * @return void
     */
    public function init(): void;
    
    /**
     * 获取插件路由
     * @return array
     */
    public function getRoutes(): array;
    
    /**
     * 获取插件菜单
     * @return array
     */
    public function getMenus(): array;
    
    /**
     * 获取插件权限
     * @return array
     */
    public function getPermissions(): array;
} 