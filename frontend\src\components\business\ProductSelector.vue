<template>
  <ElSelect
    v-model="selectedValue"
    :placeholder="placeholder"
    :loading="loading"
    :disabled="disabled"
    :clearable="clearable"
    :filterable="filterable"
    :size="size"
    :style="style"
    @change="handleChange"
    @clear="handleClear"
  >
    <ElOption
      v-for="product in filteredProducts"
      :key="product.value"
      :label="product.label"
      :value="product.value"
      class="product-option"
    >
      <div class="product-option-content">
        <div class="product-main">
          <div class="product-name">{{ product.label }}</div>
          <div v-if="product.category_name" class="product-category">
            {{ product.category_name }}
          </div>
        </div>
        <div class="product-extra">
          <div v-if="product.price" class="product-price">¥{{ product.price }}</div>
          <div v-if="product.unit_name" class="product-unit">{{ product.unit_name }}</div>
        </div>
      </div>
    </ElOption>
  </ElSelect>
</template>

<script setup lang="ts">
  import { ElSelect, ElOption } from 'element-plus'
  import { CrmProductApi } from '@/api/crm/crmProduct'

  // 组件属性定义
  interface Props {
    modelValue?: number | null
    supplierId?: number | null
    placeholder?: string
    disabled?: boolean
    clearable?: boolean
    filterable?: boolean
    size?: 'large' | 'default' | 'small'
    style?: string | object
    autoLoad?: boolean
    filterBySupplier?: boolean
  }

  // 组件事件定义
  interface Emits {
    (e: 'update:modelValue', value: number | null): void
    (e: 'change', value: number | null, product?: any): void
    (e: 'clear'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择产品',
    disabled: false,
    clearable: true,
    filterable: true,
    size: 'default',
    autoLoad: true,
    filterBySupplier: true
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const products = ref<any[]>([])
  const loading = ref(false)

  // 计算属性
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 根据供应商过滤产品
  const filteredProducts = computed(() => {
    if (!props.filterBySupplier || !props.supplierId) {
      return products.value
    }
    
    return products.value.filter(product => 
      product.supplier_id === props.supplierId
    )
  })

  // 方法
  const loadProducts = async () => {
    if (loading.value) return
    
    try {
      loading.value = true
      const params: any = {}
      
      // 如果指定了供应商且需要过滤，则添加供应商条件
      if (props.filterBySupplier && props.supplierId) {
        params.supplier_id = props.supplierId
      }
      
      const response = await CrmProductApi.options(params)
      
      if (response.code === 1 && Array.isArray(response.data)) {
        products.value = response.data.map((item: any) => ({
          label: item.label || item.name,
          value: item.value || item.id,
          price: item.price,
          cost: item.cost,
          unit_name: item.unit_name,
          category_name: item.category_name,
          supplier_id: item.supplier_id,
          supplier_name: item.supplier_name,
          ...item
        }))
      } else {
        products.value = []
      }
    } catch (error) {
      console.error('加载产品选项失败:', error)
      products.value = []
    } finally {
      loading.value = false
    }
  }

  const handleChange = (value: number | null) => {
    const product = filteredProducts.value.find(p => p.value === value)
    emit('change', value, product)
  }

  const handleClear = () => {
    emit('clear')
  }

  // 获取产品信息
  const getProduct = (productId: number | null) => {
    if (!productId) return null
    return products.value.find(p => p.value === productId)
  }

  const getProductName = (productId: number | null) => {
    const product = getProduct(productId)
    return product?.label || '-'
  }

  // 刷新数据
  const refresh = () => {
    loadProducts()
  }

  // 监听供应商变化
  watch(() => props.supplierId, (newSupplierId, oldSupplierId) => {
    if (props.filterBySupplier && newSupplierId !== oldSupplierId) {
      // 供应商变化时，清空当前选择
      if (props.modelValue) {
        emit('update:modelValue', null)
      }

      // 只有当选择了新的供应商时才重新加载产品数据
      // 如果清空供应商（newSupplierId为null），则不需要加载数据
      if (newSupplierId) {
        loadProducts()
      } else {
        // 清空供应商时，如果需要按供应商过滤，则清空产品列表
        products.value = []
      }
    }
  })

  // 监听modelValue变化
  watch(() => props.modelValue, (newValue) => {
    // 如果有值但产品列表为空，则加载数据
    if (newValue && products.value.length === 0) {
      loadProducts()
    }
  })

  // 生命周期
  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      // 只有在不需要按供应商过滤，或者已经有供应商ID的情况下才自动加载
      if (props.autoLoad && (!props.filterBySupplier || props.supplierId)) {
        loadProducts()
      }
    })
  })

  // 暴露方法给父组件
  defineExpose({
    loadProducts,
    refresh,
    getProduct,
    getProductName,
    products: computed(() => products.value),
    filteredProducts,
    loading: computed(() => loading.value)
  })
</script>

<style scoped>
.product-option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: 32px;
}

.product-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.product-name {
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
  margin-bottom: 2px;
}

.product-category {
  color: #909399;
  font-size: 12px;
  line-height: 1.2;
}

.product-extra {
  text-align: right;
  color: #909399;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

.product-price {
  line-height: 1.2;
  margin-bottom: 2px;
}

.product-unit {
  line-height: 1.2;
}

/* 修复选项文字居中问题 */
:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

:deep(.el-select-dropdown__item.hover) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item.selected) {
  background-color: #ecf5ff;
  color: #409eff;
}
</style>
