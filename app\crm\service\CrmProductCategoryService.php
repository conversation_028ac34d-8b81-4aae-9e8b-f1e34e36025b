<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\core\crud\strategy\TreeCrudService;
use app\crm\model\CrmProductCategory;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 产品分类表服务类
 */
class CrmProductCategoryService extends BaseService
{
	
	use ExportableTrait;
	
	
	use ImportableTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmProductCategory();
		parent::__construct();
		$this->crudService = new TreeCrudService($this->model);
	}
	
	/**
	 * 获取产品分类树形选项
	 *
	 * @return array
	 */
	public function getOptions(): array
	{
		$where = [
			[
				'status',
				'=',
				1
			]
			// 只获取启用状态的分类
		];
		
		$list = $this->model->field('id,parent_id,name')
		                    ->where($where)
		                    ->order('sort asc, id asc')
		                    ->select();
		
		return list_to_tree($list->toArray());
	}
	
	/**
	 * 为列表数据添加父级分类名称
	 */
	public function addParentName($info)
	{
		
		$info['parent_name'] = $this->model->where('id', $info['parent_id'])
		                                   ->value('name');
		return $info;
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'parent_id' => ['type' => 'eq'],
			
			'name' => ['type' => 'like'],
			
			'code' => ['type' => 'like'],
			
			'status' => ['type' => 'eq'],
		
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'name'        => 'require|max:50',
			'code'        => 'max:30',
			'parent_id'   => 'number|egt:0',
			'sort'        => 'number|egt:0',
			'status'      => 'in:0,1',
			'description' => 'max:500'
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
} 