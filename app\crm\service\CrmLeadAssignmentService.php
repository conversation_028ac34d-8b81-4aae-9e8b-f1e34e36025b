<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmLeadAssignment;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 线索分配记录表服务类
 */
class CrmLeadAssignmentService extends BaseService
{

    use ExportableTrait;


    use ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmLeadAssignment();
        parent::__construct();
    }

    /**
     * 初始化配置
     */
    protected function initialize(): void
    {
        // 分配记录需要数据权限过滤
        $this->crudService->setEnableDataPermission(true);

        // 设置字段场景
        $this->crudService->setFieldScenes([
            'list' => [
                'id', 'lead_id', 'from_user_id', 'to_user_id', 'assignment_type',
                'reason', 'created_by', 'created_at'
            ],
            'detail' => ['*'],
            'select' => ['id', 'lead_id', 'assignment_type']
        ]);
    }
    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

            'lead_id' => ['type' => 'eq'],

            'from_user_id' => ['type' => 'eq'],

            'to_user_id' => ['type' => 'eq'],

            'assignment_type' => ['type' => 'eq'],

            'created_at' => ['type' => 'date'],

        ];
    }
    
    /**
     * 获取验证规则
     * 
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            // 在这里定义验证规则
            // 例如：'username' => 'require|unique:crm_lead_assignment',
        ];
        
        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('id', $ids)->delete();
    }
} 