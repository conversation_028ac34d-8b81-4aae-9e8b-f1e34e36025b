# 出差申请天数计算算法规范

## 📋 算法概述

**目的：** 确保前后端使用相同的算法计算出差总天数  
**应用场景：** 出差申请表单的天数自动计算和后端数据校验  
**数据来源：** 行程明细中的开始时间和结束时间  

## 🔢 核心算法

### **算法步骤**

1. **收集时间数据**
   - 遍历所有行程明细项
   - 提取每项的 `start_time` 和 `end_time`
   - 忽略时间为空的明细项

2. **确定时间范围**
   - 找到所有明细中最早的开始时间 (`minStartTime`)
   - 找到所有明细中最晚的结束时间 (`maxEndTime`)

3. **计算时间差**
   - 计算毫秒差：`diffMs = maxEndTime.getTime() - minStartTime.getTime()`
   - 转换为天数：`diffDays = diffMs / (1000 * 60 * 60 * 24)`

4. **精度处理**
   - 保留一位小数：`Math.round(diffDays * 10) / 10`
   - 确保结果大于0

### **前端JavaScript实现**

```javascript
/**
 * 计算出差总天数
 * @param {Array} items - 行程明细数组
 * @returns {number} 出差天数（保留一位小数）
 */
function calculateBusinessTripDuration(items) {
  if (!items || items.length === 0) {
    return 0;
  }

  let minStartTime = null;
  let maxEndTime = null;
  
  // 1. 遍历所有明细项，找到时间范围
  items.forEach(item => {
    if (item.start_time && item.end_time) {
      const startTime = new Date(item.start_time);
      const endTime = new Date(item.end_time);
      
      if (!minStartTime || startTime < minStartTime) {
        minStartTime = startTime;
      }
      if (!maxEndTime || endTime > maxEndTime) {
        maxEndTime = endTime;
      }
    }
  });
  
  // 2. 计算天数
  if (minStartTime && maxEndTime) {
    const diffMs = maxEndTime.getTime() - minStartTime.getTime();
    const diffDays = diffMs / (1000 * 60 * 60 * 24);
    
    if (diffDays > 0) {
      return Math.round(diffDays * 10) / 10; // 保留一位小数
    }
  }
  
  return 0;
}
```

### **后端PHP实现示例**

```php
<?php
/**
 * 计算出差总天数
 * @param array $items 行程明细数组
 * @return float 出差天数（保留一位小数）
 */
function calculateBusinessTripDuration($items) {
    if (empty($items)) {
        return 0;
    }

    $minStartTime = null;
    $maxEndTime = null;
    
    // 1. 遍历所有明细项，找到时间范围
    foreach ($items as $item) {
        if (!empty($item['start_time']) && !empty($item['end_time'])) {
            $startTime = strtotime($item['start_time']);
            $endTime = strtotime($item['end_time']);
            
            if ($minStartTime === null || $startTime < $minStartTime) {
                $minStartTime = $startTime;
            }
            if ($maxEndTime === null || $endTime > $maxEndTime) {
                $maxEndTime = $endTime;
            }
        }
    }
    
    // 2. 计算天数
    if ($minStartTime !== null && $maxEndTime !== null) {
        $diffSeconds = $maxEndTime - $minStartTime;
        $diffDays = $diffSeconds / (60 * 60 * 24);
        
        if ($diffDays > 0) {
            return round($diffDays, 1); // 保留一位小数
        }
    }
    
    return 0;
}

/**
 * 验证出差申请数据
 * @param array $data 出差申请数据
 * @return array 验证结果
 */
function validateBusinessTripData($data) {
    $errors = [];
    
    // 验证明细项
    if (empty($data['items'])) {
        $errors[] = '请至少添加一条行程明细';
        return ['valid' => false, 'errors' => $errors];
    }
    
    // 计算天数并验证
    $calculatedDuration = calculateBusinessTripDuration($data['items']);
    $submittedDuration = floatval($data['duration'] ?? 0);
    
    // 允许0.1天的误差（考虑浮点数精度问题）
    if (abs($calculatedDuration - $submittedDuration) > 0.1) {
        $errors[] = "出差天数不匹配：计算值为{$calculatedDuration}天，提交值为{$submittedDuration}天";
    }
    
    // 验证时间逻辑
    foreach ($data['items'] as $index => $item) {
        $rowNum = $index + 1;
        
        if (empty($item['start_time']) || empty($item['end_time'])) {
            $errors[] = "第{$rowNum}行：开始时间和结束时间不能为空";
            continue;
        }
        
        $startTime = strtotime($item['start_time']);
        $endTime = strtotime($item['end_time']);
        
        if ($endTime <= $startTime) {
            $errors[] = "第{$rowNum}行：结束时间必须晚于开始时间";
        }
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'calculated_duration' => $calculatedDuration
    ];
}
?>
```

## 📊 算法测试用例

### **测试用例1：单个行程**
```json
{
  "items": [
    {
      "start_time": "2025-01-01 09:00:00",
      "end_time": "2025-01-03 18:00:00"
    }
  ]
}
```
**预期结果：** 2.4天

### **测试用例2：多个行程**
```json
{
  "items": [
    {
      "start_time": "2025-01-01 09:00:00",
      "end_time": "2025-01-01 18:00:00"
    },
    {
      "start_time": "2025-01-02 08:00:00",
      "end_time": "2025-01-03 20:00:00"
    }
  ]
}
```
**预期结果：** 2.5天（从1月1日09:00到1月3日20:00）

### **测试用例3：跨月行程**
```json
{
  "items": [
    {
      "start_time": "2025-01-30 10:00:00",
      "end_time": "2025-02-02 16:00:00"
    }
  ]
}
```
**预期结果：** 3.3天

## ⚠️ 重要注意事项

### **时间格式**
- **前端格式：** `YYYY-MM-DD HH:mm:ss`
- **后端格式：** 确保能正确解析为时间戳
- **时区处理：** 建议统一使用服务器时区

### **精度处理**
- **保留位数：** 一位小数
- **舍入方式：** 四舍五入 (`Math.round` / `round`)
- **误差容忍：** 后端验证时允许0.1天误差

### **边界情况**
- **空明细：** 返回0天
- **无效时间：** 忽略该明细项
- **负时长：** 单个明细项结束时间早于开始时间时报错
- **零时长：** 开始时间等于结束时间时该明细贡献0天

### **数据验证**
- **前端验证：** 实时计算，用户友好提示
- **后端验证：** 严格校验，防止数据篡改
- **一致性检查：** 前后端计算结果必须一致（允许0.1天误差）

## 🔧 实施建议

### **前端实施**
1. 在时间选择器的 `@change` 事件中调用计算函数
2. 实时更新总天数显示
3. 提交前进行最终验证

### **后端实施**
1. 在数据保存前调用验证函数
2. 记录计算过程日志便于调试
3. 返回详细的错误信息

### **测试验证**
1. 使用相同测试用例验证前后端一致性
2. 测试各种边界情况
3. 验证时区和精度处理

## 📝 更新日志

- **2025-07-28：** 初始版本，定义核心算法和实现规范
- **算法版本：** v1.0
- **兼容性：** 前后端通用

---

**出差天数计算算法** | **前后端一致** | **精确到0.1天** | **完整验证**
