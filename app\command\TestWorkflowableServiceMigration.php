<?php
declare(strict_types=1);

namespace app\command;

use app\daily\model\DailyPriceOrder;
use app\daily\model\DailyPriceItem;
use app\crm\model\CrmContract;
use app\crm\model\CrmContractReceivable;
use app\workflow\service\UnifiedWorkflowService;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 测试WorkflowableService迁移到UnifiedWorkflowService
 */
class TestWorkflowableServiceMigration extends Command
{
    protected function configure()
    {
        $this->setName('test:workflowable-service-migration')
            ->setDescription('测试WorkflowableService迁移到UnifiedWorkflowService的效果');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('<info>=== WorkflowableService迁移测试 ===</info>');
        $output->writeln('');

        // 模拟登录用户
        request()->tenantId = 1;
        request()->adminId = 1;

        $testResults = [];
        
        try {
            // 1. 测试每日报价单工作流
            $output->writeln('<comment>1. 测试每日报价单工作流</comment>');
            $testResults['daily_price_order'] = $this->testDailyPriceOrderWorkflow($output);

            // 2. 测试CRM合同工作流
            $output->writeln('<comment>2. 测试CRM合同工作流</comment>');
            $testResults['crm_contract'] = $this->testCrmContractWorkflow($output);

            // 3. 测试CRM回款工作流
            $output->writeln('<comment>3. 测试CRM回款工作流</comment>');
            $testResults['crm_contract_receivable'] = $this->testCrmContractReceivableWorkflow($output);

            // 4. 验证WorkflowableService已被完全移除
            $output->writeln('<comment>4. 验证WorkflowableService已被完全移除</comment>');
            $testResults['cleanup_verification'] = $this->verifyCleanup($output);

            // 5. 生成测试报告
            $output->writeln('<comment>5. 生成测试报告</comment>');
            $this->generateMigrationReport($testResults, $output);

            $output->writeln('<info>🎉 WorkflowableService迁移测试完成！</info>');

        } catch (\Exception $e) {
            $output->writeln('<error>❌ 测试异常: ' . $e->getMessage() . '</error>');
        }

        $output->writeln('');
        $output->writeln('<info>=== 测试完成 ===</info>');
    }

    private function testDailyPriceOrderWorkflow(Output $output): array
    {
        $results = ['passed' => 0, 'failed' => 0, 'details' => []];
        
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            
            // 测试业务代码映射
            $formService = $unifiedWorkflowService->getFormService('daily_price_order');
            if ($formService) {
                $results['passed']++;
                $results['details'][] = '✅ 每日报价单FormService创建成功';
                $output->writeln('  ✅ 每日报价单FormService创建成功');
            } else {
                $results['failed']++;
                $results['details'][] = '❌ 每日报价单FormService创建失败';
                $output->writeln('  ❌ 每日报价单FormService创建失败');
            }
            
        } catch (\Exception $e) {
            $results['failed']++;
            $results['details'][] = '❌ 每日报价单测试异常: ' . $e->getMessage();
            $output->writeln('  ❌ 每日报价单测试异常: ' . $e->getMessage());
        }
        
        return $results;
    }

    private function testCrmContractWorkflow(Output $output): array
    {
        $results = ['passed' => 0, 'failed' => 0, 'details' => []];
        
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            
            // 测试业务代码映射
            $formService = $unifiedWorkflowService->getFormService('crm_contract');
            if ($formService) {
                $results['passed']++;
                $results['details'][] = '✅ CRM合同FormService创建成功';
                $output->writeln('  ✅ CRM合同FormService创建成功');
            } else {
                $results['failed']++;
                $results['details'][] = '❌ CRM合同FormService创建失败';
                $output->writeln('  ❌ CRM合同FormService创建失败');
            }
            
        } catch (\Exception $e) {
            $results['failed']++;
            $results['details'][] = '❌ CRM合同测试异常: ' . $e->getMessage();
            $output->writeln('  ❌ CRM合同测试异常: ' . $e->getMessage());
        }
        
        return $results;
    }

    private function testCrmContractReceivableWorkflow(Output $output): array
    {
        $results = ['passed' => 0, 'failed' => 0, 'details' => []];
        
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            
            // 测试业务代码映射
            $formService = $unifiedWorkflowService->getFormService('crm_contract_receivable');
            if ($formService) {
                $results['passed']++;
                $results['details'][] = '✅ CRM回款FormService创建成功';
                $output->writeln('  ✅ CRM回款FormService创建成功');
            } else {
                $results['failed']++;
                $results['details'][] = '❌ CRM回款FormService创建失败';
                $output->writeln('  ❌ CRM回款FormService创建失败');
            }
            
        } catch (\Exception $e) {
            $results['failed']++;
            $results['details'][] = '❌ CRM回款测试异常: ' . $e->getMessage();
            $output->writeln('  ❌ CRM回款测试异常: ' . $e->getMessage());
        }
        
        return $results;
    }

    private function verifyCleanup(Output $output): array
    {
        $results = ['passed' => 0, 'failed' => 0, 'details' => []];
        
        // 检查WorkflowableService文件是否已删除
        $workflowableServiceFile = app_path() . 'common/service/WorkflowableService.php';
        if (!file_exists($workflowableServiceFile)) {
            $results['passed']++;
            $results['details'][] = '✅ WorkflowableService.php已删除';
            $output->writeln('  ✅ WorkflowableService.php已删除');
        } else {
            $results['failed']++;
            $results['details'][] = '❌ WorkflowableService.php仍然存在';
            $output->writeln('  ❌ WorkflowableService.php仍然存在');
        }
        
        // 检查各个WorkflowService子类是否已删除
        $workflowServiceFiles = [
            'app/daily/service/DailyPriceOrderWorkflowService.php',
            'app/crm/service/CrmContractWorkflowService.php',
            'app/crm/service/CrmContractReceivableWorkflowService.php'
        ];
        
        foreach ($workflowServiceFiles as $file) {
            $fullPath = base_path() . '/' . $file;
            if (!file_exists($fullPath)) {
                $results['passed']++;
                $results['details'][] = "✅ {$file}已删除";
                $output->writeln("  ✅ {$file}已删除");
            } else {
                $results['failed']++;
                $results['details'][] = "❌ {$file}仍然存在";
                $output->writeln("  ❌ {$file}仍然存在");
            }
        }
        
        return $results;
    }

    private function generateMigrationReport(array $testResults, Output $output): void
    {
        $output->writeln('');
        $output->writeln('<info>📊 WorkflowableService迁移报告</info>');
        $output->writeln('=====================================');
        
        $totalPassed = 0;
        $totalFailed = 0;
        
        foreach ($testResults as $testName => $result) {
            $totalPassed += $result['passed'];
            $totalFailed += $result['failed'];
            
            $output->writeln("<comment>{$testName}:</comment>");
            $output->writeln("  通过: {$result['passed']}, 失败: {$result['failed']}");
        }
        
        $output->writeln('');
        $output->writeln("<info>总计: 通过 {$totalPassed}, 失败 {$totalFailed}</info>");
        $successRate = $totalPassed + $totalFailed > 0 ? round($totalPassed / ($totalPassed + $totalFailed) * 100, 2) : 0;
        $output->writeln("<info>成功率: {$successRate}%</info>");
        
        if ($successRate >= 90) {
            $output->writeln('<info>🎉 WorkflowableService迁移成功！</info>');
            $output->writeln('<info>✅ 所有业务模块已成功迁移到UnifiedWorkflowService</info>');
            $output->writeln('<info>✅ 旧的WorkflowableService及其子类已完全清理</info>');
        } elseif ($successRate >= 70) {
            $output->writeln('<comment>⚠️ WorkflowableService迁移基本成功，有待完善</comment>');
        } else {
            $output->writeln('<error>❌ WorkflowableService迁移需要进一步优化</error>');
        }
    }
}
