-- 修复出库和出货申请工作流配置
-- 确保workflow_type表中有正确的记录

-- 检查是否存在出库申请工作流类型
INSERT IGNORE INTO workflow_type (name, module_code, business_code, status, creator_id, created_at, updated_at, tenant_id) 
VALUES ('出库申请', 'ims', 'ims_outbound_approval', 1, 1, NOW(), NOW(), 0);

-- 检查是否存在出货申请工作流类型
INSERT IGNORE INTO workflow_type (name, module_code, business_code, status, creator_id, created_at, updated_at, tenant_id) 
VALUES ('出货申请', 'ims', 'ims_shipment_approval', 1, 1, NOW(), NOW(), 0);

-- 获取工作流类型ID
SET @ims_outbound_type_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_outbound_approval' LIMIT 1);
SET @ims_shipment_type_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_shipment_approval' LIMIT 1);

-- 标准流程配置（简单的一级审批）
SET @standard_flow_config = JSON_OBJECT(
    'nodeConfig', JSON_OBJECT(
        'nodeId', 'start',
        'nodeName', '发起人',
        'type', 'promoter'
    ),
    'childNode', JSON_OBJECT(
        'nodeId', 'approval1',
        'nodeName', '审批人',
        'type', 'approval',
        'props', JSON_OBJECT(
            'assignedUser', JSON_ARRAY(
                JSON_OBJECT('name', '管理员', 'id', 1)
            )
        )
    )
);

-- 插入工作流定义（如果不存在）
INSERT IGNORE INTO workflow_definition (name, type_id, flow_config, status, is_template, remark, creator_id, created_at, updated_at, tenant_id) 
VALUES 
('出库申请标准审批流程', @ims_outbound_type_id, @standard_flow_config, 1, 0, '出库申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),
('出货申请标准审批流程', @ims_shipment_type_id, @standard_flow_config, 1, 0, '出货申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0);

-- 验证配置结果
SELECT 
    wt.id as type_id,
    wt.name as type_name,
    wt.module_code,
    wt.business_code,
    wt.status as type_status,
    wd.id as definition_id,
    wd.name as definition_name,
    wd.status as definition_status
FROM workflow_type wt
LEFT JOIN workflow_definition wd ON wt.id = wd.type_id
WHERE wt.business_code IN ('ims_outbound_approval', 'ims_shipment_approval')
ORDER BY wt.business_code;

-- 检查DynamicWorkflowFactory映射
-- business_code: ims_outbound_approval -> app\ims\service\ImsOutboundApprovalService
-- business_code: ims_shipment_approval -> app\ims\service\ImsShipmentApprovalService
