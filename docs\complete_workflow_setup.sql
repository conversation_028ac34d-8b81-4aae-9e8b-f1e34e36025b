-- 完整的工作流表单配置脚本
USE base_admin;

-- 插入所有需要的工作流类型
INSERT IGNORE INTO workflow_type (name, module_code, business_code, status, creator_id, created_at, updated_at, tenant_id) VALUES
('出差申请', 'hr', 'hr_business_trip', 1, 1, NOW(), NOW(), 0),
('外出申请', 'hr', 'hr_outing', 1, 1, NOW(), NOW(), 0),
('样品邮寄申请', 'office', 'office_sample_mail', 1, 1, NOW(), NOW(), 0),
('办公采购申请', 'office', 'office_procurement', 1, 1, NOW(), NOW(), 0),
('付款申请', 'finance', 'finance_payment_approval', 1, 1, NOW(), NOW(), 0),
('报销申请', 'finance', 'finance_expense_reimbursement', 1, 1, NOW(), NOW(), 0),
('出库申请', 'ims', 'ims_outbound_approval', 1, 1, NOW(), NOW(), 0),
('出货申请', 'ims', 'ims_shipment_approval', 1, 1, NOW(), NOW(), 0);

-- 获取工作流类型ID
SET @hr_business_trip_id = (SELECT id FROM workflow_type WHERE business_code = 'hr_business_trip' LIMIT 1);
SET @hr_outing_id = (SELECT id FROM workflow_type WHERE business_code = 'hr_outing' LIMIT 1);
SET @office_sample_mail_id = (SELECT id FROM workflow_type WHERE business_code = 'office_sample_mail' LIMIT 1);
SET @office_procurement_id = (SELECT id FROM workflow_type WHERE business_code = 'office_procurement' LIMIT 1);
SET @finance_payment_id = (SELECT id FROM workflow_type WHERE business_code = 'finance_payment_approval' LIMIT 1);
SET @finance_expense_id = (SELECT id FROM workflow_type WHERE business_code = 'finance_expense_reimbursement' LIMIT 1);
SET @ims_outbound_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_outbound_approval' LIMIT 1);
SET @ims_shipment_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_shipment_approval' LIMIT 1);

-- 标准流程配置（简单的一级审批）
SET @standard_flow_config = JSON_OBJECT(
    'nodeConfig', JSON_OBJECT(
        'nodeId', 'start',
        'nodeName', '发起人',
        'type', 'promoter'
    ),
    'childNode', JSON_OBJECT(
        'nodeId', 'approval1',
        'nodeName', '审批人',
        'type', 'approval',
        'props', JSON_OBJECT(
            'assignedUser', JSON_ARRAY(
                JSON_OBJECT('name', '管理员', 'id', 1)
            )
        )
    )
);

-- 插入工作流定义
INSERT IGNORE INTO workflow_definition (name, type_id, flow_config, status, is_template, remark, creator_id, created_at, updated_at, tenant_id) VALUES
('出差申请标准审批流程', @hr_business_trip_id, @standard_flow_config, 1, 0, '出差申请标准审批流程', 1, NOW(), NOW(), 0),
('外出申请标准审批流程', @hr_outing_id, @standard_flow_config, 1, 0, '外出申请标准审批流程', 1, NOW(), NOW(), 0),
('样品邮寄申请标准审批流程', @office_sample_mail_id, @standard_flow_config, 1, 0, '样品邮寄申请标准审批流程', 1, NOW(), NOW(), 0),
('办公采购申请标准审批流程', @office_procurement_id, @standard_flow_config, 1, 0, '办公采购申请标准审批流程', 1, NOW(), NOW(), 0),
('付款申请标准审批流程', @finance_payment_id, @standard_flow_config, 1, 0, '付款申请标准审批流程', 1, NOW(), NOW(), 0),
('报销申请标准审批流程', @finance_expense_id, @standard_flow_config, 1, 0, '报销申请标准审批流程', 1, NOW(), NOW(), 0),
('出库申请标准审批流程', @ims_outbound_id, @standard_flow_config, 1, 0, '出库申请标准审批流程', 1, NOW(), NOW(), 0),
('出货申请标准审批流程', @ims_shipment_id, @standard_flow_config, 1, 0, '出货申请标准审批流程', 1, NOW(), NOW(), 0);

-- 验证配置结果
SELECT 'Workflow Types Configuration:' as message;
SELECT 
    wt.id as type_id,
    wt.name as type_name,
    wt.module_code,
    wt.business_code,
    wt.status as type_status,
    CASE WHEN wd.id IS NOT NULL THEN 'YES' ELSE 'NO' END as has_definition
FROM workflow_type wt
LEFT JOIN workflow_definition wd ON wt.id = wd.type_id
WHERE wt.business_code IN (
    'hr_business_trip',
    'hr_outing',
    'office_sample_mail',
    'office_procurement',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'ims_outbound_approval',
    'ims_shipment_approval'
)
ORDER BY wt.business_code;
