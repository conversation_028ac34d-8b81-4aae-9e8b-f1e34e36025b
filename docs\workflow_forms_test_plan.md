# 工作流表单测试计划

## 📋 测试概述

**测试页面：** http://localhost:3006/#/office/workflow/application  
**测试账号：** admin / 123456  
**测试时间：** 2025-07-28  
**测试范围：** 7个新增/更新的工作流表单  

## 🎯 测试目标

验证以下表单的完整功能：
1. ✅ 表单正常打开
2. ✅ 保存功能正常
3. ✅ 编辑功能正常  
4. ✅ 提交功能正常
5. ✅ 数据验证正确
6. ✅ 工作流集成正常

## 📝 测试用例

### **1. 出差申请表单测试**

#### **测试步骤**
1. 点击"申请"按钮
2. 选择"出差申请"
3. 填写表单数据：
   - 出差开始时间：2025-07-29 09:00
   - 出差结束时间：2025-07-31 18:00
   - 出差地点：北京
   - 出差事由：客户拜访
   - 添加明细：
     - 日期：2025-07-29，地点：北京，事由：客户会议
     - 日期：2025-07-30，地点：北京，事由：项目洽谈
   - 上传附件：测试文档
   - 备注：重要客户拜访

#### **预期结果**
- ✅ 表单正常打开
- ✅ 天数自动计算（3天）
- ✅ 明细表格操作正常
- ✅ 保存功能正常
- ✅ 编辑功能正常
- ✅ 提交功能正常
- ✅ 至少一个明细验证

### **2. 外出申请表单测试**

#### **测试步骤**
1. 选择"外出申请"
2. 填写表单数据：
   - 开始时间：2025-07-28 14:00
   - 结束时间：2025-07-28 17:00
   - 外出事由：银行办事
   - 上传附件：相关文档

#### **预期结果**
- ✅ 表单正常打开
- ✅ 时长自动计算（3小时）
- ✅ 保存功能正常
- ✅ 编辑功能正常
- ✅ 提交功能正常

### **3. 样品邮寄申请表单测试**

#### **测试步骤**
1. 选择"样品邮寄申请"
2. 填写表单数据：
   - 样品名称：产品样品A
   - 样品描述：新款产品样品，用于客户测试
   - 寄件人电话：13800138000
   - 收件信息：张三，北京市朝阳区xxx路xxx号，13900139000
   - 备注：加急处理

#### **预期结果**
- ✅ 表单正常打开
- ✅ 保存功能正常
- ✅ 编辑功能正常
- ✅ 提交功能正常
- ✅ 必填字段验证

### **4. 付款申请表单测试**

#### **测试步骤**
1. 选择"付款申请"
2. 填写表单数据：
   - 付款事由：供应商货款支付
   - 付款总额：50000
   - 付款方式：银行转账
   - 支付日期：2025-07-30
   - 支付对象：ABC供应商
   - 开户行：中国银行北京分行
   - 银行账户：1234567890123456789
   - 备注：月度货款结算

#### **预期结果**
- ✅ 表单正常打开
- ✅ 保存功能正常
- ✅ 编辑功能正常
- ✅ 提交功能正常
- ✅ 必填字段验证

### **5. 报销申请表单测试**

#### **测试步骤**
1. 选择"报销申请"
2. 填写表单数据：
   - 报销金额：1500
   - 报销类型：差旅费
   - 添加明细：
     - 费用说明：高铁票费用
     - 费用说明：住宿费用
     - 费用说明：餐费
   - 上传附件：发票图片
   - 备注：出差报销

#### **预期结果**
- ✅ 表单正常打开
- ✅ 明细表格操作正常
- ✅ 保存功能正常
- ✅ 编辑功能正常
- ✅ 提交功能正常
- ✅ 至少一个明细验证

### **6. 出库申请表单测试**

#### **测试步骤**
1. 选择"出库申请"
2. 填写表单数据：
   - 所在部门：销售部
   - 出库日期：2025-07-29
   - 接收单位：客户A
   - 添加明细：
     - 产品：产品A，数量：10，单价：100
     - 产品：产品B，数量：5，单价：200
   - 备注：客户订单出库
   - 上传图片：出库单据

#### **预期结果**
- ✅ 表单正常打开
- ✅ 部门选择组件正常
- ✅ 客户选择组件正常
- ✅ 明细表格操作正常
- ✅ 单价大写自动转换
- ✅ 总金额自动计算（2000元）
- ✅ 总金额大写自动转换
- ✅ 保存功能正常
- ✅ 编辑功能正常
- ✅ 提交功能正常
- ✅ 至少一个明细验证

### **7. 出货申请表单测试**

#### **测试步骤**
1. 选择"出货申请"
2. 填写表单数据：
   - 所在部门：销售部
   - 出货日期：2025-07-30
   - 接收单位：客户B
   - 添加明细：
     - 产品：产品C，数量：8，单价：150
     - 产品：产品D，数量：12，单价：80
   - 备注：月度出货
   - 上传图片：出货凭证

#### **预期结果**
- ✅ 表单正常打开
- ✅ 部门选择组件正常
- ✅ 客户选择组件正常
- ✅ 明细表格操作正常
- ✅ 单价大写自动转换
- ✅ 总金额自动计算（2160元）
- ✅ 总金额大写自动转换
- ✅ 保存功能正常
- ✅ 编辑功能正常
- ✅ 提交功能正常
- ✅ 至少一个明细验证

## 🔍 重点测试项

### **1. 适老化设计验证**
- ✅ 表格尺寸为default（较大字体）
- ✅ 按钮尺寸为default（较大点击区域）
- ✅ 列宽充足，操作便利

### **2. 数字转中文大写功能**
- ✅ 出库/出货单价大写自动转换
- ✅ 总金额大写自动转换
- ✅ 转换格式符合财务规范

### **3. 明细表格功能**
- ✅ 添加明细行
- ✅ 删除明细行
- ✅ 明细内容编辑
- ✅ 自动计算功能

### **4. 组件统一性**
- ✅ DepartmentTreeSelect组件
- ✅ ApiSelect客户选择组件
- ✅ FormUploader附件组件

### **5. 数据验证**
- ✅ 必填字段验证
- ✅ 明细至少一个验证
- ✅ 数值范围验证
- ✅ 日期格式验证

### **6. 工作流集成**
- ✅ 保存草稿功能
- ✅ 提交审批功能
- ✅ 状态更新正常
- ✅ 工作流实例创建

## 🐛 常见问题检查

### **1. 前端问题**
- ❌ 表单不弹出 → 检查workflow_type配置
- ❌ 组件报错 → 检查ApiSelect使用方式
- ❌ 类型错误 → 检查TypeScript类型定义

### **2. 后端问题**
- ❌ 保存失败 → 检查Service实现
- ❌ 验证错误 → 检查校验规则
- ❌ 工作流错误 → 检查DynamicWorkflowFactory映射

### **3. 数据库问题**
- ❌ 业务类型不存在 → 执行workflow配置SQL
- ❌ 字段缺失 → 检查表结构
- ❌ 权限问题 → 检查租户和用户权限

## 📊 测试报告模板

### **测试结果记录**
```
表单名称：[表单名称]
测试时间：[测试时间]
测试人员：[测试人员]

功能测试：
- 表单打开：✅/❌
- 保存功能：✅/❌
- 编辑功能：✅/❌
- 提交功能：✅/❌

特殊功能：
- 自动计算：✅/❌
- 明细操作：✅/❌
- 组件集成：✅/❌
- 数据验证：✅/❌

问题记录：
[记录发现的问题]

修复建议：
[提出修复建议]
```

## 🚀 测试执行

**请按照以上测试计划逐一测试每个表单，并记录测试结果。如发现问题，请详细描述问题现象和重现步骤。**
