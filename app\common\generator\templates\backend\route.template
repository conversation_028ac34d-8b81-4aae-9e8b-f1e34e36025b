<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// {{COMMENT}}路由
Route::group('api/{{MODULE}}/{{TABLE_NAME}}', function () {
    Route::get('index', '{{CONTROLLER_CLASS}}@index');
    Route::get('detail/:id', '{{CONTROLLER_CLASS}}@detail');
    Route::post('add', '{{CONTROLLER_CLASS}}@add');
    Route::post('edit/:id', '{{CONTROLLER_CLASS}}@edit');
    Route::post('delete/:id', '{{CONTROLLER_CLASS}}@delete');
    Route::post('batchDelete', '{{CONTROLLER_CLASS}}@batchDelete');
    Route::post('updateField', '{{CONTROLLER_CLASS}}@updateField');
    Route::post('status/:id', '{{CONTROLLER_CLASS}}@status');{{#HAS_IMPORT}}
    Route::post('import', '{{CONTROLLER_CLASS}}@import');
    Route::get('importTemplate', '{{CONTROLLER_CLASS}}@importTemplate');
    Route::get('downloadTemplate', '{{CONTROLLER_CLASS}}@downloadTemplate');{{/HAS_IMPORT}}{{#HAS_EXPORT}}
    Route::get('export', '{{CONTROLLER_CLASS}}@export');{{/HAS_EXPORT}}
})->middleware([
    TokenAuthMiddleware::class,
//    PermissionMiddleware::class
]);