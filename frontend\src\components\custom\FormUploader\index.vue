<template>
  <div class="form-uploader">
    <!-- 已上传文件展示区域 -->
    <div v-if="fileList.length > 0" class="file-list">
      <div
        v-for="(item, index) in fileList"
        :key="item.uid || index"
        class="file-item"
        :class="{
          'is-image': isImageFile(item),
          'is-video': isVideoFile(item),
          'is-audio': isAudioFile(item),
          'is-document': isDocumentFile(item)
        }"
      >
        <!-- 图片预览 -->
        <div v-if="isImageFile(item)" class="image-preview">
          <el-image
            :src="item.url || ''"
            fit="cover"
            :preview-src-list="[item.url || '']"
            :preview-teleported="true"
            :initial-index="0"
            :hide-on-click-modal="true"
            :z-index="3000"
            :infinite="false"
            :zoom-rate="1.2"
            :min-scale="0.2"
            :max-scale="7"
            class="preview-image"
          />
        </div>

        <!-- 视频预览 -->
        <div v-else-if="isVideoFile(item)" class="file-preview video-preview">
          <div class="file-icon">
            <el-icon>
              <VideoPlay />
            </el-icon>
          </div>
          <div class="file-info">
            <div class="file-name" :title="item.name">{{ item.name }}</div>
            <div class="file-size">{{ formatFileSize(item.size) }}</div>
          </div>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="isAudioFile(item)" class="file-preview audio-preview">
          <div class="file-icon">
            <el-icon>
              <Headset />
            </el-icon>
          </div>
          <div class="file-info">
            <div class="file-name" :title="item.name">{{ item.name }}</div>
            <div class="file-size">{{ formatFileSize(item.size) }}</div>
          </div>
        </div>

        <!-- 文档预览 -->
        <div v-else-if="isDocumentFile(item)" class="file-preview document-preview">
          <div class="file-icon">
            <el-icon v-if="isPdfFile(item)">
              <Document />
            </el-icon>
            <el-icon v-else-if="isWordFile(item)">
              <DocumentCopy />
            </el-icon>
            <el-icon v-else-if="isExcelFile(item)">
              <Grid />
            </el-icon>
            <el-icon v-else>
              <Document />
            </el-icon>
          </div>
          <div class="file-info">
            <div class="file-name" :title="item.name">{{ item.name }}</div>
            <div class="file-size">{{ formatFileSize(item.size) }}</div>
          </div>
        </div>

        <!-- 其他文件预览 -->
        <div v-else class="file-preview">
          <div class="file-icon">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <div class="file-info">
            <div class="file-name" :title="item.name">{{ item.name }}</div>
            <div class="file-size">{{ formatFileSize(item.size) }}</div>
          </div>
        </div>

        <!-- 状态指示器 -->
        <div v-if="item.status === 'uploading'" class="upload-progress">
          <el-progress :percentage="item.percentage || 0" :show-text="false" />
        </div>

        <!-- 删除按钮 -->
        <div
          v-if="!disabled && item.status !== 'uploading'"
          class="delete-button"
          @click.stop="removeFile(index)"
        >
          <el-icon>
            <Delete />
          </el-icon>
        </div>

        <!-- 文件操作菜单 -->
        <div v-if="item.status === 'success'" class="file-actions">
          <el-tooltip content="预览" placement="top" v-if="canPreview(item)">
            <el-button type="primary" text size="small" @click="handlePreviewFile(item)">
              <el-icon>
                <View />
              </el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="下载" placement="top">
            <el-button type="primary" text size="small" @click="downloadFile(item)">
              <el-icon>
                <Download />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 上传按钮 -->
    <div v-if="showUploadButton" class="upload-button-container">
      <el-upload
        ref="uploadRef"
        :action="action"
        :http-request="customUpload"
        :before-upload="beforeUpload"
        :on-error="handleError"
        :on-progress="handleProgress"
        :on-exceed="handleExceed"
        :multiple="multiple"
        :limit="limit"
        :accept="accept"
        :disabled="disabled"
        :auto-upload="autoUpload"
        :show-file-list="false"
        :drag="drag"
      >
        <template v-if="drag">
          <el-icon class="el-icon--upload">
            <upload-filled />
          </el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" v-if="showTip">{{ tipText }}</div>
        </template>
        <template v-else>
          <el-button :type="buttonType as any" :disabled="disabled">
            <el-icon v-if="buttonIcon">
              <component :is="buttonIcon" />
            </el-icon>
            {{ buttonText }}
          </el-button>
        </template>
      </el-upload>

      <!-- 提示信息 - 放在按钮下方，更加清晰 -->
      <div v-if="showTip && !drag" class="upload-tip">{{ tipText }}</div>
    </div>

    <!-- 媒体选择器集成 -->
    <div v-if="useMediaSelector && showUploadButton" class="media-selector-button">
      <el-divider content-position="center">或</el-divider>
      <el-button @click="openMediaSelector" :disabled="disabled">从媒体库选择</el-button>
    </div>

    <!-- 媒体选择器对话框 -->
    <MediaSelector
      v-if="useMediaSelector"
      v-model="mediaDialogVisible"
      :media-type="mediaType"
      :max-count="limit === 0 ? 0 : limit - fileList.length"
      :initial-selected="[]"
      @confirm="handleMediaConfirm"
    />

    <!-- 文件预览对话框 -->
    <el-dialog v-model="previewDialogVisible" :title="previewFile?.name || '文件预览'" width="70%">
      <div class="file-preview-container">
        <!-- 图片预览 -->
        <div v-if="previewFile && isImageFile(previewFile)" class="image-preview-dialog">
          <img :src="previewFile.url" alt="图片预览" class="preview-image-full" />
        </div>

        <!-- 视频预览 -->
        <div v-else-if="previewFile && isVideoFile(previewFile)" class="video-preview-dialog">
          <video controls :src="previewFile.url" class="preview-video"></video>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="previewFile && isAudioFile(previewFile)" class="audio-preview-dialog">
          <audio controls :src="previewFile.url" class="preview-audio"></audio>
          <div class="audio-info">
            <h3>{{ previewFile.name }}</h3>
            <p>{{ formatFileSize(previewFile.size) }}</p>
          </div>
        </div>

        <!-- PDF预览 -->
        <div v-else-if="previewFile && isPdfFile(previewFile)" class="pdf-preview-dialog">
          <iframe :src="previewFile.url" frameborder="0" class="preview-pdf"></iframe>
        </div>

        <!-- 其他文件 - 提供下载链接 -->
        <div v-else class="other-file-preview">
          <el-empty description="无法预览此类型文件">
            <el-button type="primary" @click="downloadFile(previewFile)">下载文件</el-button>
          </el-empty>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import {
    Delete,
    Document,
    UploadFilled,
    VideoPlay,
    Headset,
    DocumentCopy,
    Grid,
    View,
    Download
  } from '@element-plus/icons-vue'
  import { ElMessage, ElUpload } from 'element-plus'
  import { useUploadStore } from '@/store/modules/upload'
  import { UploadService } from '@/utils/upload/UploadService'
  import { MediaSelector } from '@/components/custom/MediaSelector'
  import type { UploadRawFile, UploadRequestOptions } from 'element-plus'
  import type { MediaItem } from '../FormMediaSelector/MediaItem'
  import { CustomUploadFile, CustomUploadFiles } from './types'

  // 定义组件事件
  const emit = defineEmits<{
    (e: 'update:modelValue', value: string | string[]): void
    (e: 'change', value: string | string[], files: CustomUploadFiles): void
    (e: 'success', response: any, file: CustomUploadFile): void
    (e: 'error', error: Error, file: CustomUploadFile): void
    (e: 'progress', event: ProgressEvent, file: CustomUploadFile): void
    (e: 'exceed', files: File[], uploadFiles: CustomUploadFiles): void
  }>()

  // 组件属性定义
  const props = defineProps({
    // 组件的值（v-model绑定）- 字符串或字符串数组
    modelValue: {
      type: [String, Array],
      default: ''
    },
    // 文件类型：'image'(图片), 'video'(视频), 'audio'(音频), 'file'(文件)
    fileType: {
      type: String,
      default: 'image',
      validator: (value: string) => ['image', 'video', 'audio', 'file', 'all'].includes(value)
    },
    // 上传地址，默认使用系统配置
    action: {
      type: String,
      default: ''
    },
    // 最大上传数量，0表示不限制
    limit: {
      type: Number,
      default: 1
    },
    // 是否支持多选文件
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 接受上传的文件类型
    accept: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否启用拖拽上传
    drag: {
      type: Boolean,
      default: false
    },
    // 按钮文本
    buttonText: {
      type: String,
      default: '上传文件'
    },
    // 按钮类型
    buttonType: {
      type: String,
      default: 'primary',
      validator: (value: string) => {
        return ['primary', 'success', 'warning', 'danger', 'info', 'text', 'default', ''].includes(
          value
        )
      }
    },
    // 按钮图标
    buttonIcon: {
      type: String,
      default: ''
    },
    // 提示文本
    tipText: {
      type: String,
      default: ''
    },
    // 是否显示提示
    showTip: {
      type: Boolean,
      default: true
    },
    // 是否使用媒体选择器
    useMediaSelector: {
      type: Boolean,
      default: false
    },
    // 上传文件的分类ID
    categoryId: {
      type: [Number, String],
      default: 0
    },
    // 存储类型
    storage: {
      type: String,
      default: ''
    },
    // 返回值模式：'string'(逗号分隔字符串) | 'array'(数组)
    returnValueMode: {
      type: String,
      default: 'string',
      validator: (value: string) => ['string', 'array'].includes(value)
    },
    // 自定义允许的文件扩展名（字符串或数组）
    allowedExtensions: {
      type: [String, Array],
      default: ''
    },
    // 自定义允许的最大文件大小(MB)
    maxFileSize: {
      type: Number,
      default: 0
    }
  })

  // 获取上传 store
  const uploadStore = useUploadStore()

  // 上传组件引用
  const uploadRef = ref<InstanceType<typeof ElUpload> | null>(null)

  // 媒体选择器对话框可见性
  const mediaDialogVisible = ref(false)

  // 文件列表
  const fileList = ref<CustomUploadFiles>([])

  // 预览对话框可见性
  const previewDialogVisible = ref(false)

  // 当前预览的文件
  const previewFile = ref<CustomUploadFile | null>(null)

  // 媒体类型
  const mediaType = computed(() => {
    if (props.fileType === 'all') return 'file'
    return props.fileType
  })

  // 是否显示上传按钮
  const showUploadButton = computed(() => {
    if (props.disabled) return false
    if (props.limit === 0) return true
    return fileList.value.length < props.limit
  })

  // 获取允许的文件扩展名数组
  const allowedExtensions = computed(() => {
    // 如果传入了accept属性，优先使用accept解析扩展名
    if (props.accept) {
      const acceptTypes = props.accept.split(',').map((type) => type.trim())
      const extensions: string[] = []

      acceptTypes.forEach((type) => {
        if (type.startsWith('.')) {
          // 直接是扩展名，如 .jpg, .png
          extensions.push(type.substring(1).toLowerCase())
        } else if (type.includes('/')) {
          // MIME类型，如 image/*, image/jpeg
          const [mainType, subType] = type.split('/')
          if (subType === '*') {
            // 通配符类型，根据主类型添加常见扩展名
            if (mainType === 'image') {
              extensions.push('jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg')
            } else if (mainType === 'video') {
              extensions.push('mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'rmvb')
            } else if (mainType === 'audio') {
              extensions.push('mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a', 'wma', 'ape')
            }
          } else {
            // 具体MIME类型，转换为扩展名
            const mimeToExt: Record<string, string[]> = {
              'image/jpeg': ['jpg', 'jpeg'],
              'image/png': ['png'],
              'image/gif': ['gif'],
              'image/bmp': ['bmp'],
              'image/webp': ['webp'],
              'image/svg+xml': ['svg'],
              'video/mp4': ['mp4'],
              'video/avi': ['avi'],
              'video/quicktime': ['mov'],
              'audio/mpeg': ['mp3'],
              'audio/wav': ['wav'],
              'audio/ogg': ['ogg'],
              'application/pdf': ['pdf'],
              'application/msword': ['doc'],
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
              'application/vnd.ms-excel': ['xls'],
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['xlsx']
            }
            const exts = mimeToExt[type]
            if (exts) {
              extensions.push(...exts)
            }
          }
        }
      })

      return [...new Set(extensions)] // 去重
    }

    // 如果提供了自定义扩展名，优先使用
    if (props.allowedExtensions) {
      if (Array.isArray(props.allowedExtensions)) {
        return props.allowedExtensions
      } else if (typeof props.allowedExtensions === 'string') {
        return props.allowedExtensions.split(',').map((ext) => ext.trim().replace(/^\./, ''))
      }
    }

    // 根据fileType从上传配置中过滤对应类型的扩展名
    if (uploadStore.uploadConfig?.upload_allow_ext_array) {
      const allExts = uploadStore.uploadConfig.upload_allow_ext_array

      // 定义各类型对应的扩展名
      const typeExtensions = {
        image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
        video: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'rmvb'],
        audio: ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a', 'wma', 'ape']
      }

      // 根据fileType过滤扩展名
      if (props.fileType === 'image') {
        return allExts.filter(ext => typeExtensions.image.includes(ext.toLowerCase()))
      } else if (props.fileType === 'video') {
        return allExts.filter(ext => typeExtensions.video.includes(ext.toLowerCase()))
      } else if (props.fileType === 'audio') {
        return allExts.filter(ext => typeExtensions.audio.includes(ext.toLowerCase()))
      } else if (props.fileType === 'file' || props.fileType === 'all') {
        // file或all类型返回所有扩展名
        return allExts
      } else {
        // 未知类型，返回所有扩展名
        return allExts
      }
    }

    return []
  })

  // 获取最大文件大小(MB)
  const maxFileSizeMB = computed(() => {
    // 如果提供了自定义大小，优先使用
    if (props.maxFileSize > 0) {
      return props.maxFileSize
    }

    // 否则使用上传配置中的大小
    if (uploadStore.uploadConfig?.upload_allow_size) {
      return uploadStore.uploadConfig.upload_allow_size
    }

    return 10 // 默认10MB
  })

  // 自动生成提示文本
  const tipText = computed(() => {
    // 如果用户提供了自定义提示文字，直接使用
    if (props.tipText) return props.tipText

    // 获取配置信息
    const sizeLimit = maxFileSizeMB.value

    // 如果有accept属性，优先根据accept生成类型文本
    let typeText = ''
    if (props.accept) {
      const acceptTypes = props.accept.split(',').map((type) => type.trim())
      const hasImage = acceptTypes.some(
        (type) =>
          type.startsWith('image/') ||
          ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].some((ext) => type === ext)
      )
      const hasVideo = acceptTypes.some(
        (type) =>
          type.startsWith('video/') ||
          ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'].some((ext) => type === ext)
      )
      const hasAudio = acceptTypes.some(
        (type) =>
          type.startsWith('audio/') ||
          ['.mp3', '.wav', '.ogg', '.flac', '.aac', '.m4a'].some((ext) => type === ext)
      )
      const hasDoc = acceptTypes.some(
        (type) =>
          type.startsWith('application/') ||
          ['.pdf', '.doc', '.docx', '.xls', '.xlsx'].some((ext) => type === ext)
      )

      const types = []
      if (hasImage) types.push('图片')
      if (hasVideo) types.push('视频')
      if (hasAudio) types.push('音频')
      if (hasDoc) types.push('文档')

      typeText = types.length > 0 ? types.join('、') : '文件'
    } else {
      // 否则根据fileType生成
      typeText =
        props.fileType === 'image'
          ? '图片'
          : props.fileType === 'video'
            ? '视频'
            : props.fileType === 'audio'
              ? '音频'
              : props.fileType === 'file'
                ? '文件'
                : '媒体'
    }

    // 获取允许的文件扩展名
    const exts = allowedExtensions.value

    // 构建扩展名文本
    let extText = ''
    if (exts.length > 0) {
      // 如果扩展名太多，只显示前6个，避免提示文本过长
      if (exts.length > 6) {
        extText = exts.slice(0, 6).join('、') + '等'
      } else {
        extText = exts.join('、')
      }
    } else {
      extText = typeText
    }

    // 构建完整的提示文本
    let tipParts = []

    // 添加格式支持信息
    tipParts.push(`支持${extText}格式`)

    // 添加文件大小限制
    tipParts.push(`单个文件不超过${sizeLimit}MB`)

    // 添加数量限制（如果有）
    if (props.limit > 0) {
      tipParts.push(`最多上传${props.limit}个文件`)
    }

    return tipParts.join('，')
  })

  // 初始化
  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      // 同步初始化文件列表
      initFileList()

      // 异步初始化上传配置
      initializeUploader()
    })
  })

  // 异步初始化函数
  const initializeUploader = async () => {
    // 确保上传配置已加载（只在没有配置时获取）
    if (!uploadStore.uploadConfig && !uploadStore.loading) {
      try {
        // 尝试获取上传配置
        await uploadStore.getUploadConfig()
      } catch (error) {
        console.error('获取上传配置失败', error)
      }
    }
  }

  // 初始化文件列表
  const initFileList = () => {
    const value = props.modelValue
    if (
      !value ||
      (Array.isArray(value) && value.length === 0) ||
      (typeof value === 'string' && value.trim() === '')
    ) {
      fileList.value = []
      successUrls.value = []
      return
    }

    const urls = Array.isArray(value)
      ? value.map(String).filter((url) => url.trim())
      : value
          .split(',')
          .map((url) => url.trim())
          .filter((url) => url)

    // 如果过滤后没有有效URL，清空列表
    if (urls.length === 0) {
      fileList.value = []
      successUrls.value = []
      return
    }

    // 同步成功URL列表
    successUrls.value = [...urls]

    fileList.value = urls.map((url) => {
      // 从URL中提取文件名
      const urlStr = String(url)
      const name = urlStr.split('/').pop() || `file-${Math.random().toString(36).substring(2)}`

      return {
        name,
        url: urlStr,
        uid: `existing-${Math.random().toString(36).substring(2)}`,
        status: 'success',
        size: 0,
        raw: undefined
      } as CustomUploadFile
    })
  }

  // 成功上传的文件URL数组
  const successUrls = ref<string[]>([])

  // 防止循环更新的标志
  const isInternalUpdate = ref(false)

  // 监听modelValue变化
  watch(
    () => props.modelValue,
    () => {
      try {
        // 如果是内部更新触发的，忽略
        if (isInternalUpdate.value) {
          isInternalUpdate.value = false
          return
        }

        // 清空当前文件列表，避免重复显示
        fileList.value = []

        // 初始化文件列表
        initFileList()
      } catch (error) {
        console.error('FormUploader watch callback error:', error)
        // 发生错误时重置状态
        fileList.value = []
        successUrls.value = []
      }
    },
    { immediate: true } // 立即执行一次
  )

  // 更新组件值
  const updateValue = () => {
    // 设置内部更新标志，防止触发watch回调
    isInternalUpdate.value = true

    if (props.returnValueMode === 'array') {
      emit('update:modelValue', successUrls.value)
      emit('change', successUrls.value, fileList.value)
    } else {
      const valueString = successUrls.value.join(',')
      emit('update:modelValue', valueString)
      emit('change', valueString, fileList.value)
    }
  }

  // 添加成功上传的文件URL
  const addSuccessUrl = (url: string) => {
    if (url && !successUrls.value.includes(url)) {
      successUrls.value.push(url)
      updateValue()
    }
  }

  // 移除文件URL
  const removeSuccessUrl = (url: string) => {
    const index = successUrls.value.indexOf(url)
    if (index > -1) {
      successUrls.value.splice(index, 1)
      updateValue()
    }
  }

  // 上传错误处理
  const handleError = (error: Error, file: any) => {
    console.error('上传失败:', error, file)
    ElMessage.error(`${file.name} 上传失败: ${error.message}`)
    emit('error', error, file)
  }

  // 上传进度处理
  const handleProgress = (event: ProgressEvent, file: any) => {
    const uploadFile = fileList.value.find((item) => item.uid === file.uid)
    if (uploadFile) {
      uploadFile.percentage = Math.round((event.loaded / event.total) * 100)
    }
    emit('progress', event, file)
  }

  // 超出数量限制处理
  const handleExceed = (files: File[], uploadFiles: any[]) => {
    ElMessage.warning(`最多只能上传${props.limit}个文件`)
    emit('exceed', files, uploadFiles)
  }

  // 删除文件
  const removeFile = (index: number) => {
    // 如果是图片且有本地URL，需要释放资源
    const file = fileList.value[index]
    if (file && isImageFile(file) && file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }

    // 从成功URL列表中移除
    if (file.url) {
      removeSuccessUrl(file.url)
    }

    // 从文件列表中移除
    fileList.value.splice(index, 1)
  }

  // 打开媒体选择器
  const openMediaSelector = () => {
    mediaDialogVisible.value = true
  }

  // 媒体选择器确认回调
  const handleMediaConfirm = (media: MediaItem[]) => {
    if (media.length === 0) return

    // 添加到文件列表
    const newFiles = media.map((item) => {
      return {
        name: item.name || item.real_name || '未命名文件',
        url: item.path,
        uid: `media-${Date.now()}-${Math.random().toString(36).substring(2)}`,
        status: 'success',
        size: item.size || 0,
        raw: undefined
      } as CustomUploadFile
    })

    // 检查数量限制
    if (props.limit > 0 && fileList.value.length + newFiles.length > props.limit) {
      // 只添加能够添加的数量
      const availableCount = props.limit - fileList.value.length
      fileList.value = [...fileList.value, ...newFiles.slice(0, availableCount)]
      ElMessage.warning(`最多只能上传${props.limit}个文件，已自动截取`)
    } else {
      fileList.value = [...fileList.value, ...newFiles]
    }

    // 添加媒体文件的URL到成功列表
    newFiles.forEach((file) => {
      if (file.url) {
        addSuccessUrl(file.url)
      }
    })
  }

  // 判断是否为图片文件
  const isImageFile = (file: CustomUploadFile) => {
    if (file.raw?.type?.startsWith('image/')) return true
    if (file.url?.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)) return true
    return false
  }

  // 判断是否为视频文件
  const isVideoFile = (file: CustomUploadFile) => {
    if (file.raw?.type?.startsWith('video/')) return true
    if (file.url?.match(/\.(mp4|avi|mov|mkv|wmv|flv|webm)$/i)) return true
    return false
  }

  // 判断是否为音频文件
  const isAudioFile = (file: CustomUploadFile) => {
    if (file.raw?.type?.startsWith('audio/')) return true
    if (file.url?.match(/\.(mp3|wav|ogg|flac|aac|m4a)$/i)) return true
    return false
  }

  // 判断是否为文档文件
  const isDocumentFile = (file: CustomUploadFile) => {
    if (file.raw?.type?.startsWith('application/')) return true
    if (file.url?.match(/\.(pdf|doc|docx|ppt|pptx|xls|xlsx|txt|md|html|css|js|json)$/i)) return true
    return false
  }

  // 判断是否为PDF文件
  const isPdfFile = (file: CustomUploadFile) => {
    if (file.raw?.type?.startsWith('application/pdf')) return true
    if (file.url?.match(/\.pdf$/i)) return true
    return false
  }

  // 判断是否为Word文件
  const isWordFile = (file: CustomUploadFile) => {
    if (file.raw?.type?.startsWith('application/msword')) return true
    if (file.url?.match(/\.docx?$/i)) return true
    return false
  }

  // 判断是否为Excel文件
  const isExcelFile = (file: CustomUploadFile) => {
    if (file.raw?.type?.startsWith('application/vnd.ms-excel')) return true
    if (file.url?.match(/\.xlsx?$/i)) return true
    return false
  }

  // 格式化文件大小
  const formatFileSize = (size: number | undefined) => {
    if (!size) return ''

    if (size < 1024) {
      return `${size} B`
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`
    } else {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`
    }
  }

  // 判断是否可以预览文件
  const canPreview = (file: CustomUploadFile) => {
    if (file.status !== 'success') return false
    if (isImageFile(file) || isVideoFile(file) || isAudioFile(file) || isPdfFile(file)) return true
    return false
  }

  // 预览文件
  const handlePreviewFile = (file: CustomUploadFile) => {
    previewFile.value = file
    previewDialogVisible.value = true
  }

  // 下载文件
  const downloadFile = (file: CustomUploadFile | null) => {
    if (!file || !file.url) return

    // 创建一个隐藏的a标签来下载文件
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name || 'download'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 自定义上传实现
  const customUpload = async (options: UploadRequestOptions) => {
    const { file, onError, onProgress } = options

    try {
      // 确保上传配置已加载（避免重复获取）
      if (!uploadStore.uploadConfig && !uploadStore.loading) {
        try {
          await uploadStore.getUploadConfig()
        } catch (error) {
          console.error('获取上传配置失败', error)
          throw new Error('上传配置加载失败，请稍后重试')
        }
      }

      // 确保上传配置是否存在
      if (!uploadStore.uploadConfig) {
        throw new Error('上传配置未加载，请刷新页面重试')
      }

      // 确保upload_allow_ext_array存在
      if (!uploadStore.uploadConfig.upload_allow_ext_array) {
        // 如果不存在但有upload_allow_ext，则尝试从中创建
        if (uploadStore.uploadConfig.upload_allow_ext) {
          const extArray = uploadStore.uploadConfig.upload_allow_ext
            .split(',')
            .map((ext) => ext.trim().replace(/^\./, ''))
          uploadStore.uploadConfig.upload_allow_ext_array = extArray
        } else {
          // 如果两者都不存在，则抛出错误
          throw new Error('上传配置错误：未定义允许的文件扩展名')
        }
      }

      // 检查文件类型是否符合要求
      const fileObj = file as File
      const fileName = fileObj.name
      const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
      const fileType = fileObj.type.split('/')[0]
      const allowedExts = allowedExtensions.value

      // 如果有accept属性，优先使用accept进行校验
      if (props.accept) {
        if (allowedExts.length > 0 && !allowedExts.includes(fileExt)) {
          throw new Error(`不支持的文件类型，请上传以下格式的文件：${allowedExts.join(', ')}`)
        }
      } else if (props.fileType === 'file' || props.fileType === 'all') {
        // 如果是file类型，允许上传图片、视频、音频和文档
        if (allowedExts.length > 0 && !allowedExts.includes(fileExt)) {
          throw new Error(`不支持的文件类型，请上传以下格式的文件：${allowedExts.join(', ')}`)
        }
      } else if (props.fileType === 'image' && fileType !== 'image') {
        throw new Error('当前选择的是图片类型，请上传图片文件')
      } else if (props.fileType === 'video' && fileType !== 'video') {
        throw new Error('当前选择的是视频类型，请上传视频文件')
      } else if (props.fileType === 'audio' && fileType !== 'audio') {
        throw new Error('当前选择的是音频类型，请上传音频文件')
      }

      // 先创建一个临时文件对象，显示上传进度
      const tempFile: CustomUploadFile = {
        uid: (file as any).uid,
        name: fileObj.name,
        status: 'uploading',
        percentage: 0,
        size: fileObj.size,
        raw: fileObj
      }

      // 如果是图片，先创建一个本地预览URL
      if (isImageFile(tempFile)) {
        tempFile.url = URL.createObjectURL(fileObj)
      }

      // 添加到文件列表
      fileList.value.push(tempFile)

      // 设置进度回调
      const progressHandler = (event: ProgressEvent) => {
        const percent = Math.round((event.loaded / event.total) * 100)
        const tempFileIndex = fileList.value.findIndex((item) => item.uid === tempFile.uid)
        if (tempFileIndex !== -1) {
          fileList.value[tempFileIndex].percentage = percent
        }
        // 创建一个符合UploadProgressEvent的对象
        if (onProgress) {
          const progressEvent = {
            percent,
            ...event
          } as any
          onProgress(progressEvent)
        }
      }

      // 使用UploadService上传文件
      const result = await UploadService.uploadFile(
        file as File,
        props.fileType === 'file' ? fileType : props.fileType,
        {
          cate_id: props.categoryId,
          storage: props.storage || uploadStore.uploadConfig.upload_allow_type || undefined
        },
        progressHandler
      )

      if (result.success && result.data) {
        console.log('上传成功，返回数据:', result.data)

        // 不再手动调用onSuccess，而是直接更新文件状态
        const tempFileIndex = fileList.value.findIndex((item) => item.uid === tempFile.uid)
        if (tempFileIndex !== -1) {
          const responseData = result.data
          const fileUrl = responseData.url || responseData.path || ''
          const fileName =
            responseData.name || responseData.real_name || fileObj.name || '未命名文件'

          // 更新文件信息
          fileList.value[tempFileIndex].status = 'success'
          fileList.value[tempFileIndex].url = fileUrl
          fileList.value[tempFileIndex].name = fileName
          fileList.value[tempFileIndex].size = responseData.size || fileObj.size

          // 添加成功上传的文件URL
          addSuccessUrl(fileUrl)

          // 触发成功事件
          emit('success', responseData, fileList.value[tempFileIndex])
        }

        // 返回一个包含文件信息的对象，让Element Plus知道上传成功了
        return {
          ...result.data,
          status: 'success'
        }
      } else {
        // 创建一个符合UploadAjaxError接口的错误对象
        const error = new Error(result.message || '上传失败') as any
        error.status = 400
        error.method = 'POST'
        error.url = ''
        onError(error)
        // 从文件列表中移除临时文件
        const tempFileIndex = fileList.value.findIndex((item) => item.uid === tempFile.uid)
        if (tempFileIndex !== -1) {
          fileList.value.splice(tempFileIndex, 1)
        }
        throw error
      }
    } catch (error: any) {
      // 确保错误对象符合UploadAjaxError接口
      if (!error.status) {
        error.status = 500
        error.method = 'POST'
        error.url = ''
      }
      onError(error)
      // 从文件列表中移除临时文件
      const tempFileIndex = fileList.value.findIndex((item) => item.uid === (file as any).uid)
      if (tempFileIndex !== -1) {
        fileList.value.splice(tempFileIndex, 1)
      }
      throw error
    }
  }

  // 上传前检查
  const beforeUpload = (file: UploadRawFile) => {
    // 检查上传配置是否已加载
    if (!uploadStore.uploadConfig) {
      ElMessage.error('上传配置未加载，请刷新页面重试')
      return false
    }

    // 确保upload_allow_ext_array存在
    if (!uploadStore.uploadConfig.upload_allow_ext_array) {
      // 如果不存在但有upload_allow_ext，则尝试从中创建
      if (uploadStore.uploadConfig.upload_allow_ext) {
        const extArray = uploadStore.uploadConfig.upload_allow_ext
          .split(',')
          .map((ext) => ext.trim().replace(/^\./, ''))
        uploadStore.uploadConfig.upload_allow_ext_array = extArray
      } else {
        ElMessage.error('上传配置错误：未定义允许的文件扩展名')
        return false
      }
    }

    // 检查文件类型
    const fileName = file.name
    const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
    const allowedExts = allowedExtensions.value

    // 如果有accept属性，优先使用accept进行校验
    if (props.accept) {
      if (allowedExts.length > 0 && !allowedExts.includes(fileExt)) {
        ElMessage.error(`不支持的文件类型，请上传以下格式的文件：${allowedExts.join(', ')}`)
        return false
      }
    } else if (props.fileType !== 'all' && props.fileType !== 'file') {
      // 对于图片、视频、音频类型进行特定检查
      const fileType = file.type.split('/')[0]
      if (props.fileType !== fileType) {
        ElMessage.error(
          `请上传${props.fileType === 'image' ? '图片' : props.fileType === 'video' ? '视频' : props.fileType === 'audio' ? '音频' : '文件'}类型文件`
        )
        return false
      }
    } else if (props.fileType === 'file' || props.fileType === 'all') {
      // 对于file或all类型，检查文件扩展名是否在允许的列表中
      if (allowedExts.length > 0 && !allowedExts.includes(fileExt)) {
        ElMessage.error(`不支持的文件类型，请上传以下格式的文件：${allowedExts.join(', ')}`)
        return false
      }
    }

    // 检查文件大小
    const maxSize = maxFileSizeMB.value * 1024 * 1024
    if (file.size > maxSize) {
      ElMessage.error(`文件大小不能超过${maxFileSizeMB.value}MB`)
      return false
    }

    return true
  }
</script>

<style lang="scss" scoped>
  .form-uploader {
    width: 100%;

    .file-list {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 12px;

      .file-item {
        position: relative;
        width: 120px;
        height: 120px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        overflow: hidden;
        transition: all 0.3s;
        background-color: #f5f7fa;

        &:hover {
          border-color: var(--el-color-primary);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

          .delete-button,
          .file-actions {
            opacity: 1;
          }
        }

        &.is-image {
          .image-preview {
            width: 100%;
            height: 100%;

            .preview-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        &.is-video .file-icon,
        &.is-audio .file-icon,
        &.is-document .file-icon {
          font-size: 36px;
        }

        &.is-video .file-icon {
          color: #ff6d70;
        }

        &.is-audio .file-icon {
          color: #67c23a;
        }

        &.is-document .file-icon {
          color: #409eff;
        }

        .file-preview {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          padding: 10px;

          .file-icon {
            font-size: 32px;
            color: #909399;
            margin-bottom: 8px;
          }

          .file-info {
            width: 100%;
            text-align: center;

            .file-name {
              font-size: 12px;
              color: #606266;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .file-size {
              font-size: 12px;
              color: #909399;
            }
          }
        }

        .upload-progress {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          padding: 4px;
          background-color: rgba(255, 255, 255, 0.8);
        }

        .delete-button {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.5);
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          opacity: 0;
          transition: opacity 0.3s;

          &:hover {
            background-color: rgba(0, 0, 0, 0.7);
          }
        }

        .file-actions {
          position: absolute;
          bottom: 4px;
          left: 0;
          right: 0;
          display: flex;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 2px 0;
          opacity: 0;
          transition: opacity 0.3s;

          .el-button {
            color: #fff;
            padding: 2px 5px;

            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }

    .upload-button-container {
      :deep(.el-upload) {
        //width: 100%;
      }

      :deep(.el-upload-dragger) {
        width: 100%;
        //height: 180px;
        padding: 0 5px;

        .el-icon--upload {
          margin-top: 40px;
          font-size: 48px;
          color: var(--el-color-primary);
        }

        .el-upload__text {
          margin-top: 16px;
          color: #606266;

          em {
            color: var(--el-color-primary);
            font-style: normal;
          }
        }
      }

      .upload-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 8px;
        line-height: 1.5;
      }
    }

    .media-selector-button {
      margin-top: 16px;
      text-align: center;
    }
  }

  // 预览对话框样式
  .file-preview-container {
    width: 100%;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;

    .image-preview-dialog {
      width: 100%;
      text-align: center;

      .preview-image-full {
        max-width: 100%;
        max-height: 70vh;
      }
    }

    .video-preview-dialog {
      width: 100%;

      .preview-video {
        width: 100%;
        max-height: 70vh;
      }
    }

    .audio-preview-dialog {
      width: 100%;
      padding: 20px;
      text-align: center;

      .preview-audio {
        width: 100%;
        margin-bottom: 20px;
      }

      .audio-info {
        text-align: center;
      }
    }

    .pdf-preview-dialog {
      width: 100%;
      height: 70vh;

      .preview-pdf {
        width: 100%;
        height: 100%;
      }
    }

    .other-file-preview {
      width: 100%;
      padding: 30px;
      text-align: center;
    }
  }

  /* 优化图片预览器的行为 */
  :deep(.el-image-viewer__wrapper) {
    z-index: 3000 !important;
  }

  :deep(.el-image-viewer__mask) {
    /* 确保遮罩层可以正确响应点击事件 */
    pointer-events: auto !important;
    cursor: pointer;
  }

  :deep(.el-image-viewer__canvas) {
    /* 防止图片拖动时干扰点击事件 */
    user-select: none;
  }

  :deep(.el-image-viewer__canvas img) {
    /* 确保图片本身不会阻止遮罩层的点击事件 */
    pointer-events: none;
  }
</style>
