<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 文章分类表模型
 */
class SystemArticleCategory extends BaseModel
{
	// 设置表名
	protected $name = 'system_article_category';
	
	// 设置主键
	protected $pk = 'id';
	
	public function creator()
	{
		return $this->hasOne(AdminModel::class, 'id', 'creator_id')
		            ->bind([
			            'creator_name' => 'real_name',
		            ]);
	}

	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'name' => ['type' => 'like'],
			'status' => ['type' => 'eq'],
			'created_at' => ['type' => 'date'],
			'updated_at' => ['type' => 'date'],
		];
	}
} 