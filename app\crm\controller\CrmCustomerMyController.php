<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmCustomerService;
use app\crm\service\CustomerSeaManagementService;
use app\crm\service\CustomerPermissionService;
use app\crm\controller\traits\CustomerContactTrait;
use app\crm\controller\traits\CustomerContractTrait;
use app\crm\controller\traits\CustomerReceivableTrait;
use app\crm\controller\traits\CustomerFollowTrait;
use think\facade\Log;
use think\response\Json;

/**
 * 客户表控制器
 */
class CrmCustomerMyController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	use CustomerContactTrait, CustomerContractTrait, CustomerReceivableTrait, CustomerFollowTrait;
	
	/**
	 * @var CrmCustomerService
	 */
	protected CrmCustomerService $service;
	
	/**
	 * @var CustomerSeaManagementService
	 */
	protected CustomerSeaManagementService $seaService;
	
	/**
	 * @var CustomerPermissionService
	 */
	protected CustomerPermissionService $permissionService;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service           = CrmCustomerService::getInstance();
		$this->seaService        = CustomerSeaManagementService::getInstance();
		$this->permissionService = app(CustomerPermissionService::class);
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params           = $this->request->param();
		$params['in_sea'] = 0;
		$result           = $this->service->search($params);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id], [
			'owner'
		]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}
	
	/**
	 * 新增
	 *
	 * @return Json
	 */
	public function add(): Json
	{
		try {
			$result = $this->service->add($this->request->post());
			if ($result) {
				return $this->success('添加成功');
			}
			else {
				return $this->error('添加失败');
			}
		}
		catch (\Exception $e) {
			return $this->error('添加失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新
	 *
	 * @param int $id
	 * @return Json
	 */
	public function edit(int $id): Json
	{
		try {
			$data     = $this->request->post();
			$unsetArr = [
				'in_sea',
				'owner_user_id'
			];
			foreach ($unsetArr as $item) {
				if (isset($data[$item])) {
					unset($data[$item]);
				}
			}
			$result = $this->service->edit($data, ['id' => $id]);
			if ($result) {
				return $this->success('更新成功');
			}
			else {
				return $this->error('更新失败');
			}
		}
		catch (\Exception $e) {
			return $this->error('更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	// ==================== 公海客户相关方法 ====================
	
	/**
	 * 获取公海客户列表
	 *
	 * @return Json
	 */
	public function sea(): Json
	{
		$params = $this->request->get();
		
		try {
			// 添加公海筛选条件
			$params['in_sea'] = 1;
			$params['status'] = 1;
			
			// 获取列表数据
			$result = $this->service->search($params);
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			Log::error('获取公海客户列表失败: ' . $e->getMessage());
			return $this->error('获取失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 认领公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function claimFromSea(int $id): Json
	{
		try {
			$result = $this->seaService->claimCustomer($id, get_user_id());
			
			return $result
				? $this->success('认领成功')
				: $this->error('认领失败');
		}
		catch (\Exception $e) {
			Log::error('认领客户失败: ' . $e->getMessage());
			return $this->error('认领失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 批量认领公海客户
	 *
	 * @return Json
	 */
	public function batchClaimFromSea(): Json
	{
		$customerIds = $this->request->post('customer_ids', []);
		
		if (empty($customerIds) || !is_array($customerIds)) {
			return $this->error('请选择要认领的客户');
		}
		
		try {
			$successCount = 0;
			$failedCount  = 0;
			$errors       = [];
			
			foreach ($customerIds as $customerId) {
				try {
					$result = $this->seaService->claimCustomer((int)$customerId, get_user_id());
					if ($result) {
						$successCount++;
					}
					else {
						$failedCount++;
						$errors[] = "客户ID {$customerId}:认领失败";
					}
				}
				catch (\Exception $e) {
					$failedCount++;
					$errors[] = "客户ID {$customerId}: " . $e->getMessage();
				}
			}
			
			$message = "批量认领完成，成功 {$successCount} 个";
			if ($failedCount > 0) {
				$message .= "，失败 {$failedCount} 个";
			}
			
			return $this->success($message, [
				'success_count' => $successCount,
				'failed_count'  => $failedCount,
				'errors'        => $errors
			]);
		}
		catch (\Exception $e) {
			Log::error('批量认领客户失败: ' . $e->getMessage());
			return $this->error('批量认领失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 回收客户到公海
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function recycle_customer(int $id): Json
	{
		$reason = $this->request->post('reason', '');
		
		try {
			$result = $this->seaService->recycleCustomer($id, $reason);
			
			return $result
				? $this->success('认领成功')
				: $this->error('认领失败');
		}
		catch (\Exception $e) {
			Log::error('回收客户失败: ' . $e->getMessage());
			return $this->error('回收失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 锁定公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function lockSeaCustomer(int $id): Json
	{
		$lockMinutes = $this->request->post('lock_minutes', 30);
		
		try {
			$lockExpireTime = date('Y-m-d H:i:s', time() + $lockMinutes * 60);
			
			$result = $this->service->updateField($id, [
				'lock_status'      => 1,
				'lock_expire_time' => $lockExpireTime
			]);
			
			if ($result) {
				return $this->success('锁定成功', [
					'customer_id'      => $id,
					'lock_expire_time' => $lockExpireTime
				]);
			}
			else {
				return $this->error('锁定失败');
			}
		}
		catch (\Exception $e) {
			Log::error('锁定客户失败: ' . $e->getMessage());
			return $this->error('锁定失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 解锁公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function unlockSeaCustomer(int $id): Json
	{
		try {
			$result = $this->service->updateField($id, [
				'lock_status'      => 0,
				'lock_expire_time' => null
			]);
			
			if ($result) {
				return $this->success('解锁成功');
			}
			else {
				return $this->error('解锁失败');
			}
		}
		catch (\Exception $e) {
			Log::error('解锁客户失败: ' . $e->getMessage());
			return $this->error('解锁失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 获取下拉选项
	 *
	 * @return Json
	 */
	public function options(): Json
	{
		$params     = $this->request->param();
		$labelField = $params['label_field'] ?? 'customer_name';
		$valueField = $params['value_field'] ?? 'id';
		$where      = $params['where'] ?? [];
		
		$result = $this->service->getSelectOptions($where, $labelField, $valueField);
		return $this->success('获取成功', $result);
	}
	
}