CREATE TABLE `system_rate_limit_rule` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '规则名称',
    `type` varchar(20) NOT NULL COMMENT '规则类型：global, tenant, user',
    `tenant_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '租户ID，type=tenant时必填',
    `key` varchar(100) NOT NULL COMMENT '限流键名',
    `algorithm` varchar(20) NOT NULL DEFAULT 'fixed' COMMENT '算法类型：fixed, sliding',
    `limit_count` int(10) UNSIGNED NOT NULL DEFAULT 100 COMMENT '限流阈值',
    `time_window` int(10) UNSIGNED NOT NULL DEFAULT 60 COMMENT '时间窗口(秒)',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_type_tenant` (`type`, `tenant_id`),
    KEY `idx_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口限流规则表';

CREATE TABLE `system_rate_limit_log` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_id` bigint(20) UNSIGNED NOT NULL COMMENT '规则ID',
    `key` varchar(100) NOT NULL COMMENT '限流键名',
    `identifier` varchar(100) NOT NULL COMMENT '标识值（IP/用户ID等）',
    `request_count` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '请求次数',
    `is_limited` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被限制：0-否，1-是',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_rule_identifier` (`rule_id`, `identifier`),
    KEY `idx_key_identifier` (`key`, `identifier`),
    KEY `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口限流日志表'; 