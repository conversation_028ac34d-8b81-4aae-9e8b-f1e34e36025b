# 第三阶段：前端页面开发任务清单

## 📋 阶段概述
**阶段目标**：基于生成器生成的前端页面，开发审批状态展示和明细编辑功能
**预计时间**：2-3天（已有状态管理和API封装系统）
**前置条件**：✅ 第二阶段已完成（后端业务逻辑开发）
**技术优势**：✅ 现有状态管理系统 ✅ 现有API封装模式 ✅ 现有组件库

## 🎯 核心任务分解

### 任务3.1：基础页面扩展 ⭐⭐
**负责人**：前端开发
**预计时间**：0.5天
**优先级**：高

#### 📝 具体任务
- [ ] **扩展报价单列表页**
  - 文件路径：`src/views/daily/daily_price_order/index.vue`
  - 基于生成器页面进行扩展

- [ ] **列表页扩展功能**
  ```vue
  ✅ 审批状态筛选器     # 下拉选择：全部/草稿/审批中/已通过等
  ✅ 审批状态标签显示   # 不同颜色的状态标签
  ✅ 操作按钮组        # 根据状态显示不同操作按钮
  ✅ 快速操作功能      # 提交审批、撤回、作废等
  ✅ 日期范围筛选      # 按报价日期筛选
  ```

- [ ] **扩展报价单表单页**
  - 文件路径：`src/views/daily/daily_price_order/form.vue`
  - 集成审批状态展示和明细编辑

- [ ] **表单页扩展功能**
  ```vue
  ✅ 审批状态卡片      # 显示当前审批状态和进度
  ✅ 明细表格编辑      # 内联编辑产品明细
  ✅ 操作按钮组        # 根据状态显示操作按钮
  ✅ 自动标题生成      # 基于日期自动生成标题
  ✅ 从昨日复制功能    # 一键复制昨日报价
  ```

- [ ] **扩展详情页**
  - 文件路径：`src/views/daily/daily_price_order/detail.vue`
  - 添加审批信息和操作历史

#### 🧪 测试要点
- [ ] 列表页筛选和操作功能测试
- [ ] 表单页编辑和保存功能测试
- [ ] 详情页信息展示测试
- [ ] 页面路由跳转测试

---

### 任务3.2：核心组件开发 ⭐⭐⭐
**负责人**：前端开发
**预计时间**：1.5天
**优先级**：高

#### 📝 具体任务
- [ ] **创建审批状态卡片组件**
  - 文件路径：`src/views/daily/daily_price_order/components/ApprovalStatusCard.vue`
  - 功能：状态展示、进度条、操作按钮

- [ ] **ApprovalStatusCard组件功能**
  ```vue
  ✅ 状态标签显示      # 草稿/审批中/已通过等，不同颜色
  ✅ 审批进度条        # 显示审批流程进度
  ✅ 审批信息详情      # 提交人、审批人、时间等
  ✅ 操作按钮组        # 根据状态显示相应操作按钮
  ✅ 审批意见显示      # 拒绝原因、审批意见等
  ```

- [ ] **创建明细表格编辑组件**
  - 文件路径：`src/views/daily/daily_price_order/components/PriceItemTable.vue`
  - 功能：内联编辑、价格计算、批量操作

- [ ] **PriceItemTable组件功能**
  ```vue
  ✅ 内联编辑功能      # 直接在表格中编辑价格
  ✅ 价格变动计算      # 自动计算涨跌幅度
  ✅ 价格变动指示器    # 涨价红色↗️、降价绿色↘️、无变化灰色➡️
  ✅ 产品供应商选择器  # 联动选择供应商和产品
  ✅ 批量操作功能      # 批量添加、删除明细
  ✅ 数据验证提示      # 价格格式、必填项验证
  ```

- [ ] **创建价格变动指示器组件**
  - 文件路径：`src/views/daily/daily_price_order/components/PriceChangeIndicator.vue`
  - 功能：涨跌标识、颜色区分、图标显示

- [ ] **创建产品供应商选择器组件**
  - 文件路径：`src/views/daily/daily_price_order/components/ProductSupplierSelector.vue`
  - 功能：联动选择、搜索过滤

#### 🧪 测试要点
- [ ] 审批状态卡片显示和交互测试
- [ ] 明细表格编辑和计算功能测试
- [ ] 价格变动指示器显示测试
- [ ] 产品选择器联动功能测试
- [ ] 组件响应式布局测试

---

### 任务3.3：API集成与状态管理优化 ⭐
**负责人**：前端开发
**预计时间**：0.5天
**优先级**：中（已有状态管理系统）

#### 📝 具体任务
- [ ] **创建API接口文件**
  - 文件路径：`src/api/daily/dailyPriceOrder.ts`
  - 参考文件：`src/api/crm/crmCustomerDetail.ts`
  - 使用现有的状态管理系统

- [ ] **API类设计（基于现有模式）**
  ```typescript
  export class DailyPriceOrderApi {
    // 基础CRUD - 复用现有CRUD模式
    static getList(params?: any)
    static getDetail(id: number)
    static create(data: any)
    static update(id: number, data: any)
    static delete(id: number)

    // 业务扩展 - 新增审批相关接口
    static submitApproval(id: number)
    static withdrawApproval(id: number)
    static voidOrder(id: number, reason: string)
    static saveItems(orderId: number, items: any[])
    static copyFromYesterday(targetDate: string)
  }
  ```

- [ ] **集成现有状态管理**
  - 使用现有的Pinia Store模式
  - 复用现有的loading、error处理机制
  - 集成现有的权限控制系统

- [ ] **封装API接口（基于现有模式）**
  - 文件路径：`src/api/daily/dailyPriceOrder.ts`
  - 参考模式：`src/api/crm/crmCustomerDetail.ts`
  - 使用类静态方法封装

- [ ] **API接口设计（参考CrmCustomerDetailApi模式）**
  ```typescript
  export class DailyPriceOrderApi {
    // ==================== 基础CRUD操作 ====================
    static getList(params?: any)                    # 报价单列表
    static getDetail(id: number)                    # 报价单详情
    static create(data: any)                        # 创建报价单
    static update(id: number, data: any)            # 更新报价单
    static delete(id: number)                       # 删除报价单

    // ==================== 明细操作 ====================
    static saveItems(orderId: number, items: any[]) # 保存明细
    static getItems(orderId: number)                # 获取明细列表

    // ==================== 审批操作 ====================
    static submitApproval(id: number)               # 提交审批
    static withdrawApproval(id: number)             # 撤回审批
    static voidOrder(id: number, reason: string)    # 作废报价单

    // ==================== 业务操作 ====================
    static copyFromYesterday(targetDate: string)    # 从昨日复制
    static export(id: number)                       # 导出报价单
    static getStatistics(params?: any)              # 获取统计数据
  }
  ```

#### 🧪 测试要点
- [ ] Store状态管理测试
- [ ] API接口调用测试
- [ ] 权限控制逻辑测试
- [ ] 错误处理机制测试
- [ ] 数据同步测试

---

## 🎨 UI/UX设计规范

### 审批状态色彩系统
```css
.status-draft { color: #909399; background: #f4f4f5; }      /* 草稿 - 灰色 */
.status-pending { color: #409eff; background: #ecf5ff; }    /* 审批中 - 蓝色 */
.status-approved { color: #67c23a; background: #f0f9ff; }   /* 已通过 - 绿色 */
.status-rejected { color: #f56c6c; background: #fef0f0; }   /* 已拒绝 - 红色 */
.status-terminated { color: #e6a23c; background: #fdf6ec; } /* 已终止 - 橙色 */
.status-recalled { color: #909399; background: #f4f4f5; }   /* 已撤回 - 灰色 */
.status-voided { color: #606266; background: #f0f0f0; }     /* 已作废 - 深灰 */
```

### 价格变动色彩系统
```css
.price-rise { color: #f56c6c; }    /* 涨价 - 红色 */
.price-fall { color: #67c23a; }    /* 降价 - 绿色 */
.price-stable { color: #909399; }  /* 无变化 - 灰色 */
```

### 响应式设计断点
```css
/* 桌面端 */
@media (min-width: 1200px) {
  .price-order-form { max-width: 1200px; margin: 0 auto; }
  .form-columns { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
}

/* 平板端 */
@media (min-width: 768px) and (max-width: 1199px) {
  .price-items-table { font-size: 14px; }
  .action-buttons .el-button { padding: 8px 12px; }
}

/* 移动端 */
@media (max-width: 767px) {
  .price-items-table { display: none; }
  .mobile-card-list { display: block; }
}
```

---

## 🔧 技术实现要点

### 1. 组件通信设计
```typescript
// 父子组件通信
interface PriceItemTableProps {
  modelValue: PriceItem[]
  readonly: boolean
  loading: boolean
}

interface PriceItemTableEmits {
  'update:modelValue': [value: PriceItem[]]
  'change': [items: PriceItem[]]
  'add-item': []
  'remove-item': [index: number]
}
```

### 2. 表单验证规则
```typescript
const formRules = {
  price_date: [
    { required: true, message: '请选择报价日期', trigger: 'change' }
  ],
  items: [
    { 
      validator: (rule: any, value: PriceItem[], callback: Function) => {
        if (!value || value.length === 0) {
          callback(new Error('至少添加一个产品明细'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}
```

### 3. 权限控制实现
```typescript
// 基于审批状态的权限控制
const permissions = computed(() => ({
  canEdit: [0, 3, 5].includes(currentOrder.value?.approval_status),
  canSubmit: currentOrder.value?.approval_status === 0 && 
             currentOrder.value?.total_items > 0,
  canWithdraw: currentOrder.value?.approval_status === 1,
  canVoid: currentOrder.value?.approval_status === 2
}))
```

---

## 📊 质量保证

### 代码质量要求
- [ ] **TypeScript严格模式**：启用strict模式，类型定义完整
- [ ] **ESLint规范**：遵循项目ESLint配置
- [ ] **组件复用性**：组件设计考虑复用性和扩展性
- [ ] **性能优化**：合理使用computed、watch、memo等
- [ ] **无障碍访问**：添加适当的aria标签和键盘导航

### 测试覆盖要求
- [ ] **组件单元测试**：核心组件测试覆盖率 > 80%
- [ ] **用户交互测试**：关键用户操作流程测试
- [ ] **响应式测试**：不同屏幕尺寸适配测试
- [ ] **兼容性测试**：主流浏览器兼容性测试

---

## 🚀 验收标准

### 功能验收
- [ ] ✅ 报价单列表筛选和操作功能正常
- [ ] ✅ 报价单表单编辑和保存功能正常
- [ ] ✅ 审批状态展示和操作正确
- [ ] ✅ 明细表格编辑和计算准确
- [ ] ✅ 从昨日复制功能正常
- [ ] ✅ 响应式布局适配良好

### 技术验收
- [ ] ✅ 代码通过ESLint检查
- [ ] ✅ TypeScript类型检查通过
- [ ] ✅ 组件单元测试通过
- [ ] ✅ 页面加载性能良好（< 2秒）
- [ ] ✅ 内存泄漏检查通过

### 用户体验验收
- [ ] ✅ 界面美观，交互流畅
- [ ] ✅ 操作反馈及时准确
- [ ] ✅ 错误提示友好明确
- [ ] ✅ 加载状态展示合理
- [ ] ✅ 移动端体验良好

---

## 📝 开发注意事项

1. **基于生成器扩展**：在生成的基础页面上扩展，不要重写
2. **组件化设计**：合理拆分组件，提高复用性
3. **状态管理**：使用Pinia进行统一状态管理
4. **类型安全**：充分利用TypeScript类型系统
5. **性能优化**：合理使用Vue3的响应式特性
6. **用户体验**：注重加载状态、错误处理、操作反馈

---

## 📞 协作方式

- **每日站会**：上午9:30，汇报进度和问题
- **UI评审**：每个组件完成后进行UI评审
- **联调测试**：前后端接口联调测试
- **用户测试**：功能完成后进行用户体验测试

---

**任务开始时间**：第二阶段完成后  
**预期完成时间**：3-4个工作日  
**下一阶段**：功能完善与测试部署
