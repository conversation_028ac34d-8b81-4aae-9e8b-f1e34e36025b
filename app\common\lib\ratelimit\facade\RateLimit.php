<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\facade;

use think\Facade;
use app\common\lib\ratelimit\RateLimiter;

/**
 * 限流门面类
 * 
 * @see \app\common\lib\ratelimit\RateLimiter
 * @method static bool check(string $key, ?int $tenantId = null) 检查一个API键是否被允许通过
 * @method static bool checkRule(\app\common\lib\ratelimit\rule\Rule $rule) 检查一个自定义规则是否被允许通过
 * @method static \app\common\lib\ratelimit\algorithm\AlgorithmInterface createFixedWindow(string $key, int $limitCount, int $timeWindow, ?\app\common\lib\ratelimit\identifier\IdentifierInterface $identifier = null) 创建固定窗口限流器
 * @method static \app\common\lib\ratelimit\algorithm\AlgorithmInterface createSlidingWindow(string $key, int $limitCount, int $timeWindow, ?\app\common\lib\ratelimit\identifier\IdentifierInterface $identifier = null) 创建滑动窗口限流器
 * @method static \app\common\lib\ratelimit\identifier\IdentifierInterface createIpIdentifier() 创建IP标识符
 * @method static \app\common\lib\ratelimit\identifier\IdentifierInterface createUserIdentifier() 创建用户标识符
 * @method static \app\common\lib\ratelimit\identifier\IdentifierInterface createTenantIdentifier() 创建租户标识符
 * @method static \app\common\lib\ratelimit\config\RateLimitConfig getConfig() 获取配置管理实例
 * @method static bool clearRulesCache(?string $type = null, ?int $tenantId = null, ?string $key = null) 清除规则缓存
 * @method static \app\common\lib\ratelimit\RateLimiter enableLogging(bool $enable = true) 设置是否记录日志
 */
class RateLimit extends Facade
{
    /**
     * 获取当前Facade对应类名
     * 
     * @return string
     */
    protected static function getFacadeClass()
    {
        return RateLimiter::class;
    }
} 