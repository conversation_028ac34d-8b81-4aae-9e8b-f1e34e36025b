<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit;

use app\common\lib\ratelimit\config\RateLimitConfig;
use app\common\lib\ratelimit\exception\RateLimitException;
use app\common\lib\ratelimit\rule\Rule;
use app\common\lib\ratelimit\storage\StorageInterface;
use app\common\lib\ratelimit\storage\RedisStorage;
use app\common\lib\ratelimit\algorithm\AlgorithmInterface;
use app\common\lib\ratelimit\algorithm\FixedWindowAlgorithm;
use app\common\lib\ratelimit\algorithm\SlidingWindowAlgorithm;
use app\common\lib\ratelimit\identifier\IdentifierInterface;
use app\common\lib\ratelimit\identifier\IpIdentifier;
use app\common\lib\ratelimit\identifier\UserIdentifier;
use app\common\lib\ratelimit\identifier\TenantIdentifier;
use think\facade\Log;

/**
 * 接口限流主类
 */
class RateLimiter
{
    /**
     * 配置管理
     */
    protected RateLimitConfig $config;
    
    /**
     * 默认存储实例
     */
    protected StorageInterface $storage;
    
    /**
     * 规则预加载，避免重复加载
     */
    protected array $loadedRules = [];
    
    /**
     * 是否记录日志
     */
    protected bool $enableLogging = true;
    
    /**
     * 构造函数
     *
     * @param RateLimitConfig|null $config 配置管理
     * @param StorageInterface|null $storage 存储适配器
     */
    public function __construct(?RateLimitConfig $config = null, ?StorageInterface $storage = null)
    {
        $this->storage = $storage ?? new RedisStorage();
        $this->config = $config ?? new RateLimitConfig($this->storage);
    }
    
    /**
     * 检查一个API键是否被允许通过
     *
     * @param string $key API键名
     * @param int|null $tenantId 租户ID（可选）
     * @throws RateLimitException 当请求被限流时抛出
     * @return bool 是否允许通过
     */
    public function check(string $key, ?int $tenantId = null): bool
    {
        // 获取所有匹配的规则
        $rules = $this->config->getRulesByKey($key, $tenantId);
        
        // 如果没有规则，则默认允许通过
        if (empty($rules)) {
            return true;
        }
        
        // 检查所有规则
        foreach ($rules as $rule) {
            if (!$this->checkRule($rule)) {
                // 获取限流信息
                $remaining = $rule->getRemainingLimit();
                $resetTime = $rule->getResetTime();
                
                // 记录日志
                if ($this->enableLogging) {
                    $this->logRateLimited($rule, $remaining, $resetTime);
                }
                
                // 抛出限流异常
                throw new RateLimitException($rule, $remaining, $resetTime);
            }
        }
        
        return true;
    }
    
    /**
     * 检查一个自定义规则是否被允许通过
     *
     * @param Rule $rule 规则实例
     * @throws RateLimitException 当请求被限流时抛出
     * @return bool 是否允许通过
     */
    public function checkRule(Rule $rule): bool
    {
        if (!$rule->allow()) {
            // 获取限流信息
            $remaining = $rule->getRemainingLimit();
            $resetTime = $rule->getResetTime();
            
            // 记录日志
            if ($this->enableLogging) {
                $this->logRateLimited($rule, $remaining, $resetTime);
            }
            
            throw new RateLimitException($rule, $remaining, $resetTime);
        }
        
        return true;
    }
    
    /**
     * 创建固定窗口限流器
     *
     * @param string $key 键名
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @param IdentifierInterface|null $identifier 标识符（默认IP）
     * @return AlgorithmInterface 固定窗口算法
     */
    public function createFixedWindow(
        string $key,
        int $limitCount,
        int $timeWindow,
        ?IdentifierInterface $identifier = null
    ): AlgorithmInterface {
        return new FixedWindowAlgorithm($this->storage);
    }
    
    /**
     * 创建滑动窗口限流器
     *
     * @param string $key 键名
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @param IdentifierInterface|null $identifier 标识符（默认IP）
     * @return AlgorithmInterface 滑动窗口算法
     */
    public function createSlidingWindow(
        string $key,
        int $limitCount,
        int $timeWindow,
        ?IdentifierInterface $identifier = null
    ): AlgorithmInterface {
        return new SlidingWindowAlgorithm($this->storage);
    }
    
    /**
     * 创建IP标识符
     * 
     * @return IdentifierInterface IP标识符
     */
    public function createIpIdentifier(): IdentifierInterface
    {
        return new IpIdentifier();
    }
    
    /**
     * 创建用户标识符
     * 
     * @return IdentifierInterface 用户标识符
     */
    public function createUserIdentifier(): IdentifierInterface
    {
        return new UserIdentifier();
    }
    
    /**
     * 创建租户标识符
     * 
     * @return IdentifierInterface 租户标识符
     */
    public function createTenantIdentifier(): IdentifierInterface
    {
        return new TenantIdentifier();
    }
    
    /**
     * 获取配置管理实例
     * 
     * @return RateLimitConfig 配置管理实例
     */
    public function getConfig(): RateLimitConfig
    {
        return $this->config;
    }
    
    /**
     * 清除规则缓存
     * 
     * @param string|null $type 规则类型
     * @param int|null $tenantId 租户ID（可选）
     * @param string|null $key 键名（可选）
     * @return bool 是否成功
     */
    public function clearRulesCache(?string $type = null, ?int $tenantId = null, ?string $key = null): bool
    {
        return $this->config->clearRulesCache($type, $tenantId, $key);
    }
    
    /**
     * 设置是否记录日志
     * 
     * @param bool $enable 是否启用日志
     * @return self
     */
    public function enableLogging(bool $enable = true): self
    {
        $this->enableLogging = $enable;
        return $this;
    }
    
    /**
     * 记录限流日志
     * 
     * @param Rule $rule 规则实例
     * @param int $remaining 剩余可用请求次数
     * @param int $resetTime 重置时间（秒）
     * @return void
     */
    protected function logRateLimited(Rule $rule, int $remaining, int $resetTime): void
    {
        Log::warning('接口限流', [
            'rule_id' => $rule->getId(),
            'rule_name' => $rule->getName(),
            'rule_type' => $rule->getType(),
            'remaining' => $remaining,
            'reset_time' => $resetTime,
        ]);
    }
} 