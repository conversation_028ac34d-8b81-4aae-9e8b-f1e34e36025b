<?php
declare(strict_types=1);

namespace app\system\service;

use app\system\model\MessageModel;

/**
 * 消息服务类
 */
class MessageService
{
	/**
	 * 获取用户消息列表
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getMessageList(array $params): array
	{
		$page = isset($params['page']) ? (int)$params['page'] : 1;
		$limit = isset($params['limit']) ? (int)$params['limit'] : 10;
		
		$query = MessageModel::where('deleted_at', null);
		
		// 用户ID筛选
		if (isset($params['user_id'])) {
			$query->where('user_id', $params['user_id']);
		}
		
		// 租户ID筛选
		if (isset($params['tenant_id'])) {
			$query->where('tenant_id', $params['tenant_id']);
		}
		
		// 条件筛选
		if (isset($params['is_read']) && $params['is_read'] !== '') {
			$query->where('is_read', $params['is_read']);
		}
		
		if (isset($params['type']) && $params['type'] !== '') {
			$query->where('type', $params['type']);
		}
		
		if (!empty($params['title'])) {
			$query->whereLike('title', '%' . $params['title'] . '%');
		}
		
		// 获取总数
		$total = $query->count();
		
		// 获取分页数据
		$list = $query->page($page, $limit)
		              ->order('id', 'desc')
		              ->select()
		              ->toArray();
		
		return [
			'list' => $list,
			'total' => $total
		];
	}
	
	/**
	 * 获取消息详情
	 * @param int $id 消息ID
	 * @return array|null
	 */
	public function getMessageDetail(int $id): ?array
	{
		$message = MessageModel::where('id', $id)
		                       ->where('deleted_at', null)
		                       ->findOrEmpty();
					
		return $message->isEmpty() ? null : $message->toArray();
	}
	
	/**
	 * 创建消息
	 * @param array $params 消息信息
	 * @return array|false
	 */
	public function createMessage(array $params)
	{
		try {
			$message = new MessageModel();
			$message->user_id = $params['user_id'];
			$message->title = $params['title'];
			$message->content = $params['content'];
			$message->type = $params['type'];
			$message->module = $params['module'] ?? '';
			$message->business_id = $params['business_id'] ?? 0;
			$message->business_type = $params['business_type'] ?? '';
			$message->is_read = 0;
			$message->sender_id = $params['sender_id'] ?? 0;
			$message->sender_name = $params['sender_name'] ?? '';
			$message->sender_type = $params['sender_type'] ?? 1;
			$message->status = 1;
			$message->tenant_id = $params['tenant_id'] ?? 0;
			$message->creator_id = $params['creator_id'] ?? 0;
			$message->updater_id = $params['creator_id'] ?? 0;
			
			if ($message->save()) {
				return $this->getMessageDetail($message->id);
			}
			
			return false;
		} catch (\Exception $e) {
			return false;
		}
	}
	
	/**
	 * 标记消息为已读
	 * @param int $id 消息ID
	 * @param int $adminId 用户ID
	 * @return bool
	 */
	public function markMessageRead(int $id, int $adminId): bool
	{
		return MessageModel::markAsRead($id, $adminId);
	}
	
	/**
	 * 批量标记消息为已读
	 * @param array $ids 消息ID数组
	 * @param int $adminId 用户ID
	 * @return int 已标记为已读的消息数
	 */
	public function batchMarkMessageRead(array $ids, int $adminId): int
	{
		return MessageModel::batchMarkAsRead($ids, $adminId);
	}
	
	/**
	 * 删除消息
	 * @param int $id 消息ID
	 * @param int $adminId 用户ID
	 * @return bool
	 */
	public function deleteMessage(int $id, int $adminId): bool
	{
		$message = MessageModel::where('id', $id)
		                       ->where('user_id', $adminId)
		                       ->findOrEmpty();
					
		if ($message->isEmpty()) {
			return false;
		}
		
		$message->status = 0;
		$message->deleted_at = date('Y-m-d H:i:s');
		return $message->save();
	}
	
	/**
	 * 批量删除消息
	 * @param array $ids 消息ID数组
	 * @param int $adminId 用户ID
	 * @return int 已删除的消息数
	 */
	public function batchDeleteMessage(array $ids, int $adminId): int
	{
		return MessageModel::where('id', 'in', $ids)
		                   ->where('user_id', $adminId)
		                   ->update([
					 'status' => 0,
					 'deleted_at' => date('Y-m-d H:i:s')
				 ]);
	}
	
	/**
	 * 获取未读消息数量
	 * @param int $adminId 用户ID
	 * @param int $tenantId 租户ID
	 * @return int
	 */
	public function getUnreadMessageCount(int $adminId, int $tenantId = 0): int
	{
		return MessageModel::getUnreadCount($adminId, $tenantId);
	}
}