<?php
declare(strict_types=1);

namespace app\project\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\system\model\AdminModel;

/**
 * 项目表模型
 */
class ProjectProject extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'project_project';
	
	// 设置主键
	protected $pk = 'id';
	
	// 字段类型转换
	protected $type = [
		'status'            => 'integer',
		'priority'          => 'integer',
		'progress'          => 'float',
		'budget'            => 'float',
		'actual_cost'       => 'float',
		'owner_id'          => 'integer',
		'department_id'     => 'integer',
		'is_public'         => 'integer',
		'is_archived'       => 'integer',
		'start_date'        => 'date',
		'end_date'          => 'date',
		'actual_start_date' => 'date',
		'actual_end_date'   => 'date',
	];
	
	protected $append = [
		'owner'
	];
	
	protected string $dataRangeField = 'owner_id';
	
	public function __construct(array $data = [])
	{
		parent::__construct($data);
	}
	
	/**
	 * 项目成员关联
	 */
	public function members()
	{
		return $this->hasMany(ProjectMember::class, 'project_id');
	}
	
	/**
	 * 项目任务关联
	 */
	public function tasks()
	{
		return $this->hasMany(ProjectTask::class, 'project_id');
	}
	
	/**
	 * 项目负责人关联
	 */
	public function owner()
	{
		return $this->belongsTo(AdminModel::class, 'owner_id')
		            ->bind([
			            'owner_name' => 'real_name'
		            ]);
	}
	
	/**
	 * owner_id字段修改器 - 处理空值
	 */
	public function setOwnerIdAttr($value)
	{
		return empty($value)
			? 0
			: (int)$value;
	}
	
	/**
	 * department_id字段修改器 - 处理空值
	 */
	public function setDepartmentIdAttr($value)
	{
		return empty($value)
			? 0
			: (int)$value;
	}
	
	/**
	 * budget字段修改器 - 处理空值
	 */
	public function setBudgetAttr($value)
	{
		return empty($value)
			? 0.00
			: (float)$value;
	}
	
	/**
	 * actual_cost字段修改器 - 处理空值
	 */
	public function setActualCostAttr($value)
	{
		return empty($value)
			? 0.00
			: (float)$value;
	}
	
	/**
	 * progress字段修改器 - 处理空值
	 */
	public function setProgressAttr($value)
	{
		return empty($value)
			? 0.00
			: (float)$value;
	}
	
	/**
	 * 计算项目进度
	 */
	public function calculateProgress(): float
	{
		$totalTasks = $this->tasks()
		                   ->count();
		if ($totalTasks === 0) {
			return 0.00;
		}
		
		$completedTasks = $this->tasks()
		                       ->where('status', 3)
		                       ->count();
		return round(($completedTasks / $totalTasks) * 100, 2);
	}
	
	/**
	 * 获取项目统计信息
	 */
	public function getProjectStats(): array
	{
		return [
			'total_tasks'       => $this->tasks()
			                            ->count(),
			'completed_tasks'   => $this->tasks()
			                            ->where('status', 3)
			                            ->count(),
			'in_progress_tasks' => $this->tasks()
			                            ->where('status', 2)
			                            ->count(),
			'todo_tasks'        => $this->tasks()
			                            ->where('status', 1)
			                            ->count(),
			'member_count'      => $this->members()
			                            ->count(),
			'progress'          => $this->calculateProgress()
		];
	}
	
	/**
	 * 检查用户是否为项目成员
	 */
	public function isMember(int $userId): bool
	{
		return $this->members()
		            ->where('user_id', $userId)
		            ->count() > 0;
	}
	
	/**
	 * 检查用户是否为项目负责人
	 */
	public function isOwner(int $userId): bool
	{
		return $this->owner_id == $userId;
	}
	
	/**
	 * 添加项目成员
	 */
	public function addMember(int $userId, string $role = 'member'): bool
	{
		// 检查是否已经是成员
		if ($this->isMember($userId)) {
			return true;
		}
		
		$member = new ProjectMember();
		
		return $member->saveByCreate([
				'project_id' => $this->id,
				'user_id'    => $userId,
				'role'       => $role,
				'joined_at'  => date('Y-m-d H:i:s')
			]) > 0;
	}
	
	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'name'          => ['type' => 'like'],
			'code'          => ['type' => 'like'],
			'status'        => ['type' => 'eq'],
			'priority'      => ['type' => 'eq'],
			'owner_id'      => ['type' => 'eq'],
			'department_id' => ['type' => 'eq'],
			'is_public'     => ['type' => 'eq'],
			'is_archived'   => ['type' => 'eq'],
			'start_date'    => ['type' => 'date'],
			'end_date'      => ['type' => 'date'],
			'created_at'    => ['type' => 'date'],
			'updated_at'    => ['type' => 'date'],
		];
	}
}