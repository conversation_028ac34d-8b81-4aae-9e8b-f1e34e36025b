<?php
declare(strict_types=1);

namespace app\notice\command;

use app\notice\service\NoticeMessageService;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 处理延迟消息命令
 */
class ProcessDelayed extends Command
{
    /**
     * 配置命令
     */
    protected function configure()
    {
        $this->setName('notice:process-delayed')
            ->setDescription('处理延迟消息通知')
            ->addOption('limit', 'l', Option::VALUE_OPTIONAL, '每次处理的最大数量', 100);
    }
    
    /**
     * 执行命令
     *
     * @param Input $input 输入对象
     * @param Output $output 输出对象
     * @return int 返回状态码
     */
    protected function execute(Input $input, Output $output)
    {
        $limit = (int)$input->getOption('limit');
        
        $output->writeln("<info>开始处理延迟消息，处理限制: {$limit}</info>");
        
        $messageService = NoticeMessageService::getInstance();
        $count = $messageService->processDelayedMessages($limit);
        
        $output->writeln("<info>处理完成，共处理 {$count} 条延迟消息</info>");
        
        return 0; // 成功返回0
    }
} 