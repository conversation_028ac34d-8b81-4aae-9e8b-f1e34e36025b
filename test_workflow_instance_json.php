<?php
/**
 * WorkflowInstance JSON 字段实际数据库操作测试
 * 
 * 测试场景：
 * 1. 创建包含数字类型的工作流实例
 * 2. 保存到数据库
 * 3. 从数据库读取
 * 4. 验证数字类型是否保持
 */

// 这是一个示例测试脚本，实际使用时需要在 ThinkPHP 环境中运行

echo "=== WorkflowInstance JSON 字段数据库操作测试 ===\n\n";

// 模拟测试数据
$testFormData = [
    // 财务相关数字
    'total_amount' => 15000.50,
    'tax_amount' => 1500.05,
    'discount_amount' => 500.00,
    
    // 数量相关
    'quantity' => 100,
    'approved_quantity' => 95,
    
    // ID 相关
    'applicant_id' => 1001,
    'department_id' => 2001,
    'category_id' => 3001,
    
    // 布尔值
    'is_urgent' => true,
    'requires_approval' => false,
    
    // 字符串
    'description' => '测试申请单',
    'remark' => '这是一个测试',
    
    // 嵌套数组 - 申请明细
    'items' => [
        [
            'item_id' => 1,
            'item_name' => '办公用品A',
            'unit_price' => 25.50,
            'quantity' => 10,
            'total_price' => 255.00
        ],
        [
            'item_id' => 2,
            'item_name' => '办公用品B', 
            'unit_price' => 45.00,
            'quantity' => 5,
            'total_price' => 225.00
        ]
    ],
    
    // 审批配置
    'approval_config' => [
        'auto_approve_limit' => 1000.00,
        'require_finance_approval' => true,
        'max_approval_days' => 7
    ]
];

$testCcUsers = [1001, 1002, 1003, 1004];

$testProcessData = [
    'workflow_version' => 1.2,
    'nodes' => [
        [
            'node_id' => 'start',
            'node_type' => 1,
            'timeout_seconds' => 3600,
            'retry_count' => 3,
            'position' => ['x' => 100, 'y' => 200]
        ],
        [
            'node_id' => 'approval_1',
            'node_type' => 2,
            'timeout_seconds' => 86400,
            'retry_count' => 1,
            'position' => ['x' => 300, 'y' => 200]
        ]
    ],
    'settings' => [
        'max_duration_hours' => 168,
        'notification_interval_minutes' => 60,
        'escalation_threshold_hours' => 24
    ]
];

echo "1. 测试数据准备完成\n";
echo "   - form_data 包含 " . count($testFormData) . " 个字段\n";
echo "   - cc_users 包含 " . count($testCcUsers) . " 个用户ID\n";
echo "   - process_data 包含工作流配置信息\n\n";

echo "2. 原始数据类型检查:\n";
echo "   form_data.total_amount: " . gettype($testFormData['total_amount']) . " = " . $testFormData['total_amount'] . "\n";
echo "   form_data.quantity: " . gettype($testFormData['quantity']) . " = " . $testFormData['quantity'] . "\n";
echo "   form_data.applicant_id: " . gettype($testFormData['applicant_id']) . " = " . $testFormData['applicant_id'] . "\n";
echo "   form_data.items[0].unit_price: " . gettype($testFormData['items'][0]['unit_price']) . " = " . $testFormData['items'][0]['unit_price'] . "\n";
echo "   cc_users[0]: " . gettype($testCcUsers[0]) . " = " . $testCcUsers[0] . "\n";
echo "   process_data.settings.max_duration_hours: " . gettype($testProcessData['settings']['max_duration_hours']) . " = " . $testProcessData['settings']['max_duration_hours'] . "\n\n";

// 模拟 JSON 序列化和反序列化过程
echo "3. JSON 序列化测试:\n";

// 使用新的序列化方法（带 JSON_NUMERIC_CHECK）
$formDataJson = json_encode($testFormData, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
$ccUsersJson = json_encode($testCcUsers, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
$processDataJson = json_encode($testProcessData, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);

echo "   form_data JSON 长度: " . strlen($formDataJson) . " 字符\n";
echo "   cc_users JSON: " . $ccUsersJson . "\n";
echo "   process_data JSON 长度: " . strlen($processDataJson) . " 字符\n\n";

echo "4. JSON 反序列化测试:\n";

// 反序列化
$decodedFormData = json_decode($formDataJson, true);
$decodedCcUsers = json_decode($ccUsersJson, true);
$decodedProcessData = json_decode($processDataJson, true);

// 应用数字类型保持逻辑
function preserveNumericTypes($data) {
    if (!is_array($data)) {
        if (is_string($data) && is_numeric($data)) {
            return strpos($data, '.') !== false ? (float)$data : (int)$data;
        }
        return $data;
    }
    
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $data[$key] = preserveNumericTypes($value);
        } elseif (is_string($value) && is_numeric($value)) {
            $data[$key] = strpos($value, '.') !== false ? (float)$value : (int)$value;
        }
    }
    
    return $data;
}

$processedFormData = preserveNumericTypes($decodedFormData);
$processedCcUsers = preserveNumericTypes($decodedCcUsers);
$processedProcessData = preserveNumericTypes($decodedProcessData);

echo "5. 处理后数据类型检查:\n";
echo "   form_data.total_amount: " . gettype($processedFormData['total_amount']) . " = " . $processedFormData['total_amount'] . "\n";
echo "   form_data.quantity: " . gettype($processedFormData['quantity']) . " = " . $processedFormData['quantity'] . "\n";
echo "   form_data.applicant_id: " . gettype($processedFormData['applicant_id']) . " = " . $processedFormData['applicant_id'] . "\n";
echo "   form_data.items[0].unit_price: " . gettype($processedFormData['items'][0]['unit_price']) . " = " . $processedFormData['items'][0]['unit_price'] . "\n";
echo "   cc_users[0]: " . gettype($processedCcUsers[0]) . " = " . $processedCcUsers[0] . "\n";
echo "   process_data.settings.max_duration_hours: " . gettype($processedProcessData['settings']['max_duration_hours']) . " = " . $processedProcessData['settings']['max_duration_hours'] . "\n\n";

echo "6. 类型验证结果:\n";
$allTestsPassed = true;

// 验证各种数字类型
$tests = [
    ['form_data.total_amount', $processedFormData['total_amount'], 'float'],
    ['form_data.quantity', $processedFormData['quantity'], 'integer'],
    ['form_data.applicant_id', $processedFormData['applicant_id'], 'integer'],
    ['form_data.items[0].unit_price', $processedFormData['items'][0]['unit_price'], 'float'],
    ['form_data.items[0].quantity', $processedFormData['items'][0]['quantity'], 'integer'],
    ['cc_users[0]', $processedCcUsers[0], 'integer'],
    ['process_data.workflow_version', $processedProcessData['workflow_version'], 'float'],
    ['process_data.nodes[0].timeout_seconds', $processedProcessData['nodes'][0]['timeout_seconds'], 'integer'],
    ['process_data.settings.max_duration_hours', $processedProcessData['settings']['max_duration_hours'], 'integer']
];

foreach ($tests as $test) {
    [$fieldName, $value, $expectedType] = $test;
    $actualType = gettype($value);
    
    if ($actualType === $expectedType) {
        echo "   ✅ {$fieldName}: {$actualType} (正确)\n";
    } else {
        echo "   ❌ {$fieldName}: 期望 {$expectedType}，实际 {$actualType}\n";
        $allTestsPassed = false;
    }
}

echo "\n7. 测试总结:\n";
if ($allTestsPassed) {
    echo "   🎉 所有测试通过！JSON 字段数字类型保持功能正常工作。\n";
    echo "   \n";
    echo "   优势:\n";
    echo "   - 浮点数正确保持为 float 类型\n";
    echo "   - 整数正确保持为 int 类型\n";
    echo "   - 嵌套数组中的数字类型也得到正确处理\n";
    echo "   - 字符串和布尔值类型不受影响\n";
} else {
    echo "   ❌ 部分测试失败，需要检查实现逻辑。\n";
}

echo "\n=== 测试完成 ===\n";
