/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActionColumn: typeof import('./../components/core/tables/columns/ActionColumn.vue')['default']
    AddNode: typeof import('./../components/custom/workflow/components/AddNode.vue')['default']
    ApiSelect: typeof import('./../components/core/forms/ApiSelect/index.vue')['default']
    ApproverDrawer: typeof import('./../components/custom/workflow/components/drawers/ApproverDrawer.vue')['default']
    ArtBackToTop: typeof import('./../components/core/base/ArtBackToTop.vue')['default']
    ArtBarChart: typeof import('./../components/core/charts/ArtBarChart.vue')['default']
    ArtBarChartCard: typeof import('./../components/core/cards/ArtBarChartCard.vue')['default']
    ArtBasicBanner: typeof import('./../components/core/banners/ArtBasicBanner.vue')['default']
    ArtBreadcrumb: typeof import('./../components/core/layouts/art-breadcrumb/index.vue')['default']
    ArtButtonMore: typeof import('./../components/core/forms/ArtButtonMore.vue')['default']
    ArtButtonTable: typeof import('./../components/core/forms/ArtButtonTable.vue')['default']
    ArtCardBanner: typeof import('./../components/core/banners/ArtCardBanner.vue')['default']
    ArtChatWindow: typeof import('./../components/core/layouts/art-chat-window/index.vue')['default']
    ArtCutterImg: typeof import('./../components/core/media/ArtCutterImg.vue')['default']
    ArtDataListCard: typeof import('./../components/core/cards/ArtDataListCard.vue')['default']
    ArtDonutChartCard: typeof import('./../components/core/cards/ArtDonutChartCard.vue')['default']
    ArtDualBarCompareChart: typeof import('./../components/core/charts/ArtDualBarCompareChart.vue')['default']
    ArtExcelExport: typeof import('./../components/core/forms/ArtExcelExport.vue')['default']
    ArtExcelImport: typeof import('./../components/core/forms/ArtExcelImport.vue')['default']
    ArtException: typeof import('./../components/core/views/exception/ArtException.vue')['default']
    ArtFastEnter: typeof import('./../components/core/layouts/art-fast-enter/index.vue')['default']
    ArtFestivalTextScroll: typeof import('./../components/core/text-effect/ArtFestivalTextScroll.vue')['default']
    ArtFireworksEffect: typeof import('./../components/core/layouts/art-fireworks-effect/index.vue')['default']
    ArtFormDialog: typeof import('./../components/core/form/ArtFormDialog.vue')['default']
    ArtGlobalSearch: typeof import('./../components/core/layouts/art-global-search/index.vue')['default']
    ArtHBarChart: typeof import('./../components/core/charts/ArtHBarChart.vue')['default']
    ArtHeaderBar: typeof import('./../components/core/layouts/art-header-bar/index.vue')['default']
    ArtHorizontalMenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/index.vue')['default']
    ArtIconSelector: typeof import('./../components/core/base/ArtIconSelector.vue')['default']
    ArtImageCard: typeof import('./../components/core/cards/ArtImageCard.vue')['default']
    ArtKLineChart: typeof import('./../components/core/charts/ArtKLineChart.vue')['default']
    ArtLineChart: typeof import('./../components/core/charts/ArtLineChart.vue')['default']
    ArtLineChartCard: typeof import('./../components/core/cards/ArtLineChartCard.vue')['default']
    ArtLogo: typeof import('./../components/core/base/ArtLogo.vue')['default']
    ArtMapChart: typeof import('./../components/core/charts/ArtMapChart.vue')['default']
    ArtMenuRight: typeof import('./../components/core/others/ArtMenuRight.vue')['default']
    ArtMixedMenu: typeof import('./../components/core/layouts/art-menus/art-mixed-menu/index.vue')['default']
    ArtNotification: typeof import('./../components/core/layouts/art-notification/index.vue')['default']
    ArtPageContent: typeof import('./../components/core/layouts/art-page-content/index.vue')['default']
    ArtProgressCard: typeof import('./../components/core/cards/ArtProgressCard.vue')['default']
    ArtRadarChart: typeof import('./../components/core/charts/ArtRadarChart.vue')['default']
    ArtRingChart: typeof import('./../components/core/charts/ArtRingChart.vue')['default']
    ArtScatterChart: typeof import('./../components/core/charts/ArtScatterChart.vue')['default']
    ArtScreenLock: typeof import('./../components/core/layouts/art-screen-lock/index.vue')['default']
    ArtSearchApiSelect: typeof import('./../components/core/forms/art-search-bar/widget/art-search-api-select/index.vue')['default']
    ArtSearchBar: typeof import('./../components/core/forms/art-search-bar/index.vue')['default']
    ArtSearchDate: typeof import('./../components/core/forms/art-search-bar/widget/art-search-date/index.vue')['default']
    ArtSearchInput: typeof import('./../components/core/forms/art-search-bar/widget/art-search-input/index.vue')['default']
    ArtSearchNumberRange: typeof import('./../components/core/forms/art-search-bar/widget/art-search-number-range/index.vue')['default']
    ArtSearchRadio: typeof import('./../components/core/forms/art-search-bar/widget/art-search-radio/index.vue')['default']
    ArtSearchSelect: typeof import('./../components/core/forms/art-search-bar/widget/art-search-select/index.vue')['default']
    ArtSettingsPanel: typeof import('./../components/core/layouts/art-settings-panel/index.vue')['default']
    ArtSidebarMenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/index.vue')['default']
    ArtStatsCard: typeof import('./../components/core/cards/ArtStatsCard.vue')['default']
    ArtTable: typeof import('./../components/core/tables/ArtTable.vue')['default']
    ArtTableFullScreen: typeof import('./../components/core/tables/ArtTableFullScreen.vue')['default']
    ArtTableHeader: typeof import('./../components/core/tables/ArtTableHeader.vue')['default']
    ArtTableHeaderTest: typeof import('./../components/core/tables/ArtTableHeader-test.vue')['default']
    ArtTextScroll: typeof import('./../components/core/text-effect/ArtTextScroll.vue')['default']
    ArtTimelineListCard: typeof import('./../components/core/cards/ArtTimelineListCard.vue')['default']
    ArtVideoPlayer: typeof import('./../components/core/media/ArtVideoPlayer.vue')['default']
    ArtWangEditor: typeof import('./../components/core/forms/ArtWangEditor.vue')['default']
    ArtWatermark: typeof import('./../components/core/others/ArtWatermark.vue')['default']
    ArtWorkTab: typeof import('./../components/core/layouts/art-work-tab/index.vue')['default']
    BasicSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/BasicSettings.vue')['default']
    BoxStyleSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/BoxStyleSettings.vue')['default']
    CategoryDialog: typeof import('./../components/custom/MediaSelector/components/CategoryDialog.vue')['default']
    ColorColumn: typeof import('./../components/core/tables/columns/ColorColumn.vue')['default']
    ColorSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/ColorSettings.vue')['default']
    CommentItem: typeof import('./../components/custom/comment-widget/widget/CommentItem.vue')['default']
    CommentWidget: typeof import('./../components/custom/comment-widget/index.vue')['default']
    ConditionDrawer: typeof import('./../components/custom/workflow/components/drawers/ConditionDrawer.vue')['default']
    ContactDetailDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/ContactDetailDialog.vue')['default']
    ContactFormDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/ContactFormDialog.vue')['default']
    ContactSelector: typeof import('./../components/custom/ContactSelector/index.vue')['default']
    ContainerSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/ContainerSettings.vue')['default']
    ContractDetailDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/ContractDetailDialog.vue')['default']
    ContractFormDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/ContractFormDialog.vue')['default']
    CopyableColumn: typeof import('./../components/core/tables/columns/CopyableColumn.vue')['default']
    CopyerDrawer: typeof import('./../components/custom/workflow/components/drawers/CopyerDrawer.vue')['default']
    CrmApprovalButton: typeof import('./../components/crm/CrmApprovalButton.vue')['default']
    CrmApprovalStatus: typeof import('./../components/crm/CrmApprovalStatus.vue')['default']
    CrmApprovalSubmitDialog: typeof import('./../components/crm/CrmApprovalSubmitDialog.vue')['default']
    CrmDetailDrawer: typeof import('./../components/custom/CrmDetailDrawer/index.vue')['default']
    CrmInfoCard: typeof import('./../components/custom/CrmInfoCard/index.vue')['default']
    CurrencyColumn: typeof import('./../components/core/tables/columns/CurrencyColumn.vue')['default']
    CustomerBusinessPanel: typeof import('./../components/custom/CustomerDetailDrawer/panels/CustomerBusinessPanel.vue')['default']
    CustomerContactPanel: typeof import('./../components/custom/CustomerDetailDrawer/panels/CustomerContactPanel.vue')['default']
    CustomerContractPanel: typeof import('./../components/custom/CustomerDetailDrawer/panels/CustomerContractPanel.vue')['default']
    CustomerDetailDrawer: typeof import('./../components/custom/CustomerDetailDrawer/index.vue')['default']
    CustomerFollowPanel: typeof import('./../components/custom/CustomerDetailDrawer/panels/CustomerFollowPanel.vue')['default']
    CustomerInfoPanel: typeof import('./../components/custom/CustomerDetailDrawer/panels/CustomerInfoPanel.vue')['default']
    Demo: typeof import('./../components/core/forms/ApiSelect/demo.vue')['default']
    DepartmentPersonForm: typeof import('./../components/custom/DepartmentPersonForm.vue')['default']
    DepartmentPersonSearch: typeof import('./../components/custom/DepartmentPersonSearch.vue')['default']
    DepartmentPersonSelector: typeof import('./../components/custom/DepartmentPersonSelector.vue')['default']
    DepartmentTreeSelect: typeof import('./../components/custom/DepartmentTreeSelect.vue')['default']
    DirectiveExample: typeof import('./../components/core/examples/DirectiveExample.vue')['default']
    DocumentColumn: typeof import('./../components/core/tables/columns/DocumentColumn.vue')['default']
    EditableColumn: typeof import('./../components/core/tables/columns/EditableColumn.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    EmployeeSelector: typeof import('./../components/custom/workflow/components/selectors/EmployeeSelector.vue')['default']
    EmployeeSelectorNew: typeof import('./../components/custom/workflow/components/selectors/EmployeeSelectorNew.vue')['default']
    ErrorDialog: typeof import('./../components/custom/workflow/components/dialogs/ErrorDialog.vue')['default']
    ExpenseItemTable: typeof import('./../components/business/ExpenseItemTable.vue')['default']
    FollowDetailDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/FollowDetailDialog.vue')['default']
    FollowFormDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/FollowFormDialog.vue')['default']
    FormMediaSelector: typeof import('./../components/custom/FormMediaSelector/index.vue')['default']
    FormUploader: typeof import('./../components/custom/FormUploader/index.vue')['default']
    HorizontalSubmenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/widget/HorizontalSubmenu.vue')['default']
    ImageColumn: typeof import('./../components/core/tables/columns/ImageColumn.vue')['default']
    ImageViewer: typeof import('./../components/custom/ImageViewer/src/ImageViewer.vue')['default']
    LeadCompanyInfoColumn: typeof import('./../components/crm/LeadCompanyInfoColumn.vue')['default']
    LeadContactInfoColumn: typeof import('./../components/crm/LeadContactInfoColumn.vue')['default']
    LeadDetailInfo: typeof import('./../components/crm/LeadDetailInfo.vue')['default']
    LinkColumn: typeof import('./../components/core/tables/columns/LinkColumn.vue')['default']
    LoginLeftView: typeof import('./../components/core/views/login/LoginLeftView.vue')['default']
    LongTextColumn: typeof import('./../components/core/tables/columns/LongTextColumn.vue')['default']
    MediaColumn: typeof import('./../components/core/tables/columns/MediaColumn.vue')['default']
    MediaSelector: typeof import('./../components/custom/MediaSelector/index.vue')['default']
    MenuLayoutSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/MenuLayoutSettings.vue')['default']
    MenuLoadStatus: typeof import('./../components/MenuLoadStatus.vue')['default']
    MenuStyleSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/MenuStyleSettings.vue')['default']
    MobileItemTable: typeof import('./../components/business/MobileItemTable.vue')['default']
    MoveDialog: typeof import('./../components/custom/MediaSelector/components/MoveDialog.vue')['default']
    NodeWrap: typeof import('./../components/custom/workflow/components/NodeWrap.vue')['default']
    PostSelect: typeof import('./../components/custom/PostSelect.vue')['default']
    ProductCategoryTreeSelect: typeof import('./../components/custom/ProductCategoryTreeSelect.vue')['default']
    ProductSelector: typeof import('./../components/business/ProductSelector.vue')['default']
    ProgressColumn: typeof import('./../components/core/tables/columns/ProgressColumn.vue')['default']
    PromoterDrawer: typeof import('./../components/custom/workflow/components/drawers/PromoterDrawer.vue')['default']
    QrcodeColumn: typeof import('./../components/core/tables/columns/QrcodeColumn.vue')['default']
    ReceivableDetailDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/ReceivableDetailDialog.vue')['default']
    ReceivableFormDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/ReceivableFormDialog.vue')['default']
    ReceivableListDialog: typeof import('./../components/custom/CustomerDetailDrawer/forms/ReceivableListDialog.vue')['default']
    RegionSelector: typeof import('./../components/custom/RegionSelector/index.vue')['default']
    RenameDialog: typeof import('./../components/custom/MediaSelector/components/RenameDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SectionTitle: typeof import('./../components/core/layouts/art-settings-panel/widget/SectionTitle.vue')['default']
    SettingDrawer: typeof import('./../components/core/layouts/art-settings-panel/widget/SettingDrawer.vue')['default']
    SettingHeader: typeof import('./../components/core/layouts/art-settings-panel/widget/SettingHeader.vue')['default']
    SettingItem: typeof import('./../components/core/layouts/art-settings-panel/widget/SettingItem.vue')['default']
    SidebarSubmenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/widget/SidebarSubmenu.vue')['default']
    SimpleApiSelect: typeof import('./../components/test/SimpleApiSelect.vue')['default']
    SupplierSelector: typeof import('./../components/business/SupplierSelector.vue')['default']
    SwitchColumn: typeof import('./../components/core/tables/columns/SwitchColumn.vue')['default']
    TableExportImport: typeof import('./../components/core/tables/TableExportImport.vue')['default']
    TagColumn: typeof import('./../components/core/tables/columns/TagColumn.vue')['default']
    TestWorkflow: typeof import('./../components/custom/workflow/test/TestWorkflow.vue')['default']
    ThemeSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/ThemeSettings.vue')['default']
    TripItemTable: typeof import('./../components/business/TripItemTable.vue')['default']
    UploadProgress: typeof import('./../components/custom/MediaSelector/components/UploadProgress.vue')['default']
    UserSelector: typeof import('./../components/custom/workflow/components/selectors/UserSelector.vue')['default']
    Workflow: typeof import('./../components/custom/workflow/index.vue')['default']
    WorkflowHistoryTimeline: typeof import('./../components/custom/workflow/components/workflow-history-timeline.vue')['default']
    WorkflowProcessGraph: typeof import('./../components/custom/workflow/components/workflow-process-graph.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
