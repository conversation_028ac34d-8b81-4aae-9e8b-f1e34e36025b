-- =====================================================
-- 产品表添加供应商关联字段
-- 执行时间：请在数据清空后执行
-- =====================================================

-- 1. 为产品表添加供应商ID字段
ALTER TABLE `crm_product` 
ADD COLUMN `supplier_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 
COMMENT '供应商ID @required @search:eq @exp @imp' 
AFTER `unit_id`;

-- 2. 添加供应商字段索引
ALTER TABLE `crm_product` 
ADD KEY `idx_supplier` (`supplier_id`);

-- 3. 添加外键约束（核心业务关系）
ALTER TABLE `crm_product` 
ADD CONSTRAINT `fk_product_supplier` 
FOREIGN KEY (`supplier_id`) REFERENCES `ims_supplier` (`id`) 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- 4. 验证表结构
DESCRIBE `crm_product`;

-- 5. 验证外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME,
    DELETE_RULE,
    UPDATE_RULE
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'crm_product' 
AND CONSTRAINT_NAME = 'fk_product_supplier';

-- 执行完成提示
SELECT 
    '数据库结构调整完成' as 状态,
    '产品表已添加supplier_id字段和外键约束' as 说明,
    NOW() as 执行时间;
