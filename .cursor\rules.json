{"product": "多租户前后端分离框架系统", "backend": {"projectType": "thinkphp", "phpVersion": "8.2", "framework": "thinkphp8", "crud": {"description": "基于ThinkPHP开发的增删改查操作封装框架", "coreFeatures": ["组件化设计", "标准化CRUD操作", "字段场景管理", "灵活的查询构建", "数据权限控制", "自动数据过滤", "自动数据验证", "代码生成器"], "coreComponents": {"CrudService": "核心服务类", "traits": {"CrudOperationsTrait": "基础CRUD操作", "FieldSceneTrait": "字段场景管理", "SearchTrait": "搜索功能", "QueryBuilderTrait": "查询构建", "DataPermissionTrait": "数据权限", "TransactionTrait": "事务处理", "DataFilterTrait": "数据过滤", "ModelConfigTrait": "模型配置", "OrderTrait": "排序处理", "ValidatorTrait": "数据验证"}, "generators": {"command": "php think crud [表名] --module=[模块名]", "options": ["--module (-m): 模块名称", "--controller (-c): 控制器名称", "--model (-o): 模型名称", "--service (-s): 服务类名称", "--with-frontend (-f): 是否生成前端代码", "--frontend-path (-p): 前端代码生成路径", "--force: 强制覆盖已存在文件"]}}}, "fileStructure": {"app": {"controller": "控制器目录", "model": "模型目录", "service": "服务层目录", "validate": "验证器目录", "middleware": "中间件目录", "common": "公共函数目录"}, "config": "配置文件目录", "public": "公共资源目录", "runtime": "运行时目录", "vendor": "第三方依赖目录"}, "namingConventions": {"controller": "PascalCase", "model": "PascalCase", "service": "PascalCase", "table": "snake_case", "class": "PascalCase", "method": "camelCase", "variable": "camelCase"}, "coding": {"indentation": 4, "docBlockStyle": "phpdoc", "lineLength": 120}, "suggestions": {"database": {"tablePrefix": "system_", "fieldCasing": "snake_case", "commonFields": ["id", "created_at", "updated_at", "deleted_at", "tenant_id"]}, "modules": ["admin", "role", "menu", "dept", "post", "tenant", "log", "attachment", "notice", "message", "dict"]}}, "frontend": {"projectName": "Art Design Pro", "projectPath": "frontend/art-design-pro", "framework": "Vue 3", "language": "TypeScript", "buildTool": "Vite", "uiLibrary": "Element Plus", "stateManagement": "Pinia", "router": "Vue Router", "cssPreprocessor": "SCSS", "features": ["多主题切换", "全局搜索", "锁屏功能", "多标签页", "全局面包屑", "多语言支持", "图标库", "富文本编辑器", "Echarts图表", "工具包", "网络异常处理", "权限管理", "移动端适配"], "fileStructure": {"src": {"api": "API接口", "assets": "静态资源", "components": "公共组件", "composables": "组合式函数", "config": "配置文件", "directives": "自定义指令", "enums": "枚举定义", "language": "多语言配置", "plugins": "插件", "router": "路由配置", "store": "状态管理", "stores": "Pinia存储", "types": "类型定义", "utils": "工具函数", "views": "页面视图"}}, "namingConventions": {"component": "PascalCase", "view": "PascalCase", "composable": "camelCase", "util": "camelCase", "store": "camelCase", "cssClass": "kebab-case"}, "coding": {"indentation": 2, "lineLength": 100, "linters": ["ESLint", "<PERSON>ttier", "Stylelint"], "commitConvention": "cz-git"}}}