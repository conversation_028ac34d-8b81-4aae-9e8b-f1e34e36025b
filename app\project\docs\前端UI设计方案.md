# 项目任务管理前端UI设计方案

## 🎨 设计理念

### 飞书风格设计原则
- **简洁高效**: 界面简洁，操作高效，减少用户认知负担
- **一致性**: 保持设计语言和交互模式的一致性
- **响应式**: 适配不同屏幕尺寸，支持移动端访问
- **可视化**: 通过图表、看板等方式直观展示项目状态

### 色彩系统
```css
/* 主色调 */
--primary-color: #1664FF;        /* 飞书蓝 */
--primary-light: #4080FF;        /* 浅蓝 */
--primary-dark: #0E4BC7;         /* 深蓝 */

/* 辅助色 */
--success-color: #00BC70;        /* 成功绿 */
--warning-color: #FF8800;        /* 警告橙 */
--danger-color: #F54A45;         /* 危险红 */
--info-color: #3370FF;           /* 信息蓝 */

/* 中性色 */
--text-primary: #1F2329;         /* 主文本 */
--text-secondary: #646A73;       /* 次要文本 */
--text-placeholder: #8F959E;     /* 占位文本 */
--border-color: #E5E6EB;         /* 边框色 */
--background-color: #F7F8FA;     /* 背景色 */
--card-background: #FFFFFF;      /* 卡片背景 */
```

## 📱 页面布局设计

### 1. 项目概览页 (Dashboard)

#### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│ 🏠 项目管理 / 项目概览                    [+ 新建项目]    │
├─────────────────────────────────────────────────────────┤
│ 📊 数据概览卡片区域                                      │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐         │
│ │进行中项目│ │待办任务 │ │本周完成 │ │团队成员 │         │
│ │   12    │ │   45   │ │   23   │ │   8    │         │
│ │ +15%    │ │ -5%    │ │ +32%   │ │ +2     │         │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘         │
├─────────────────────────────────────────────────────────┤
│ 📈 项目进度图表区域                                      │
│ ┌─────────────────────┐ ┌─────────────────────────────┐ │
│ │   项目完成率趋势     │ │      任务状态分布饼图        │ │
│ │                    │ │                            │ │
│ │   📊 折线图         │ │      🥧 饼图               │ │
│ └─────────────────────┘ └─────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 🚀 快速操作区域                                          │
│ [创建项目] [分配任务] [记录工时] [查看报表]               │
└─────────────────────────────────────────────────────────┘
```

#### 组件设计
- **统计卡片**: 数值展示 + 趋势指示器
- **图表组件**: 基于ECharts的响应式图表
- **快速操作**: 常用功能的快捷入口

### 2. 项目列表页

#### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│ 🏠 项目管理 / 我的项目                    [+ 新建项目]    │
├─────────────────────────────────────────────────────────┤
│ 🔍 搜索筛选区域                                          │
│ [搜索框] [状态筛选] [负责人筛选] [时间筛选] [高级筛选]     │
├─────────────────────────────────────────────────────────┤
│ 📋 视图切换                              [列表] [卡片]    │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 🎯 产品V2.0开发                                     │ │
│ │ 进行中 | 80% | 张三 | 2024-01-15 | [查看] [编辑]    │ │
│ │ ─────────────────────────────────────────────────── │ │
│ │ 📱 移动端优化                                       │ │
│ │ 计划中 | 0%  | 李四 | 2024-02-01 | [查看] [编辑]    │ │
│ │ ─────────────────────────────────────────────────── │ │
│ │ 🎨 UI设计改版                                       │ │
│ │ 已完成 | 100%| 王五 | 2024-01-10 | [查看] [编辑]    │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 📄 分页组件                                              │
└─────────────────────────────────────────────────────────┘
```

#### 卡片视图设计
```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ 🎯 产品V2.0开发  │ │ 📱 移动端优化    │ │ 🎨 UI设计改版   │
│ ─────────────── │ │ ─────────────── │ │ ─────────────── │
│ 进行中          │ │ 计划中          │ │ 已完成          │
│ ████████░░ 80%  │ │ ░░░░░░░░░░ 0%   │ │ ██████████ 100% │
│ 👤 张三         │ │ 👤 李四         │ │ 👤 王五         │
│ 📅 2024-01-15   │ │ 📅 2024-02-01   │ │ 📅 2024-01-10   │
│ [查看] [编辑]   │ │ [查看] [编辑]   │ │ [查看] [编辑]   │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

### 3. 项目详情页 (看板视图)

#### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│ 🎯 产品V2.0开发                                          │
│ [概览] [任务] [成员] [文档] [设置]                        │
├─────────────────────────────────────────────────────────┤
│ 📊 项目信息栏                                            │
│ 进度: 80% | 成员: 8人 | 任务: 25个 | 截止: 2024-03-01    │
├─────────────────────────────────────────────────────────┤
│ 🎛️ 视图控制                              [筛选] [搜索]   │
│ [看板] [列表] [甘特图] [日历]                             │
├─────────────────────────────────────────────────────────┤
│ 📋 看板区域                                              │
│ 待办 (5)      │ 进行中 (3)    │ 测试中 (2)    │ 已完成 (12) │
│ ┌───────────┐ │ ┌───────────┐ │ ┌───────────┐ │ ┌───────────┐ │
│ │🔴 登录功能 │ │ │🟡 用户管理 │ │ │🔵 数据统计 │ │ │✅ 需求分析 │ │
│ │#001       │ │ │#002       │ │ │#003       │ │ │#004       │ │
│ │👤 张三     │ │ │👤 李四     │ │ │👤 王五     │ │ │👤 赵六     │ │
│ │⏰ 2天      │ │ │⏰ 1天      │ │ │🧪 测试中   │ │ │✅ 已完成   │ │
│ │🏷️ 后端     │ │ │🏷️ 前端     │ │ │🏷️ 测试     │ │ │🏷️ 产品     │ │
│ └───────────┘ │ └───────────┘ │ └───────────┘ │ └───────────┘ │
│ ┌───────────┐ │ ┌───────────┐ │ ┌───────────┐ │ ┌───────────┐ │
│ │🟠 支付模块 │ │ │🟢 订单系统 │ │ │🔵 性能优化 │ │ │✅ 原型设计 │ │
│ │#005       │ │ │#006       │ │ │#007       │ │ │#008       │ │
│ │👤 待分配   │ │ │👤 钱七     │ │ │👤 孙八     │ │ │👤 周九     │ │
│ │⏰ 未开始   │ │ │⏰ 3天      │ │ │🧪 测试中   │ │ │✅ 已完成   │ │
│ │🏷️ 后端     │ │ │🏷️ 后端     │ │ │🏷️ 优化     │ │ │🏷️ 设计     │ │
│ └───────────┘ │ └───────────┘ │ └───────────┘ │ └───────────┘ │
│ [+ 添加任务] │ │              │ │              │ │              │
└─────────────────────────────────────────────────────────┘
```

#### 任务卡片设计
```css
.task-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border-left: 4px solid var(--priority-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.task-priority {
  --priority-color: #F54A45; /* 高优先级 */
  --priority-color: #FF8800; /* 中优先级 */
  --priority-color: #1664FF; /* 低优先级 */
}
```

### 4. 任务详情弹窗

#### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│ 📝 任务详情 - #001 登录功能开发            [编辑] [关闭] │
├─────────────────────────────────────────────────────────┤
│ 📋 基本信息                                              │
│ 标题: 登录功能开发                                       │
│ 状态: [进行中 ▼]  优先级: [高 ▼]  负责人: [张三 ▼]      │
│ 开始: 2024-01-10  截止: 2024-01-15  进度: [████░░] 80%  │
├─────────────────────────────────────────────────────────┤
│ 📄 任务描述                                              │
│ 实现用户登录功能，包括用户名密码登录、手机号登录、       │
│ 第三方登录等方式...                                      │
├─────────────────────────────────────────────────────────┤
│ 📎 附件 (3)                              [+ 上传附件]    │
│ 📄 登录流程图.png  📄 接口文档.md  📄 测试用例.xlsx      │
├─────────────────────────────────────────────────────────┤
│ 💬 评论 (5)                              [发表评论]      │
│ 👤 张三  2024-01-12 10:30                               │
│ 登录接口已完成，正在进行前端对接...                      │
│ ─────────────────────────────────────────────────────── │
│ 👤 李四  2024-01-12 14:20                               │
│ @张三 前端页面已完成，可以开始联调                       │
├─────────────────────────────────────────────────────────┤
│ 📊 操作日志                                              │
│ 2024-01-12 10:30  张三  将状态从"待办"改为"进行中"       │
│ 2024-01-11 09:00  李四  分配给张三                       │
└─────────────────────────────────────────────────────────┘
```

## 🧩 核心组件设计

### 1. 项目卡片组件 (ProjectCard)
```vue
<template>
  <div class="project-card" :class="statusClass">
    <div class="card-header">
      <div class="project-icon">{{ project.icon }}</div>
      <div class="project-info">
        <h3 class="project-name">{{ project.name }}</h3>
        <p class="project-desc">{{ project.description }}</p>
      </div>
      <div class="project-actions">
        <el-dropdown>
          <el-button type="text" icon="el-icon-more" />
        </el-dropdown>
      </div>
    </div>
    
    <div class="card-body">
      <div class="progress-section">
        <div class="progress-info">
          <span>进度</span>
          <span>{{ project.progress }}%</span>
        </div>
        <el-progress :percentage="project.progress" :color="progressColor" />
      </div>
      
      <div class="meta-info">
        <div class="meta-item">
          <el-icon name="user" />
          <span>{{ project.owner_name }}</span>
        </div>
        <div class="meta-item">
          <el-icon name="calendar" />
          <span>{{ formatDate(project.end_date) }}</span>
        </div>
        <div class="meta-item">
          <el-tag :type="statusType">{{ project.status_name }}</el-tag>
        </div>
      </div>
    </div>
    
    <div class="card-footer">
      <div class="team-avatars">
        <el-avatar-group :max="3">
          <el-avatar v-for="member in project.members" :key="member.id" 
                     :src="member.avatar" :title="member.name" />
        </el-avatar-group>
      </div>
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="viewProject">查看</el-button>
        <el-button size="small" @click="editProject">编辑</el-button>
      </div>
    </div>
  </div>
</template>
```

### 2. 任务卡片组件 (TaskCard)
```vue
<template>
  <div class="task-card" 
       :class="[`priority-${task.priority}`, `status-${task.status}`]"
       draggable="true"
       @dragstart="onDragStart"
       @click="showTaskDetail">
    
    <div class="task-header">
      <div class="task-priority" :class="`priority-${task.priority}`"></div>
      <div class="task-number">#{{ task.task_no }}</div>
      <el-dropdown trigger="click" @command="handleCommand">
        <el-button type="text" icon="el-icon-more" size="mini" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="edit">编辑</el-dropdown-item>
            <el-dropdown-item command="assign">分配</el-dropdown-item>
            <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <div class="task-content">
      <h4 class="task-title">{{ task.title }}</h4>
      <p class="task-desc" v-if="task.description">{{ task.description }}</p>
    </div>
    
    <div class="task-meta">
      <div class="assignee" v-if="task.assignee">
        <el-avatar :size="20" :src="task.assignee.avatar" />
        <span>{{ task.assignee.name }}</span>
      </div>
      <div class="due-date" v-if="task.due_date">
        <el-icon name="calendar" />
        <span :class="{ 'overdue': isOverdue }">{{ formatDate(task.due_date) }}</span>
      </div>
    </div>
    
    <div class="task-tags" v-if="task.tags && task.tags.length">
      <el-tag v-for="tag in task.tags" :key="tag" size="mini" type="info">
        {{ tag }}
      </el-tag>
    </div>
    
    <div class="task-footer">
      <div class="task-stats">
        <span v-if="task.comments_count" class="stat-item">
          <el-icon name="chat-dot-round" />
          {{ task.comments_count }}
        </span>
        <span v-if="task.attachments_count" class="stat-item">
          <el-icon name="paperclip" />
          {{ task.attachments_count }}
        </span>
      </div>
      <div class="task-progress" v-if="task.progress > 0">
        <el-progress :percentage="task.progress" :width="60" type="circle" />
      </div>
    </div>
  </div>
</template>
```

### 3. 看板列组件 (KanbanColumn)
```vue
<template>
  <div class="kanban-column">
    <div class="column-header">
      <div class="column-title">
        <span class="status-dot" :style="{ backgroundColor: status.color }"></span>
        <h3>{{ status.name }}</h3>
        <span class="task-count">({{ tasks.length }})</span>
      </div>
      <div class="column-actions">
        <el-button type="text" icon="el-icon-plus" @click="addTask">
          添加任务
        </el-button>
      </div>
    </div>
    
    <div class="column-body" 
         @drop="onDrop" 
         @dragover.prevent 
         @dragenter.prevent>
      <task-card 
        v-for="task in tasks" 
        :key="task.id" 
        :task="task"
        @drag-start="onTaskDragStart"
        @click="onTaskClick" />
      
      <div v-if="tasks.length === 0" class="empty-column">
        <el-empty description="暂无任务" :image-size="80" />
      </div>
    </div>
  </div>
</template>
```

## 📱 响应式设计

### 断点设置
```css
/* 移动端 */
@media (max-width: 768px) {
  .kanban-board {
    flex-direction: column;
  }
  
  .kanban-column {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .project-card {
    margin-bottom: 12px;
  }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  .kanban-column {
    min-width: 280px;
  }
  
  .project-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 桌面端 */
@media (min-width: 1025px) {
  .kanban-column {
    min-width: 320px;
  }
  
  .project-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 🎯 交互设计

### 拖拽功能
- **任务拖拽**: 支持任务在不同状态列之间拖拽
- **优先级调整**: 支持任务在同一列内拖拽排序
- **批量操作**: 支持多选任务进行批量状态变更

### 快捷操作
- **快捷键**: 支持常用操作的快捷键
- **右键菜单**: 提供上下文相关的操作菜单
- **批量编辑**: 支持批量修改任务属性

### 实时更新
- **WebSocket**: 实时同步任务状态变更
- **乐观更新**: 操作后立即更新UI，提升用户体验
- **冲突处理**: 处理多用户同时编辑的冲突情况

---

*本UI设计方案基于飞书的设计理念，结合Element Plus组件库，为100人以下企业提供简洁高效的项目管理界面。*
