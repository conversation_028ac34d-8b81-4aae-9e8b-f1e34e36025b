# 消息通知系统

本模块提供了一个完整的多租户消息通知系统，支持多种通知通道，包括站内信、邮件、短信、企业微信、钉钉和自定义Webhook等。

## 功能特点

- 多租户支持：每个租户可以有自己的通知模板和通道配置
- 多通道支持：支持站内信、邮件、短信、企业微信、钉钉和自定义Webhook等多种通知通道
- 模板管理：支持创建和管理通知模板，支持变量替换
- 通道管理：支持配置和管理不同的通知通道
- 消息管理：支持查看和管理已发送的消息
- 延迟发送：支持设置延迟发送时间
- 消息队列：使用消息队列处理通知发送，提高系统性能和可靠性

## 安装步骤

### 1. 数据库配置

执行以下SQL脚本创建必要的数据库表：

```sql
-- 执行数据库迁移脚本
php think migrate:run
```

### 2. 菜单配置

执行以下SQL脚本添加菜单项：

```sql
-- 执行菜单配置脚本
mysql -u用户名 -p密码 数据库名 < app/notice/menu_setup.sql
```

### 3. 配置队列处理器

在配置文件中添加队列处理器配置：

```php
// config/queue.php
return [
    'default' => 'redis',
    'connections' => [
        'redis' => [
            'driver' => 'redis',
            'queue' => 'default',
            'retry_after' => 90,
        ],
    ],
];
```

### 4. 启动队列处理器

```bash
php think queue:work
```

## 使用说明

### 1. 通知模板管理

在"通知模板"页面，您可以创建和管理通知模板。模板支持变量替换，使用双大括号语法，例如：`{{变量名}}`。

### 2. 通知通道管理

在"通知通道"页面，您可以配置不同的通知通道，包括邮件服务器、短信服务商等。

### 3. 租户配置

在"租户配置"下，您可以为每个租户配置：

- **模板配置**：启用/禁用特定模板，以及为每个模板配置可用的通道
- **通道配置**：为每个租户配置通道参数，如邮件服务器、短信API密钥等

### 4. 发送消息

在"发送消息"页面，您可以：

1. 选择通知模板
2. 填写模板变量
3. 选择接收人
4. 配置发送选项（优先级、通道、延迟时间等）
5. 预览和发送消息

### 5. 消息管理

在"消息管理"页面，您可以查看所有已发送的消息，包括发送状态、发送时间等信息。

## API使用

### 发送消息API

```php
use app\notice\service\MessageService;

// 发送消息
$result = MessageService::send([
    'template_code' => 'welcome_email',
    'variables' => [
        'username' => '张三',
        'company' => '示例公司'
    ],
    'recipients' => [1, 2, 3], // 用户ID数组
    'options' => [
        'tenant_id' => 1,
        'sender_id' => 1,
        'sender_name' => '系统管理员',
        'module_code' => 'user',
        'business_id' => '123',
        'priority' => 0,
        'send_channels' => 'site,email',
        'delay_minutes' => 0
    ]
]);
```

## 故障排除

### 消息未发送

1. 检查队列处理器是否正在运行
2. 检查通道配置是否正确
3. 检查日志文件中的错误信息

### 模板变量未替换

1. 确保变量名称与模板中的变量名称完全匹配
2. 检查变量值是否正确传递

## 更新日志

### v1.0.0 (2023-06-01)

- 初始版本发布
- 支持站内信、邮件、短信通道
- 基本模板管理功能

### v1.1.0 (2023-08-15)

- 添加企业微信、钉钉和自定义Webhook通道
- 添加延迟发送功能
- 改进队列处理机制

### v1.2.0 (2023-10-20)

- 添加多租户支持
- 添加模板和通道的租户级配置
- 优化消息发送流程

## 系统概述

消息通知系统是一个轻量级、可扩展的通知服务，支持工作流引擎的催办通知等场景，并允许CRM、进销存等其他业务模块接入使用。系统支持单租户环境下150人以内规模的使用，与现有多租户框架无缝集成。

## 已实现功能

### 核心功能
- 消息模板管理与渲染
- 多通道消息发送（站内信）
- 消息队列与异步处理
- 消息状态管理（已读/未读）
- 用户消息中心
- 未读消息计数与缓存

### 接口服务
- 管理端接口（模板管理、通道配置等）
- 用户端接口（消息列表、标记已读等）
- 集成接口（消息发送、状态查询等）

### 通道支持
- 站内信通道（已实现）
- 电子邮件通道（预留）
- 短信通道（预留）
- 企业微信/钉钉通道（预留）

### 其他特性
- 多终端URL支持
- 消息队列命令行工具
- 缓存优化
- 数据权限控制

## 使用说明

### 发送消息
```php
// 示例：发送工作流催办通知
app('notice.service')->send(
    'workflow_urge_task',  // 模板代码
    [                      // 模板变量
        'task_name' => $task['task_name'],
        'process_title' => $task['title'],
        'urge_reason' => $reason,
        'urge_time' => date('Y-m-d H:i:s')
    ],
    [$task['approver_id']], // 接收人
    [                       // 选项
        'module_code' => 'workflow',
        'business_id' => $task['id'],
        'priority' => 1,    // 重要
        'send_channels' => 'site,email'
    ]
);
```

### 获取用户未读消息数
```php
$count = app('notice.service')->getUnreadCount($userId);
```

### 获取用户消息列表
```php
$messages = app('notice.service')->getUserMessages($userId, [
    'read_status' => 0,  // 0未读，1已读
    'type' => 'workflow' // 消息类型
]);
```

### 标记消息已读
```php
app('notice.service')->markAsRead($messageId, $userId);
```

### 处理消息队列（命令行）
```bash
php think notice:process-queue --limit=20 --clean --days=7
```

## 目录结构
```
app/notice/
├── command/                # 命令行工具
│   └── ProcessQueue.php    # 处理队列命令
├── controller/             # 控制器
│   ├── NoticeChannelConfigController.php
│   ├── NoticeMessageController.php
│   ├── NoticeQueueController.php
│   ├── NoticeRecipientController.php
│   └── NoticeTemplateController.php
├── model/                  # 模型
│   ├── NoticeChannelConfigModel.php
│   ├── NoticeMessageModel.php
│   ├── NoticeQueueModel.php
│   ├── NoticeRecipientModel.php
│   └── NoticeTemplateModel.php
├── route/                  # 路由
│   └── route.php
├── service/                # 服务
│   ├── channel/            # 通道服务
│   │   ├── BaseChannelService.php
│   │   ├── ChannelFactory.php
│   │   └── SiteChannelService.php
│   ├── interfaces/         # 接口定义
│   │   ├── ChannelServiceInterface.php
│   │   ├── NoticeServiceInterface.php
│   │   ├── QueueServiceInterface.php
│   │   └── TemplateServiceInterface.php
│   ├── NoticeChannelConfigService.php
│   ├── NoticeMessageService.php
│   ├── NoticeQueueService.php
│   ├── NoticeRecipientService.php
│   └── NoticeTemplateService.php
├── backend_todo.md         # 后端开发计划
├── data.sql                # 数据库脚本
├── design.md               # 设计文档
├── frontend_todo.md        # 前端开发计划
└── README.md               # 说明文档
```