<?php
declare(strict_types=1);

namespace {{NAMESPACE}};

use app\common\core\base\BaseModel;

/**
 * {{COMMENT}}模型
 */
class {{ENTITY_NAME}} extends BaseModel
{
    // 设置表名
    protected $name = '{{TABLE_NAME}}';
    
    // 设置主键
    protected $pk = '{{PRIMARY_KEY}}';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段转换
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // 软删除
    protected string $deleteTime = 'deleted_at';

    public function __construct(array $data = [])
    {
        parent::__construct($data);
    }
}