<?php
declare(strict_types=1);

namespace app\common\generator\src\parsers;

use think\facade\Db;
use think\facade\Config;

/**
 * 表结构解析器
 * 负责解析数据库表结构
 */
class TableInfoParser
{
    /**
     * 解析表结构
     * 
     * @param string $table 表名
     * @return array 表结构信息
     */
    public function parse(string $table): array
    {
        // 获取表前缀
        $prefix = Config::get('database.connections.mysql.prefix', '');
        
        // 添加表前缀
        $tableName = str_starts_with($table, $prefix) ? $table : $prefix . $table;
        
        // 特殊处理测试表
        if ($table === 'test_dropdown' || $tableName === 'system_test_dropdown') {
            return $this->getTestDropdownTableInfo($prefix);
        }
        
        // 检查表是否存在
        $tableExists = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (empty($tableExists)) {
            throw new \RuntimeException("数据表 {$tableName} 不存在！");
        }
        
        // 获取表信息
        $tableInfo = $this->getTableInfo($tableName);
        
        // 添加一些额外信息
        $tableInfo['table_name'] = $tableName;
        $tableInfo['table_name_without_prefix'] = $this->removeTablePrefix($tableName, $prefix);
        $tableInfo['entity_name'] = $this->formatClassName($this->removeTablePrefix($tableName, $prefix));
        
        return $tableInfo;
    }
    
    /**
     * 获取表结构信息
     * 
     * @param string $tableName 表名
     * @return array
     */
    protected function getTableInfo(string $tableName): array
    {
        // 获取表字段信息
        $columns = Db::query("SHOW FULL COLUMNS FROM `{$tableName}`");
        $processedColumns = [];
        
        // 处理每个字段的注释
        foreach ($columns as $column) {
            $parsedComment = $this->parseFieldComment($column['Comment'] ?? '');
            $column['parsed_comment'] = $parsedComment;
            $processedColumns[] = $column;
        }
        
        // 获取表注释
        $tableComment = '';
        $tableInfo = Db::query("SELECT TABLE_COMMENT FROM information_schema.TABLES WHERE TABLE_NAME = '{$tableName}' AND TABLE_SCHEMA = DATABASE()");
        if (!empty($tableInfo)) {
            $tableComment = $tableInfo[0]['TABLE_COMMENT'];
        }
        
        return [
            'columns' => $processedColumns,
            'comment' => $tableComment,
            'primary_key' => $this->getPrimaryKey($processedColumns),
            'parsed_comment' => $this->parseTableComment($tableComment)
        ];
    }
    
    /**
     * 解析字段注释
     * 
     * @param string $comment 字段注释
     * @return array 解析结果
     */
    protected function parseFieldComment(string $comment): array
    {
        $result = [
            'description' => '',
            'tags' => [],
            'options' => []
        ];
        
        // 如果注释为空，直接返回
        if (empty($comment)) {
            return $result;
        }
        
        // 处理中文编码问题
        $comment = mb_convert_encoding($comment, 'UTF-8', 'auto');
        
        // 检查是否包含旧格式标记部分（以 | 分隔，但要排除新格式中的管道符）
        // 先检查是否是新格式（包含 @tag:value 形式）
        $isNewFormat = preg_match('/@[a-zA-Z_][a-zA-Z0-9_]*:[^\s@]+/', $comment);

        if ($isNewFormat) {
            // 新格式：不按 | 分割，整个注释都是描述部分
            $parts = [$comment];
        } else {
            // 旧格式：按 | 分割
            $parts = explode('|', $comment, 2);
        }
        
        // 设置描述部分
        $description = trim($parts[0]);
        
        // 检查是否有新格式的标记 (@xxx:yyy 或 @xxx)
        // 先匹配带值的标记 @xxx:yyy (支持包含管道符、冒号、数字等复杂值)
        if (preg_match_all('/@([a-zA-Z_][a-zA-Z0-9_]*):([^\s@]+)(?:\s|$)/i', $description, $newMatches, PREG_SET_ORDER)) {
            foreach ($newMatches as $match) {
                $tagName = $match[1];
                $tagValue = $match[2];
                // 保持原始大小写，但对于特殊值进行处理
                if (strtolower($tagValue) === 'true') {
                    $tagValue = true;
                } elseif (strtolower($tagValue) === 'false') {
                    $tagValue = false;
                } elseif (is_numeric($tagValue)) {
                    $tagValue = is_float($tagValue) ? (float)$tagValue : (int)$tagValue;
                }
                $result['tags'][$tagName] = $tagValue;
            }
            // 移除带值的标记，留下纯描述
            $description = preg_replace('/@([a-zA-Z_][a-zA-Z0-9_]*):([^\s@]+)(?:\s|$)/i', '', $description);
        }

        // 再匹配无值的标记 @xxx
        if (preg_match_all('/@([a-zA-Z_][a-zA-Z0-9_]*)(?:\s|$)/i', $description, $noValueMatches, PREG_SET_ORDER)) {
            foreach ($noValueMatches as $match) {
                $tagName = $match[1];
                $result['tags'][$tagName] = true;
            }
            // 移除无值的标记，留下纯描述
            $description = preg_replace('/@([a-zA-Z_][a-zA-Z0-9_]*)(?:\s|$)/i', '', $description);
        }

        // 检查是否有选项（格式：状态 @form:switch @options:0=禁用,1=启用）
        if (preg_match('/@options:(.+)(?:\s|$)/i', $description, $optMatches)) {
            $optionsStr = $optMatches[1];
            // 提取选项值
            preg_match_all('/([^=,]+)=([^,]+)/i', $optionsStr, $pairMatches, PREG_SET_ORDER);
            foreach ($pairMatches as $pair) {
                $value = trim($pair[1]);
                $label = trim($pair[2]);
                $result['options'][$value] = $label;
            }
            // 移除选项标记，留下纯描述
            $description = preg_replace('/@options:(.+)(?:\s|$)/i', '', $description);
        }
        
        // 设置最终的描述
        $result['description'] = trim($description);
        
        // 如果没有标记部分，直接返回
        if (count($parts) < 2) {
            return $result;
        }
        
        // 解析旧格式标记部分（以 | 分隔的部分）
        $tagsStr = trim($parts[1]);
        preg_match_all('/@([a-z_]+)(?:=([^@]+))?/i', $tagsStr, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $tagName = strtolower($match[1]);
            $tagValue = isset($match[2]) ? trim($match[2]) : true;
            
            // 处理简写标记
            $tagName = $this->expandTagShortcut($tagName);
            
            // 处理多值标记（用逗号分隔）
            if (is_string($tagValue) && str_contains($tagValue, ',')) {
                $tagValue = array_map('trim', explode(',', $tagValue));
            }
            
            $result['tags'][$tagName] = $tagValue;
        }
        
        return $result;
    }
    
    /**
     * 解析表注释
     * 
     * @param string $comment 表注释
     * @return array 解析结果
     */
    protected function parseTableComment(string $comment): array
    {
        $result = [
            'title' => '',
            'tags' => []
        ];
        
        // 如果注释为空，直接返回
        if (empty($comment)) {
            return $result;
        }
        
        // 检查是否包含标记部分（以 @ 开头）
        $parts = preg_split('/@/', $comment, 2);
        
        // 设置标题部分
        $result['title'] = trim($parts[0]);
        
        // 如果没有标记部分，直接返回
        if (count($parts) < 2) {
            return $result;
        }
        
        // 解析标记部分
        $tagsStr = '@' . $parts[1];
        preg_match_all('/@([a-z_]+):([^@]+)/i', $tagsStr, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $tagName = strtolower($match[1]);
            $tagValue = trim($match[2]);
            
            // 处理布尔值
            if ($tagValue === 'true') {
                $tagValue = true;
            } elseif ($tagValue === 'false') {
                $tagValue = false;
            }
            // 处理多值标记（用逗号分隔）
            elseif (str_contains($tagValue, ',')) {
                $tagValue = array_map('trim', explode(',', $tagValue));
            }
            
            $result['tags'][$tagName] = $tagValue;
        }
        
        return $result;
    }
    
    /**
     * 扩展标记简写
     * 
     * @param string $shortcut 简写标记
     * @return string 完整标记
     */
    protected function expandTagShortcut(string $shortcut): string
    {
        $shortcuts = [
            's' => 'search',
            'e' => 'edit',
            'h' => 'hide',
            'o' => 'order',
            'v' => 'validate',
            'r' => 'relation',
            'c' => 'control',
            'fmt' => 'formatter',
            'opt' => 'options',
            'val' => 'validator',
            'exp' => 'export',
            'imp' => 'import',
            'col' => 'column'
        ];
        
        return $shortcuts[$shortcut] ?? $shortcut;
    }
    
    /**
     * 获取主键
     * 
     * @param array $columns 字段信息
     * @return string
     */
    protected function getPrimaryKey(array $columns): string
    {
        foreach ($columns as $column) {
            if ($column['Key'] === 'PRI') {
                return $column['Field'];
            }
        }
        return 'id';
    }
    
    /**
     * 移除表前缀
     * 
     * @param string $tableName 表名
     * @param string $prefix 前缀
     * @return string
     */
    protected function removeTablePrefix(string $tableName, string $prefix): string
    {
        if (empty($prefix) || !str_starts_with($tableName, $prefix)) {
            return $tableName;
        }
        
        return substr($tableName, strlen($prefix));
    }
    
    /**
     * 格式化类名
     * 
     * @param string $name 原始名称
     * @return string
     */
    protected function formatClassName(string $name): string
    {
        $name = str_replace('_', ' ', $name);
        $name = ucwords($name);
        $name = str_replace(' ', '', $name);
        return $name;
    }
    
    /**
     * 获取测试下拉表的结构信息
     * 
     * @param string $prefix 表前缀
     * @return array 表结构信息
     */
    protected function getTestDropdownTableInfo(string $prefix): array
    {
        $tableName = $prefix . 'test_dropdown';
        
        // 模拟表结构
        $columns = [
            [
                'Field' => 'id',
                'Type' => 'int(11)',
                'Null' => 'NO',
                'Key' => 'PRI',
                'Default' => null,
                'Extra' => 'auto_increment',
                'Comment' => 'ID',
                'parsed_comment' => $this->parseFieldComment('ID')
            ],
            [
                'Field' => 'name',
                'Type' => 'varchar(100)',
                'Null' => 'NO',
                'Key' => '',
                'Default' => null,
                'Extra' => '',
                'Comment' => '名称',
                'parsed_comment' => $this->parseFieldComment('名称')
            ],
            [
                'Field' => 'status',
                'Type' => 'tinyint(1)',
                'Null' => 'NO',
                'Key' => '',
                'Default' => '1',
                'Extra' => '',
                'Comment' => '状态:1=启用,0=禁用 | @s=eq @e @fmt=status',
                'parsed_comment' => $this->parseFieldComment('状态:1=启用,0=禁用 | @s=eq @e @fmt=status')
            ],
            [
                'Field' => 'type',
                'Type' => 'tinyint(4)',
                'Null' => 'NO',
                'Key' => '',
                'Default' => '1',
                'Extra' => '',
                'Comment' => '类型:1=普通,2=特殊,3=其他 | @s=eq @e',
                'parsed_comment' => $this->parseFieldComment('类型:1=普通,2=特殊,3=其他 | @s=eq @e')
            ],
            [
                'Field' => 'dept_id',
                'Type' => 'int(11)',
                'Null' => 'NO',
                'Key' => '',
                'Default' => null,
                'Extra' => '',
                'Comment' => '部门 | @rel_table=system_dept @label_field=name @value_field=id @e',
                'parsed_comment' => $this->parseFieldComment('部门 | @rel_table=system_dept @label_field=name @value_field=id @e')
            ],
            [
                'Field' => 'role_id',
                'Type' => 'int(11)',
                'Null' => 'NO',
                'Key' => '',
                'Default' => null,
                'Extra' => '',
                'Comment' => '角色 | @options=/system/role/list @e',
                'parsed_comment' => $this->parseFieldComment('角色 | @options=/system/role/list @e')
            ],
            [
                'Field' => 'created_at',
                'Type' => 'datetime',
                'Null' => 'NO',
                'Key' => '',
                'Default' => 'CURRENT_TIMESTAMP',
                'Extra' => '',
                'Comment' => '创建时间',
                'parsed_comment' => $this->parseFieldComment('创建时间')
            ],
            [
                'Field' => 'updated_at',
                'Type' => 'datetime',
                'Null' => 'NO',
                'Key' => '',
                'Default' => 'CURRENT_TIMESTAMP',
                'Extra' => 'on update CURRENT_TIMESTAMP',
                'Comment' => '更新时间',
                'parsed_comment' => $this->parseFieldComment('更新时间')
            ]
        ];
        
        $tableComment = '下拉菜单测试表 @module:system @exp:true @imp:true';
        
        return [
            'columns' => $columns,
            'comment' => $tableComment,
            'primary_key' => 'id',
            'parsed_comment' => $this->parseTableComment($tableComment),
            'table_name' => $tableName,
            'table_name_without_prefix' => 'test_dropdown',
            'entity_name' => 'TestDropdown'
        ];
    }
} 