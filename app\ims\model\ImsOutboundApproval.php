<?php
declare(strict_types=1);

namespace app\ims\model;

use app\common\core\base\BaseModel;
use app\crm\model\CrmCustomer;
use app\system\model\DeptModel;
use app\system\model\SystemAdmin;
use app\workflow\constants\WorkflowStatusConstant;

/**
 * 出库申请表模型
 */
class ImsOutboundApproval extends BaseModel
{
	// 设置表名
	protected $name = 'ims_outbound_approval';
	
	// 字段类型转换
	protected $type = [
		'workflow_instance_id' => 'integer',
		'approval_status'      => 'integer',
		'submitter_id'         => 'integer',
		'dept_id'              => 'integer',
		'warehouse_id'         => 'integer',
		'customer_id'          => 'integer',
		'total_amount'         => 'float',
		'total_quantity'       => 'float',
		'creator_id'           => 'integer',
	];
	
	protected $append = [
		'items',
		'submitter',
		'department',
		'customer',
		'creator',
	];
	
	
	// 审批状态常量
	const STATUS_DRAFT      = WorkflowStatusConstant::STATUS_DRAFT;                                                                                                                                                                                                                                                                                                       // 草稿
	const STATUS_PROCESSING = WorkflowStatusConstant::STATUS_PROCESSING;                                                                                                                                                                                                                                                                                                  // 审批中
	const STATUS_COMPLETED  = WorkflowStatusConstant::STATUS_COMPLETED;                                                                                                                                                                                                                                                                                                   // 已通过
	const STATUS_REJECTED   = WorkflowStatusConstant::STATUS_REJECTED;                                                                                                                                                                                                                                                                                                    // 已拒绝
	const STATUS_TERMINATED = WorkflowStatusConstant::STATUS_TERMINATED;                                                                                                                                                                                                                                                                                                  // 已终止
	const STATUS_RECALLED   = WorkflowStatusConstant::STATUS_RECALLED;                                                                                                                                                                                                                                                                                                    // 已撤回
	const STATUS_VOID       = WorkflowStatusConstant::STATUS_VOID;                                                                                                                                                                                                                                                                                                        // 已作废
	
	// 出库类型常量
	const TYPE_SALES    = 1;                                                                                                                                                                                                                                                                                                                                              // 销售出库
	const TYPE_TRANSFER = 2;                                                                                                                                                                                                                                                                                                                                              // 调拨出库
	const TYPE_OTHER    = 3;                                                                                                                                                                                                                                                                                                                                              // 其他出库
	
	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'outbound_no'     => ['type' => 'like'],
			'outbound_type'   => ['type' => 'eq'],
			'approval_status' => ['type' => 'eq'],
			'dept_id'         => ['type' => 'eq'],
			'warehouse_id'    => ['type' => 'eq'],
			'customer_id'     => ['type' => 'eq'],
			'outbound_date'   => ['type' => 'date'],
			'total_amount'    => ['type' => 'between'],
			'total_quantity'  => ['type' => 'between'],
			'submit_time'     => ['type' => 'datetime'],
			'approval_time'   => ['type' => 'datetime'],
			'created_at'      => ['type' => 'date'],
		];
	}
	
	public function getAttachmentAttr($value)
	{
		return empty($value)
			? []
			: explode(',', $value);
	}
	
	/**
	 * 关联出库明细
	 */
	public function items()
	{
		return $this->hasMany(ImsOutboundItem::class, 'outbound_id', 'id');
	}
	
	/**
	 * 关联部门
	 */
	public function department()
	{
		return $this->belongsTo(DeptModel::class, 'dept_id', 'id')
		            ->bind([
			            'dept_name' => 'name'
		            ]);
	}
	
	/**
	 * 关联仓库
	 */
	/*public function warehouse()
	{
		return $this->belongsTo(\app\ims\model\ImsWarehouse::class, 'warehouse_id', 'id')->bind([
			'warehouse_name' => 'name'
		]);
	}*/
	
	/**
	 * 关联客户
	 */
	public function customer()
	{
		return $this->belongsTo(CrmCustomer::class, 'customer_id', 'id')
		            ->bind([
			            'customer_name'
		            ]);
	}
	
	/**
	 * 关联提交人
	 */
	public function submitter()
	{
		return $this->belongsTo(SystemAdmin::class, 'submitter_id', 'id')
		            ->bind([
			            'submitter_name' => 'real_name'
		            ]);
	}
	
	/**
	 * 关联创建人
	 */
	public function creator()
	{
		return $this->belongsTo(SystemAdmin::class, 'creator_id', 'id')
		            ->bind([
			            'creator_name' => 'real_name'
		            ]);
	}
	
	/**
	 * 获取出库类型文本
	 */
	public function getOutboundTypeTextAttr($value, $data)
	{
		$types = [
			self::TYPE_SALES    => '销售出库',
			self::TYPE_TRANSFER => '调拨出库',
			self::TYPE_OTHER    => '其他出库',
		];
		
		return $types[$data['outbound_type']] ?? '未知';
	}
	
	/**
	 * 获取审批状态文本
	 */
	public function getApprovalStatusTextAttr($value, $data)
	{
		$statuses = [
			self::STATUS_DRAFT      => '草稿',
			self::STATUS_PROCESSING => '审批中',
			self::STATUS_COMPLETED  => '已通过',
			self::STATUS_REJECTED   => '已拒绝',
			self::STATUS_TERMINATED => '已终止',
			self::STATUS_RECALLED   => '已撤回',
			self::STATUS_VOID       => '已作废',
		];
		
		return $statuses[$data['approval_status']] ?? '未知';
	}
}
