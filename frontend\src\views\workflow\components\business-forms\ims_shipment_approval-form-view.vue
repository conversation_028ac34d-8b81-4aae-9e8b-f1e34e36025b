<template>
  <div class="shipment-form-view">
    <el-descriptions :column="2" border>
      <!--      <el-descriptions-item label="出货单号">
              {{ formData.shipment_no || '-' }}
            </el-descriptions-item>-->

      <el-descriptions-item label="出货日期">
        {{ formData.shipment_date || '-' }}
      </el-descriptions-item>

      <!--      <el-descriptions-item label="供应商">
              {{ formData.supplier_name || '-' }}
            </el-descriptions-item>-->

      <el-descriptions-item label="客户名称">
        {{ formData.customer_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="所在部门">
        {{ formData.dept_name || '-' }}
      </el-descriptions-item>

      <!--      <el-descriptions-item label="联系人">
              {{ formData.contact_person || '-' }}
            </el-descriptions-item>

            <el-descriptions-item label="联系电话">
              {{ formData.contact_phone || '-' }}
            </el-descriptions-item>-->

      <!--      <el-descriptions-item label="物流公司">
              {{ formData.logistics_company || '-' }}
            </el-descriptions-item>

            <el-descriptions-item label="快递单号">
              {{ formData.tracking_no || '-' }}
            </el-descriptions-item>-->

      <!--      <el-descriptions-item label="明细数量">
              {{ formData.total_quantity || 0 }}
            </el-descriptions-item>-->

      <el-descriptions-item label="总金额">
        ¥{{ formData.total_amount || 0 }}
      </el-descriptions-item>

      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        {{ formData.remark }}
      </el-descriptions-item>

      <!-- 图片附件 -->
      <el-descriptions-item
        label="图片"
        :span="2"
        v-if="formData.attachment && formData.attachment.length > 0"
      >
        <div class="attachment-preview">
          <el-image
            v-for="(attachment, index) in formData.attachment"
            :key="index"
            :src="attachment"
            :preview-src-list="formData.attachment"
            :initial-index="index"
            fit="cover"
            class="attachment-image"
            preview-teleported
            :hide-on-click-modal="true"
          />
        </div>
      </el-descriptions-item>

      <!-- 出货明细 -->
      <el-descriptions-item
        label="出货明细"
        :span="2"
        v-if="formData.items && formData.items.length > 0"
      >
        <el-table :data="formData.items" border size="small">
          <el-table-column prop="supplier_name" label="供应商" />
          <el-table-column prop="product_name" label="产品名称" />
          <el-table-column label="数量">
            <template #default="{ row }">
              {{ row.quantity || 0 }}
              <span v-if="row.product_unit" style="margin-left: 4px; color: #909399">{{
                row.product_unit
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit_price" label="单价(元)" />
          <el-table-column prop="total_amount" label="小计" />
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem, ElTable, ElTableColumn, ElImage } from 'element-plus'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'ims_shipment_approval'
    }
  })
</script>

<style scoped lang="scss">
  .attachment-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .attachment-image {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #dcdfe6;
  }
</style>
