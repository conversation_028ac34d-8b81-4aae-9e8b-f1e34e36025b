<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\algorithm;

/**
 * 限流算法接口
 */
interface AlgorithmInterface
{
    /**
     * 检查是否允许请求通过
     *
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @return bool 是否允许通过
     */
    public function allow(string $key, string $identifier, int $limitCount, int $timeWindow): bool;
    
    /**
     * 获取剩余可用请求次数
     *
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @return int 剩余可用请求次数
     */
    public function getRemainingLimit(string $key, string $identifier, int $limitCount, int $timeWindow): int;
    
    /**
     * 获取重置时间（秒）
     * 
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $timeWindow 时间窗口(秒)
     * @return int 重置时间
     */
    public function getResetTime(string $key, string $identifier, int $timeWindow): int;
} 