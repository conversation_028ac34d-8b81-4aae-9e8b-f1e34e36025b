<?php
declare(strict_types=1);

namespace app\system\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * 租户套餐模型
 */
class TenantPackageModel extends Model
{
    use SoftDelete;
    
    // 设置表名
    protected $name = 'system_tenant_package';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'name'           => 'string',
        'price'          => 'float',
        'duration'       => 'int',
        'max_user_count' => 'int',
        'menu_ids'       => 'string',
        'status'         => 'int',
        'remark'         => 'string',
        'creator_id'     => 'int',
        'updater_id'     => 'int',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
        'deleted_at'     => 'datetime',
    ];
    
    // 获取菜单ID数组
    public function getMenuIdsArrayAttr()
    {
        if (empty($this->menu_ids)) {
            return [];
        }
        
        return explode(',', $this->menu_ids);
    }
    
    // 设置菜单ID数组
    public function setMenuIdsArrayAttr($value)
    {
        if (is_array($value)) {
            $this->menu_ids = implode(',', array_unique(array_filter($value)));
        }
    }
} 