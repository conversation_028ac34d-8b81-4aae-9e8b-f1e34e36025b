<template>
  <div class="test-page">
    <h2>回款详情弹出框测试页面</h2>
    
    <div class="test-buttons">
      <el-button type="primary" @click="showListDialog">
        测试回款列表对话框
      </el-button>
      
      <el-button type="success" @click="showDetailDialog">
        测试回款详情对话框
      </el-button>
    </div>

    <!-- 回款列表对话框 -->
    <ReceivableListDialog
      v-model="showList"
      :customer-id="1"
      :contract-data="mockContractData"
    />

    <!-- 回款详情对话框 -->
    <ReceivableDetailDialog
      v-model="showDetail"
      :receivable-id="1"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import ReceivableListDialog from '@/components/custom/CustomerDetailDrawer/forms/ReceivableListDialog.vue'
  import ReceivableDetailDialog from '@/components/custom/CustomerDetailDrawer/forms/ReceivableDetailDialog.vue'

  const showList = ref(false)
  const showDetail = ref(false)

  // 模拟合同数据
  const mockContractData = ref({
    id: 1,
    contract_name: '测试合同',
    contract_amount: 100000,
    paid_amount: 50000
  })

  const showListDialog = () => {
    showList.value = true
  }

  const showDetailDialog = () => {
    showDetail.value = true
  }
</script>

<style scoped lang="scss">
  .test-page {
    padding: 20px;

    h2 {
      margin-bottom: 20px;
      color: #303133;
    }

    .test-buttons {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;

      .el-button {
        padding: 12px 24px;
      }
    }
  }
</style>
