<?php
declare(strict_types=1);

namespace app\project\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\project\model\ProjectProject;
use app\project\model\ProjectMember;
use app\project\model\ProjectTask;
use app\system\model\AdminModel;

/**
 * 项目表服务类
 */
class ProjectProjectService extends BaseService
{
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new ProjectProject();
		parent::__construct();
		$this->crudService->setDataRangeField('owner_id');
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'name' => ['type' => 'like'],
			
			'status' => ['type' => 'eq'],
			
			'priority' => ['type' => 'eq'],
			
			'start_date' => ['type' => 'between'],
			
			'end_date' => ['type' => 'between'],
			
			'owner_id' => ['type' => 'eq'],
			
			'is_archived' => ['type' => 'eq'],
		
		];
	}
	
	/**
	 * 获取验证规则 - 基于project_project表字段约束
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'name'              => 'require|max:100',
			'description'       => 'max:65535',
			'status'            => 'require|integer|in:1,2,3,4,5',
			'priority'          => 'integer|in:1,2,3,4',
			'start_date'        => 'date',
			'end_date'          => 'date',
			'actual_start_date' => 'date',
			'actual_end_date'   => 'date',
			'progress'          => 'float|between:0,100',
			'budget'            => 'float|egt:0',
			'actual_cost'       => 'float|egt:0',
			'owner_id'          => 'require|integer|gt:0',
			'department_id'     => 'integer|egt:0',
			'is_public'         => 'integer|in:0,1',
			'is_archived'       => 'integer|in:0,1',
			'tags'              => 'max:500',
			'cover_image'       => 'max:65535',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 获取我的项目列表
	 */
	public function getMyProjects(int $userId, array $params = [])
	{
		// 获取我参与的项目ID列表
		$memberModel = new ProjectMember();
		$projectIds  = $memberModel->where('user_id', $userId)
		                           ->column('project_id');
		
		$page  = intval($params['page'] ?? 1);
		$limit = intval($params['limit'] ?? 10);
		$ids   = ProjectProject::where('owner_id', $userId)
		                       ->column('id');
		$where = [];
		if (!empty($projectIds)) {
			array_merge($ids, $projectIds);
			$ids = array_unique($ids);
		}
		
		if (!empty($ids)) {
			$where[] = [
				'id',
				'in',
				$ids
			];
		}
		
		
		// 搜索条件
		if (!empty($params['name'])) {
			$where[] = [
				'name',
				'like',
				'%' . $params['name'] . '%'
			];
		}
		
		if (!empty($params['status'])) {
			$where[] = [
				'status',
				'=',
				$params['status']
			];
		}
		
		// 使用分页
		return $this->crudService->getPageList($where, ['id' => 'desc'], $page, $limit, ['owner']);
	}
	
	/**
	 * 获取项目详情
	 */
	public function getProjectDetail(int $id)
	{
		$project = $this->crudService->getDetail($id);
		if ($project->isEmpty()) {
			throw new \Exception('项目不存在');
		}
		
		$stats = $project->getProjectStats();
		
		return [
			'project' => $project->toArray(),
			'stats'   => $stats
		];
	}
	
	/**
	 * 获取看板数据
	 */
	public function getKanbanData(int $projectId): array
	{
		$taskModel = new ProjectTask();
		
		$statuses = [
			[
				'id'    => 1,
				'name'  => '待办',
				'color' => '#8C8C8C'
			],
			[
				'id'    => 2,
				'name'  => '进行中',
				'color' => '#1664FF'
			],
			[
				'id'    => 3,
				'name'  => '已完成',
				'color' => '#00BC70'
			],
			[
				'id'    => 4,
				'name'  => '已关闭',
				'color' => '#F54A45'
			]
		];
		
		$kanbanData = [];
		foreach ($statuses as $status) {
			$tasks = $taskModel->where('project_id', $projectId)
			                   ->where('status', $status['id'])
			                   ->with(['assignee'])
			                   ->order('sort', 'asc')
			                   ->select()
			                   ->toArray();
			
			$kanbanData[] = [
				'status' => $status,
				'tasks'  => $tasks
			];
		}
		
		return $kanbanData;
	}
	
	/**
	 * 添加项目成员
	 */
	public function addProjectMember(int $projectId, int $userId, string $role = 'member'): bool
	{
		$project = $this->crudService->getDetail($projectId);
		if ($project->isEmpty()) {
			throw new \Exception('项目不存在');
		}
		
		return $project->addMember($userId, $role);
	}
	
	/**
	 * 移除项目成员
	 */
	public function removeProjectMember(int $projectId, int $userId): bool
	{
		$project = $this->crudService->getDetail($projectId);
		if ($project->isEmpty()) {
			throw new \Exception('项目不存在');
		}
		$member = (new ProjectMember())->where('project_id', $projectId)
		                               ->where('user_id', $userId)
		                               ->findOrEmpty();
		if ($member->isEmpty()) {
			throw new \Exception('项目成员不存在');
		}
		return $member->delete();
	}
	
	/**
	 * 获取任务状态统计
	 */
	public function getTaskStatusStats($projectId)
	{
		// 模拟数据，实际应该从数据库查询
		return [
			[
				'name'  => '待办',
				'value' => 5,
				'color' => '#8C8C8C'
			],
			[
				'name'  => '进行中',
				'value' => 8,
				'color' => '#1664FF'
			],
			[
				'name'  => '已完成',
				'value' => 12,
				'color' => '#00BC70'
			],
			[
				'name'  => '已关闭',
				'value' => 2,
				'color' => '#F54A45'
			]
		];
	}
	
	/**
	 * 获取任务优先级统计
	 */
	public function getTaskPriorityStats($projectId)
	{
		// 模拟数据，实际应该从数据库查询
		return [
			[
				'name'  => '低',
				'value' => 6
			],
			[
				'name'  => '中',
				'value' => 12
			],
			[
				'name'  => '高',
				'value' => 9
			]
		];
	}
	
	/**
	 * 获取项目进度趋势
	 */
	public function getProgressTrend($projectId)
	{
		// 模拟数据，实际应该从数据库查询
		return [
			[
				'date'     => '2024-01',
				'progress' => 10
			],
			[
				'date'     => '2024-02',
				'progress' => 25
			],
			[
				'date'     => '2024-03',
				'progress' => 40
			],
			[
				'date'     => '2024-04',
				'progress' => 55
			],
			[
				'date'     => '2024-05',
				'progress' => 70
			],
			[
				'date'     => '2024-06',
				'progress' => 85
			]
		];
	}
	
	/**
	 * 获取成员统计
	 */
	public function getMemberStats($projectId)
	{
		// 模拟数据，实际应该从数据库查询
		return [
			[
				'name'            => '张三',
				'completed_tasks' => 8,
				'total_tasks'     => 10
			],
			[
				'name'            => '李四',
				'completed_tasks' => 6,
				'total_tasks'     => 8
			],
			[
				'name'            => '王五',
				'completed_tasks' => 4,
				'total_tasks'     => 6
			]
		];
	}
	
	/**
	 * 获取最近活动
	 */
	public function getRecentActivities($projectId)
	{
		// 模拟数据，实际应该从数据库查询
		return [
			[
				'user'   => '张三',
				'action' => '完成了任务',
				'target' => '用户界面设计',
				'time'   => '2小时前'
			],
			[
				'user'   => '李四',
				'action' => '创建了任务',
				'target' => '数据库优化',
				'time'   => '4小时前'
			],
			[
				'user'   => '王五',
				'action' => '更新了任务',
				'target' => '代码重构',
				'time'   => '6小时前'
			]
		];
	}
	
	/**
	 * 获取项目成员选项（用于任务执行人选择）
	 *
	 * @param int $projectId 项目ID
	 * @param int $userId    当前用户ID
	 * @return array
	 */
	public function getMemberOptions(int $projectId, int $userId): array
	{
		// 验证项目是否存在
		$project = $this->crudService->getDetail($projectId);
		if (!$project) {
			throw new BusinessException('项目不存在');
		}
		
		// 检查权限：只有项目负责人可以选择执行人，非负责人只能看到自己
		$isOwner = $project->isOwner($userId);
		
		if ($isOwner) {
			// 项目负责人：获取所有项目成员
			$adminIds = (new ProjectMember())->where('project_id', $projectId)
			                                 ->where('deleted_at', null)
			                                 ->column('user_id');
		}
		else {
			// 非项目负责人：只能看到自己
			$adminIds = [$userId];
		}
		
		// 如果没有成员，返回空数组
		if (empty($adminIds)) {
			return [];
		}
		
		// 查询用户信息，返回ApiSelect需要的格式
		return (new AdminModel())->whereIn('id', $adminIds)
		                         ->where('deleted_at', null)
		                         ->field('id as value, real_name as name')
		                         ->select()
		                         ->toArray();
	}
	
	/**
	 * 检查用户是否为项目负责人
	 *
	 * @param int $projectId 项目ID
	 * @param int $userId    用户ID
	 * @return bool
	 */
	public function isProjectOwner(int $projectId, int $userId): bool
	{
		$project = $this->crudService->getDetail($projectId);
		if (!$project) {
			return false;
		}
		
		return $project->isOwner($userId);
	}
	
	/**
	 * 获取项目负责人选项列表
	 *
	 * @return array
	 */
	public function getOwnerOptions(): array
	{
		// 获取所有项目负责人的用户ID（去重）
		$ownerIds = $this->crudService->getModel()
		                              ->where('deleted_at', null)
		                              ->where('owner_id', '>', 0)
		                              ->column('owner_id');
		
		if (empty($ownerIds)) {
			return [];
		}
		
		// 去重
		$ownerIds = array_unique($ownerIds);
		
		// 查询用户信息，返回下拉选项格式
		return (new AdminModel())->whereIn('id', $ownerIds)
		                         ->where('deleted_at', null)
		                         ->field('id, real_name as name')
		                         ->select()
		                         ->toArray();
	}
}