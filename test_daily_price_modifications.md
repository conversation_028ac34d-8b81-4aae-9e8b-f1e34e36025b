# 每日报价单修改测试指南

## 📋 修改内容总结

### 1. 涨跌幅格式优化

#### 后端修改
- **文件**: `app/daily/model/DailyPriceItem.php`
- **新增方法**: `getPriceChangeFormatAttr()`
- **格式**: `+5.20(+2.08%)` 或 `-3.50(-1.25%)` 或 `无变动`

#### 前端修改
- **文件**: `frontend/src/views/daily/daily_price_order/components/PriceItemTable.vue`
- **修改函数**: `formatPriceChangeRate()`
- **新增参数**: 支持传入 `item` 参数计算具体数值
- **列宽调整**: 涨幅列宽度从 100px 调整为 120px

### 2. 重复检测逻辑优化

#### 后端修改
- **文件**: `app/daily/service/DailyPriceOrderService.php`
- **修改方法**: `checkDuplicateDate()`
- **新增参数**: `$scene` (create/submit), `$excludeId`
- **新增方法**: `getDuplicateMessage()`

#### 控制器修改
- **文件**: `app/daily/controller/DailyPriceOrderController.php`
- **修改方法**: `submitApproval()`
- **新增逻辑**: 提交前检测重复

## 🧪 测试用例

### 测试1: 涨跌幅显示格式

#### 测试数据
```sql
-- 昨日价格: 100.00
-- 今日价格: 105.20
-- 预期显示: +5.20(+5.20%)
```

#### 测试步骤
1. 创建昨日已通过的报价单，产品价格 100.00
2. 创建今日报价单，同一产品价格 105.20
3. 查看涨幅列显示格式

#### 预期结果
- 涨价显示: `+5.20(+5.20%)`，红色字体
- 降价显示: `-3.50(-3.50%)`，绿色字体
- 无变动显示: `无变动`，灰色字体

### 测试2: 新增时重复检测

#### 测试场景A: 当天已有草稿
1. 创建 2025-07-27 的草稿报价单
2. 再次尝试创建 2025-07-27 的报价单
3. 预期: 阻止创建，提示"当天已存在草稿状态的报价单，不能重复创建"

#### 测试场景B: 当天已有审批中
1. 创建并提交 2025-07-27 的报价单（状态变为审批中）
2. 尝试创建新的 2025-07-27 报价单
3. 预期: 阻止创建，提示"当天已存在审批中状态的报价单，不能重复创建"

#### 测试场景C: 当天已有已通过
1. 创建、提交并审批通过 2025-07-27 的报价单
2. 尝试创建新的 2025-07-27 报价单
3. 预期: 阻止创建，提示"当天已存在已通过状态的报价单，不能重复创建"

### 测试3: 提交时重复检测

#### 测试场景A: 当天已有审批中
1. 用户A创建并提交 2025-07-27 的报价单（状态: 审批中）
2. 用户B创建 2025-07-27 的草稿报价单
3. 用户B尝试提交草稿
4. 预期: 阻止提交，提示"当天已存在审批中状态的报价单，不能重复提交"

#### 测试场景B: 当天已有已通过
1. 创建、提交并审批通过 2025-07-27 的报价单
2. 创建新的 2025-07-27 草稿报价单
3. 尝试提交草稿
4. 预期: 阻止提交，提示"当天已存在已通过状态的报价单，不能重复提交"

#### 测试场景C: 正常提交
1. 创建 2025-07-28 的草稿报价单（当天无其他报价单）
2. 提交审批
3. 预期: 提交成功，状态变为审批中

## 🔧 调试指南

### 后端调试
```php
// 在 DailyPriceOrderService.php 中添加日志
Log::info('重复检测', [
    'date' => $date,
    'scene' => $scene,
    'excludeId' => $excludeId,
    'existing_order' => $existingOrder?->toArray()
]);
```

### 前端调试
```javascript
// 在 PriceItemTable.vue 中添加控制台输出
console.log('涨幅计算', {
    rate,
    item,
    yesterdayData: yesterdayPrices.value[`${item.supplier_id}-${item.product_id}`],
    result: formatPriceChangeRate(rate, item)
});
```

## 📝 验证清单

- [ ] 涨跌幅显示格式正确（包含数值和百分比）
- [ ] 新增时检测所有状态（0,1,2）
- [ ] 提交时只检测审批中和已通过状态（1,2）
- [ ] 错误提示信息准确
- [ ] 编辑时排除当前记录
- [ ] 前端表格列宽适配新格式
- [ ] 样式类正确应用（涨价红色，降价绿色）

## 🚨 注意事项

1. **数据库状态一致性**: 确保测试前数据库状态正确
2. **缓存清理**: 修改后清理浏览器缓存
3. **权限验证**: 确保测试用户有相应操作权限
4. **时区问题**: 注意日期比较时的时区设置
