<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\core\traits\DataPermissionTrait;
use app\common\core\traits\TenantTrait;
use app\common\exception\BusinessException;
use app\common\exception\LoginFailedException;
use app\common\exception\ValidateFailedException;
use app\common\service\TenantSwitchService;
use app\common\utils\CaptchaUtil;
use app\common\utils\TokenUtil;
use app\system\model\AdminModel;
use app\system\validate\AdminLoginValidate;
use think\exception\ValidateException;

/**
 * 认证服务类
 * 负责用户登录、登出、验证码等认证相关功能
 */
class AuthService extends BaseService
{
	
	use DataPermissionTrait, TenantTrait;
	
	/**
	 * 构造函数
	 */
	protected function __construct()
	{
		$this->model = new AdminModel();
		parent::__construct();
	}
	
	/**
	 * 管理员登录
	 *
	 * @param array $params 登录参数
	 * @return array
	 * @throws \app\common\exception\BusinessException
	 * @throws \app\common\exception\ValidateFailedException
	 */
	public function login(array $params): array
	{
		try {
			validate(AdminLoginValidate::class)->check($params);
		}
		catch (ValidateException $e) {
			throw new ValidateFailedException($e->getMessage());
		}
		
		// 验证码校验（根据配置决定是否启用）
		if (env('CAPTCHA', 1) == 1) {
			$captchaUtil = new CaptchaUtil();
			$code        = $params['captcha'] ?? '';
			$codeKey     = $params['key'] ?? '';

			if (!$captchaUtil->check($code, $codeKey)) {
				throw new ValidateFailedException('验证码错误');
			}
		}
		
		// 获取管理员信息
		$adminInfo = $this->model->setEnableTenantIsolation(false)
		                         ->getAdminByUsername($params['username']);
		if ($adminInfo->isEmpty()) {
			throw new LoginFailedException('用户名或密码错误');
		}
		
		// 验证密码
		if (!$this->checkPassword($params['password'], $adminInfo->getData('password'))) {
			throw new LoginFailedException('用户名或密码错误');
		}
		
		// 检查账号状态
		if ($adminInfo->getData('status') != 1) {
			throw new ValidateFailedException('账号已停用');
		}
		
		// 更新登录信息
		$ip = request()->ip();
		$adminInfo->save([
			'login_at' => date('Y-m-d H:i:s'),
			'login_ip' => $ip,
		]);
		
		// 生成token
		$data = $adminInfo->toArray();
		unset($data['password']);
		unset($data['salt']);
		
		$id         = $adminInfo->getData('id');
		$isRemember = $params['remember_password'] ?? false;
		
		$token = TokenUtil::generate($id, $data, $isRemember
			? 86400
			: 3600);    // todo 后续可添加该参数
		
		// 记录登录日志
		LoginLogService::getInstance()
		               ->create([
			               'admin_id'   => $id,
			               'ip'         => $ip,
			               'location'   => implode(',', getIpTopLocation($ip)),
			               'browser'    => browser_info(),
			               'os'         => os_info(),
			               'creator_id' => $id,
			               'log_type'   => 0
		               ]);
		
		// 返回登录信息
		return [
			'info'  => [
				'username'  => $adminInfo->getData('username'),
				'avatar'    => $adminInfo->getData('avatar'),
				'real_name' => $adminInfo->getData('real_name'),
				'gender'    => $adminInfo->getData('gender'),
				'email'     => $adminInfo->getData('email'),
				'mobile'    => $adminInfo->getData('mobile'),
			],
			'token' => $token,
		];
	}
	
	/**
	 * 管理员登出
	 *
	 * @param string $token 认证令牌
	 * @return bool
	 */
	public function logout(string $token): bool
	{
		$adminInfo = TokenUtil::getTokenInfo($token);
		if (!empty($adminInfo) && !empty($adminInfo['data'])) {
			$ip        = request()->ip();
			$adminData = $adminInfo['data'];
			$id        = $adminData['id'];
			
			// 记录登出日志
			LoginLogService::getInstance()
			               ->create([
				               'admin_id'   => $id,
				               'ip'         => $ip,
				               'location'   => implode(',', getIpTopLocation($ip)),
				               'browser'    => browser_info(),
				               'os'         => os_info(),
				               'creator_id' => $id,
				               'tenant_id'  => $adminData['tenant_id'],
				               'log_type'   => 1
			               ]);
			
			// 清除租户切换状态（如果是系统超级管理员）
			if (is_super_admin()) {
				$tenantSwitchService = app(TenantSwitchService::class);
				$tenantSwitchService->clearUserSwitchState($id);
			}

			// 清理用户数据权限缓存
			\app\common\utils\DataPermissionCacheUtil::clearUserDataPermissionCache($id, $adminData['tenant_id']);

			// 清理用户相关业务缓存
			\app\common\utils\CacheUtil::tag('user:' . $id)->clear();
		}
		// 删除token
		TokenUtil::delete($token);
		return true;
	}
	
	/**
	 * 检查密码是否正确
	 *
	 * @param string $password    输入的密码
	 * @param string $oldPassword 数据库中的密码
	 * @return bool
	 */
	public function checkPassword(string $password, string $oldPassword): bool
	{
		return password_verify($password, $oldPassword);
	}
	
	/**
	 * 生成验证码
	 *
	 * @return array
	 */
	public function generateCaptcha(): array
	{
		$captchaUtil = new CaptchaUtil();
		return $captchaUtil->create();
	}
}