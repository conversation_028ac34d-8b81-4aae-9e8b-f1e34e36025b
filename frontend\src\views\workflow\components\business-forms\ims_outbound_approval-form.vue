<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="ims-shipment-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
        class="shipment-form"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="所在部门" prop="dept_id">
              <DepartmentTreeSelect
                v-model="formData.dept_id"
                placeholder="请选择所在部门"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="出库日期" prop="outbound_date">
              <ElDatePicker
                v-model="formData.outbound_date"
                type="date"
                placeholder="请选择出库日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="接收单位" prop="customer_id">
              <ApiSelect
                v-model="formData.customer_id"
                :api="{ url: '/crm/crm_customer_my/options' }"
                placeholder="请选择接收单位"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="车辆信息" prop="vehicle_info">
              <ElInput
                v-model="formData.vehicle_info"
                placeholder="请输入车辆信息（如：车牌号、司机姓名等）"
                :disabled="!isEditable"
                maxlength="200"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="货款总额(元)" prop="total_amount">
              <ElInputNumber
                v-model="formData.total_amount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="自动计算"
                :disabled="true"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="货款总额大写">
              <ElInput
                :value="convertToChinese(formData.total_amount || 0)"
                placeholder="自动转换"
                :disabled="true"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="图片">
          <FormUploader
            v-model="formData.attachment"
            :disabled="!isEditable"
            :limit="5"
            multiple
            fileType="image"
          />
        </ElFormItem>
      </ElForm>

      <!-- 出库明细 - 独立的卡片区域 -->
      <ElCard class="items-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">出库明细</span>
          </div>
        </template>

        <MobileItemTable
          v-model="formData.items"
          :readonly="!isEditable"
          :item-template="getItemTemplate"
          @change="onItemsChange"
        />
      </ElCard>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import DepartmentTreeSelect from '@/components/custom/DepartmentTreeSelect.vue'
  import ApiSelect from '@/components/core/forms/ApiSelect/index.vue'
  import FormUploader from '@/components/custom/FormUploader/index.vue'
  import MobileItemTable from '@/components/business/MobileItemTable.vue'
  import { convertToChineseNumber as convertToChinese, safeAdd, safeMultiply } from '@/utils/number'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface ImsOutboundFormData {
    id?: number
    dept_id: number | null
    outbound_date: string
    customer_id: number | null
    items: any[]
    total_amount: number
    remark: string
    attachment: any[]
    approval_status?: number
    workflow_instance_id?: number
    vehicle_info: string
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 组件实例检查 ====================

  /** 当前组件实例 */
  const instance = getCurrentInstance()

  /** 组件是否已挂载 */
  const isMounted = ref(false)

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `出库申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起出库申请'
  })

  /** 表单数据 */
  const formData = reactive<ImsOutboundFormData & any>({
    dept_id: null,
    outbound_date: '',
    customer_id: null,
    vehicle_info: '', // 车辆信息
    items: [],
    total_amount: 0,
    remark: '',
    attachment: [],
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  // 选项数据
  const warehouseOptions = ref<any[]>([])
  const customerOptions = ref<any[]>([])
  const deptOptions = ref<any[]>([])
  const productOptions = ref<any[]>([])

  // 计算总数量和总金额
  const totalQuantity = computed(() => {
    return formData.items.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0)
  })

  const totalAmount = computed(() => {
    return formData.items.reduce((sum: number, item: any) => sum + (item.total_amount || 0), 0)
  })

  // 监听总金额变化，自动更新表单数据
  watch(totalAmount, (newValue) => {
    formData.total_amount = newValue
  })

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    // outbound_no: [{ required: true, message: '请输入出库单号', trigger: 'blur' }],
    outbound_type: [{ required: true, message: '请选择出库类型', trigger: 'change' }],
    outbound_date: [{ required: true, message: '请选择出库日期', trigger: 'change' }],
    // warehouse_id: [{ required: true, message: '请选择出库仓库', trigger: 'change' }],
    dept_id: [{ required: true, message: '请选择所在部门', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选择接收单位', trigger: 'change' }],
    vehicle_info: [
      { required: true, message: '请输入车辆信息', trigger: 'blur' },
      { min: 2, message: '车辆信息长度不能少于2个字符', trigger: 'blur' }
    ]
  }

  // ==================== 方法定义 ====================

  /**
   * 添加明细项
   */
  const addItem = () => {
    formData.items.push({
      product_id: null,
      product_name: '',
      quantity: 0,
      unit_price: 0,
      total_amount: 0
    })
  }

  /**
   * 删除明细项
   */
  const removeItem = (index: number) => {
    formData.items.splice(index, 1)
  }

  /**
   * 计算明细小计
   */
  const calculateItemTotal = (item: any) => {
    item.total_amount = (item.quantity || 0) * (item.unit_price || 0)
  }

  /**
   * 产品选择变化
   */
  const onProductChange = (item: any) => {
    const product = productOptions.value.find((p) => p.value === item.product_id)
    if (product) {
      item.product_name = product.label
      item.unit_price = product.price || 0
      calculateItemTotal(item)
    }
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('ims_outbound_approval-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      outbound_no: '',
      outbound_type: null,
      outbound_date: '',
      warehouse_id: null,
      customer_id: null,
      dept_id: null,
      vehicle_info: '', // 车辆信息
      remark: '',
      items: [],
      total_quantity: 0,
      total_amount: 0,
      attachment: [], // 清空图片附件
      approval_status: 0,
      workflow_instance_id: 0
    })
    // 清除表单验证状态
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        // 确保数字字段的类型正确
        if (formData.total_amount !== null && formData.total_amount !== undefined) {
          formData.total_amount = Number(formData.total_amount) || 0
        }

        // 确保items是数组
        if (!Array.isArray(formData.items)) {
          formData.items = []
        }

        console.log('出库申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('ims_outbound_approval-form setFormData called with:', data)
    Object.assign(formData, data)

    // 确保数字字段的类型正确
    if (formData.total_amount !== null && formData.total_amount !== undefined) {
      formData.total_amount = Number(formData.total_amount) || 0
    }

    if (!Array.isArray(formData.items)) {
      formData.items = []
    }
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('ims_outbound_approval-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: ImsOutboundFormData = {
        dept_id: formData.dept_id,
        outbound_date: formData.outbound_date,
        customer_id: formData.customer_id,
        items: formData.items,
        total_amount: formData.total_amount,
        remark: formData.remark,
        attachment: formData.attachment,
        vehicle_info: formData.vehicle_info
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('出库申请保存数据:', submitData)
      emit('save', submitData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      if (formData.items.length === 0) {
        ElMessage.warning('请至少添加一条出库明细')
        return
      }

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: ImsOutboundFormData = {
        dept_id: formData.dept_id,
        outbound_date: formData.outbound_date,
        customer_id: formData.customer_id,
        items: formData.items,
        total_amount: formData.total_amount,
        remark: formData.remark,
        attachment: formData.attachment,
        vehicle_info: formData.vehicle_info
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('出库申请提交数据:', submitData)
      emit('submit', submitData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  // 明细表格相关方法
  const getItemTemplate = () => ({
    supplier_id: null,
    product_id: null,
    product_name: '',
    product_unit: '', // 产品单位
    quantity: 0,
    unit_price: 0,
    total_amount: 0
  })

  const onItemsChange = (items: any[]) => {
    formData.items = items
    // 重新计算总计
    calculateTotals()
  }

  const calculateTotals = () => {
    formData.total_quantity = totalQuantity.value
    formData.total_amount = totalAmount.value
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 监听总数量和总金额变化
  watch([totalQuantity, totalAmount], () => {
    formData.total_quantity = totalQuantity.value
    formData.total_amount = totalAmount.value
  })

  // ==================== 生命周期 ====================

  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      if (instance) {
        isMounted.value = true
        console.log('出库表单组件已完全挂载')
      }
    })
  })

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }

  .ims-shipment-dialog {
    // 参考请假表单的对话框样式
    :deep(.el-dialog) {
      margin-top: 5vh !important;
      margin-bottom: 5vh !important;
      display: flex;
      flex-direction: column;
      max-height: 90vh;
    }

    :deep(.el-dialog__body) {
      overflow: auto;
      padding: 20px;
      max-height: 65vh;
    }

    .dialog-content {
      max-height: 60vh;
      padding: 10px 30px;
      overflow-y: auto;
      border-bottom: 1px solid #eaeaea;
    }

    .shipment-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      .el-textarea {
        .el-textarea__inner {
          resize: vertical;
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 16px;

      .el-button {
        min-width: 100px;
      }
    }
  }

  // 卡片样式
  .items-card {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .total-summary {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ims-shipment-dialog {
      .dialog-footer {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
</style>
