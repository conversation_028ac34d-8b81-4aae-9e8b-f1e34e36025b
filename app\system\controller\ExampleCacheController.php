<?php
declare(strict_types=1);

namespace app\system\controller;

use app\BaseController;
use app\common\utils\CacheUtil;
use think\Response;

/**
 * 缓存工具类示例控制器
 */
class ExampleCacheController extends BaseController
{
    /**
     * 成功响应
     * 
     * @param string $msg 提示信息
     * @param array $data 响应数据
     * @param int $code 响应码
     * @return Response
     */
    protected function success(string $msg = 'success', array $data = [], int $code = 200): Response
    {
        return Response::create([
            'code' => $code,
            'msg'  => $msg,
            'data' => $data
        ], 'json');
    }
    
    /**
     * 失败响应
     * 
     * @param string $msg 提示信息
     * @param array $data 响应数据
     * @param int $code 响应码
     * @return Response
     */
    protected function error(string $msg = 'error', array $data = [], int $code = 400): Response
    {
        return Response::create([
            'code' => $code,
            'msg'  => $msg,
            'data' => $data
        ], 'json');
    }
    
    /**
     * 测试设置缓存
     */
    public function setCache(): Response
    {
        // 设置租户缓存（默认过期时间1小时）
        CacheUtil::set('example:cache:key', ['name' => '测试数据', 'time' => time()]);
        
        // 设置租户缓存（指定过期时间为5分钟）
        CacheUtil::set('example:cache:short', '短期缓存数据', 300);
        
        // 设置总后台缓存
        CacheUtil::set('example:admin:cache', ['admin' => true, 'time' => time()], null, false);
        
        return $this->success('设置缓存成功');
    }
    
    /**
     * 测试获取缓存
     */
    public function getCache(): Response
    {
        // 获取租户缓存
        $data1 = CacheUtil::get('example:cache:key', []);
        
        // 获取短期缓存
        $data2 = CacheUtil::get('example:cache:short', '');
        
        // 获取总后台缓存
        $data3 = CacheUtil::get('example:admin:cache', [], false);
        
        return $this->success('获取缓存成功', [
            'tenant_cache' => $data1,
            'short_cache' => $data2,
            'admin_cache' => $data3
        ]);
    }
    
    /**
     * 测试删除缓存
     */
    public function deleteCache(): Response
    {
        // 删除租户缓存
        $result1 = CacheUtil::delete('example:cache:key');
        
        // 删除短期缓存
        $result2 = CacheUtil::delete('example:cache:short');
        
        // 删除总后台缓存
        $result3 = CacheUtil::delete('example:admin:cache', false);
        
        return $this->success('删除缓存成功', [
            'tenant_result' => $result1,
            'short_result' => $result2,
            'admin_result' => $result3
        ]);
    }
    
    
    /**
     * 测试清除所有租户缓存
     */
    public function clearTenantCache(): Response
    {
        $result = CacheUtil::clearTenantCache();
        
        return $this->success('清除租户缓存' . ($result ? '成功' : '失败'));
    }
    
    /**
     * 测试清除所有总后台缓存
     */
    public function clearAdminCache(): Response
    {
        $result = CacheUtil::clearAdminCache();
        
        return $this->success('清除总后台缓存' . ($result ? '成功' : '失败'));
    }
    
    /**
     * 测试使用缓存标签
     */
    public function tagCache(): Response
    {
        // 使用标签设置多个缓存
        CacheUtil::tag('example')->set('tag:item:1', '标签缓存项1');
        CacheUtil::tag('example')->set('tag:item:2', '标签缓存项2');
        
        // 获取标签下的缓存
        $data1 = CacheUtil::get('tag:item:1');
        $data2 = CacheUtil::get('tag:item:2');
        
        return $this->success('标签缓存设置成功', [
            'item1' => $data1,
            'item2' => $data2
        ]);
    }
    
    /**
     * 测试清除标签缓存
     */
    public function clearTagCache(): Response
    {
        // 清除标签下的所有缓存
        CacheUtil::tag('example')->clear();
        
        // 验证清除结果
        $data1 = CacheUtil::get('tag:item:1');
        $data2 = CacheUtil::get('tag:item:2');
        
        return $this->success('清除标签缓存成功', [
            'item1' => $data1,
            'item2' => $data2
        ]);
    }
    
    /**
     * 测试缓存防穿透
     */
    public function testRemember(): Response
    {
        // 使用remember方法
        $data = CacheUtil::remember('example:remember', function() {
            // 模拟从数据库获取数据
            return [
                'id' => 1,
                'name' => '动态生成的数据',
                'time' => date('Y-m-d H:i:s')
            ];
        }, 300);
        
        return $this->success('防穿透缓存测试', [
            'data' => $data
        ]);
    }
} 