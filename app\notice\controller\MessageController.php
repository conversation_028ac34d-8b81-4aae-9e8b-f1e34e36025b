<?php
declare(strict_types=1);

namespace app\notice\controller;

use app\common\core\base\BaseController;
use app\notice\service\NoticeMessageService;
use think\response\Json;

/**
 * 消息记录表控制器
 */
class MessageController extends BaseController
{
	
	/**
	 * 服务实例
	 *
	 * @var NoticeMessageService
	 */
	protected NoticeMessageService $service;
	
	/**
	 * 初始化
	 */
	protected function initialize(): void
	{
		parent::initialize();
		$this->service = NoticeMessageService::getInstance();
	}
	
	/**
	 * 发送消息
	 *
	 * @return Json
	 */
	public function send(): Json
	{
		$params = $this->request->param();
		
		// 检查必填参数
		if (empty($params['template_code'])) {
			return $this->error('模板编码不能为空');
		}
		
		if (empty($params['recipients']) || !is_array($params['recipients'])) {
			return $this->error('接收人不能为空且必须为数组');
		}
		
		$templateCode = $params['template_code'];
		$variables    = $params['variables'] ?? [];
		$recipients   = $params['recipients'];
		$options      = $params['options'] ?? [];
		
		// 添加当前用户ID作为创建者
		if (!isset($options['creator_id']) && $this->request->adminId) {
			$options['creator_id'] = $this->request->adminId;
		}
		
		// 发送消息
		$result = $this->service->send($templateCode, $variables, $recipients, $options);
		
		if (!$result) {
			return $this->error('发送失败');
		}
		
		return $this->success('发送成功', ['message_id' => $result]);
	}
	
	/**
	 * 获取用户消息详情
	 *
	 * @param $id
	 * @return Json
	 */
	public function detail($id): Json
	{
		return $this->success('获取成功', $this->service->getMessageDetail($id));
	}
	
	/**
	 * 获取用户未读消息数量
	 *
	 * @return Json
	 */
	public function unreadCount(): Json
	{
		$count = $this->service->getUnreadCount($this->request->adminId);
		return $this->success('获取成功', ['count' => $count]);
	}
	
	/**
	 * 获取用户消息列表
	 *
	 * @return Json
	 */
	public function list(): Json
	{
		$params  = $this->request->param();
		$filters = [
			'read_status' => $params['read_status'] ?? '',
			'type'        => $params['type'] ?? '',
			'module_code' => $params['module_code'] ?? '',
			'keyword'     => $params['keyword'] ?? '',
			'start_time'  => $params['start_time'] ?? '',
			'end_time'    => $params['end_time'] ?? ''
		];
		
		$pagination = [
			'page'      => intval($params['page']) ?? 1,
			'page_size' => intval($params['page_size']) ?? 15
		];
		
		$result = $this->service->getUserMessages($this->request->adminId, $filters, $pagination);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 标记消息为已读
	 *
	 * @return Json
	 */
	public function read(int $id): Json
	{
		if (!$id) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->markAsRead($id);
		return $result
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	/**
	 * 批量标记消息为已读
	 *
	 * @return Json
	 */
	public function batchRead(): Json
	{
		
		$messageIds = $this->request->param('message_ids');
		if (empty($messageIds) || !is_array($messageIds)) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->batchMarkAsRead($messageIds, $this->request->adminId);
		return $result
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	/**
	 * 标记全部消息为已读
	 *
	 * @return Json
	 */
	public function allRead(): Json
	{
		$result = $this->service->markAllAsRead($this->request->adminId);
		return $result
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	/**
	 * 删除消息
	 *
	 * @return Json
	 */
	public function delete(): Json
	{
		$messageId = $this->request->param('message_id', 0, 'intval');
		if (!$messageId) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->deleteUserMessage($messageId, $this->request->adminId);
		return $result
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * 批量删除消息
	 *
	 * @return Json
	 */
	public function batchDelete(): Json
	{
		
		$messageIds = $this->request->param('message_ids');
		if (empty($messageIds) || !is_array($messageIds)) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->batchDeleteUserMessages($messageIds, $this->request->adminId);
		return $result
			? $this->success('批量删除消息成功')
			: $this->error('批量删除失败');
	}
} 