<?php
declare(strict_types=1);

namespace app\ims\model;

use app\common\core\base\BaseModel;

/**
 * 采购明细表模型
 */
class ImsPurchaseItem extends BaseModel
{
    // 设置表名
    protected $name = 'ims_purchase_item';
    
    // 设置主键
    protected $pk = 'id';
    
    // 字段类型转换
    protected $type = [
        'purchase_id' => 'integer',
        'product_id' => 'integer',
        'quantity' => 'float',
        'unit_price' => 'float',
        'total_amount' => 'float',
        'urgent_level' => 'integer',
    ];
    
    // 紧急程度常量
    const URGENT_NORMAL = 1;  // 一般
    const URGENT_HIGH = 2;    // 紧急
    const URGENT_SUPER = 3;   // 特急
    
    /**
     * 获取默认搜索字段
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'purchase_id' => ['type' => 'eq'],
            'product_id' => ['type' => 'eq'],
            'quantity' => ['type' => 'between'],
            'unit_price' => ['type' => 'between'],
            'total_amount' => ['type' => 'between'],
            'urgent_level' => ['type' => 'eq'],
            'created_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 关联采购申请
     */
    public function purchase()
    {
        return $this->belongsTo(ImsPurchaseApproval::class, 'purchase_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo(\app\ims\model\ImsProduct::class, 'product_id', 'id')->bind([
            'product_name' => 'name',
            'product_code' => 'code',
            'product_unit' => 'unit'
        ]);
    }
    
    /**
     * 获取紧急程度文本
     */
    public function getUrgentLevelTextAttr($value, $data)
    {
        $levels = [
            self::URGENT_NORMAL => '一般',
            self::URGENT_HIGH => '紧急',
            self::URGENT_SUPER => '特急',
        ];
        
        return $levels[$data['urgent_level']] ?? '未知';
    }
}
