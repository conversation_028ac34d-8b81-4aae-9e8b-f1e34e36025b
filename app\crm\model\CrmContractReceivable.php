<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\workflow\model\WorkflowInstance;
use think\model\relation\BelongsTo;
use app\system\model\AdminModel;

/**
 * 回款记录表模型
 */
class CrmContractReceivable extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_contract_receivable';
	
	protected $append = [
		'contract',
		'creator',
		'customer',
		'owner'
	];
	
	public function getVoucherFilesAttr($value, $data)
	{
		return empty($value)
			? []
			: explode(',', $value);
	}
	
	// 关联负责人
	public function owner(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'owner_user_id', 'id')
		            ->bind([
			            'owner_name' => 'real_name'
		            ]);
	}
	
	// 关联客户信息
	public function customer(): BelongsTo
	{
		return $this->belongsTo(CrmCustomer::class, 'customer_id', 'id')
		            ->bind([
			            'customer_number',
			            'customer_name',
		            ]);
	}
	
	// 关联合同信息
	public function contract(): BelongsTo
	{
		return $this->belongsTo(CrmContract::class, 'contract_id', 'id')
		            ->bind([
			            'contract_number',
			            'contract_name',
			            'contract_amount',
		            ]);
	}
	
	// 关联工作流实例
	public function workflow(): BelongsTo
	{
		return $this->belongsTo(WorkflowInstance::class, 'workflow_instance_id', 'id')
		            ->bind([
			            'workflow_status' => 'status',
			            'workflow_title'  => 'title'
		            ]);
	}
	
	// 获取收款状态文本（基于workflow_instance状态）
	public function getStatusTextAttr($value, $data)
	{
		// 如果有工作流实例，使用工作流状态
		if (isset($data['workflow_status'])) {
			$statusMap = [
				0 => '已保存(草稿)',
				1 => '审批中',
				2 => '已通过',
				3 => '已驳回',
				4 => '已终止',
				5 => '已撤回'
			];
			return $statusMap[$data['workflow_status']] ?? '已保存(草稿)';
		}
		return $statusMap[$data['status']] ?? '待审核';
	}
	
	/**
	 * 获取业务代码（WorkflowableModel抽象方法实现）
	 */
	public function getBusinessCode(): string
	{
		return 'crm_receivable';
	}
	
	/**
	 * 获取审批标题（WorkflowableModel抽象方法实现）
	 */
	public function getApprovalTitle(): string
	{
		return '回款审批-' . $this->contract_name . '-' . $this->receivable_money . '元';
	}
	
	public function getDefaultSearchFields(): array
	{
		return [
			'approval_status'   => ['type' => 'eq'],
			'payment_method'    => ['type' => 'eq'],
			'receivable_number' => ['type' => 'like'],
			'received_date'     => ['type' => 'eq'],
		];
	}
}