<?php
declare(strict_types=1);

namespace app\office\model;

use app\common\core\base\BaseModel;
use app\workflow\constants\WorkflowStatusConstant;

/**
 * 办公采购申请表模型
 */
class OfficeProcurement extends BaseModel
{
	// 设置表名
	protected $name = 'office_procurement';
	
	// 字段类型转换
	protected $type = [
		'workflow_instance_id' => 'integer',
		'approval_status'      => 'integer',
		'submitter_id'         => 'integer',
		'creator_id'           => 'integer',
		'procurement_type'     => 'integer',
		'unit_price'           => 'float',
		'quantity'             => 'integer',
		'payment_amount'       => 'float',
		'payment_method'       => 'integer',
	];
	
	public function getAttachmentAttr($value)
	{
		return !empty($value)
			? explode(',', $value)
			: [];
	}
	
	// 审批状态常量
	const STATUS_DRAFT      = WorkflowStatusConstant::STATUS_DRAFT;
	const STATUS_PROCESSING = WorkflowStatusConstant::STATUS_PROCESSING;
	const STATUS_COMPLETED  = WorkflowStatusConstant::STATUS_COMPLETED;
	const STATUS_REJECTED   = WorkflowStatusConstant::STATUS_REJECTED;
	const STATUS_TERMINATED = WorkflowStatusConstant::STATUS_TERMINATED;
	const STATUS_RECALLED   = WorkflowStatusConstant::STATUS_RECALLED;
	const STATUS_VOID       = WorkflowStatusConstant::STATUS_VOID;
	
	// 采购类型常量
	const PROCUREMENT_TYPE_OFFICE_SUPPLIES = 1;           // 办公用品
	const PROCUREMENT_TYPE_EQUIPMENT       = 2;           // 生产原料
	const PROCUREMENT_TYPE_OTHER           = 3;           // 其他
	
	// 支付方式常量
	const PAYMENT_METHOD_BANK_TRANSFER = 1;               // 银行转账
	const PAYMENT_METHOD_CASH          = 2;               // 现金支付
	const PAYMENT_METHOD_CHECK         = 3;               // 支票
	const PAYMENT_METHOD_ALIPAY        = 4;               // 支付宝
	const PAYMENT_METHOD_WECHAT        = 5;               // 微信
	const PAYMENT_METHOD_OTHER         = 6;               // 其他
	
	/**
	 * 获取默认搜索字段
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'approval_status'  => ['type' => 'eq'],
			'procurement_type' => ['type' => 'eq'],
			'item_name'        => ['type' => 'like'],
			'supplier_name'    => ['type' => 'like'],
			'payee_name'       => ['type' => 'like'],
			'payment_method'   => ['type' => 'eq'],
			'delivery_date'    => ['type' => 'date'],
			'payment_amount'   => ['type' => 'between'],
			'submit_time'      => ['type' => 'datetime'],
			'approval_time'    => ['type' => 'datetime'],
			'created_at'       => ['type' => 'date'],
		];
	}
	
	/**
	 * 关联提交人
	 */
	public function submitter()
	{
		return $this->belongsTo(\app\system\model\SystemAdmin::class, 'submitter_id', 'id')
		            ->bind([
			            'submitter_name' => 'username'
		            ]);
	}
	
	/**
	 * 关联创建人
	 */
	public function creator()
	{
		return $this->belongsTo(\app\system\model\SystemAdmin::class, 'creator_id', 'id')
		            ->bind([
			            'creator_name' => 'username'
		            ]);
	}
	
	/**
	 * 获取审批状态文本
	 */
	public function getApprovalStatusTextAttr($value, $data)
	{
		$statuses = [
			self::STATUS_DRAFT      => '草稿',
			self::STATUS_PROCESSING => '审批中',
			self::STATUS_COMPLETED  => '已通过',
			self::STATUS_REJECTED   => '已拒绝',
			self::STATUS_TERMINATED => '已终止',
			self::STATUS_RECALLED   => '已撤回',
			self::STATUS_VOID       => '已作废',
		];
		
		return $statuses[$data['approval_status']] ?? '未知';
	}
	
	/**
	 * 获取采购类型文本
	 */
	public function getProcurementTypeTextAttr($value, $data)
	{
		$types = [
			self::PROCUREMENT_TYPE_OFFICE_SUPPLIES => '办公用品',
			self::PROCUREMENT_TYPE_EQUIPMENT       => '设备采购',
			self::PROCUREMENT_TYPE_SERVICE         => '服务采购',
			self::PROCUREMENT_TYPE_OTHER           => '其他',
		];
		
		return $types[$data['procurement_type']] ?? '';
	}
	
	/**
	 * 获取支付方式文本
	 */
	public function getPaymentMethodTextAttr($value, $data)
	{
		$methods = [
			self::PAYMENT_METHOD_BANK_TRANSFER => '银行转账',
			self::PAYMENT_METHOD_CASH          => '现金支付',
			self::PAYMENT_METHOD_CHECK         => '支票',
			self::PAYMENT_METHOD_ALIPAY        => '支付宝',
			self::PAYMENT_METHOD_WECHAT        => '微信',
			self::PAYMENT_METHOD_OTHER         => '其他',
		];
		
		return $methods[$data['payment_method']] ?? '未知';
	}
}
