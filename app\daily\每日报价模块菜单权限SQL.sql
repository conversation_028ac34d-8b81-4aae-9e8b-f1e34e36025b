-- =====================================================
-- 每日报价模块菜单与按钮权限SQL语句
-- 创建时间：2025-07-22
-- 说明：为每日报价单审批系统创建完整的菜单结构和权限控制
-- =====================================================

-- 1. 创建每日报价菜单（直接作为菜单项，不需要目录）
INSERT INTO `system_menu` (
    `id`, `parent_id`, `title`, `name`, `path`, `component`,
    `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`,
    `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`
) VALUES (
    2600, 0, '每日报价', 'daily:daily_price_order:index', '/daily_price_order', '/daily/daily_price_order/list',
    1, 'icon-price', 250, 0, 1, 1,
    1, '每日报价单管理', 1, NOW(), NOW(), NULL
);

-- =====================================================
-- 每日报价按钮权限（parent_id = 2600）
-- =====================================================

-- 基础CRUD权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2601, 2600, '新增', 'daily:daily_price_order:add', '', '', 2, '', 1, 0, 0, 1, 1, '新增报价单', 1, NOW(), NOW(), NULL),
(2602, 2600, '编辑', 'daily:daily_price_order:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑报价单', 1, NOW(), NOW(), NULL),
(2603, 2600, '删除', 'daily:daily_price_order:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除报价单', 1, NOW(), NOW(), NULL),
(2604, 2600, '详情', 'daily:daily_price_order:detail', '', '', 2, '', 4, 0, 0, 1, 1, '查看报价单详情', 1, NOW(), NOW(), NULL),
(2605, 2600, '导出', 'daily:daily_price_order:export', '', '', 2, '', 5, 0, 0, 1, 1, '导出报价单', 1, NOW(), NOW(), NULL),
(2606, 2600, '导入', 'daily:daily_price_order:import', '', '', 2, '', 6, 0, 0, 1, 1, '导入报价单', 1, NOW(), NOW(), NULL);

-- 审批相关权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2607, 2600, '提交审批', 'daily:daily_price_order:submit_approval', '', '', 2, '', 10, 0, 0, 1, 1, '提交报价单审批', 1, NOW(), NOW(), NULL),
(2608, 2600, '撤回审批', 'daily:daily_price_order:withdraw_approval', '', '', 2, '', 11, 0, 0, 1, 1, '撤回报价单审批', 1, NOW(), NOW(), NULL),
(2609, 2600, '作废报价单', 'daily:daily_price_order:void', '', '', 2, '', 12, 0, 0, 1, 1, '作废报价单', 1, NOW(), NOW(), NULL);

-- 业务操作权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2610, 2600, '保存明细', 'daily:daily_price_order:save_items', '', '', 2, '', 20, 0, 0, 1, 1, '保存报价明细', 1, NOW(), NOW(), NULL),
(2611, 2600, '从昨日复制', 'daily:daily_price_order:copy_yesterday', '', '', 2, '', 21, 0, 0, 1, 1, '从昨日复制报价', 1, NOW(), NOW(), NULL),
(2612, 2600, '获取统计', 'daily:daily_price_order:statistics', '', '', 2, '', 22, 0, 0, 1, 1, '获取统计数据', 1, NOW(), NOW(), NULL);

-- =====================================================
-- 辅助查询权限（用于下拉选择等）
-- =====================================================

INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2620, 2600, '获取供应商列表', 'daily:daily_price_order:get_suppliers', '', '', 2, '', 30, 0, 0, 1, 1, '获取供应商下拉列表', 1, NOW(), NOW(), NULL),
(2621, 2600, '获取产品列表', 'daily:daily_price_order:get_products', '', '', 2, '', 31, 0, 0, 1, 1, '获取产品下拉列表', 1, NOW(), NOW(), NULL),
(2622, 2600, '获取价格历史', 'daily:daily_price_order:get_price_history', '', '', 2, '', 32, 0, 0, 1, 1, '获取产品价格历史', 1, NOW(), NOW(), NULL);

-- =====================================================
-- 管理员角色权限分配（可选）
-- 为超级管理员角色分配所有权限
-- =====================================================

-- 获取超级管理员角色ID（通常为1）
-- INSERT INTO `system_role_menu` (`role_id`, `menu_id`)
-- SELECT 1, id FROM `system_menu` WHERE id BETWEEN 2600 AND 2622;

-- =====================================================
-- 菜单权限说明
-- =====================================================

/*
菜单结构：
├── 每日报价 (2600) - 菜单项
    ├── 新增 (2601) - 按钮权限
    ├── 编辑 (2602) - 按钮权限
    ├── 删除 (2603) - 按钮权限
    ├── 详情 (2604) - 按钮权限
    ├── 导出 (2605) - 按钮权限
    ├── 导入 (2606) - 按钮权限
    ├── 提交审批 (2607) - 按钮权限
    ├── 撤回审批 (2608) - 按钮权限
    ├── 作废报价单 (2609) - 按钮权限
    ├── 保存明细 (2610) - 按钮权限
    ├── 从昨日复制 (2611) - 按钮权限
    ├── 获取统计 (2612) - 按钮权限
    ├── 获取供应商列表 (2620) - 按钮权限
    ├── 获取产品列表 (2621) - 按钮权限
    └── 获取价格历史 (2622) - 按钮权限

权限说明：
- type=1：菜单项（显示在左侧菜单）
- type=2：按钮权限（控制页面内操作按钮）
- sort：排序，数字越小越靠前
- status=1：启用，status=0：禁用
- visible=1：显示，visible=0：隐藏
- external=0：内部链接，external=1：外部链接
- keep_alive=1：缓存页面，keep_alive=0：不缓存

使用方法：
1. 执行此SQL文件创建菜单和权限
2. 在角色管理中为相应角色分配权限
3. 前端页面使用权限标识控制按钮显示
4. 后端接口使用权限标识进行权限验证
*/
