<template>
  <ElSelect
    v-model="selectedValue"
    :placeholder="placeholder"
    :loading="loading"
    :disabled="disabled"
    :clearable="clearable"
    :filterable="filterable"
    :size="size"
    :style="style"
    @change="handleChange"
    @clear="handleClear"
  >
    <ElOption
      v-for="supplier in suppliers"
      :key="supplier.value"
      :label="supplier.label"
      :value="supplier.value"
    >
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span>{{ supplier.label }}</span>
        <span v-if="supplier.code" style="color: #909399; font-size: 12px">
          {{ supplier.code }}
        </span>
      </div>
    </ElOption>
  </ElSelect>
</template>

<script setup lang="ts">
  import { ElSelect, ElOption } from 'element-plus'
  import { ImsSupplierApi } from '@/api/ims/imsSupplier'

  // 组件属性定义
  interface Props {
    modelValue?: number | null
    placeholder?: string
    disabled?: boolean
    clearable?: boolean
    filterable?: boolean
    size?: 'large' | 'default' | 'small'
    style?: string | object
    autoLoad?: boolean
  }

  // 组件事件定义
  interface Emits {
    (e: 'update:modelValue', value: number | null): void

    (e: 'change', value: number | null, supplier?: any): void

    (e: 'clear'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择供应商',
    disabled: false,
    clearable: true,
    filterable: true,
    size: 'default',
    autoLoad: true
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const suppliers = ref<any[]>([])
  const loading = ref(false)

  // 计算属性
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 方法
  const loadSuppliers = async () => {
    if (loading.value) return

    try {
      loading.value = true
      const response = await ImsSupplierApi.options()

      if (response.code === 1 && Array.isArray(response.data)) {
        suppliers.value = response.data.map((item: any) => ({
          label: item.label || item.name,
          value: item.value || item.id,
          code: item.code,
          ...item
        }))
      } else {
        suppliers.value = []
      }
    } catch (error) {
      console.error('加载供应商选项失败:', error)
      suppliers.value = []
    } finally {
      loading.value = false
    }
  }

  const handleChange = (value: number | null) => {
    const supplier = suppliers.value.find((s) => s.value === value)
    emit('change', value, supplier)
  }

  const handleClear = () => {
    emit('clear')
  }

  // 获取供应商信息
  const getSupplier = (supplierId: number | null) => {
    if (!supplierId) return null
    return suppliers.value.find((s) => s.value === supplierId)
  }

  const getSupplierName = (supplierId: number | null) => {
    const supplier = getSupplier(supplierId)
    return supplier?.label || '-'
  }

  // 刷新数据
  const refresh = () => {
    loadSuppliers()
  }

  // 监听modelValue变化
  watch(
    () => props.modelValue,
    (newValue) => {
      // 如果有值但供应商列表为空，则加载数据
      if (newValue && suppliers.value.length === 0) {
        loadSuppliers()
      }
    }
  )

  // 生命周期
  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      if (props.autoLoad) {
        loadSuppliers()
      }
    })
  })

  // 暴露方法给父组件
  defineExpose({
    loadSuppliers,
    refresh,
    getSupplier,
    getSupplierName,
    suppliers: computed(() => suppliers.value),
    loading: computed(() => loading.value)
  })
</script>

<style scoped>
  /* 可以添加自定义样式 */
</style>
