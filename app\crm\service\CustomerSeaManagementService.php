<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\crm\model\CrmCustomer;
use app\crm\model\CrmSeaRule;
use app\system\model\AdminModel;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use think\Model;

/**
 * 客户公海管理业务服务类
 * 负责处理客户公海池的管理逻辑
 */
class CustomerSeaManagementService extends BaseService
{
	
	/**
	 * 构造函数
	 */
	protected function __construct()
	{
		$this->model = new CrmCustomer();
		parent::__construct();
	}
	
	/**
	 * 客户认领
	 *
	 * @param int      $customerId 客户ID
	 * @param int|null $ownerId    认领人ID (可选，默认当前用户)
	 * @return bool 认领结果
	 */
	public function claimCustomer(int $customerId, int $ownerId = null): bool
	{
		// 开启数据库事务
		Db::startTrans();
		
		try {
			$ownerId = $ownerId
				?: get_user_id();
			
			$info = $this->crudService->getOne([
				'id' => $customerId
			]);
			
			// 1. 验证客户状态
			$this->validateCustomerForClaim($info);
			
			// 2. 验证认领权限和限制
			$this->validateClaimPermission($ownerId);
			
			// 3. 执行认领操作
			$info->saveByupdate([
				'owner_user_id' => $ownerId,
				'in_sea'        => 0,
				'updated_id'    => get_user_id(),
			]);
			
			// 4. 记录认领历史
			$this->recordClaimHistory($customerId, $ownerId);
			
			
			// 5. 发送认领通知
			$this->sendClaimNotification($info->toArray(), $ownerId);
			
			
			// 提交事务
			Db::commit();
			
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('客户认领失败：' . $e->getMessage());
		}
		
		return true;
	}
	
	
	/**
	 * 分配客户
	 *
	 * @param int $customerId  客户ID
	 * @param int $ownerUserId 分配的用户ID
	 * @return bool 认领结果
	 */
	public function assignCustomer(int $customerId, int $ownerUserId): bool
	{
		$seaCustomer = $this->crudService->getOne([
			'id' => $customerId
		]);
		
		if ($seaCustomer->getData('status') != 1) {
			throw new BusinessException('客户已停用');
		}
		
		$this->validateCustomerLockStatus($seaCustomer);
		
		$ownerInfo = AdminModel::where('id', $ownerUserId)
		                       ->findOrEmpty();
		if ($ownerInfo->isEmpty()) {
			throw new BusinessException('用户不存在');
		}
		if ($ownerInfo->getData('status') != 1) {
			throw new BusinessException('用户已禁用');
		}
		return $seaCustomer->saveByupdate([
			'owner_user_id' => $ownerUserId,
			'updated_id'    => get_user_id(),
			'in_sea'        => 0,
			'into_sea_time' => null
		]);
	}
	
	/**
	 * 客户回收到公海
	 *
	 * @param int    $customerId 客户ID
	 * @param string $reason     回收原因
	 * @return bool 回收结果
	 * @throws BusinessException
	 */
	public function recycleCustomer(int $customerId, string $reason = ''): bool
	{
		Db::startTrans();
		
		try {
			
			$info = $this->crudService->getOne([
				'id' => $customerId
			]);
			
			// 1. 验证客户状态
			$this->validateCustomerForRecycle($info);
			
			// 2. 执行回收操作
			$userId = get_user_id();
			$info->save([
				'owner_user_id'   => 0,
				'in_sea'          => 1,
				'recycle_reason'  => $reason,
				'recycle_user_id' => $userId,
				'updated_id'      => $userId,
			]);
			
			// 3. 记录回收历史
			$this->recordRecycleHistory($customerId, $reason);
			
			
			// 4. 发送回收通知 todo 如果失败，则抛出异常，记录日志
			$this->sendRecycleNotification($info->toArray(), $reason);
			
			Db::commit();
			
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('客户回收失败：' . $e->getMessage());
		}
		
		return true;
	}
	
	/**
	 * 执行公海规则检查和自动回收
	 *
	 * @return array 执行结果
	 */
	public function executeSeaRules(): array
	{
		try {
			// 1. 获取所有启用的公海规则
			$rules = $this->getActiveSeaRules();
			
			$recycledCount  = 0;
			$processedRules = [];
			
			foreach ($rules as $rule) {
				// 2. 执行单个规则
				$ruleResult       = $this->executeSingleRule($rule);
				$recycledCount    += $ruleResult['recycled_count'];
				$processedRules[] = $ruleResult;
			}
			
			// 3. 记录执行日志
			$this->recordRuleExecutionLog($processedRules, $recycledCount);
			
			return [
				'success' => true,
				'message' => "公海规则执行完成，共回收 {$recycledCount} 个客户",
				'data'    => [
					'recycled_count'  => $recycledCount,
					'processed_rules' => count($processedRules),
					'execution_time'  => date('Y-m-d H:i:s')
				]
			];
			
		}
		catch (\Exception $e) {
			Log::error('公海规则执行失败', [
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			
			return [
				'success' => false,
				'message' => '公海规则执行失败：' . $e->getMessage(),
				'data'    => []
			];
		}
	}
	
	/**
	 * 获取公海客户统计
	 *
	 * @return array 统计数据
	 */
	public function getSeaStatistics(): array
	{
		$cacheKey = "crm:sea_statistics:tenant_{get_tenant_id()}";
		
		return Cache::remember($cacheKey, function () {
			$customerSeaModel = new CrmCustomer();
			
			// 总公海客户数
			$totalCount = $customerSeaModel->where('tenant_id', get_tenant_id())
			                               ->count();
			
			// 今日新增
			$todayCount = $customerSeaModel->where('tenant_id', get_tenant_id())
			                               ->whereTime('created_at', 'today')
			                               ->count();
			
			// 本周新增
			$weekCount = $customerSeaModel->where('tenant_id', get_tenant_id())
			                              ->whereTime('created_at', 'week')
			                              ->count();
			
			// 按来源分组统计
			$sourceStats = $customerSeaModel->alias('cs')
			                                ->join('crm_customer c', 'cs.customer_id = c.id')
			                                ->where('cs.tenant_id', get_tenant_id())
			                                ->group('c.source')
			                                ->field('c.source, COUNT(*) as count')
			                                ->select()
			                                ->toArray();
			
			return [
				'total_count'  => $totalCount,
				'today_count'  => $todayCount,
				'week_count'   => $weekCount,
				'source_stats' => $sourceStats,
				'updated_at'   => date('Y-m-d H:i:s')
			];
		}, 300); // 缓存5分钟
	}
	
	
	/**
	 * 验证客户状态是否已锁定和锁定是否过期
	 * todo 如果过期会自动解锁
	 *
	 * @param Model $seaCustomer
	 * @return void
	 */
	public function validateCustomerLockStatus(Model $seaCustomer): void
	{
		if ($seaCustomer['lock_status'] == 1 && $seaCustomer['lock_expire_time'] && $seaCustomer['lock_expire_time'] < time()) {
			// 解锁
			$seaCustomer->save([
				'lock_status'      => 0,
				'lock_expire_time' => null
			]);
		}
		
		if ($seaCustomer['lock_status'] == 1) {
			throw new BusinessException('客户已锁定，请稍后再试');
		}
	}
	
	
	/**
	 * 验证客户是否可以认领
	 */
	private function validateCustomerForClaim(Model $seaCustomer): void
	{
		
		if ($seaCustomer->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		
		$this->validateCustomerLockStatus($seaCustomer);
		
		if ($seaCustomer['status'] == 0) {
			throw new BusinessException('客户已禁用，无法认领');
		}
		
		if ($seaCustomer['in_sea'] == 0) {
			throw new BusinessException('客户已认领');
		}
	}
	
	/**
	 * 验证认领权限和限制
	 */
	private function validateClaimPermission(int $ownerId): void
	{
		// 检查用户当日认领数量限制
		$todayClaimCount = $this->getTodayClaimCount($ownerId);
		$maxClaimPerDay  = 10; // todo 适配规则 每日最大认领数量，可配置
		
		if ($todayClaimCount >= $maxClaimPerDay) {
			throw new BusinessException("今日认领数量已达上限({$maxClaimPerDay}个)");
		}
	}
	
	/**
	 * 获取用户今日认领数量
	 */
	private function getTodayClaimCount(int $ownerId): int
	{
		return CrmCustomer::where('owner_user_id', $ownerId)
		                  ->whereTime('updated_at', 'today')
		                  ->count();
	}
	
	/**
	 * 验证客户是否可以回收
	 */
	private function validateCustomerForRecycle(Model $customer): void
	{
		if ($customer->isEmpty()) {
			throw new BusinessException('客户不存在或无权限访问');
		}
		
		if ($customer['in_sea'] == 1) {
			throw new BusinessException('客户已在公海中，无需重复回收');
		}
	}
	
	/**
	 * 获取启用的公海规则
	 */
	private function getActiveSeaRules(): array
	{
		$seaRuleModel = new CrmSeaRule();
		return $seaRuleModel->where('status', 1)
		                    ->order('sort_order', 'asc')
		                    ->select()
		                    ->toArray();
	}
	
	/**
	 * 执行单个公海规则
	 */
	private function executeSingleRule(array $rule): array
	{
		// 这里实现具体的规则执行逻辑
		// 根据规则条件查找符合条件的客户并回收
		
		$recycledCount = 0;
		
		try {
			// 解析规则条件
			$conditions = $this->parseRuleConditions($rule);
			
			// 查找符合条件的客户
			$customers = $this->findCustomersByConditions($conditions);
			
			// 批量回收客户
			foreach ($customers as $customer) {
				$this->recycleCustomer($customer['id'], "自动回收：{$rule['rule_name']}");
				$recycledCount++;
			}
			
		}
		catch (\Exception $e) {
			Log::error('公海规则执行失败', [
				'rule_id'   => $rule['id'],
				'rule_name' => $rule['rule_name'],
				'error'     => $e->getMessage()
			]);
		}
		
		return [
			'rule_id'        => $rule['id'],
			'rule_name'      => $rule['rule_name'],
			'recycled_count' => $recycledCount,
			'executed_at'    => date('Y-m-d H:i:s')
		];
	}
	
	/**
	 * 解析规则条件
	 */
	private function parseRuleConditions(array $rule): array
	{
		// 这里解析规则条件，返回查询条件数组
		// 具体实现根据规则表的字段设计
		return [
			'days_no_follow'      => $rule['days_no_follow'] ?? 0,
			'days_no_business'    => $rule['days_no_business'] ?? 0,
			'owner_inactive_days' => $rule['owner_inactive_days'] ?? 0
		];
	}
	
	/**
	 * 根据条件查找客户
	 */
	private function findCustomersByConditions(array $conditions): array
	{
		$customerModel = new CrmCustomer();
		$query         = $customerModel->where('tenant_id', get_tenant_id())
		                               ->where('owner_user_id', '>', 0); // 有所有者的客户
		
		// 根据条件添加查询条件
		if ($conditions['days_no_follow'] > 0) {
			$query->where('last_follow_time', '<', date('Y-m-d H:i:s', strtotime("-{$conditions['days_no_follow']} days")));
		}
		
		return $query->limit(100) // 限制每次处理数量
		             ->select()
		             ->toArray();
	}
	
	/**
	 * 记录认领历史
	 */
	private function recordClaimHistory(int $customerId, int $ownerId): void
	{
		Log::info('客户认领记录', [
			'customer_id'   => $customerId,
			'owner_user_id' => $ownerId,
			'operator_id'   => get_user_id(),
			'tenant_id'     => get_tenant_id(),
			'claimed_at'    => date('Y-m-d H:i:s')
		]);
	}
	
	/**
	 * 记录回收历史
	 */
	private function recordRecycleHistory(int $customerId, string $reason): void
	{
		Log::info('客户回收记录', [
			'customer_id' => $customerId,
			'reason'      => $reason,
			'operator_id' => get_user_id(),
			'tenant_id'   => get_tenant_id(),
			'recycled_at' => date('Y-m-d H:i:s')
		]);
	}
	
	/**
	 * 记录规则执行日志
	 */
	private function recordRuleExecutionLog(array $processedRules, int $recycledCount): void
	{
		Log::info('公海规则执行完成', [
			'processed_rules' => $processedRules,
			'total_recycled'  => $recycledCount,
			'tenant_id'       => get_tenant_id(),
			'executed_at'     => date('Y-m-d H:i:s')
		]);
	}
	
	/**
	 * 发送认领通知
	 */
	private function sendClaimNotification(array $customer, int $ownerId): void
	{
		// 集成消息通知系统
		Log::info('发送客户认领通知', [
			'customer_name' => $customer['customer_name'],
			'owner_user_id' => $ownerId,
			'operator_id'   => get_user_id()
		]);
	}
	
	/**
	 * 发送回收通知
	 */
	private function sendRecycleNotification(array $customer, string $reason): void
	{
		// 集成消息通知系统
		Log::info('发送客户回收通知', [
			'customer_name' => $customer['customer_name'],
			'reason'        => $reason,
			'operator_id'   => get_user_id()
		]);
	}
}
