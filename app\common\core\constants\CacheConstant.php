<?php


namespace app\common\core\constants;

/**
 * 管理员常量
 * Class AdminConstant
 *
 * @package app\common\constants
 */
class CacheConstant
{
	
	
	/**
	 * 权限缓存标签
	 * */
	const AUTH_TAG_PREFIX = 'auth_tag';
	
	
	/**
	 * 商户配置缓存标签
	 * */
	const TENANT_CONFIG_PREFIX = 'tenant_config';
	
	/**
	 * 配置缓存标签
	 * */
	const CONFIG_PREFIX = 'config';
	
	/**
	 * 租户缓存前缀
	 */
	const TENANT_PREFIX = 'tenant:';
	
	/**
	 * 总后台缓存前缀
	 */
	const ADMIN_PREFIX = 'admin:';
	
	/**
	 * 登录缓存前缀
	 * todo 后续清理缓存需带上
	 */
	const LOGIN_PREFIX = 'admin_login:';
	
	/**
	 * 登录缓存前缀
	 */
	const LOGIN_CACHE_KEY = 'login_attempts:';
}