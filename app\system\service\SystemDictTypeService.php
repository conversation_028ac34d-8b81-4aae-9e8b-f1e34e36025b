<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\system\model\SystemDictTypeModel;

/**
 * 字典类型表服务类
 */
class SystemDictTypeService extends BaseService
{
    /**
     * 单例实例
     * 
     * @var static|null
     */
    protected static ?self $instance = null;
    
    /**
     * 构造函数
     */
    protected function __construct()
    {
        $this->model = new SystemDictTypeModel();
        parent::__construct();
    }
    
    /**
     * 获取单例
     *
     * @return static
     */
    public static function getInstance(): self
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }
        return static::$instance;
    }
    
    /**
     * 自定义业务方法示例
     *
     * @param array $params 参数
     * @return mixed
     */
    public function customMethod(array $params)
    {
        // 在这里实现自定义业务逻辑
        return $this->getList(['status' => 1]);
    }
} 