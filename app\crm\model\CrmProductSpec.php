<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;

/**
 * 产品规格表模型
 */
class CrmProductSpec extends BaseModel
{
    // 设置表名
    protected $name = 'crm_product_spec';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段转换
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // 软删除
    protected string $deleteTime = 'deleted_at';

    public function __construct(array $data = [])
    {
        parent::__construct($data);
    }
}