<?php
declare(strict_types=1);

namespace app\office\service;

use app\common\core\base\BaseService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use app\office\model\OfficeSampleMail;
use app\common\exception\BusinessException;
use think\facade\Log;

/**
 * 样品邮寄申请服务类
 */
class OfficeSampleMailService extends BaseService implements FormServiceInterface
{
	protected string $modelClass = OfficeSampleMail::class;
	
	public function __construct()
	{
		$this->model = new OfficeSampleMail();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = $this->model->with([
			'submitter',
			'creator'
		])
		                     ->find($id);
		
		if (!$model) {
			throw new BusinessException('样品邮寄申请记录不存在');
		}
		
		return $model->toArray();
	}
	
	/**
	 * 创建表单数据
	 */
	public function saveForm(array $data): array
	{
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			// 验证数据
			$validatedData = $this->validateFormData($formData, 'create');
			
			// 创建主记录
			$id = $this->model->saveByCreate($validatedData);
			
			// 返回完整数据
			$formData = $this->getFormData($id);
			
			return [
				$id,
				$formData
			];
			
		}
		catch (\Exception $e) {
			Log::error('样品邮寄申请创建失败: ' . $e->getMessage(), [
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('样品邮寄申请创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				throw new BusinessException('样品邮寄申请记录不存在');
			}
			
			// 验证数据
			$validatedData = $this->validateFormData($data, 'update');
			
			// 更新记录
			return $model->saveByUpdate($validatedData);
			
		}
		catch (\Exception $e) {
			Log::error('样品邮寄申请更新失败: ' . $e->getMessage(), [
				'id'    => $id,
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('样品邮寄申请更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			$updateData = ['approval_status' => $status];
			
			// 处理额外数据
			if (!empty($extra['workflow_instance_id'])) {
				$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
			}
			
			if (!empty($extra['submit_time'])) {
				$updateData['submit_time'] = $extra['submit_time'];
			}
			
			if (!empty($extra['approval_time'])) {
				$updateData['approval_time'] = $extra['approval_time'];
			}
			
			return $model->saveByUpdate($updateData);
			
		}
		catch (\Exception $e) {
			Log::error('样品邮寄申请状态更新失败: ' . $e->getMessage(), [
				'id'     => $id,
				'status' => $status,
				'extra'  => $extra,
				'trace'  => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 删除表单
	 */
	public function deleteForm(int $id): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			// 软删除主记录
			$model->delete();
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('样品邮寄申请删除失败: ' . $e->getMessage(), [
				'id'    => $id,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 获取流程实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		if (is_array($formData)) {
			$submitterName = $formData['submitter_name'] ?? '';
			
			return "{$submitterName}样品邮寄申请";
		}
		
		return '样品邮寄申请';
	}
	
	/**
	 * 验证表单数据
	 *
	 * @param array  $data
	 * @param string $scene
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		$rules = [
			'sample_name'        => 'require|max:200',
			'sample_description' => 'require|max:500',
			'sender_phone'       => 'require|mobile',
			'delivery_address'   => 'require|max:800',
		];
		
		$msg = [
			'sample_name.require'        => '请填写样品名称',
			'sample_name.max'            => '样品名称长度不能超过200个字符',
			'sample_description.require' => '请填写样品描述',
			'sample_description.max'     => '样品描述长度不能超过500个字符',
			'sender_phone.require'       => '请填写收件人手机号码',
			'sender_phone.mobile'        => '收件人手机号码格式不正确',
			'delivery_address.require'   => '请填写收件人地址',
			'delivery_address.max'       => '收件人地址长度不能超过800个字符',
		];
		
		$validate = validate($rules, $msg);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		return $data;
	}
	
	/**
	 * 工作流状态变更后的处理
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		// TODO: Implement afterWorkflowStatusChange() method.
		return true;
	}
}
