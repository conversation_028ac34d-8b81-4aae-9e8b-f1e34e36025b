<?php
declare(strict_types=1);

namespace app\common\generator\src\engines;

/**
 * 模板引擎类
 * 负责解析和渲染模板
 */
class TemplateEngine
{
    /**
     * 模板路径
     * @var string
     */
    protected string $templatePath;
    
    /**
     * 构造函数
     * 
     * @param string|null $templatePath 模板路径
     */
    public function __construct(?string $templatePath = null)
    {
        $this->templatePath = $templatePath ?? app()->getRootPath() . 'app/common/generator/templates/';
    }
    
    /**
     * 设置模板路径
     * 
     * @param string $path 模板路径
     * @return self
     */
    public function setTemplatePath(string $path): self
    {
        $this->templatePath = rtrim($path, '/') . '/';
        return $this;
    }
    
    /**
     * 获取模板路径
     * 
     * @return string
     */
    public function getTemplatePath(): string
    {
        return $this->templatePath;
    }
    
    /**
     * 渲染模板
     * 
     * @param string $template 模板名称
     * @param array $data 模板数据
     * @return string 渲染结果
     */
    public function render(string $template, array $data = []): string
    {
        // 获取模板文件路径
        $templateFile = $this->getTemplateFile($template);
        
        // 检查模板文件是否存在
        if (!file_exists($templateFile)) {
            throw new \RuntimeException("模板文件不存在: {$templateFile}");
        }
        
        // 读取模板内容
        $content = file_get_contents($templateFile);
        
        // 替换模板变量
        return $this->replaceVariables($content, $data);
    }
    
    /**
     * 获取模板文件路径
     * 
     * @param string $template 模板名称
     * @return string 模板文件路径
     */
    protected function getTemplateFile(string $template): string
    {
        // 检查是否包含文件扩展名
        if (!str_contains($template, '.')) {
            $template .= '.template';
        }
        
        // 返回完整路径
        return $this->templatePath . $template;
    }
    
    /**
     * 替换模板变量
     * 
     * @param string $content 模板内容
     * @param array $data 模板数据
     * @return string 替换后的内容
     */
    protected function replaceVariables(string $content, array $data): string
    {
        // 处理条件标签 {{#condition}}...{{/condition}} 和 {{^condition}}...{{/condition}}
        // 使用递归处理，以支持嵌套条件
        $maxIterations = 10; // 防止无限循环
        $iteration = 0;

        // 先处理正向条件块 {{#condition}}...{{/condition}}
        while (preg_match('/\{\{#([a-zA-Z0-9_]+)\}\}(.*?)\{\{\/\1\}\}/s', $content) && $iteration < $maxIterations) {
            $content = preg_replace_callback('/\{\{#([a-zA-Z0-9_]+)\}\}(.*?)\{\{\/\1\}\}/s', function ($matches) use ($data) {
                $key = $matches[1];
                $innerContent = $matches[2];

                // 获取数据值
                $value = $this->getValue($data, $key);

                // 如果是数组，进行循环处理
                if (is_array($value) && !empty($value)) {
                    $result = '';
                    foreach ($value as $item) {
                        // 递归处理条件块和变量替换
                        $itemContent = $this->replaceVariables($innerContent, $item);
                        $result .= $itemContent;
                    }
                    return $result;
                }

                // 检查条件是否为真
                if ($this->checkCondition($data, $key)) {
                    return $innerContent;
                }

                // 条件不满足，返回空字符串
                return '';
            }, $content);

            $iteration++;
        }

        // 处理反向条件块 {{^condition}}...{{/condition}}
        $iteration = 0;
        while (preg_match('/\{\{\^([a-zA-Z0-9_]+)\}\}(.*?)\{\{\/\1\}\}/s', $content) && $iteration < $maxIterations) {
            $content = preg_replace_callback('/\{\{\^([a-zA-Z0-9_]+)\}\}(.*?)\{\{\/\1\}\}/s', function ($matches) use ($data) {
                $key = $matches[1];
                $innerContent = $matches[2];

                // 检查条件是否为假（反向条件）
                if (!$this->checkCondition($data, $key)) {
                    return $innerContent;
                }

                // 条件满足（但这是反向条件），返回空字符串
                return '';
            }, $content);

            $iteration++;
        }
        
        // 处理普通变量 {{var_name}} 或 {{VAR_NAME}}
        $content = preg_replace_callback('/\{\{([a-zA-Z0-9_]+)\}\}/', function ($matches) use ($data) {
            $key = $matches[1];
            
            // 跳过条件标签
            if ($key[0] === '#' || $key[0] === '/') {
                return $matches[0];
            }
            
            // 尝试直接匹配
            if (isset($data[$key])) {
                return $data[$key];
            }
            
            // 尝试匹配小写键
            $keyLower = strtolower($key);
            if (isset($data[$keyLower])) {
                return $data[$keyLower];
            }
            
            // 尝试匹配大写键
            $keyUpper = strtoupper($key);
            if (isset($data[$keyUpper])) {
                return $data[$keyUpper];
            }
            
            // 默认返回空字符串
            return '';
        }, $content);
        
        return $content;
    }

    /**
     * 替换简单变量（不处理条件块）
     *
     * @param string $content 模板内容
     * @param array $data 模板数据
     * @return string 替换后的内容
     */
    protected function replaceSimpleVariables(string $content, array $data): string
    {
        // 处理普通变量 {{var_name}} 或 {{VAR_NAME}}
        return preg_replace_callback('/\{\{([a-zA-Z0-9_]+)\}\}/', function ($matches) use ($data) {
            $key = $matches[1];

            // 跳过条件标签
            if ($key[0] === '#' || $key[0] === '/') {
                return $matches[0];
            }

            // 获取值
            $value = $this->getValue($data, $key);
            return $value !== null ? (string)$value : '';
        }, $content);
    }

    /**
     * 获取数据值
     *
     * @param array $data 模板数据
     * @param string $key 数据键
     * @return mixed
     */
    protected function getValue(array $data, string $key)
    {
        // 尝试直接匹配
        if (isset($data[$key])) {
            return $data[$key];
        }

        // 尝试匹配小写键
        $keyLower = strtolower($key);
        if (isset($data[$keyLower])) {
            return $data[$keyLower];
        }

        // 尝试匹配大写键
        $keyUpper = strtoupper($key);
        if (isset($data[$keyUpper])) {
            return $data[$keyUpper];
        }

        // 默认返回null
        return null;
    }

    /**
     * 检查条件是否为真
     *
     * @param array $data 模板数据
     * @param string $key 条件键
     * @return bool
     */
    protected function checkCondition(array $data, string $key): bool
    {
        $value = $this->getValue($data, $key);
        return !empty($value);
    }
}