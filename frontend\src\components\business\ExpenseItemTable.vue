<!--报销费用明细表格组件-->
<template>
  <div class="expense-item-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <ElButton type="primary" :disabled="readonly" @click="addItem" size="large">
          <ElIcon>
            <Plus />
          </ElIcon>
          添加明细
        </ElButton>
        <ElButton
          v-if="items.length > 0"
          type="danger"
          :disabled="readonly"
          @click="clearAll"
          size="large"
        >
          <ElIcon>
            <Delete />
          </ElIcon>
          清空
        </ElButton>
      </div>
      <div class="toolbar-right">
        <span class="item-count">共 {{ items.length }} 项</span>
      </div>
    </div>

    <!-- 移动端卡片列表 -->
    <div v-if="isMobile" class="mobile-card-list">
      <div v-for="(item, index) in items" :key="index" class="mobile-card">
        <div class="card-header">
          <span class="card-index">{{ index + 1 }}</span>
          <ElButton v-if="!readonly" type="danger" size="small" text @click="removeItem(index)">
            <ElIcon>
              <Delete />
            </ElIcon>
          </ElButton>
        </div>

        <div class="card-content">
          <!-- 费用说明 -->
          <div class="form-row">
            <label class="form-label">费用说明</label>
            <ElInput
              v-if="!readonly"
              v-model="item.description"
              placeholder="请输入费用说明"
              size="large"
              maxlength="200"
              show-word-limit
            />
            <span v-else class="readonly-value">{{ item.description || '-' }}</span>
          </div>

          <!-- 费用金额 -->
          <div class="form-row">
            <label class="form-label">费用金额(元)</label>
            <ElInputNumber
              v-if="!readonly"
              v-model="item.amount"
              :min="0"
              :precision="2"
              placeholder="请输入金额"
              size="large"
              style="width: 100%"
            />
            <span v-else class="readonly-value amount">¥{{ (item.amount || 0).toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 桌面端表格 -->
    <div v-else class="desktop-table">
      <ElTable :data="items" border size="default" style="width: 100%">
        <ElTableColumn label="序号" width="80" align="center">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="费用说明" min-width="300">
          <template #default="{ row }">
            <ElInput
              v-if="!readonly"
              v-model="row.description"
              placeholder="请输入费用说明"
              size="default"
              maxlength="200"
              show-word-limit
            />
            <span v-else>{{ row.description || '-' }}</span>
          </template>
        </ElTableColumn>
        <!--        <ElTableColumn label="费用金额(元)" width="150">
                  <template #default="{ row }">
                    <ElInputNumber
                      v-if="!readonly"
                      v-model="row.amount"
                      :min="0"
                      :precision="2"
                      placeholder="请输入金额"
                      size="default"
                      style="width: 100%"
                    />
                    <span v-else class="amount-text">¥{{ (row.amount || 0).toFixed(2) }}</span>
                  </template>
                </ElTableColumn>-->
        <ElTableColumn label="操作" width="100" v-if="!readonly" fixed="right">
          <template #default="{ $index }">
            <ElButton type="danger" size="default" @click="removeItem($index)">删除</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <!-- 总计信息 -->
    <!--    <div v-if="items.length > 0" class="summary-section">
          <div class="summary-item">
            <span class="summary-label">费用总计:</span>
            <span class="summary-value amount">¥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </div>-->
  </div>
</template>

<script setup lang="ts">
  import { ElButton, ElIcon, ElTable, ElTableColumn, ElInput, ElInputNumber } from 'element-plus'
  import { Plus, Delete } from '@element-plus/icons-vue'

  // 组件属性
  interface Props {
    modelValue: any[]
    readonly?: boolean
    itemTemplate?: () => any
  }

  const props = withDefaults(defineProps<Props>(), {
    readonly: false,
    itemTemplate: () => ({
      description: '',
      amount: 0
    })
  })

  // 事件定义
  interface Emits {
    'update:modelValue': [value: any[]]
    change: [value: any[]]
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const items = computed({
    get: () => props.modelValue || [],
    set: (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }
  })

  // 检测移动端
  const isMobile = ref(false)

  const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768
  }

  onMounted(() => {
    checkMobile()
    window.addEventListener('resize', checkMobile)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })

  // 计算总金额
  const totalAmount = computed(() => {
    return items.value.reduce((sum, item) => sum + (item.amount || 0), 0)
  })

  // 方法
  const addItem = () => {
    const newItem = props.itemTemplate()
    items.value = [...items.value, newItem]
  }

  const removeItem = (index: number) => {
    const newItems = [...items.value]
    newItems.splice(index, 1)
    items.value = newItems
  }

  const clearAll = () => {
    items.value = []
  }
</script>

<style lang="scss" scoped>
  .expense-item-table {
    width: 100%;

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 8px;

      .toolbar-left {
        display: flex;
        gap: 12px;
      }

      .toolbar-right {
        .item-count {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
    }

    .mobile-card-list {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .mobile-card {
        border: 1px solid #e4e7ed;
        border-radius: 12px;
        padding: 16px;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #f0f0f0;

          .card-index {
            font-size: 16px;
            font-weight: 600;
            color: #409eff;
            background: #ecf5ff;
            padding: 4px 12px;
            border-radius: 16px;
          }
        }

        .card-content {
          .form-row {
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .form-label {
              font-size: 14px;
              color: #606266;
              margin-bottom: 8px;
              font-weight: 500;
            }

            .readonly-value {
              font-size: 14px;
              color: #303133;
              padding: 8px 0;

              &.amount {
                font-size: 16px;
                font-weight: 600;
                color: #f56c6c;
              }
            }
          }
        }
      }
    }

    .desktop-table {
      :deep(.el-table) {
        width: 100%;
      }

      :deep(.el-table__body-wrapper) {
        overflow-x: auto;
      }

      .amount-text {
        font-weight: 600;
        color: #f56c6c;
      }
    }

    .summary-section {
      margin-top: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      justify-content: flex-end;
      gap: 24px;

      .summary-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .summary-label {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }

        .summary-value {
          font-size: 18px;
          font-weight: 600;

          &.amount {
            color: #f56c6c;
          }
        }
      }
    }

    // 移动端适配
    @media (max-width: 768px) {
      .table-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .toolbar-left {
          justify-content: center;
        }

        .toolbar-right {
          text-align: center;
        }
      }
    }
  }
</style>
