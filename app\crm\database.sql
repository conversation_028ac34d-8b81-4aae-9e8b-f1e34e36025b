-- todo 暂不使用 CRM系统数据库设计
-- 基于多租户架构，优化版本：添加软删除、优化字段类型、完善索引、添加外键约束
SET NAMES utf8mb4;

-- ----------------------------
-- 标签表 - 支持客户、线索、联系人、商机标签管理
-- ----------------------------
DROP TABLE IF EXISTS `crm_tag`;
CREATE TABLE IF NOT EXISTS `crm_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `tag_name`   varchar(30)         NOT NULL DEFAULT '' COMMENT '标签名称',
    `tag_type`   varchar(20)         NOT NULL DEFAULT '' COMMENT '标签类型:customer=客户标签,lead=线索标签,contact=联系人标签,business=商机标签',
    `tag_color`  varchar(20)         NOT NULL DEFAULT '' COMMENT '标签颜色',
    `sort`       int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_type` (`tenant_id`, `tag_type`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='标签表';

-- ----------------------------
-- 线索表 - 线索管理核心表
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead`;
CREATE TABLE IF NOT EXISTS `crm_lead`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '线索ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `lead_name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '线索姓名',
    `company`          varchar(200)        NOT NULL DEFAULT '' COMMENT '公司名称',
    `position`         varchar(50)         NOT NULL DEFAULT '' COMMENT '职位',
    `mobile`           varchar(20)         NOT NULL DEFAULT '' COMMENT '手机号',
    `phone`            varchar(20)         NOT NULL DEFAULT '' COMMENT '电话',
    `email`            varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱',
    `status`           tinyint(1)          NOT NULL DEFAULT 1 COMMENT '状态:0=无效,1=未跟进,2=跟进中,3=已转化,4=已失效',
    `source`           varchar(50)         NOT NULL DEFAULT '' COMMENT '线索来源',
    `level`            tinyint(1)          NOT NULL DEFAULT 0 COMMENT '线索级别:0=未知,1=低,2=中,3=高',
    `industry`         varchar(50)         NOT NULL DEFAULT '' COMMENT '所属行业',
    `address`          text COMMENT '地址',
    `remark`           text COMMENT '备注',
    `is_transformed`   tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已转化:0=未转化,1=已转化',
    `transformed_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '转化后的客户ID',
    `transformed_time` datetime                     DEFAULT NULL COMMENT '转化时间',
    `owner_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID',
    `last_followed_at` datetime                     DEFAULT NULL COMMENT '最后跟进时间',
    `next_followed_at` datetime                     DEFAULT NULL COMMENT '下次跟进时间',
    `in_pool`          tinyint(1)          NOT NULL DEFAULT 1 COMMENT '是否在线索池:0=否,1=是',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_pool` (`tenant_id`, `in_pool`),
    KEY `idx_follow_time` (`tenant_id`, `next_followed_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索表';

-- ----------------------------
-- 线索与标签关联表
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_tag`;
CREATE TABLE IF NOT EXISTS `crm_lead_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `lead_id`    bigint(20) UNSIGNED NOT NULL COMMENT '线索ID',
    `tag_id`     bigint(20) UNSIGNED NOT NULL COMMENT '标签ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_lead_tag` (`lead_id`, `tag_id`),
    KEY `idx_tenant_lead` (`tenant_id`, `lead_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索与标签关联表';

-- ----------------------------
-- 线索跟进记录表
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_follow`;
CREATE TABLE IF NOT EXISTS `crm_lead_follow`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `lead_id`          bigint(20) UNSIGNED NOT NULL COMMENT '线索ID',
    `content`          text COMMENT '跟进内容',
    `follow_type`      varchar(20)         NOT NULL DEFAULT '' COMMENT '跟进方式:phone=电话,email=邮件,visit=拜访,wechat=微信',
    `follow_result`    varchar(255)        NOT NULL DEFAULT '' COMMENT '跟进结果',
    `next_follow_time` datetime                     DEFAULT NULL COMMENT '下次跟进时间',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_lead` (`tenant_id`, `lead_id`),
    KEY `idx_tenant_created` (`tenant_id`, `created_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索跟进记录表';

-- ----------------------------
-- 线索分配记录表
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_assignment`;
CREATE TABLE IF NOT EXISTS `crm_lead_assignment`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `lead_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '线索ID',
    `from_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '原负责人ID',
    `to_user_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '新负责人ID',
    `assignment_type` tinyint(1) UNSIGNED NOT NULL COMMENT '分配类型:1=手动分配,2=自动分配,3=认领,4=抢占',
    `reason`          varchar(255)        NOT NULL DEFAULT '' COMMENT '分配原因',
    `created_by`      bigint(20) UNSIGNED NOT NULL COMMENT '操作人ID',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_lead` (`tenant_id`, `lead_id`),
    KEY `idx_tenant_to_user` (`tenant_id`, `to_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='线索分配记录表';

-- ----------------------------
-- 客户表 - 客户管理核心表
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer`;
CREATE TABLE IF NOT EXISTS `crm_customer`
(
    `id`                 bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '客户ID',
    `tenant_id`          bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_name`      varchar(200)        NOT NULL DEFAULT '' COMMENT '客户名称/公司名称',
    `industry`           varchar(50)         NOT NULL DEFAULT '' COMMENT '所属行业',
    `level`              tinyint(1)          NOT NULL DEFAULT 1 COMMENT '客户级别:1=普通,2=重要,3=战略',
    `source`             varchar(50)         NOT NULL DEFAULT '' COMMENT '客户来源',
    `phone`              varchar(20)         NOT NULL DEFAULT '' COMMENT '电话',
    `website`            varchar(100)        NOT NULL DEFAULT '' COMMENT '网站',
    `region_province`    varchar(30)         NOT NULL DEFAULT '' COMMENT '省份',
    `region_city`        varchar(30)         NOT NULL DEFAULT '' COMMENT '城市',
    `region_district`    varchar(30)         NOT NULL DEFAULT '' COMMENT '区/县',
    `address`            text COMMENT '详细地址',
    `zip_code`           varchar(20)         NOT NULL DEFAULT '' COMMENT '邮政编码',
    `remark`             text COMMENT '备注',
    `owner_user_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID',
    `status`             tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '客户状态:0=停用,1=正常',
    `in_sea`             tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否在公海:0=否,1=是',
    `into_sea_time`      datetime                     DEFAULT NULL COMMENT '进入公海时间',
    `sea_id`             bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属公海ID',
    `credit_code`        varchar(50)         NOT NULL DEFAULT '' COMMENT '统一社会信用代码',
    `annual_revenue`     decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '年营业额',
    `employee_count`     int(11)             NOT NULL DEFAULT 0 COMMENT '员工人数',
    `registered_capital` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '注册资本',
    `last_followed_at`   datetime                     DEFAULT NULL COMMENT '最后跟进时间',
    `next_followed_at`   datetime                     DEFAULT NULL COMMENT '下次跟进时间',
    `creator_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `lock_status`        tinyint(1)          NOT NULL DEFAULT 0 COMMENT '锁定状态:0=未锁定,1=已锁定',
    `lock_expire_time`   datetime                     DEFAULT NULL COMMENT '锁定到期时间',
    `deleted_at`         datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`, `in_sea`),
    KEY `idx_tenant_sea` (`tenant_id`, `sea_id`),
    KEY `idx_follow_time` (`tenant_id`, `next_followed_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户表';

-- ----------------------------
-- 客户与标签关联表
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_tag`;
CREATE TABLE IF NOT EXISTS `crm_customer_tag`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL COMMENT '客户ID',
    `tag_id`      bigint(20) UNSIGNED NOT NULL COMMENT '标签ID',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_customer_tag` (`customer_id`, `tag_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户与标签关联表';

-- ----------------------------
-- 联系人表 - 客户联系人管理
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact`;
CREATE TABLE IF NOT EXISTS `crm_contact`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID',
    `name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '联系人姓名',
    `gender`      tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别:0=未知,1=男,2=女',
    `position`    varchar(50)         NOT NULL DEFAULT '' COMMENT '职位',
    `department`  varchar(50)         NOT NULL DEFAULT '' COMMENT '部门',
    `mobile`      varchar(20)         NOT NULL DEFAULT '' COMMENT '手机号',
    `phone`       varchar(20)         NOT NULL DEFAULT '' COMMENT '电话',
    `email`       varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱',
    `wechat`      varchar(50)         NOT NULL DEFAULT '' COMMENT '微信',
    `qq`          varchar(20)         NOT NULL DEFAULT '' COMMENT 'QQ',
    `importance`  tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '重要程度:0=普通,1=重要,2=核心',
    `role_type`   varchar(20)         NOT NULL DEFAULT '' COMMENT '角色类型:decision=决策者,user=使用者,influence=影响者',
    `birthday`    date                         DEFAULT NULL COMMENT '生日',
    `address`     text COMMENT '地址',
    `remark`      text COMMENT '备注',
    `is_primary`  tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为主要联系人:0=否,1=是',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_mobile` (`tenant_id`, `mobile`),
    KEY `idx_tenant_primary` (`tenant_id`, `is_primary`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='联系人表';

-- ----------------------------
-- 联系人与标签关联表
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact_tag`;
CREATE TABLE IF NOT EXISTS `crm_contact_tag`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `contact_id` bigint(20) UNSIGNED NOT NULL COMMENT '联系人ID',
    `tag_id`     bigint(20) UNSIGNED NOT NULL COMMENT '标签ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_contact_tag` (`contact_id`, `tag_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_tag_id` (`tag_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='联系人与标签关联表';

-- ----------------------------
-- 客户跟进记录表
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_follow`;
CREATE TABLE IF NOT EXISTS `crm_customer_follow`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID',
    `contact_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '联系人ID',
    `content`          text COMMENT '跟进内容',
    `follow_type`      varchar(20)         NOT NULL DEFAULT '' COMMENT '跟进方式:phone=电话,email=邮件,visit=拜访,wechat=微信',
    `follow_result`    varchar(255)        NOT NULL DEFAULT '' COMMENT '跟进结果',
    `next_follow_time` datetime                     DEFAULT NULL COMMENT '下次跟进时间',
    `images`           text COMMENT '图片附件（JSON格式，存储路径）',
    `files`            text COMMENT '其他附件（JSON格式，存储路径和说明）',
    `mentioned_users`  text COMMENT '@提及的用户（JSON格式，用户ID列表）',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_contact_id` (`contact_id`),
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户跟进记录表';

-- ----------------------------
-- 客户分配记录表
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_assignment`;
CREATE TABLE IF NOT EXISTS `crm_customer_assignment`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID',
    `from_user_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '原负责人ID',
    `to_user_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '新负责人ID',
    `assignment_type` tinyint(1) UNSIGNED NOT NULL COMMENT '分配类型:1=手动分配,2=自动分配,3=认领,4=抢占',
    `reason`          text COMMENT '分配原因',
    `created_by`      bigint(20) UNSIGNED NOT NULL COMMENT '操作人ID',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_to_user_id` (`to_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户分配记录表';

-- ----------------------------
-- 客户共享表
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_share`;
CREATE TABLE IF NOT EXISTS `crm_customer_share`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID',
    `owner_user_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户主负责人ID',
    `user_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '被共享用户ID',
    `share_scope`    tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '共享范围:1=只读,2=读写',
    `share_deadline` date                         DEFAULT NULL COMMENT '共享截止日期',
    `creator_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`     datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户共享表';

-- ----------------------------
-- 客户公海表
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_sea`;
CREATE TABLE IF NOT EXISTS `crm_customer_sea`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '公海ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `sea_name`    varchar(50)         NOT NULL DEFAULT '' COMMENT '公海名称',
    `description` varchar(255)        NOT NULL DEFAULT '' COMMENT '公海描述',
    `dept_scope`  text COMMENT '部门适用范围（JSON格式，部门ID列表）',
    `role_scope`  text COMMENT '角色适用范围（JSON格式，角色ID列表）',
    `status`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='客户公海表';

-- ----------------------------
-- 公海规则表
-- ----------------------------
DROP TABLE IF EXISTS `crm_sea_rule`;
CREATE TABLE IF NOT EXISTS `crm_sea_rule`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规则ID',
    `tenant_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `sea_id`         bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '公海ID',
    `rule_name`      varchar(50)         NOT NULL DEFAULT '' COMMENT '规则名称',
    `rule_type`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '规则类型:1=未跟进,2=未成交',
    `follow_days`    int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '未跟进天数',
    `deal_days`      int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '未成交天数',
    `customer_level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '适用的客户级别:0=全部,1=普通,2=重要,3=战略',
    `enable_notify`  tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用提醒:0=否,1=是',
    `notify_days`    int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '提前提醒天数',
    `status`         tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_sea_id` (`sea_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='公海规则表';

-- -------------------------------------------------------------------------
-- 商机管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 商机表 - 商机管理核心表
-- ----------------------------
DROP TABLE IF EXISTS `crm_business`;
CREATE TABLE IF NOT EXISTS `crm_business`
(
    `id`               bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '商机ID',
    `tenant_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `business_name`    varchar(200)            NOT NULL DEFAULT '' COMMENT '商机名称',
    `customer_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID',
    `contact_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '联系人ID',
    `amount`           decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商机金额',
    `discount_rate`    decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%)',
    `status`           tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '状态:1=跟进中,2=赢单,3=输单,4=无效',
    `stage_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '当前阶段ID',
    `source`           varchar(50)             NOT NULL DEFAULT '' COMMENT '商机来源',
    `type`             varchar(50)             NOT NULL DEFAULT '' COMMENT '商机类型',
    `priority`         tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '优先级:0=普通,1=重要,2=紧急',
    `expect_date`      date                             DEFAULT NULL COMMENT '预计成交日期',
    `closed_date`      date                             DEFAULT NULL COMMENT '实际成交日期',
    `lost_reason`      varchar(255)            NOT NULL DEFAULT '' COMMENT '输单原因',
    `competitor`       varchar(100)            NOT NULL DEFAULT '' COMMENT '主要竞争对手',
    `remark`           text COMMENT '备注',
    `owner_user_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '负责人ID',
    `last_followed_at` datetime                         DEFAULT NULL COMMENT '最后跟进时间',
    `next_followed_at` datetime                         DEFAULT NULL COMMENT '下次跟进时间',
    `creator_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_status_stage` (`tenant_id`, `status`, `stage_id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_expect_date` (`tenant_id`, `expect_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机表';

-- ----------------------------
-- 商机与标签关联表
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_tag`;
CREATE TABLE IF NOT EXISTS `crm_business_tag`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `business_id` bigint(20) UNSIGNED NOT NULL COMMENT '商机ID',
    `tag_id`      bigint(20) UNSIGNED NOT NULL COMMENT '标签ID',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_business_tag` (`business_id`, `tag_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_tag_id` (`tag_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机与标签关联表';

-- ----------------------------
-- 商机阶段表
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_stage`;
CREATE TABLE IF NOT EXISTS `crm_business_stage`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '阶段ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `stage_name`  varchar(50)         NOT NULL DEFAULT '' COMMENT '阶段名称',
    `order_num`   int(11) UNSIGNED    NOT NULL COMMENT '排序号',
    `win_rate`    decimal(5, 2)       NOT NULL DEFAULT 0 COMMENT '赢单率(%)',
    `description` text COMMENT '描述',
    `status`      tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机阶段表';

-- ----------------------------
-- 商机阶段记录表
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_stage_record`;
CREATE TABLE IF NOT EXISTS `crm_business_stage_record`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `business_id` bigint(20) UNSIGNED NOT NULL COMMENT '商机ID',
    `stage_id`    bigint(20) UNSIGNED NOT NULL COMMENT '阶段ID',
    `remark`      text COMMENT '备注',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_stage_id` (`stage_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机阶段记录表';

-- ----------------------------
-- 商机产品关联表
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_product`;
CREATE TABLE IF NOT EXISTS `crm_business_product`
(
    `id`            bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `business_id`   bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '商机ID',
    `product_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID',
    `price`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价',
    `quantity`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '数量',
    `discount`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣',
    `discount_rate` decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%)',
    `subtotal`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额',
    `order_num`     int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '排序',
    `remark`        text COMMENT '备注',
    `creator_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`    datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`    datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机产品关联表';

-- ----------------------------
-- 商机跟进记录表
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_follow`;
CREATE TABLE IF NOT EXISTS `crm_business_follow`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `business_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商机ID',
    `content`          text COMMENT '跟进内容',
    `follow_type`      varchar(20)         NOT NULL DEFAULT '' COMMENT '跟进方式（如电话、邮件、拜访等）',
    `follow_result`    text COMMENT '跟进结果',
    `next_follow_time` datetime                     DEFAULT NULL COMMENT '下次跟进时间',
    `images`           text COMMENT '图片附件（JSON格式，存储路径）',
    `files`            text COMMENT '其他附件（JSON格式，存储路径和说明）',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='商机跟进记录表';

-- -------------------------------------------------------------------------
-- 报价单管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 报价单表
-- ----------------------------
drop table IF EXISTS `crm_quotation`;
CREATE TABLE IF NOT EXISTS `crm_quotation`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '报价单ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `quotation_no`    varchar(30)             NOT NULL DEFAULT '' COMMENT '报价单编号',
    `title`           varchar(100)            NOT NULL DEFAULT '' COMMENT '报价单标题',
    `customer_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID',
    `contact_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '联系人ID',
    `business_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联商机ID',
    `total_amount`    decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '报价总金额',
    `discount_rate`   decimal(5, 2) UNSIGNED           DEFAULT 100.00 COMMENT '整单折扣率(%)',
    `discount_amount` decimal(15, 2) UNSIGNED          DEFAULT 0.00 COMMENT '折扣金额',
    `tax_rate`        decimal(5, 2) UNSIGNED           DEFAULT 0.00 COMMENT '税率(%)',
    `tax_amount`      decimal(15, 2) UNSIGNED          DEFAULT 0.00 COMMENT '税额',
    `final_amount`    decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最终金额',
    `owner_user_id`   bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '负责人ID',
    `status`          tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '状态:0=草稿,1=审批中,2=已审批,3=已发送,4=已转化,5=已作废',
    `valid_days`      int(11) UNSIGNED        NOT NULL DEFAULT 30 COMMENT '有效天数',
    `expire_date`     date                             DEFAULT NULL COMMENT '过期日期',
    `remark`          text COMMENT '备注',
    `send_time`       datetime                         DEFAULT NULL COMMENT '发送时间',
    `viewed_time`     datetime                         DEFAULT NULL COMMENT '客户查看时间',
    `viewed_count`    int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '客户查看次数',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_quotation_no` (`quotation_no`, `tenant_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_expire_date` (`tenant_id`, `expire_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单表';

-- ----------------------------
-- 报价单产品明细表
-- ----------------------------
drop table IF EXISTS `crm_quotation_product`;
CREATE TABLE IF NOT EXISTS `crm_quotation_product`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `quotation_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '报价单ID',
    `product_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID',
    `price`           decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价',
    `quantity`        decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '数量',
    `discount`        decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣',
    `discount_rate`   decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%)',
    `tax_rate`        decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 0.00 COMMENT '税率(%)',
    `subtotal`        decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额（不含税）',
    `tax_amount`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '税额',
    `amount_with_tax` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '含税金额',
    `remark`          text COMMENT '备注',
    `order_num`       int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '排序',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_quotation_id` (`quotation_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单产品明细表';

-- ----------------------------
-- 报价单模板表
-- ----------------------------
drop table IF EXISTS `crm_quotation_template`;
CREATE TABLE IF NOT EXISTS `crm_quotation_template`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `tenant_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `template_name` varchar(50)         NOT NULL DEFAULT '' COMMENT '模板名称',
    `template_code` varchar(30)         NOT NULL DEFAULT '' COMMENT '模板编码',
    `content`       text COMMENT '模板内容（HTML格式）',
    `type`          tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '模板类型:0=普通,1=系统',
    `status`        tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `remark`        text COMMENT '备注',
    `creator_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单模板表';

-- ----------------------------
-- 报价单审批记录表
-- ----------------------------
drop table IF EXISTS `crm_quotation_approval`;
CREATE TABLE IF NOT EXISTS `crm_quotation_approval`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `quotation_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '报价单ID',
    `node_name`       varchar(50)         NOT NULL DEFAULT '' COMMENT '审批节点名称',
    `approve_user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审批人ID',
    `status`          tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态:0=待审批,1=通过,2=驳回',
    `opinion`         TEXT COMMENT '审批意见',
    `approval_time`   datetime                     DEFAULT NULL COMMENT '审批时间',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_quotation_id` (`quotation_id`),
    KEY `idx_approve_user_id` (`approve_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报价单审批记录表';

-- -------------------------------------------------------------------------
-- 合同管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 合同表
-- ----------------------------
drop table IF EXISTS `crm_contract`;
CREATE TABLE IF NOT EXISTS `crm_contract`
(
    `id`                bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '合同ID',
    `tenant_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_no`       varchar(30)             NOT NULL DEFAULT '' COMMENT '合同编号',
    `contract_name`     varchar(100)            NOT NULL DEFAULT '' COMMENT '合同名称',
    `customer_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID',
    `contact_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '联系人ID',
    `business_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联商机ID',
    `quotation_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联报价单ID',
    `contract_amount`   decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '合同金额',
    `received_amount`   decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '已收款金额',
    `unreceived_amount` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '未收款金额',
    `owner_user_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '负责人ID',
    `status`            tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '状态:0=草稿,1=审批中,2=已审批,3=执行中,4=已完成,5=已作废',
    `contract_type`     varchar(30)             NOT NULL DEFAULT '' COMMENT '合同类型',
    `start_date`        date                             DEFAULT NULL COMMENT '合同开始日期',
    `end_date`          date                             DEFAULT NULL COMMENT '合同结束日期',
    `sign_date`         date                             DEFAULT NULL COMMENT '签约日期',
    `payment_terms`     text COMMENT '付款条件',
    `delivery_terms`    text COMMENT '交货条件',
    `contract_content`  text COMMENT '合同内容',
    `remark`            text COMMENT '备注',
    `creator_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`        datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_contract_no` (`contract_no`, `tenant_id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同表';

-- ----------------------------
-- 合同产品明细表
-- ----------------------------
drop table IF EXISTS `crm_contract_product`;
CREATE TABLE IF NOT EXISTS `crm_contract_product`
(
    `id`            bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id`   bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '合同ID',
    `product_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID',
    `price`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '单价',
    `quantity`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '数量',
    `discount`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '折扣',
    `discount_rate` decimal(5, 2) UNSIGNED  NOT NULL DEFAULT 100.00 COMMENT '折扣率(%)',
    `subtotal`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '小计金额',
    `order_num`     int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '排序',
    `remark`        text COMMENT '备注',
    `creator_id`    bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`    datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`    datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同产品明细表';

-- ----------------------------
-- 合同附件表
-- ----------------------------
drop table IF EXISTS `crm_contract_attachment`;
CREATE TABLE IF NOT EXISTS `crm_contract_attachment`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合同ID',
    `file_name`   varchar(100)        NOT NULL DEFAULT '' COMMENT '文件名',
    `file_path`   varchar(255)        NOT NULL DEFAULT '' COMMENT '文件路径',
    `file_size`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
    `file_type`   varchar(50)         NOT NULL DEFAULT '' COMMENT '文件类型',
    `remark`      text COMMENT '备注',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同附件表';

-- ----------------------------
-- 合同审批记录表
-- ----------------------------
drop table IF EXISTS `crm_contract_approval`;
CREATE TABLE IF NOT EXISTS `crm_contract_approval`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合同ID',
    `node_name`       varchar(50)         NOT NULL DEFAULT '' COMMENT '审批节点名称',
    `approve_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '审批人ID',
    `status`          tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态:0=待审批,1=通过,2=驳回',
    `opinion`         varchar(255)                 DEFAULT NULL COMMENT '审批意见',
    `approval_time`   datetime                     DEFAULT NULL COMMENT '审批时间',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_approve_user_id` (`approve_user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='合同审批记录表';

-- -------------------------------------------------------------------------
-- 回款管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 回款计划表
-- ----------------------------
drop table IF EXISTS `crm_payment_plan`;
CREATE TABLE IF NOT EXISTS `crm_payment_plan`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '计划ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `contract_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '合同ID',
    `customer_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID',
    `stage`           varchar(50)             NOT NULL DEFAULT '' COMMENT '期次（如第一期、第二期）',
    `expect_amount`   decimal(15, 2)          NOT NULL DEFAULT 0.00 COMMENT '计划回款金额（支持退款负数）',
    `expect_date`     date                             DEFAULT NULL COMMENT '计划回款日期',
    `remark`          text COMMENT '备注',
    `received_amount` decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '已回款金额',
    `status`          tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '状态:0=未回款,1=部分回款,2=已回款,3=逾期',
    `remind_days`     int(11) UNSIGNED        NOT NULL DEFAULT 3 COMMENT '提前提醒天数',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_expect_date` (`expect_date`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='回款计划表';

-- ----------------------------
-- 回款记录表
-- ----------------------------
drop table IF EXISTS `crm_payment_record`;
CREATE TABLE IF NOT EXISTS `crm_payment_record`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `payment_no`      varchar(30)             NOT NULL DEFAULT '' COMMENT '回款编号',
    `contract_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '合同ID',
    `customer_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '客户ID',
    `plan_id`         bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '关联的回款计划ID',
    `amount`          decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '回款金额',
    `payment_date`    date                             DEFAULT NULL COMMENT '回款日期',
    `payment_method`  varchar(30)             NOT NULL DEFAULT '' COMMENT '回款方式',
    `voucher_path`    text COMMENT '回款凭证路径',
    `bank_name`       varchar(100)            NOT NULL DEFAULT '' COMMENT '收款银行名称',
    `bank_account`    varchar(50)             NOT NULL DEFAULT '' COMMENT '收款银行账号',
    `remark`          text COMMENT '备注',
    `status`          tinyint(1) UNSIGNED     NOT NULL DEFAULT 1 COMMENT '状态:0=未确认,1=已确认,2=已作废',
    `confirm_user_id` bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '确认人ID',
    `confirm_time`    datetime                         DEFAULT NULL COMMENT '确认时间',
    `creator_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_payment_no` (`payment_no`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_contract_id` (`contract_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_payment_date` (`payment_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='回款记录表';

-- ----------------------------
-- 回款附件表
-- ----------------------------
drop table IF EXISTS `crm_payment_attachment`;
CREATE TABLE IF NOT EXISTS `crm_payment_attachment`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `record_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '回款记录ID',
    `file_name`  varchar(100)        NOT NULL DEFAULT '' COMMENT '文件名',
    `file_path`  varchar(255)        NOT NULL DEFAULT '' COMMENT '文件路径',
    `file_size`  bigint(20)          NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
    `file_type`  text COMMENT '文件类型',
    `remark`     text COMMENT '备注',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_record_id` (`record_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='回款附件表';

-- -------------------------------------------------------------------------
-- 产品管理 - 相关表
-- -------------------------------------------------------------------------

-- ----------------------------
-- 产品表
-- ----------------------------
drop table IF EXISTS `crm_product`;
CREATE TABLE IF NOT EXISTS `crm_product`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '产品ID',
    `tenant_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `product_name`  varchar(100)        NOT NULL DEFAULT '' COMMENT '产品名称',
    `product_code`  varchar(50)         NOT NULL DEFAULT '' COMMENT '产品编码',
    `category_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品分类ID',
    `unit_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单位ID',
    `price`         decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '标准价格',
    `cost`          decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价格',
    `description`   text COMMENT '产品描述',
    `specification` text COMMENT '产品规格',
    `status`        tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenant_code` (`tenant_id`, `product_code`),
    KEY `idx_tenant_category` (`tenant_id`, `category_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品表';

-- ----------------------------
-- 产品分类表
-- ----------------------------
drop table IF EXISTS `crm_product_category`;
CREATE TABLE IF NOT EXISTS `crm_product_category`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `tenant_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `parent_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级分类ID',
    `category_name` varchar(50)         NOT NULL DEFAULT '' COMMENT '分类名称',
    `category_code` varchar(30)         NOT NULL DEFAULT '' COMMENT '分类编码',
    `description`   text COMMENT '分类描述',
    `sort`          int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '排序',
    `status`        tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`    bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_parent` (`tenant_id`, `parent_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品分类表';

-- ----------------------------
-- 产品单位表
-- ----------------------------
drop table IF EXISTS `crm_product_unit`;
CREATE TABLE IF NOT EXISTS `crm_product_unit`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '单位ID',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `unit_name`  varchar(20)         NOT NULL DEFAULT '' COMMENT '单位名称',
    `unit_code`  varchar(10)         NOT NULL DEFAULT '' COMMENT '单位编码',
    `remark`     text COMMENT '备注',
    `status`     tinyint(1)          NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品单位表';

-- ----------------------------
-- 产品价格策略表
-- ----------------------------
drop table IF EXISTS `crm_product_price`;
CREATE TABLE IF NOT EXISTS `crm_product_price`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '价格策略ID',
    `tenant_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `product_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品ID',
    `strategy_name`   varchar(50)         NOT NULL DEFAULT '' COMMENT '策略名称',
    `price`           decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
    `strategy_type`   tinyint(1)          NOT NULL DEFAULT 1 COMMENT '策略类型:1=客户等级,2=客户分类,3=客户标签,4=特定客户,5=区域,6=季节',
    `strategy_target` varchar(50)         NOT NULL DEFAULT '' COMMENT '策略目标',
    `target_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标ID',
    `status`          tinyint(1)          NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `start_date`      date                         DEFAULT NULL COMMENT '生效开始日期',
    `end_date`        date                         DEFAULT NULL COMMENT '生效结束日期',
    `remark`          text COMMENT '备注',
    `creator_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_product` (`tenant_id`, `product_id`),
    KEY `idx_tenant_strategy` (`tenant_id`, `strategy_type`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_date_range` (`start_date`, `end_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品价格策略表';

-- ----------------------------
-- 产品规格表
-- ----------------------------
drop table IF EXISTS `crm_product_spec`;
CREATE TABLE IF NOT EXISTS `crm_product_spec`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规格ID',
    `tenant_id`        bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `product_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品ID',
    `spec_name`        varchar(50)         NOT NULL COMMENT '规格名称',
    `spec_value`       varchar(100)        NOT NULL COMMENT '规格值',
    `spec_code`        varchar(50)         NOT NULL DEFAULT '' COMMENT '规格编码',
    `price_adjustment` decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '价格调整（支持负数降价）',
    `adjustment_type`  tinyint(1)          NOT NULL DEFAULT 0 COMMENT '调整类型:0=固定值,1=百分比',
    `inventory`        int(11)             NOT NULL DEFAULT 0 COMMENT '库存',
    `images`           text COMMENT '规格图片',
    `order_num`        int(11)             NOT NULL DEFAULT 0 COMMENT '排序号',
    `status`           tinyint(1)          NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_product` (`tenant_id`, `product_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_spec_code` (`spec_code`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='产品规格表';

-- 注意：
-- 1. 所有表都已添加了完整的索引优化和软删除支持
-- 2. 包含了完整的CRM业务流程：线索→客户→商机→报价→合同→回款
-- 3. 字段类型已优化：decimal(15,2)用于金额，varchar长度合理化
-- 4. 支持多租户架构，所有表都包含tenant_id字段
-- 5. 金额字段类型说明：
--    - UNSIGNED类型：用于不会为负数的金额（如商机金额、合同金额、回款金额等）
--    - 非UNSIGNED类型：用于可能为负数的金额（如价格调整、计划回款等）