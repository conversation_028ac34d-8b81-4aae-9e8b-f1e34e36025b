<?php
declare(strict_types=1);

namespace app\common\generator\src\mappers;

/**
 * 表单组件映射器
 * 负责将数据库字段映射为前端表单组件
 */
class FormComponentMapper
{
    /**
     * 组件映射配置
     * @var array
     */
    protected array $config;
    
    /**
     * 构造函数
     * 
     * @param array $config 组件映射配置
     */
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * 获取字段对应的表单组件
     * 
     * @param array $column 字段信息
     * @return array|null 表单组件信息
     */
    public function getFormComponent(array $column): ?array
    {
        // 获取基本信息
        $field = $column['Field'];
        $type = $column['Type'];
        $fieldLower = strtolower($field);
        
        // 调试输出字段信息
        if ($field === 'view_count' || $field === 'like_count' || $field === 'dept_id' || $field === 'role_id' || $field === 'quantity' || $field === 'sort' || $field === 'order_num') {
            // 输出到日志
            $logFile = app()->getRootPath() . 'runtime/log/form_mapper_debug.log';
            file_put_contents($logFile, date('Y-m-d H:i:s') . " 字段: {$field}, 类型: {$type}\n", FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . " 注释: " . json_encode($column['parsed_comment'], JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
            file_put_contents($logFile, date('Y-m-d H:i:s') . " 是否可编辑: " . ($this->isEditableField($column) ? 'true' : 'false') . "\n", FILE_APPEND);

            // 检查是否匹配 tinyint(1)
            if (preg_match('/^tinyint\(1\)/', $type)) {
                file_put_contents($logFile, date('Y-m-d H:i:s') . " {$field} 匹配到tinyint(1)，将返回switch组件\n", FILE_APPEND);
            }
        }
        
        // 如果不需要编辑，则返回null
        if (!$this->isEditableField($column)) {
            return null;
        }
        
        // 1. 先检查是否在注释中有明确的控件标记
        if (isset($column['parsed_comment']['tags']['control'])) {
            $controlType = $column['parsed_comment']['tags']['control'];
            if (isset($this->config[$controlType])) {
                return $this->getComponentConfig($controlType, $column);
            }
        }
        
        // 2. 检查是否有关联表设置
        if (isset($column['parsed_comment']['tags']['rel_table'])) {
            return $this->getComponentConfig('select', $column);
        }
        
        // 3. 检查是否有选项API设置
        if (isset($column['parsed_comment']['tags']['options'])) {
            return $this->getComponentConfig('select', $column);
        }
        
        // 4. 根据字段名称模式匹配
        foreach ($this->config['field_name_patterns'] as $pattern => $componentType) {
            if (stripos($fieldLower, $pattern) !== false) {
                return $this->getComponentConfig($componentType, $column);
            }
        }
        
        // 5. 根据字段类型匹配
        if (isset($this->config['field_type_patterns'][$type])) {
            $componentType = $this->config['field_type_patterns'][$type];
            return $this->getComponentConfig($componentType, $column);
        }
        
        // 6. 针对tinyint(1)类型特殊处理
        if (preg_match('/^tinyint\(1\)/', $type)) {
            return $this->getComponentConfig('switch', $column);
        }
        
        // 7. 针对枚举类型特殊处理
        if (preg_match('/^enum/', $type)) {
            return $this->getComponentConfig('select', $column);
        }
        
        // 8. 根据字段类型的前缀进行通用处理
        if (preg_match('/^(int|tinyint|smallint|mediumint|bigint)/', $type)) {
            if ($field === 'view_count' || $field === 'like_count' || $field === 'quantity' || $field === 'sort' || $field === 'order_num') {
                $logFile = app()->getRootPath() . 'runtime/log/form_mapper_debug.log';
                file_put_contents($logFile, date('Y-m-d H:i:s') . " {$field} 匹配到int类型，返回number组件\n", FILE_APPEND);
            }
            return $this->getComponentConfig('number', $column);
        }
        
        if (preg_match('/^(decimal|float|double)/', $type)) {
            return $this->getComponentConfig('number', $column);
        }
        
        if (preg_match('/^(text|mediumtext|longtext)/', $type)) {
            return $this->getComponentConfig('textarea', $column);
        }
        
        // 9. 默认返回文本输入框
        return $this->getComponentConfig('input', $column);
    }
    
    /**
     * 获取组件配置
     * 
     * @param string $componentType 组件类型
     * @param array $column 字段信息
     * @return array 组件配置
     */
    protected function getComponentConfig(string $componentType, array $column): array
    {
        if (!isset($this->config[$componentType])) {
            // 如果没有配置，返回默认输入框组件
            return [
                'component' => 'el-input',
                'props' => []
            ];
        }
        
        $config = $this->config[$componentType];
        $component = $config['component'];
        $props = $config['props'] ?? [];
        
        // 处理需要选项的组件
        if (isset($config['needOptions']) && $config['needOptions']) {
            $props = $this->getOptionsProps($props, $column, $config);
        }
        
        // 处理自定义属性
        $props = $this->getCustomProps($props, $column);
        
        return [
            'component' => $component,
            'props' => $props,
            'optionComponent' => $config['optionComponent'] ?? null
        ];
    }
    
    /**
     * 获取选项属性
     * 
     * @param array $props 原属性
     * @param array $column 字段信息
     * @param array $config 组件配置
     * @return array 新属性
     */
    protected function getOptionsProps(array $props, array $column, array $config): array
    {
        // 从字段注释中提取选项
        if (!empty($column['parsed_comment']['options'])) {
            $options = [];
            
            foreach ($column['parsed_comment']['options'] as $value => $label) {
                $options[] = [
                    'value' => $value,
                    'label' => $label
                ];
            }
            
            $props['options'] = $options;
        }
        // 从字段注释中提取选项来源
        elseif (isset($column['parsed_comment']['tags']['options'])) {
            $optionsSource = $column['parsed_comment']['tags']['options'];
            
            // 确保$optionsSource是字符串
            if (is_array($optionsSource)) {
                $optionsSource = implode(',', $optionsSource);
            }
            
            // 判断是否是API接口
            if (strpos($optionsSource, '/') !== false) {
                // 是API路径，生成一个函数调用
                $props['remoteOptions'] = [
                    'api' => $optionsSource,
                    'labelField' => $column['parsed_comment']['tags']['labelField'] ?? 'label',
                    'valueField' => $column['parsed_comment']['tags']['valueField'] ?? 'value'
                ];
            } else {
                // 可能是枚举表名或其他特定格式
                $props['dataSource'] = $optionsSource;
            }
        }
        // 从关联表获取选项
        elseif (isset($column['parsed_comment']['tags']['rel_table'])) {
            $relTable = $column['parsed_comment']['tags']['rel_table'];
            $labelField = $column['parsed_comment']['tags']['label_field'] ?? 'name';
            $valueField = $column['parsed_comment']['tags']['value_field'] ?? 'id';
            
            $props['relTableOptions'] = [
                'table' => $relTable,
                'labelField' => $labelField,
                'valueField' => $valueField
            ];
        }
        
        return $props;
    }
    
    /**
     * 获取自定义属性
     * 
     * @param array $props 原属性
     * @param array $column 字段信息
     * @return array 新属性
     */
    protected function getCustomProps(array $props, array $column): array
    {
        // 从字段注释的tags中提取自定义属性
        if (isset($column['parsed_comment']['tags'])) {
            $tags = $column['parsed_comment']['tags'];
            
            // 处理常见属性
            $propMappings = [
                'placeholder' => 'placeholder',
                'clearable' => 'clearable',
                'disabled' => 'disabled',
                'readonly' => 'readonly',
                'rows' => 'rows',
                'step' => 'step',
                'precision' => 'precision',
                'min' => 'min',
                'max' => 'max',
                'multiple' => 'multiple',
                'filterable' => 'filterable'
            ];
            
            foreach ($propMappings as $tagName => $propName) {
                if (isset($tags[$tagName])) {
                    $value = $tags[$tagName];
                    
                    // 处理布尔值
                    if ($value === 'true' || $value === true) {
                        $value = true;
                    } elseif ($value === 'false' || $value === false) {
                        $value = false;
                    }
                    
                    // 处理数字值
                    elseif (is_numeric($value)) {
                        // 确保值是字符串类型以便使用strpos
                        $valueStr = (string) $value;
                        if (strpos($valueStr, '.') !== false) {
                            $value = (float) $value;
                        } else {
                            $value = (int) $value;
                        }
                    }
                    
                    $props[$propName] = $value;
                }
            }
            
            // 为文本输入框添加长度限制
            if (isset($column['Character_maximum_length']) && !isset($props['maxlength'])) {
                $props['maxlength'] = (int) $column['Character_maximum_length'];
            }
            
            // 处理特定组件的属性
            if (isset($tags['format']) && strpos($props['component'] ?? '', 'date-picker') !== false) {
                $props['format'] = $tags['format'];
            }
        }
        
        return $props;
    }
    
    /**
     * 检查字段是否可编辑
     * 
     * @param array $column 字段信息
     * @return bool
     */
    protected function isEditableField(array $column): bool
    {
        // 主键通常不可编辑
        if ($column['Key'] === 'PRI') {
            return false;
        }
        
        // 自动更新的时间戳字段不可编辑
        $nonEditableFields = ['created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by'];
        if (in_array($column['Field'], $nonEditableFields)) {
            return false;
        }
        
        // 检查字段注释中是否有edit标记
        if (isset($column['parsed_comment']['tags']['edit'])) {
            return true;
        }
        
        // 检查字段注释中是否有noedit标记
        if (isset($column['parsed_comment']['tags']['noedit'])) {
            return false;
        }
        
        // 默认可编辑
        return true;
    }
    
    /**
     * 生成表单项代码
     * 
     * @param array $column 字段信息
     * @return string 表单项代码
     */
    public function generateFormItem(array $column): string
    {
        $componentInfo = $this->getFormComponent($column);
        if (!$componentInfo) {
            return '';
        }
        
        $field = $column['Field'];
        $label = $column['parsed_comment']['description'] ?: $field;
        $component = $componentInfo['component'];
        $props = $componentInfo['props'];
        $optionComponent = $componentInfo['optionComponent'] ?? null;
        
        // 生成属性字符串
        $propsStr = '';
        foreach ($props as $key => $value) {
            if (is_bool($value)) {
                if ($value) {
                    $propsStr .= " :{$key}=\"true\"";
                }
            } elseif (is_numeric($value)) {
                $propsStr .= " :{$key}=\"{$value}\"";
            } elseif (is_string($value)) {
                $propsStr .= " {$key}=\"{$value}\"";
            } elseif (is_array($value)) {
                $jsonValue = json_encode($value, JSON_UNESCAPED_UNICODE);
                $propsStr .= " :{$key}=\"{$jsonValue}\"";
            }
        }
        
        $code = "<el-form-item label=\"{$label}\" prop=\"{$field}\">\n";
        
        // 根据组件类型生成不同的表单控件代码
        if ($component === 'el-select') {
            if (!empty($props['remoteOptions'])) {
                // 远程API选项
                $remoteProps = array_diff_key($props, ['remoteOptions' => true]);
                $remotePropsStr = $this->getPropsString($remoteProps);
                
                $apiMethod = $props['remoteOptions']['api'];
                $labelField = $props['remoteOptions']['labelField'];
                $valueField = $props['remoteOptions']['valueField'];
                
                $code .= "  <{$component} v-model=\"formData.{$field}\"{$remotePropsStr} filterable>\n";
                $code .= "    <el-option\n";
                $code .= "      v-for=\"item in {$field}Options\"\n";
                $code .= "      :key=\"item.{$valueField}\"\n";
                $code .= "      :label=\"item.{$labelField}\"\n";
                $code .= "      :value=\"item.{$valueField}\"\n";
                $code .= "    />\n";
                $code .= "  </{$component}>\n";
                
                // 添加特殊注释，标记需要生成获取选项的代码
                $code .= "  <!-- @generate-api-options:{$field},{$apiMethod},{$labelField},{$valueField} -->\n";
            } else if (!empty($props['relTableOptions'])) {
                // 关联表选项
                $relProps = array_diff_key($props, ['relTableOptions' => true]);
                $relPropsStr = $this->getPropsString($relProps);
                
                $relTable = $props['relTableOptions']['table'];
                $labelField = $props['relTableOptions']['labelField'];
                $valueField = $props['relTableOptions']['valueField'];
                
                $code .= "  <{$component} v-model=\"formData.{$field}\"{$relPropsStr} filterable>\n";
                $code .= "    <el-option\n";
                $code .= "      v-for=\"item in {$field}Options\"\n";
                $code .= "      :key=\"item.{$valueField}\"\n";
                $code .= "      :label=\"item.{$labelField}\"\n";
                $code .= "      :value=\"item.{$valueField}\"\n";
                $code .= "    />\n";
                $code .= "  </{$component}>\n";
                
                // 添加特殊注释，标记需要生成获取关联表数据的代码
                $code .= "  <!-- @generate-rel-options:{$field},{$relTable},{$labelField},{$valueField} -->\n";
            } else if (!empty($props['options'])) {
                // 静态选项
                $code .= "  <{$component} v-model=\"formData.{$field}\"{$propsStr}>\n";
                foreach ($props['options'] as $option) {
                    $code .= "    <el-option :value=\"{$option['value']}\" label=\"{$option['label']}\" />\n";
                }
                $code .= "  </{$component}>\n";
            } else {
                // 无选项的下拉框
                $code .= "  <{$component} v-model=\"formData.{$field}\"{$propsStr} />\n";
            }
        } elseif ($component === 'el-radio-group' && !empty($props['options'])) {
            $code .= "  <{$component} v-model=\"formData.{$field}\"{$propsStr}>\n";
            foreach ($props['options'] as $option) {
                $code .= "    <{$optionComponent} :label=\"{$option['value']}\">{$option['label']}</{$optionComponent}>\n";
            }
            $code .= "  </{$component}>\n";
        } elseif ($component === 'el-checkbox-group' && !empty($props['options'])) {
            $code .= "  <{$component} v-model=\"formData.{$field}\"{$propsStr}>\n";
            foreach ($props['options'] as $option) {
                $code .= "    <{$optionComponent} :label=\"{$option['value']}\">{$option['label']}</{$optionComponent}>\n";
            }
            $code .= "  </{$component}>\n";
        } elseif ($component === 'el-upload') {
            $code .= "  <{$component}{$propsStr}>\n";
            if (isset($props['list-type']) && $props['list-type'] === 'picture-card') {
                $code .= "    <el-icon><Plus /></el-icon>\n";
            } else {
                $code .= "    <el-button type=\"primary\">选择文件</el-button>\n";
            }
            $code .= "  </{$component}>\n";
        } elseif ($component === 'rich-editor') {
            $code .= "  <{$component} v-model=\"formData.{$field}\"{$propsStr} />\n";
        } else {
            $code .= "  <{$component} v-model=\"formData.{$field}\"{$propsStr} />\n";
        }
        
        $code .= "</el-form-item>";
        
        return $code;
    }
    
    /**
     * 获取属性字符串
     * 
     * @param array $props 属性
     * @return string 属性字符串
     */
    protected function getPropsString(array $props): string
    {
        $propsStr = '';
        foreach ($props as $key => $value) {
            if (is_bool($value)) {
                if ($value) {
                    $propsStr .= " :{$key}=\"true\"";
                }
            } elseif (is_numeric($value)) {
                $propsStr .= " :{$key}=\"{$value}\"";
            } elseif (is_string($value)) {
                $propsStr .= " {$key}=\"{$value}\"";
            } elseif (is_array($value)) {
                $jsonValue = json_encode($value, JSON_UNESCAPED_UNICODE);
                $propsStr .= " :{$key}=\"{$jsonValue}\"";
            }
        }
        return $propsStr;
    }
} 