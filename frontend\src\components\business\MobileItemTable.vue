<template>
  <div class="mobile-item-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <ElButton type="primary" :disabled="readonly" @click="addItem" size="large">
          <ElIcon>
            <Plus />
          </ElIcon>
          添加明细
        </ElButton>
        <ElButton
          v-if="items.length > 0"
          type="danger"
          :disabled="readonly"
          @click="clearAll"
          size="large"
        >
          <ElIcon>
            <Delete />
          </ElIcon>
          清空
        </ElButton>
      </div>
      <div class="toolbar-right">
        <span class="item-count">共 {{ items.length }} 项</span>
      </div>
    </div>

    <!-- 移动端卡片列表 -->
    <div v-if="isMobile" class="mobile-card-list">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="mobile-card"
        :class="{ 'duplicate-item': item.isDuplicate }"
      >
        <div class="card-header">
          <span class="card-index">{{ index + 1 }}</span>
          <ElButton v-if="!readonly" type="danger" size="small" text @click="removeItem(index)">
            <ElIcon>
              <Delete />
            </ElIcon>
          </ElButton>
        </div>

        <div class="card-content">
          <!-- 供应商选择 -->
          <div class="form-row">
            <label class="form-label">供应商</label>
            <div class="form-control">
              <SupplierSelector
                v-if="!readonly"
                v-model="item.supplier_id"
                size="small"
                @change="onSupplierChange(item)"
              />
              <span v-else class="readonly-text">{{ getSupplierName(item.supplier_id) }}</span>
            </div>
          </div>

          <!-- 产品选择 -->
          <div class="form-row">
            <label class="form-label">产品</label>
            <div class="form-control">
              <ProductSelector
                v-if="!readonly"
                v-model="item.product_id"
                :supplier-id="item.supplier_id"
                size="small"
                @change="(_, product) => onProductChange(item, product)"
              />
              <span v-else class="readonly-text">{{ getProductName(item.product_id) }}</span>
            </div>
          </div>

          <!-- 数量和单价 -->
          <div class="form-row-group">
            <div class="form-row half">
              <label class="form-label">数量</label>
              <div class="form-control">
                <div v-if="!readonly" class="quantity-input-container">
                  <ElInputNumber
                    v-model="item.quantity"
                    :min="0"
                    :precision="2"
                    size="small"
                    style="width: 100%"
                    @change="calculateItemTotal(item)"
                  />
                  <span v-if="item.product_unit" class="unit-text">{{ item.product_unit }}</span>
                </div>
                <span v-else class="readonly-text">
                  {{ item.quantity || 0 }}
                  <span v-if="item.product_unit" class="unit-text">{{ item.product_unit }}</span>
                </span>
              </div>
            </div>
            <div class="form-row half">
              <label class="form-label">单价</label>
              <div class="form-control">
                <ElInputNumber
                  v-if="!readonly"
                  v-model="item.unit_price"
                  :min="0"
                  :precision="2"
                  size="small"
                  style="width: 100%"
                  @change="calculateItemTotal(item)"
                />
                <span v-else class="readonly-text">{{ (item.unit_price || 0).toFixed(2) }}元</span>
              </div>
            </div>
          </div>

          <!-- 小计 -->
          <div class="form-row">
            <label class="form-label">小计</label>
            <div class="form-control">
              <span class="total-amount">{{ (item.total_amount || 0).toFixed(2) }}元</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 桌面端表格 -->
    <div v-else class="desktop-table">
      <div class="table-container">
        <ElTable
          :data="items"
          border
          size="large"
          style="width: 100%"
          :max-height="500"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '14px 10px', fontSize: '16px', lineHeight: '1.5' }"
          :header-cell-style="{
            fontSize: '17px',
            fontWeight: '700',
            backgroundColor: '#f8f9fa',
            color: '#303133',
            textAlign: 'center'
          }"
          empty-text="暂无明细数据，请添加产品"
        >
          <ElTableColumn type="index" label="序号" width="80" align="center" />

          <ElTableColumn label="供应商" width="200">
            <template #default="{ row }">
              <SupplierSelector
                v-if="!readonly"
                v-model="row.supplier_id"
                size="default"
                style="width: 100%; font-size: 16px"
                @change="onSupplierChange(row)"
              />
              <span v-else class="readonly-cell-text secondary-text">{{
                getSupplierName(row.supplier_id)
              }}</span>
            </template>
          </ElTableColumn>

          <ElTableColumn label="产品" width="220">
            <template #default="{ row }">
              <ProductSelector
                v-if="!readonly"
                v-model="row.product_id"
                :supplier-id="row.supplier_id"
                size="default"
                style="width: 100%; font-size: 16px"
                @change="(_, product) => onProductChange(row, product)"
              />
              <span v-else class="readonly-cell-text primary-text">
                {{ getProductName(row.product_id) }}
              </span>
            </template>
          </ElTableColumn>

          <ElTableColumn label="数量">
            <template #default="{ row }">
              <div v-if="!readonly" class="quantity-input-container">
                <ElInputNumber
                  v-model="row.quantity"
                  :min="0"
                  :precision="3"
                  size="default"
                  style="width: 100%; font-size: 16px"
                  :controls="false"
                  @change="calculateItemTotal(row)"
                />
                <span v-if="row.product_unit" class="unit-text">{{ row.product_unit }}</span>
              </div>
              <span v-else class="readonly-cell-text primary-text">
                {{ row.quantity || 0 }}
                <span v-if="row.product_unit" class="unit-text">{{ row.product_unit }}</span>
              </span>
            </template>
          </ElTableColumn>

          <ElTableColumn label="单价">
            <template #default="{ row }">
              <ElInputNumber
                v-if="!readonly"
                v-model="row.unit_price"
                :min="0"
                :precision="2"
                size="default"
                style="width: 100%; font-size: 16px"
                :controls="false"
                @change="calculateItemTotal(row)"
              />
              <span v-else class="readonly-cell-text secondary-text">
                {{ (row.unit_price || 0).toFixed(2) }}元
              </span>
            </template>
          </ElTableColumn>

          <ElTableColumn label="小计">
            <template #default="{ row }">
              <span class="amount-cell primary-text"
                >{{ (row.total_amount || 0).toFixed(2) }}元</span
              >
            </template>
          </ElTableColumn>

          <ElTableColumn label="操作" width="100" fixed="right" v-if="!readonly">
            <template #default="{ $index }">
              <ElButton
                type="danger"
                size="default"
                @click="removeItem($index)"
                style="font-size: 14px; font-weight: 500"
              >
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="table-summary">
      <div class="summary-item">
        <span class="summary-label">总数量:</span>
        <span class="summary-value">{{ totalQuantity }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">总金额:</span>
        <span class="summary-value amount">¥{{ totalAmount.toFixed(2) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ElButton,
    ElIcon,
    ElTable,
    ElTableColumn,
    ElInputNumber,
    ElMessageBox
  } from 'element-plus'
  import { Plus, Delete } from '@element-plus/icons-vue'
  import SupplierSelector from './SupplierSelector.vue'
  import ProductSelector from './ProductSelector.vue'
  import { safeAdd, safeMultiply } from '@/utils/number'

  // 组件属性定义
  interface Props {
    modelValue: any[]
    readonly?: boolean
    itemTemplate?: () => any
  }

  // 组件事件定义
  interface Emits {
    (e: 'update:modelValue', value: any[]): void

    (e: 'change', items: any[]): void
  }

  const props = withDefaults(defineProps<Props>(), {
    readonly: false,
    itemTemplate: () => ({
      supplier_id: null,
      product_id: null,
      quantity: 0,
      unit_price: 0,
      total_amount: 0
    })
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const isMobile = ref(false)

  // 计算属性
  const items = computed({
    get: () => props.modelValue || [],
    set: (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }
  })

  const totalQuantity = computed(() => {
    return items.value.reduce((sum, item) => safeAdd(sum, item.quantity || 0), 0)
  })

  const totalAmount = computed(() => {
    return items.value.reduce((sum, item) => safeAdd(sum, item.total_amount || 0), 0)
  })

  // 方法
  const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768
  }

  const addItem = () => {
    const newItem = props.itemTemplate()
    items.value = [...items.value, newItem]
  }

  const removeItem = (index: number) => {
    items.value = items.value.filter((_, i) => i !== index)
  }

  const clearAll = async () => {
    try {
      await ElMessageBox.confirm('确定要清空所有明细吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      items.value = []
    } catch {
      // 用户取消
    }
  }

  const calculateItemTotal = (item: any) => {
    if (item.quantity && item.unit_price) {
      item.total_amount = safeMultiply(item.quantity, item.unit_price)
    } else {
      item.total_amount = 0
    }
  }

  const onSupplierChange = (item: any) => {
    // 供应商变化时清空产品选择
    item.product_id = null
    calculateItemTotal(item)
  }

  const onProductChange = (item: any, product?: any) => {
    if (product) {
      // 自动填充产品信息
      item.unit_price = product.price || 0
      item.product_unit = product.unit_name || ''
      item.product_name = product.label || product.name || ''
    }
    calculateItemTotal(item)
  }

  const getSupplierName = (supplierId: number | null) => {
    // 这里可以通过ref获取SupplierSelector的方法
    return supplierId ? `供应商${supplierId}` : '-'
  }

  const getProductName = (productId: number | null) => {
    // 这里可以通过ref获取ProductSelector的方法
    return productId ? `产品${productId}` : '-'
  }

  // 生命周期
  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      checkMobile()
      window.addEventListener('resize', checkMobile)
    })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })

  // 暴露方法给父组件
  defineExpose({
    addItem,
    removeItem,
    clearAll,
    totalQuantity,
    totalAmount
  })
</script>

<style scoped>
  .mobile-item-table {
    width: 100%;
  }

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 4px;
  }

  .toolbar-left {
    display: flex;
    gap: 8px;
  }

  .toolbar-right {
    color: #909399;
    font-size: 16px;
    font-weight: 500;
  }

  .item-count {
    font-weight: 600;
    color: #606266;
  }

  /* 移动端卡片样式 */
  .mobile-card-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .mobile-card {
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .mobile-card.duplicate-item {
    border-color: #f56c6c;
    background-color: #fef0f0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .card-index {
    font-weight: 600;
    color: #409eff;
    font-size: 16px;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .form-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .form-row-group {
    display: flex;
    gap: 12px;
  }

  .form-row.half {
    flex: 1;
  }

  .form-label {
    min-width: 70px;
    font-weight: 600;
    color: #606266;
    font-size: 16px;
  }

  .form-control {
    flex: 1;
  }

  .readonly-text {
    color: #606266;
    font-size: 16px;
    font-weight: 500;
  }

  .total-amount {
    font-weight: 700;
    color: #e6a23c;
    font-size: 18px;
  }

  /* 桌面端表格样式 */
  .desktop-table {
    width: 100%;
  }

  .table-container {
    width: 100%;
    overflow-x: auto;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 确保固定列正常工作 */
  :deep(.el-table) {
    .el-table__fixed-right {
      right: 0 !important;
    }

    .el-table__fixed-right-patch {
      right: 0 !important;
    }
  }

  /* 表格单元格适老化样式 */
  .readonly-cell-text {
    font-size: 15px;
    color: #606266;
    font-weight: 500;
    line-height: 1.5;
  }

  .amount-cell {
    font-size: 16px;
    color: #e6a23c;
    font-weight: 600;
  }

  /* 统计信息样式 */
  .table-summary {
    display: flex;
    justify-content: flex-end;
    gap: 32px;
    margin-top: 20px;
    padding: 16px 24px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .summary-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .summary-label {
    font-size: 16px;
    font-weight: 500;
    color: #606266;
  }

  .summary-value {
    font-size: 18px;
    font-weight: 700;
    color: #303133;
  }

  .summary-value.amount {
    color: #e6a23c;
    font-size: 20px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .table-toolbar {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;
    }

    .toolbar-left {
      justify-content: center;
    }

    .toolbar-right {
      text-align: center;
    }

    .form-row-group {
      flex-direction: column;
      gap: 8px;
    }

    .table-summary {
      flex-direction: column;
      gap: 8px;
      text-align: center;
    }
  }

  @media (max-width: 480px) {
    .mobile-card {
      padding: 16px;
    }

    .form-row {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .form-label {
      min-width: auto;
      font-size: 15px;
      font-weight: 600;
    }

    .summary-item {
      flex-direction: column;
      gap: 4px;
      text-align: center;
    }

    .summary-label {
      font-size: 15px;
    }

    .summary-value {
      font-size: 17px;
    }

    .summary-value.amount {
      font-size: 19px;
    }
  }

  /* 数量输入容器样式 */
  .quantity-input-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .unit-text {
    color: #909399;
    font-size: 14px;
    white-space: nowrap;
    margin-left: 4px;
  }

  /* 主次文字样式 */
  .primary-text {
    color: #303133;
    font-weight: 600;
  }

  .secondary-text {
    color: #606266;
    font-weight: 400;
  }
</style>
