<?php
declare(strict_types=1);

namespace app\common\core\base;

use app\common\core\traits\ResponseTrait;
use think\App;
use think\facade\Request;

/**
 * 控制器基类
 */
abstract class BaseController
{
    use ResponseTrait;
    
    /**
     * 应用实例
     * @var App
     */
    protected $app;
    
    /**
     * 请求实例
     * @var Request
     */
    protected $request;
    
    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;
    
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];
    
    /**
     * 构造方法
     * @param App $app 应用实例
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;
        
        // 控制器初始化
        $this->initialize();
    }
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        // 可在子类中重写此方法进行初始化操作
    }
    
    /**
     * 验证数据
     * @param array $data 数据
     * @param string|array $validate 验证器名或验证规则数组
     * @param array $message 提示信息
     * @param bool $batch 是否批量验证
     * @return array|string|true
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new \think\Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                list($validate, $scene) = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }
        $v->message($message);
        
        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }
        
        return $v->failException(false)->check($data);
    }
} 