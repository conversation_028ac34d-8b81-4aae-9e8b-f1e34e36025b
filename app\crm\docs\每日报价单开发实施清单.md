# 每日报价单开发实施清单

## 📋 项目概述
**项目名称**：每日报价单审批系统
**模块名称**：daily（独立模块）
**开发周期**：预计 8-10 个工作日（基于CRUD生成器）
**技术栈**：ThinkPHP8 + Vue3 + Element Plus + 现有工作流系统 + CRUD生成器
**团队配置**：后端开发 1人，前端开发 1人，测试 1人

## 🗓️ 开发计划（基于CRUD生成器）

### 第一阶段：数据库设计与CRUD生成 (1天)

#### ✅ 任务1.1：执行SQL和生成基础CRUD
**负责人**：后端开发
**预计时间**：0.5天
**任务内容**：
- [ ] 执行 `daily_price_approval_system_v4.sql` 创建数据表
- [ ] 执行CRUD生成器命令生成基础代码
- [ ] 验证生成的模型、控制器、服务类
- [ ] 测试基础CRUD接口功能

**生成器命令**：
```bash
# 生成报价单主表CRUD
php think generator:crud daily_price_order --module=daily --frontend --overwrite

# 生成报价明细表CRUD
php think generator:crud daily_price_item --module=daily --frontend --overwrite

# 生成历史记录表CRUD
php think generator:crud daily_price_history --module=daily --frontend --overwrite
```

**验收标准**：
- 数据表创建成功，索引正常
- 基础CRUD代码生成完整
- 模型关联关系正确
- 基础接口测试通过

#### ✅ 任务1.2：工作流配置
**负责人**：后端开发
**预计时间**：0.5天
**任务内容**：
- [ ] 配置工作流类型 `crm_daily_price_order`
- [ ] 测试工作流基础功能
- [ ] 验证审批状态字段

**配置SQL**：
```sql
INSERT INTO workflow_type (code, name, description, status, created_at)
VALUES ('daily_price_order', '每日报价审批', '每日报价单审批流程', 1, NOW());
```

### 第二阶段：业务逻辑开发 (2-3天)

#### ✅ 任务2.1：工作流服务开发
**负责人**：后端开发
**预计时间**：1.5天
**任务内容**：
- [ ] 创建 `CrmDailyPriceOrderWorkflowService` 继承 `WorkflowableService`
- [ ] 实现审批前验证逻辑（价格合理性、重复提交等）
- [ ] 实现审批结果处理逻辑（通过、拒绝、终止、作废）
- [ ] 实现价格历史记录生成
- [ ] 实现审批通知功能
- [ ] 测试完整审批流程

**核心方法**：
```php
- validateForApproval()      # 审批前验证
- afterApprovalComplete()    # 审批完成后处理
- generatePriceHistory()     # 生成历史记录
- handleOrderApproved()      # 处理审批通过
- handleOrderRejected()      # 处理审批拒绝
```

#### ✅ 任务2.2：业务服务扩展
**负责人**：后端开发
**预计时间**：1天
**任务内容**：
- [ ] 扩展生成的 `CrmDailyPriceOrderService`
- [ ] 实现报价单编号生成逻辑
- [ ] 实现明细批量保存功能
- [ ] 实现从昨日复制功能
- [ ] 实现价格变动计算
- [ ] 实现统计数据查询

**核心方法**：
```php
- createOrderWithDefaults()  # 创建报价单
- saveItems()               # 保存明细
- copyFromYesterday()       # 从昨日复制
- generateOrderNumber()     # 生成编号
- getDateStatistics()       # 获取统计
```

#### ✅ 任务2.3：控制器扩展
**负责人**：后端开发
**预计时间**：0.5天
**任务内容**：
- [ ] 扩展生成的 `CrmDailyPriceOrderController`
- [ ] 添加审批相关接口
- [ ] 添加明细保存接口
- [ ] 添加复制和导出接口
- [ ] 添加统计查询接口
- [ ] 完善异常处理和日志记录

**扩展接口**：
```
POST   /crm/daily-price-order/{id}/submit-approval    # 提交审批
POST   /crm/daily-price-order/{id}/withdraw-approval  # 撤回审批
POST   /crm/daily-price-order/{id}/void               # 作废
POST   /crm/daily-price-order/{id}/items              # 保存明细
POST   /crm/daily-price-order/copy-yesterday          # 从昨日复制
GET    /crm/daily-price-order/{id}/export             # 导出
GET    /crm/daily-price-order/statistics              # 获取统计
```

### 第三阶段：前端页面开发 (3-4天)

#### ✅ 任务3.1：基于生成器页面的扩展
**负责人**：前端开发
**预计时间**：1天
**任务内容**：
- [ ] 基于生成器生成的基础页面进行扩展
- [ ] 扩展列表页，添加审批状态筛选和操作按钮
- [ ] 扩展表单页，集成审批状态展示和明细编辑
- [ ] 扩展详情页，添加审批信息和操作历史
- [ ] 配置路由和权限

**页面扩展重点**：
```
- 列表页：审批状态标签、批量操作、快速筛选
- 表单页：审批状态卡片、明细表格、操作按钮组
- 详情页：审批进度、操作历史、导出功能
```

#### ✅ 任务3.2：核心组件开发
**负责人**：前端开发
**预计时间**：2天
**任务内容**：
- [ ] 创建 `ApprovalStatusCard` 审批状态展示组件
- [ ] 创建 `PriceItemTable` 明细表格编辑组件
- [ ] 创建 `PriceChangeIndicator` 价格变动指示器
- [ ] 创建 `ProductSupplierSelector` 产品供应商选择器
- [ ] 创建 `PriceTrendDialog` 价格趋势弹窗

**组件功能重点**：
```
- ApprovalStatusCard：状态展示、进度条、操作按钮
- PriceItemTable：内联编辑、价格计算、批量操作
- PriceChangeIndicator：涨跌标识、颜色区分、图标显示
- ProductSupplierSelector：联动选择、搜索过滤
- PriceTrendDialog：图表展示、数据统计
```

#### ✅ 任务3.3：状态管理与API集成
**负责人**：前端开发
**预计时间**：1天
**任务内容**：
- [ ] 创建 `useDailyPriceOrderStore` Pinia Store
- [ ] 封装审批相关API接口
- [ ] 实现状态驱动的UI更新
- [ ] 添加操作权限控制
- [ ] 实现实时数据同步

**状态管理重点**：
```javascript
// 核心状态
- currentOrder: 当前报价单
- approvalStatus: 审批状态枚举
- canEdit/canSubmit/canWithdraw: 操作权限计算
- priceItems: 明细数据
- statistics: 统计信息

// 核心方法
- submitApproval(): 提交审批
- withdrawApproval(): 撤回审批
- saveItems(): 保存明细
- copyFromYesterday(): 从昨日复制
```

### 第四阶段：功能完善与优化 (2-3天)

#### ✅ 任务4.1：审批流程完善
**负责人**：后端开发  
**预计时间**：1天  
**任务内容**：
- [ ] 完善审批状态处理逻辑
- [ ] 实现数据清理机制
- [ ] 添加审批通知功能
- [ ] 优化审批性能
- [ ] 添加审批日志

#### ✅ 任务4.2：UI/UX优化
**负责人**：前端开发  
**预计时间**：1天  
**任务内容**：
- [ ] 优化表单交互体验
- [ ] 完善状态展示效果
- [ ] 添加操作确认提示
- [ ] 实现响应式布局
- [ ] 优化加载和错误状态

#### ✅ 任务4.3：导出功能开发
**负责人**：后端开发 + 前端开发  
**预计时间**：1天  
**任务内容**：
- [ ] 实现Excel导出功能
- [ ] 设计导出模板格式
- [ ] 添加导出权限控制
- [ ] 实现前端下载功能
- [ ] 测试导出功能

### 第五阶段：测试与部署 (2天)

#### ✅ 任务5.1：集成测试
**负责人**：测试 + 开发团队  
**预计时间**：1天  
**任务内容**：
- [ ] 功能测试：所有业务流程
- [ ] 兼容性测试：不同浏览器
- [ ] 性能测试：并发和响应时间
- [ ] 安全测试：权限和数据安全
- [ ] 用户体验测试

#### ✅ 任务5.2：部署上线
**负责人**：运维 + 开发团队  
**预计时间**：1天  
**任务内容**：
- [ ] 生产环境数据库迁移
- [ ] 后端代码部署
- [ ] 前端代码构建部署
- [ ] 配置监控和日志
- [ ] 验证线上功能

## 🔧 开发规范

### 代码规范
- **后端**：遵循 PSR-12 编码规范
- **前端**：使用 ESLint + Prettier
- **数据库**：遵循命名规范，添加必要注释
- **API**：遵循 RESTful 设计原则

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
```
main          # 主分支，用于生产环境
develop       # 开发分支
feature/*     # 功能分支
hotfix/*      # 热修复分支
```

## 📊 质量检查清单

### 功能检查
- [ ] 报价单创建、编辑、删除功能正常
- [ ] 产品明细添加、修改、删除功能正常
- [ ] 审批流程：提交、撤回、通过、拒绝功能正常
- [ ] 状态展示和权限控制正确
- [ ] 导出功能正常
- [ ] 价格计算和统计准确

### 性能检查
- [ ] 页面加载时间 < 2秒
- [ ] API响应时间 < 500ms
- [ ] 数据库查询优化
- [ ] 前端资源压缩

### 安全检查
- [ ] 权限验证完整
- [ ] 数据验证严格
- [ ] SQL注入防护
- [ ] XSS攻击防护

### 兼容性检查
- [ ] Chrome 90+
- [ ] Firefox 88+
- [ ] Safari 14+
- [ ] Edge 90+

## 🚨 风险控制

### 技术风险
- **工作流集成复杂度**：提前熟悉现有工作流API
- **数据一致性**：使用事务确保数据完整性
- **性能问题**：合理设计索引，优化查询

### 进度风险
- **需求变更**：及时沟通，控制变更范围
- **技术难点**：提前技术预研，寻求技术支持
- **资源冲突**：合理安排开发资源

### 质量风险
- **测试不充分**：制定详细测试计划
- **代码质量**：代码审查，自动化测试
- **用户体验**：用户测试，及时反馈

## 📞 联系方式

**项目经理**：[姓名] - [邮箱] - [电话]  
**后端负责人**：[姓名] - [邮箱] - [电话]  
**前端负责人**：[姓名] - [邮箱] - [电话]  
**测试负责人**：[姓名] - [邮箱] - [电话]

## 📝 备注

1. 每日站会时间：上午 9:30
2. 代码审查：每个功能完成后进行
3. 测试环境：[测试环境地址]
4. 文档地址：[文档链接]
5. 项目仓库：[Git仓库地址]

---

**最后更新时间**：2025-07-21  
**文档版本**：v1.0  
**审核状态**：待审核
