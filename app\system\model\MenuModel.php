<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 菜单模型
 */
class MenuModel extends BaseModel
{
	
	/**
	 * 表名
	 *
	 * @var string
	 */
	protected $name = 'system_menu';
	
	protected bool $enableTenantIsolation = false;
	
	// 菜单类型：目录
	const TYPE_DIR = 0;
	
	// 菜单类型：菜单
	const TYPE_MENU = 1;
	
	// 菜单类型：按钮
	const TYPE_BUTTON = 2;
	
	// 设置字段信息
	protected $schema = [
		'id'             => 'int',
		'parent_id'      => 'int',
		'title'          => 'string',
		'name'           => 'string',
		'permission'     => 'string',
		'path'           => 'string',
		'component'      => 'string',
		'type'           => 'int',
		'icon'           => 'string',
		'sort'           => 'int',
		'external'       => 'int',
		'keep_alive'     => 'int',
		'visible'        => 'int',
		'status'         => 'int',
		'remark'         => 'string',
		'creator_id'     => 'int',
		'created_at'     => 'datetime',
		'updated_at'     => 'datetime',
		'deleted_at'     => 'datetime',
	];
	
	public function getIconAttr($value)
	{
		return !empty($value) ? html_entity_decode($value) : '';
	}
	
	/**
	 * 角色菜单关联
	 *
	 * @return \think\model\relation\HasMany
	 */
	public function  menus()
	{
		return $this->hasMany(RoleMenuModel::class, 'menu_id', 'id');
	}
	
	// 检查菜单状态是否正常
	public function isActive(): bool
	{
		return $this->status === 1;
	}
	
	// 检查是否为目录
	public function isDirectory(): bool
	{
		return $this->type === self::TYPE_DIR;
	}
	
	// 检查是否为菜单
	public function isMenu(): bool
	{
		return $this->type === self::TYPE_MENU;
	}
	
	// 检查是否为按钮
	public function isButton(): bool
	{
		return $this->type === self::TYPE_BUTTON;
	}
	
	// 检查是否可见
	public function isVisible(): bool
	{
		return $this->visible === 1;
	}
	
	// 检查是否为外链
	public function isExternal(): bool
	{
		return $this->external === 1;
	}
	
	// 检查是否缓存
	public function isKeepAlive(): bool
	{
		return $this->keep_alive === 1;
	}
	
	// 获取菜单树结构
	public static function getMenuTree($roleIds = null)
	{
		$query = self::where('status', 1);
		
		// 如果指定了角色ID，则只查询这些角色有权限的菜单
		if ($roleIds !== null && !empty($roleIds)) {
			$menuIds = RoleMenuModel::where('role_id', 'in', $roleIds)
			                        ->column('menu_id');
			
			if (empty($menuIds)) {
				return [];
			}
			
			$query->where('id', 'in', $menuIds);
		}
		
		// 先查询所有菜单
		$menus = $query->order('sort', 'asc')
		               ->select()
		               ->toArray();
		
		// 构建树结构
		return self::buildMenuTree($menus);
	}
	
	// 构建树结构
	protected static function buildMenuTree(array $menus, $parentId = 0)
	{
		$tree = [];
		
		foreach ($menus as $menu) {
			if ($menu['parent_id'] == $parentId) {
				$children = self::buildMenuTree($menus, $menu['id']);
				
				if (!empty($children)) {
					$menu['children'] = $children;
				}
				
				$tree[] = $menu;
			}
		}
		
		return $tree;
	}
} 