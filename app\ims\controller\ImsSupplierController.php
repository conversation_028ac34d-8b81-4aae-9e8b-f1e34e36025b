<?php
declare(strict_types=1);

namespace app\ims\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\ims\service\ImsSupplierService;

/**
 * 供应商表控制器
 */
class ImsSupplierController extends BaseController
{
    use CrudControllerTrait, ControllerImportExportTrait;
    /**
     * @var ImsSupplierService
     */
    protected $service;

    /**
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();

        // 使用单例模式获取Service实例
        $this->service = ImsSupplierService::getInstance();
    }

    /**
     * 状态切换
     */
    public function status($id)
    {
        $status = $this->request->post('status');
        $result = $this->service->updateField($id, 'status', $status);
        return $this->success('状态更新成功', $result);
    }

    /**
     * 删除供应商（重写以处理关联检查）
     */
    public function delete($id)
    {
        try {
            $result = $this->service->delete($id);
            return $this->success('删除成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除供应商
     */
    public function batchDelete()
    {
        $ids = $this->request->post('ids', []);
        if (empty($ids)) {
            return $this->error('请选择要删除的数据');
        }

        try {
            $result = $this->service->batchDelete($ids);
            return $this->success('批量删除成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取供应商选项
     */
    public function options()
    {
        $options = $this->service->getOptions();
        return $this->success('获取成功', $options);
    }

}