<?php
declare(strict_types=1);

namespace app\crm\service;

use app\crm\model\CrmCustomer;
use think\facade\Db;

/**
 * 客户权限验证服务
 */
class CustomerPermissionService
{
    /**
     * 验证客户数据访问权限
     * 
     * @param int $customerId 客户ID
     * @param int $userId 用户ID
     * @return bool
     */
    public function validateCustomerAccess(int $customerId, int $userId): bool
    {
        // 1. 超级管理员
        if (is_super_admin() || is_tenant_super_admin()) {
            return true;
        }
        
        // 2. 获取客户信息
        $customer = CrmCustomer::find($customerId);
        if (!$customer) {
            return false;
        }
        
        // 3. 客户负责人 (创建人=负责人)
        if ($customer['creator_id'] == $userId) {
            return true;
        }
        
        // 4. 数据权限范围检查 (预留)
        // TODO: 实现部门数据权限、共享权限等
        // if ($this->inDataPermissionScope($customer['creator_id'], $userId)) {
        //     return in_array($operation, ['view', 'follow']);
        // }
        
        return false;
    }
    
    /**
     * 验证联系人访问权限
     * 
     * @param int $contactId 联系人ID
     * @param int $userId 用户ID
     * @return bool
     */
    public function validateContactAccess(int $contactId, int $userId): bool
    {
        // 通过联系人获取客户ID，然后验证客户权限
        $contact = Db::name('crm_contact')->find($contactId);
        if (!$contact) {
            return false;
        }
        
        return $this->validateCustomerAccess($contact['customer_id'], $userId);
    }
    
    /**
     * 验证合同访问权限
     * 
     * @param int $contractId 合同ID
     * @param int $userId 用户ID
     * @return bool
     */
    public function validateContractAccess(int $contractId, int $userId): bool
    {
        // 通过合同获取客户ID，然后验证客户权限
        $contract = Db::name('crm_contract')->find($contractId);
        if (!$contract) {
            return false;
        }
        
        return $this->validateCustomerAccess($contract['customer_id'], $userId);
    }
    
    /**
     * 验证回款访问权限
     * 
     * @param int $receivableId 回款ID
     * @param int $userId 用户ID
     * @return bool
     */
    public function validateReceivableAccess(int $receivableId, int $userId): bool
    {
        // 通过回款获取合同ID，再获取客户ID，然后验证客户权限
        $receivable = Db::name('crm_receivable')->find($receivableId);
        if (!$receivable) {
            return false;
        }
        
        $contract = Db::name('crm_contract')->find($receivable['contract_id']);
        if (!$contract) {
            return false;
        }
        
        return $this->validateCustomerAccess($contract['customer_id'], $userId);
    }
    
    /**
     * 验证跟进记录访问权限
     * 
     * @param int $followId 跟进记录ID
     * @param int $userId 用户ID
     * @return bool
     */
    public function validateFollowAccess(int $followId, int $userId): bool
    {
        // 通过跟进记录获取客户ID，然后验证客户权限
        $follow = Db::name('crm_follow_record')->find($followId);
        if (!$follow) {
            return false;
        }
        
        return $this->validateCustomerAccess($follow['customer_id'], $userId);
    }
    
    /**
     * 验证记录创建权限
     * 
     * @param int $recordId 记录ID
     * @param int $userId 用户ID
     * @param string $table 表名
     * @return bool
     */
    public function validateRecordOwnership(int $recordId, int $userId, string $table): bool
    {
        // 超级管理员
        if (is_super_admin() || is_tenant_super_admin()) {
            return true;
        }
        
        $record = Db::name($table)->find($recordId);
        if (!$record) {
            return false;
        }
        
        // 记录创建人可以编辑/删除
        return $record['creator_id'] == $userId;
    }
    
    /**
     * 检查是否在数据权限范围内 (预留)
     * 
     * @param int $targetUserId 目标用户ID
     * @param int $currentUserId 当前用户ID
     * @return bool
     */
    private function inDataPermissionScope(int $targetUserId, int $currentUserId): bool
    {
        // TODO: 实现部门数据权限逻辑
        // 1. 获取当前用户的数据权限范围
        // 2. 检查目标用户是否在权限范围内
        // 3. 支持：本人、本部门、本部门及下级部门、全部等
        
        return false;
    }
}
