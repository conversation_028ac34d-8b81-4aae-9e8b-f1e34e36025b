<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 商机产品关联表模型 - 基于crm_data.sql表结构
 */
class CrmBusinessProduct extends BaseModel
{
	use CreatorTrait;

    // 设置表名
    protected $name = 'crm_business_product';

    // 字段类型转换
    protected $type = [
        'business_id' => 'integer',
        'product_spec_id' => 'integer',
        'quantity' => 'float',
        'unit_price' => 'float',
        'discount_rate' => 'float',
        'discount_amount' => 'float',
        'subtotal' => 'float',
    ];

    /**
     * 关联产品规格
     */
    public function productSpec()
    {
        return $this->belongsTo(CrmProductSpec::class, 'product_spec_id', 'id');
    }

    /**
     * 关联产品（通过规格）
     */
    public function product()
    {
        return $this->hasOneThrough(
            CrmProduct::class,
            CrmProductSpec::class,
            'id',           // 规格表主键
            'id',           // 产品表主键
            'product_spec_id', // 本表外键
            'product_id'    // 规格表外键
        );
    }

    /**
     * 关联商机
     */
    public function business()
    {
        return $this->belongsTo(CrmBusiness::class, 'business_id', 'id');
    }

    public function getImpSceneFields(): array
    {
        return [
            'business_id' => [
                'label' => '商机ID',
                'type'  => 'number',
            ],
            'product_spec_id' => [
                'label' => '产品规格ID',
                'type'  => 'number',
            ],
            'spec_info' => [
                'label' => '规格信息快照',
                'type'  => 'text',
            ],
            'quantity' => [
                'label' => '数量',
                'type'  => 'number',
            ],
            'unit_price' => [
                'label' => '成交单价',
                'type'  => 'number',
            ],
            'discount_rate' => [
                'label' => '折扣率(%)',
                'type'  => 'number',
            ],
            'discount_amount' => [
                'label' => '折扣金额',
                'type'  => 'number',
            ],
            'subtotal' => [
                'label' => '小计金额',
                'type'  => 'number',
            ]
        ];
    }
}