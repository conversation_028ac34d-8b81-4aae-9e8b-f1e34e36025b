<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\crm\model\CrmLead;
use app\crm\model\CrmLeadAssignment;
use app\system\model\AdminModel;
use think\facade\Db;
use think\facade\Log;

/**
 * 线索池专用服务类
 * 专门处理线索池相关业务，不受数据权限限制
 */
class CrmLeadPoolService extends BaseService
{
	/**
	 * 线索分配记录服务
	 */
	protected CrmLeadAssignmentService $assignmentService;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmLead();
		parent::__construct();
		
		$this->assignmentService = CrmLeadAssignmentService::getInstance();
	}
	
	/**
	 * 初始化配置
	 */
	protected function initialize(): void
	{
		// 线索池不需要数据权限过滤
		$this->crudService->setEnableDataPermission(false);
		
		// 设置字段场景
		$this->crudService->setFieldScenes([
			'list'   => [
				'id',
				'lead_name',
				'company',
				'position',
				'mobile',
				'phone',
				'email',
				'status',
				'source',
				'level',
				'industry',
				'address',
				'remark',
				'created_at',
				'creator_id'
			],
			'detail' => ['*'],
			'select' => [
				'id',
				'lead_name',
				'company'
			]
		]);
	}
	
	/**
	 * 获取线索池列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getPoolList(array $params): array
	{
		// 构建查询条件
		$where = [];
		
		// 线索池基础条件：在线索池中
		$where[] = [
			'in_pool',
			'=',
			1
		];
		
		// 关键词搜索 - 支持线索姓名和公司名称
		if (!empty($params['key_word'])) {
			$keyword = trim($params['key_word']);
			$where[] = [
				'lead_name|company',
				'like',
				"%{$keyword}%"
			];
		}
		
		// 手机号搜索
		if (!empty($params['mobile'])) {
			$where[] = [
				'mobile',
				'like',
				"%{$params['mobile']}%"
			];
		}
		
		// 优先级别搜索
		if (isset($params['level']) && $params['level'] !== '') {
			$where[] = [
				'level',
				'=',
				(int)$params['level']
			];
		}
		
		// 状态搜索
		if (isset($params['status']) && $params['status'] !== '') {
			$where[] = [
				'status',
				'=',
				(int)$params['status']
			];
		}
		
		// 来源搜索
		if (!empty($params['source'])) {
			$where[] = [
				'source',
				'=',
				$params['source']
			];
		}
		
		// 行业搜索
		if (!empty($params['industry'])) {
			$where[] = [
				'industry',
				'=',
				$params['industry']
			];
		}
		
		// 分页参数
		$page  = (int)($params['page'] ?? 1);
		$limit = (int)($params['limit'] ?? 10);
		$page  = max(1, min($page, 1000));
		$limit = max(1, min($limit, 100));
		
		// 排序
		$order = ['created_at' => 'desc'];
		if (!empty($params['sort_field'])) {
			$sortField = $params['sort_field'];
			$sortOrder = strtolower($params['sort_order'] ?? 'desc') === 'asc'
				? 'asc'
				: 'desc';
			$order     = [$sortField => $sortOrder];
		}
		
		// 获取总数
		$total = $this->model->where($where)
		                     ->count();
		
		// 获取分页数据
		$list = $this->model->where($where)
		                    ->order($order)
		                    ->page($page, $limit)
		                    ->select();
		
		return [
			'list'  => $list,
			'total' => $total,
			'page'  => $page,
			'limit' => $limit
		];
	}
	
	/**
	 * 认领线索
	 *
	 * @param int $leadId 线索ID
	 * @return bool
	 */
	public function claimLead(int $leadId): bool
	{
		$userId = get_user_id();
		
		// 开启事务
		Db::startTrans();
		
		try {
			// 1. 验证线索是否可认领
			$this->validateLeadForClaim($leadId);
			
			// 2. 更新线索状态
			$this->updateLeadOwner($leadId, $userId);
			
			// 3. 记录分配历史
			$this->recordAssignment($leadId, 0, $userId, 3, '用户认领');
			
			// 提交事务
			Db::commit();
			
			return true;
			
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('认领线索失败', [
				'lead_id' => $leadId,
				'user_id' => $userId,
				'error'   => $e->getMessage()
			]);
			
			return false;
		}
	}
	
	// todo 回收线索，暂时去掉线索池，快速上线即可！
	
	/**
	 * 分配线索
	 *
	 * @param int    $leadId   线索ID
	 * @param int    $toUserId 目标用户ID
	 * @param string $reason   分配原因
	 * @return bool
	 */
	public function assignLead(int $leadId, int $toUserId, string $reason = ''): bool
	{
		// 开启事务
		Db::startTrans();
		
		try {
			// 1. 验证线索是否可分配
			$leadInfo = $this->validateLeadForAssign($leadId);
			
			// 查询$toUserId是否存在
			$userInfo = AdminModel::where('id', $toUserId)
			                      ->findOrEmpty('id');
			
			if ($userInfo->isEmpty()) {
				throw new BusinessException('分配的用户不存在');
			}
			
			if ($userInfo->getData('status') != 1) {
				throw new BusinessException('分配的用户已禁用');
			}
			
			// 2. 更新线索状态
			$this->updateLeadOwner($leadId, $toUserId);
			
			// 3. 记录分配历史
			$this->recordAssignment($leadId, $leadInfo['owner_user_id'], $toUserId, 1, $reason
				?: '管理员分配');
			
			// 提交事务
			Db::commit();
			
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('分配线索失败', [
				'lead_id'    => $leadId,
				'to_user_id' => $toUserId,
				'error'      => $e->getMessage()
			]);
			
			return false;
		}
	}
	
	/**
	 * 验证线索是否可认领
	 *
	 * @param int $leadId 线索ID
	 * @return array
	 * @throws BusinessException
	 */
	private function validateLeadForClaim(int $leadId): array
	{
		$lead = $this->getOne(['id' => $leadId]);
		
		if (!$lead) {
			throw new BusinessException('线索不存在');
		}
		
		if ($lead['in_pool'] != 1) {
			throw new BusinessException('线索不在线索池中');
		}
		
		return $lead->toArray();
	}
	
	/**
	 * 验证线索是否可分配
	 *
	 * @param int $leadId 线索ID
	 * @return array
	 * @throws BusinessException
	 */
	private function validateLeadForAssign(int $leadId): array
	{
		$lead = $this->getOne(['id' => $leadId]);
		
		if (!$lead) {
			throw new BusinessException('线索不存在');
		}
		
		return $lead->toArray();
	}
	
	/**
	 * 更新线索负责人
	 *
	 * @param int $leadId 线索ID
	 * @param int $userId 用户ID
	 */
	private function updateLeadOwner(int $leadId, int $userId): void
	{
		$res = $this->edit([
			'owner_user_id' => $userId,
			'in_pool'       => 0,
			// 移出线索池
		], ['id' => $leadId]);
		if (!$res) {
			throw new BusinessException('更新线索负责人失败');
		}
	}
	
	/**
	 * 记录分配历史
	 *
	 * @param int    $leadId         线索ID
	 * @param int    $fromUserId     原负责人ID
	 * @param int    $toUserId       新负责人ID
	 * @param int    $assignmentType 分配类型
	 * @param string $reason         分配原因
	 */
	private function recordAssignment(
		int $leadId, int $fromUserId, int $toUserId, int $assignmentType, string $reason = ''
	): void
	{
		
		$res = $this->assignmentService->add([
			'lead_id'         => $leadId,
			'from_user_id'    => $fromUserId,
			'to_user_id'      => $toUserId,
			'assignment_type' => $assignmentType,
			'reason'          => $reason
		]);
		
		if (!$res) {
			throw new BusinessException('记录分配历史失败');
		}
	}
}
