<?php
declare(strict_types=1);

namespace app\system\controller;

use app\common\core\base\BaseController;
use app\common\service\TenantSwitchService;
use think\response\Json;

/**
 * 租户切换控制器
 * 为系统超级管理员提供租户切换功能
 */
class TenantSwitchController extends BaseController
{
    protected TenantSwitchService $tenantSwitchService;
    
    public function initialize()
    {
        $this->tenantSwitchService = app(TenantSwitchService::class);
		parent::initialize();
    }
    
    /**
     * 获取当前工作模式和租户信息
     */
    public function getCurrentStatus(): Json
    {
        if (!is_super_admin()) {
            return $this->error('只有系统超级管理员才能使用租户切换功能');
        }
        
        $data = [
            'work_mode' => $this->tenantSwitchService->getCurrentMode(),
            'switched_tenant_id' => $this->tenantSwitchService->getCurrentSwitchedTenantId(),
            'current_tenant_info' => $this->tenantSwitchService->getCurrentTenantInfo(),
            'permission_context' => $this->tenantSwitchService->getPermissionContext()
        ];
        
        return $this->success('获取成功', $data);
    }
    
    /**
     * 获取可切换的租户列表
     */
    public function getAvailableTenants(): Json
    {
        if (!is_super_admin()) {
            return $this->error('只有系统超级管理员才能查看租户列表');
        }
        
        $tenants = $this->tenantSwitchService->getAvailableTenants();
        
        return $this->success('获取成功', $tenants);
    }
    
    /**
     * 切换到系统管理模式
     */
    public function switchToSystemMode(): Json
    {
        try {
            $this->tenantSwitchService->switchToSystemMode();
            return $this->success('已切换到系统管理模式');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 切换到租户管理模式
     */
    public function switchToTenantMode(): Json
    {
        $tenantId = (int)$this->request->post('tenant_id', 0);
        
        if ($tenantId <= 0) {
            return $this->error('请选择要切换的租户');
        }
        
        try {
            $this->tenantSwitchService->switchToTenantMode($tenantId);
            
            // 获取切换后的租户信息
            $tenantInfo = $this->tenantSwitchService->getCurrentTenantInfo();
            
            return $this->success('已切换到租户管理模式', [
                'tenant_info' => $tenantInfo
            ]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 清除租户切换缓存
     */
    public function clearCache(): Json
    {
        if (!is_super_admin()) {
            return $this->error('权限不足');
        }
        
        $tenantId = (int)$this->request->post('tenant_id', 0);
        
        try {
            $this->tenantSwitchService->clearCache($tenantId);
            return $this->success('缓存清除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取租户切换历史记录
     */
    public function getSwitchHistory(): Json
    {
        if (!is_super_admin()) {
            return $this->error('权限不足');
        }
        
        // 这里可以实现切换历史记录的查询
        // 暂时返回空数组，后续可以扩展
        $history = [];
        
        return $this->success('获取成功', $history);
    }
}
