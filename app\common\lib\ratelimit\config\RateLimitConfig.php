<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\config;

use think\facade\Cache;
use think\facade\Db;
use app\common\lib\ratelimit\algorithm\AlgorithmInterface;
use app\common\lib\ratelimit\algorithm\FixedWindowAlgorithm;
use app\common\lib\ratelimit\algorithm\SlidingWindowAlgorithm;
use app\common\lib\ratelimit\identifier\IdentifierInterface;
use app\common\lib\ratelimit\identifier\IpIdentifier;
use app\common\lib\ratelimit\identifier\UserIdentifier;
use app\common\lib\ratelimit\identifier\TenantIdentifier;
use app\common\lib\ratelimit\rule\GlobalRule;
use app\common\lib\ratelimit\rule\TenantRule;
use app\common\lib\ratelimit\rule\UserRule;
use app\common\lib\ratelimit\rule\Rule;
use app\common\lib\ratelimit\storage\StorageInterface;
use app\common\lib\ratelimit\storage\RedisStorage;

/**
 * 限流配置管理类
 */
class RateLimitConfig
{
    /**
     * Redis存储
     */
    protected StorageInterface $storage;
    
    /**
     * 缓存TTL（秒）
     */
    protected int $cacheTtl = 300;
    
    /**
     * 构造函数
     *
     * @param StorageInterface|null $storage 存储适配器
     */
    public function __construct(?StorageInterface $storage = null)
    {
        $this->storage = $storage ?? new RedisStorage();
    }
    
    /**
     * 获取全局规则列表
     *
     * @param string|null $key 指定键名
     * @return array 规则列表
     */
    public function getGlobalRules(?string $key = null): array
    {
        $cacheKey = 'ratelimit_rules:global';
        if ($key) {
            $cacheKey .= ':' . $key;
        }
        
        // 尝试从缓存获取规则
        $rules = Cache::get($cacheKey);
        if ($rules !== null) {
            return $this->hydrateRules($rules, 'global');
        }
        
        // 从数据库查询规则
        $query = Db::table('system_rate_limit_rule')
            ->where('type', '=', 'global')
            ->where('status', '=', 1)
            ->where('deleted_at', null);
            
        if ($key) {
            $query->where('key', '=', $key);
        }
        
        $rules = $query->select()->toArray();
        
        // 缓存规则
        Cache::set($cacheKey, $rules, $this->cacheTtl);
        
        return $this->hydrateRules($rules, 'global');
    }
    
    /**
     * 获取租户规则列表
     *
     * @param int $tenantId 租户ID
     * @param string|null $key 指定键名
     * @return array 规则列表
     */
    public function getTenantRules(int $tenantId, ?string $key = null): array
    {
        $cacheKey = 'ratelimit_rules:tenant:' . $tenantId;
        if ($key) {
            $cacheKey .= ':' . $key;
        }
        
        // 尝试从缓存获取规则
        $rules = Cache::get($cacheKey);
        if ($rules !== null) {
            return $this->hydrateRules($rules, 'tenant', $tenantId);
        }
        
        // 从数据库查询规则
        $query = Db::table('system_rate_limit_rule')
            ->where('type', '=', 'tenant')
            ->where('tenant_id', '=', $tenantId)
            ->where('status', '=', 1)
            ->where('deleted_at', null);
            
        if ($key) {
            $query->where('key', '=', $key);
        }
        
        $rules = $query->select()->toArray();
        
        // 缓存规则
        Cache::set($cacheKey, $rules, $this->cacheTtl);
        
        return $this->hydrateRules($rules, 'tenant', $tenantId);
    }
    
    /**
     * 获取用户规则列表
     *
     * @param string|null $key 指定键名
     * @param int|null $tenantId 租户ID（可选）
     * @return array 规则列表
     */
    public function getUserRules(?string $key = null, ?int $tenantId = null): array
    {
        $cacheKey = 'ratelimit_rules:user';
        if ($tenantId) {
            $cacheKey .= ':' . $tenantId;
        }
        if ($key) {
            $cacheKey .= ':' . $key;
        }
        
        // 尝试从缓存获取规则
        $rules = Cache::get($cacheKey);
        if ($rules !== null) {
            return $this->hydrateRules($rules, 'user', $tenantId);
        }
        
        // 从数据库查询规则
        $query = Db::table('system_rate_limit_rule')
            ->where('type', '=', 'user')
            ->where('status', '=', 1)
            ->where('deleted_at', null);
            
        if ($tenantId) {
            $query->where('tenant_id', '=', $tenantId);
        }
            
        if ($key) {
            $query->where('key', '=', $key);
        }
        
        $rules = $query->select()->toArray();
        
        // 缓存规则
        Cache::set($cacheKey, $rules, $this->cacheTtl);
        
        return $this->hydrateRules($rules, 'user', $tenantId);
    }
    
    /**
     * 根据键名获取所有匹配规则
     *
     * @param string $key 键名
     * @param int|null $tenantId 租户ID（可选）
     * @return array 规则列表
     */
    public function getRulesByKey(string $key, ?int $tenantId = null): array
    {
        $rules = [];
        
        // 获取全局规则
        $globalRules = $this->getGlobalRules($key);
        if (!empty($globalRules)) {
            $rules = array_merge($rules, $globalRules);
        }
        
        // 如果有租户ID，则获取租户规则
        if ($tenantId) {
            $tenantRules = $this->getTenantRules($tenantId, $key);
            if (!empty($tenantRules)) {
                $rules = array_merge($rules, $tenantRules);
            }
            
            // 获取租户用户规则
            $userRules = $this->getUserRules($key, $tenantId);
            if (!empty($userRules)) {
                $rules = array_merge($rules, $userRules);
            }
        } else {
            // 获取所有用户规则
            $userRules = $this->getUserRules($key);
            if (!empty($userRules)) {
                $rules = array_merge($rules, $userRules);
            }
        }
        
        return $rules;
    }
    
    /**
     * 清除规则缓存
     *
     * @param string|null $type 规则类型
     * @param int|null $tenantId 租户ID（可选）
     * @param string|null $key 键名（可选）
     * @return bool 是否成功
     */
    public function clearRulesCache(?string $type = null, ?int $tenantId = null, ?string $key = null): bool
    {
        if ($type === null && $tenantId === null && $key === null) {
            // 清除所有规则缓存
            return Cache::delete('ratelimit_rules:*');
        }
        
        $cacheKey = 'ratelimit_rules:';
        if ($type) {
            $cacheKey .= $type;
            
            if ($tenantId && $type !== 'global') {
                $cacheKey .= ':' . $tenantId;
            }
            
            if ($key) {
                $cacheKey .= ':' . $key;
            }
            
            return Cache::delete($cacheKey);
        }
        
        return false;
    }
    
    /**
     * 创建规则实例
     *
     * @param array $ruleData 规则数据
     * @param string $type 规则类型
     * @param int|null $tenantId 租户ID（可选）
     * @return Rule|null 规则实例
     */
    protected function createRule(array $ruleData, string $type, ?int $tenantId = null): ?Rule
    {
        // 创建算法实例
        $algorithm = $this->createAlgorithm($ruleData['algorithm'] ?? 'fixed');
        if (!$algorithm) {
            return null;
        }
        
        // 创建标识符实例
        $identifier = $this->createIdentifier($type);
        if (!$identifier) {
            return null;
        }
        
        // 根据规则类型创建对应的规则实例
        switch ($type) {
            case 'global':
                return new GlobalRule(
                    $ruleData['id'],
                    $ruleData['name'],
                    $ruleData['key'],
                    $algorithm,
                    $identifier,
                    $ruleData['limit_count'],
                    $ruleData['time_window']
                );
                
            case 'tenant':
                return new TenantRule(
                    $ruleData['id'],
                    $ruleData['name'],
                    $ruleData['key'],
                    $algorithm,
                    $identifier,
                    $ruleData['limit_count'],
                    $ruleData['time_window'],
                    $tenantId ?? $ruleData['tenant_id']
                );
                
            case 'user':
                return new UserRule(
                    $ruleData['id'],
                    $ruleData['name'],
                    $ruleData['key'],
                    $algorithm,
                    $identifier,
                    $ruleData['limit_count'],
                    $ruleData['time_window'],
                    $tenantId ?? $ruleData['tenant_id']
                );
                
            default:
                return null;
        }
    }
    
    /**
     * 创建算法实例
     *
     * @param string $algorithm 算法类型
     * @return AlgorithmInterface|null 算法实例
     */
    protected function createAlgorithm(string $algorithm): ?AlgorithmInterface
    {
        switch ($algorithm) {
            case 'fixed':
                return new FixedWindowAlgorithm($this->storage);
                
            case 'sliding':
                return new SlidingWindowAlgorithm($this->storage);
                
            default:
                return null;
        }
    }
    
    /**
     * 创建标识符实例
     *
     * @param string $type 规则类型
     * @return IdentifierInterface|null 标识符实例
     */
    protected function createIdentifier(string $type): ?IdentifierInterface
    {
        switch ($type) {
            case 'global':
                return new IpIdentifier();
                
            case 'tenant':
                return new TenantIdentifier();
                
            case 'user':
                return new UserIdentifier();
                
            default:
                return null;
        }
    }
    
    /**
     * 将规则数据转换为规则实例
     *
     * @param array $rulesData 规则数据
     * @param string $type 规则类型
     * @param int|null $tenantId 租户ID（可选）
     * @return array 规则实例列表
     */
    protected function hydrateRules(array $rulesData, string $type, ?int $tenantId = null): array
    {
        $rules = [];
        
        foreach ($rulesData as $ruleData) {
            $rule = $this->createRule($ruleData, $type, $tenantId);
            if ($rule) {
                $rules[] = $rule;
            }
        }
        
        return $rules;
    }
} 