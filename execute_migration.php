<?php
/**
 * 执行数据库迁移脚本
 */

// 数据库连接配置
$host = '*************';
$port = '3306';
$database = 'www_bs_com';
$username = 'www_bs_com';
$password = 'PdadjMXmNy8Pn9tj';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 执行数据库迁移脚本 ===\n\n";
    
    // 1. 为出库申请表添加总金额大写字段
    echo "1. 为出库申请表添加总金额大写字段...\n";
    try {
        $pdo->exec("
            ALTER TABLE `ims_outbound_approval` 
            ADD COLUMN `total_amount_chinese` varchar(200) NOT NULL DEFAULT '' 
            COMMENT '总金额大写' 
            AFTER `total_amount`
        ");
        echo "✓ 出库申请表字段添加成功\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "✓ 出库申请表字段已存在\n";
        } else {
            echo "✗ 出库申请表字段添加失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 2. 为出货申请表添加总金额大写字段
    echo "2. 为出货申请表添加总金额大写字段...\n";
    try {
        $pdo->exec("
            ALTER TABLE `ims_shipment_approval` 
            ADD COLUMN `total_amount_chinese` varchar(200) NOT NULL DEFAULT '' 
            COMMENT '总金额大写' 
            AFTER `total_amount`
        ");
        echo "✓ 出货申请表字段添加成功\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "✓ 出货申请表字段已存在\n";
        } else {
            echo "✗ 出货申请表字段添加失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 3. 检查并添加出库明细表的供应商ID字段
    echo "3. 检查出库明细表的供应商ID字段...\n";
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'ims_outbound_item' 
        AND COLUMN_NAME = 'supplier_id'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        try {
            $pdo->exec("
                ALTER TABLE `ims_outbound_item` 
                ADD COLUMN `supplier_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 
                COMMENT '供应商ID' 
                AFTER `outbound_id`
            ");
            echo "✓ 出库明细表supplier_id字段添加成功\n";
        } catch (Exception $e) {
            echo "✗ 出库明细表supplier_id字段添加失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "✓ 出库明细表supplier_id字段已存在\n";
    }
    
    // 4. 检查并添加出货明细表的供应商ID字段
    echo "4. 检查出货明细表的供应商ID字段...\n";
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'ims_shipment_item' 
        AND COLUMN_NAME = 'supplier_id'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        try {
            $pdo->exec("
                ALTER TABLE `ims_shipment_item` 
                ADD COLUMN `supplier_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 
                COMMENT '供应商ID' 
                AFTER `shipment_id`
            ");
            echo "✓ 出货明细表supplier_id字段添加成功\n";
        } catch (Exception $e) {
            echo "✗ 出货明细表supplier_id字段添加失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "✓ 出货明细表supplier_id字段已存在\n";
    }
    
    // 5. 检查并添加产品单位字段
    echo "5. 检查产品单位字段...\n";
    
    // 出库明细表
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'ims_outbound_item' 
        AND COLUMN_NAME = 'product_unit'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        try {
            $pdo->exec("
                ALTER TABLE `ims_outbound_item` 
                ADD COLUMN `product_unit` varchar(20) NOT NULL DEFAULT '' 
                COMMENT '产品单位' 
                AFTER `product_id`
            ");
            echo "✓ 出库明细表product_unit字段添加成功\n";
        } catch (Exception $e) {
            echo "✗ 出库明细表product_unit字段添加失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "✓ 出库明细表product_unit字段已存在\n";
    }
    
    // 出货明细表
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'ims_shipment_item' 
        AND COLUMN_NAME = 'product_unit'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        try {
            $pdo->exec("
                ALTER TABLE `ims_shipment_item` 
                ADD COLUMN `product_unit` varchar(20) NOT NULL DEFAULT '' 
                COMMENT '产品单位' 
                AFTER `product_id`
            ");
            echo "✓ 出货明细表product_unit字段添加成功\n";
        } catch (Exception $e) {
            echo "✗ 出货明细表product_unit字段添加失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "✓ 出货明细表product_unit字段已存在\n";
    }
    
    // 6. 添加索引
    echo "6. 添加索引优化...\n";
    
    // 检查出库明细表的供应商索引
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'ims_outbound_item' 
        AND INDEX_NAME = 'idx_supplier_id'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        try {
            $pdo->exec("ALTER TABLE `ims_outbound_item` ADD INDEX `idx_supplier_id` (`supplier_id`)");
            echo "✓ 出库明细表supplier_id索引添加成功\n";
        } catch (Exception $e) {
            echo "✗ 出库明细表supplier_id索引添加失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "✓ 出库明细表supplier_id索引已存在\n";
    }
    
    // 检查出货明细表的供应商索引
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'ims_shipment_item' 
        AND INDEX_NAME = 'idx_supplier_id'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        try {
            $pdo->exec("ALTER TABLE `ims_shipment_item` ADD INDEX `idx_supplier_id` (`supplier_id`)");
            echo "✓ 出货明细表supplier_id索引添加成功\n";
        } catch (Exception $e) {
            echo "✗ 出货明细表supplier_id索引添加失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "✓ 出货明细表supplier_id索引已存在\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "数据库迁移完成！\n";
    
    // 验证结果
    echo "\n验证迁移结果:\n";
    echo str_repeat("-", 40) . "\n";
    
    $tables = [
        'ims_outbound_approval' => ['total_amount', 'total_quantity', 'total_amount_chinese'],
        'ims_shipment_approval' => ['total_amount', 'total_quantity', 'total_amount_chinese'],
        'ims_outbound_item' => ['supplier_id', 'product_id', 'quantity', 'unit_price', 'total_amount'],
        'ims_shipment_item' => ['supplier_id', 'product_id', 'quantity', 'unit_price', 'total_amount']
    ];
    
    foreach ($tables as $tableName => $requiredFields) {
        $stmt = $pdo->prepare("
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = ?
            AND COLUMN_NAME IN ('" . implode("','", $requiredFields) . "')
        ");
        $stmt->execute([$tableName]);
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($columns) === count($requiredFields)) {
            echo "✓ {$tableName}: 所有字段完整\n";
        } else {
            echo "✗ {$tableName}: 缺少字段\n";
        }
    }
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "执行失败: " . $e->getMessage() . "\n";
    exit(1);
}
