<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmProductService;
use app\ims\service\ImsSupplierService;
use think\response\Json;

/**
 * 产品表控制器
 */
class CrmProductController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CrmProductService
	 */
	protected $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = CrmProductService::getInstance();
	}
	
	/**
	 * 获取详情（重写以包含分类名称和单位名称）
	 */
	public function detail($id)
	{
		$result = $this->service->getOne([
			[
				'id',
				'=',
				$id
			]
		]);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * 获取供应商选项
	 */
	public function getSupplierOptions()
	{
		$supplierService = ImsSupplierService::getInstance();
		$options         = $supplierService->getOptions();
		return $this->success('获取成功', $options);
	}
	
	
	/**
	 * 停用产品
	 */
	public function disable($id)
	{
		try {
			$result = $this->service->disable($id);
			return $this->success('产品已停用', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	// ==================== 产品选择器接口 ====================
	
	/**
	 * 获取产品选择器数据（支持按供应商筛选）
	 * 专门为ApiSelect组件提供数据
	 *
	 * @return Json
	 */
	public function selector()
	{
		try {
			$params = $this->request->param();
			
			// 默认只返回启用状态的产品
			$params['status'] = $params['status'] ?? 1;
			
			// 设置返回字段
			$field = [
				'id',
				'name',
				'code',
				'spec',
				'price',
				'cost',
				'unit_id',
				'category_id',
				'supplier_id'
				// 新增供应商字段
			];
			
			$result = $this->service->search($params, $field, [
				'unit',
				'category',
				'supplier'
				// 新增供应商关联
			]);
			
			// 为ApiSelect组件格式化数据
			if (!empty($result['data'])) {
				foreach ($result['data'] as &$item) {
					// 构建完整的产品名称用于显示
					$item['full_name'] = $item['name'];
					if (!empty($item['code'])) {
						$item['full_name'] .= " ({$item['code']})";
					}
					if (!empty($item['spec'])) {
						$item['full_name'] .= " - {$item['spec']}";
					}
					
					// 添加单位信息
					$item['unit_name'] = $item['unit']['unit_name'] ?? '';
					
					// 添加分类信息
					$item['category_name'] = $item['category']['name'] ?? '';
					
					// 添加供应商信息
					$item['supplier_name'] = $item['supplier']['name'] ?? '';
				}
			}
			
			return $this->success('获取成功', $result);
			
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 根据分类获取产品
	 *
	 * @return Json
	 */
	public function getByCategory(): Json
	{
		try {
			$categoryId = $this->request->param('category_id', 0);
			
			if (empty($categoryId)) {
				return $this->error('请选择产品分类');
			}
			
			$params = [
				'category_id' => $categoryId,
				'status'      => 1
			];
			
			$field  = [
				'id',
				'name',
				'code',
				'spec',
				'price',
				'unit_id'
			];
			$result = $this->service->search($params, $field, ['unit']);
			
			return $this->success('获取成功', $result);
			
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取产品详细信息
	 * 用于产品选择后显示详细信息
	 *
	 * @param int $id 产品ID
	 * @return Json
	 */
	public function getProductInfo(int $id): Json
	{
		try {
			$product = $this->service->getOne(['id' => $id], [
				'unit',
				'category'
			]);
			
			if ($product->isEmpty()) {
				return $this->error('产品不存在');
			}
			
			$result = $product->toArray();
			
			// 添加格式化信息
			$result['full_name'] = $result['name'];
			if (!empty($result['code'])) {
				$result['full_name'] .= " ({$result['code']})";
			}
			if (!empty($result['spec'])) {
				$result['full_name'] .= " - {$result['spec']}";
			}
			
			return $this->success('获取成功', $result);
			
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取产品选项（用于下拉选择）
	 */
	public function options()
	{
		try {
			$params = $this->request->param();
			$options = $this->service->getOptions($params);
			return $this->success('获取成功', $options);

		} catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
}