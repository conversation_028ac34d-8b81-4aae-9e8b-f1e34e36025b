<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\crm\model\CrmQuotationTemplate;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 报价单模板管理服务
 * 负责报价单模板的创建、管理和渲染
 */
class QuotationTemplateService extends BaseService
{
    /**
     * 缓存键前缀
     */
    const CACHE_PREFIX = 'crm:quotation_template:';
    const CACHE_TTL = 3600; // 1小时
    
    /**
     * 模板类型常量
     */
    const TYPE_NORMAL = 0; // 普通模板
    const TYPE_SYSTEM = 1; // 系统模板
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmQuotationTemplate();
        parent::__construct();
    }
    
    /**
     * 创建报价单模板
     * 
     * @param array $templateData 模板数据
     * @return int 模板ID
     * @throws BusinessException
     */
    public function createTemplate(array $templateData): int
    {
        $tenantId = $this->getTenantId();
        $adminId = $this->getAdminId();
        
        // 验证模板编码唯一性
        if (!empty($templateData['template_code'])) {
            $exists = $this->model->where('tenant_id', $tenantId)
                ->where('template_code', $templateData['template_code'])
                ->find();
            
            if ($exists) {
                throw new BusinessException('模板编码已存在');
            }
        }
        
        $templateData = array_merge([
            'tenant_id' => $tenantId,
            'type' => self::TYPE_NORMAL,
            'status' => 1,
            'creator_id' => $adminId,
            'updated_id' => $adminId,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ], $templateData);
        
        $templateId = (int)$this->model->insertGetId($templateData);
        
        // 清除缓存
        $this->clearTemplateCache();
        
        Log::info('创建报价单模板', [
            'template_id' => $templateId,
            'template_name' => $templateData['template_name'],
            'admin_id' => $adminId
        ]);
        
        return $templateId;
    }
    
    /**
     * 获取模板列表
     * 
     * @param array $params 查询参数
     * @return array 模板列表
     */
    public function getTemplateList(array $params = []): array
    {
        $tenantId = $this->getTenantId();
        
        $query = $this->model->where('tenant_id', $tenantId);
        
        // 按类型筛选
        if (isset($params['type'])) {
            $query->where('type', $params['type']);
        }
        
        // 按状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        // 按名称搜索
        if (!empty($params['template_name'])) {
            $query->where('template_name', 'like', '%' . $params['template_name'] . '%');
        }
        
        $templates = $query->order('type desc, created_at desc')
            ->select()
            ->toArray();
        
        return $templates;
    }
    
    /**
     * 渲染报价单模板
     * 
     * @param int $templateId 模板ID
     * @param array $quotationData 报价单数据
     * @return string 渲染后的HTML
     * @throws BusinessException
     */
    public function renderTemplate(int $templateId, array $quotationData): string
    {
        $template = $this->getTemplateById($templateId);
        if (!$template) {
            throw new BusinessException('模板不存在');
        }
        
        if ($template['status'] != 1) {
            throw new BusinessException('模板已停用');
        }
        
        $content = $template['content'];
        
        // 替换基础变量
        $variables = $this->prepareTemplateVariables($quotationData);
        
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }
        
        // 处理产品列表
        $content = $this->renderProductList($content, $quotationData['products'] ?? []);
        
        return $content;
    }
    
    /**
     * 准备模板变量
     * 
     * @param array $quotationData 报价单数据
     * @return array 变量数组
     */
    private function prepareTemplateVariables(array $quotationData): array
    {
        return [
            'quotation_no' => $quotationData['quotation_no'] ?? '',
            'title' => $quotationData['title'] ?? '',
            'customer_name' => $quotationData['customer_name'] ?? '',
            'contact_name' => $quotationData['contact_name'] ?? '',
            'contact_phone' => $quotationData['contact_phone'] ?? '',
            'total_amount' => number_format((float)($quotationData['total_amount'] ?? 0), 2),
            'discount_rate' => $quotationData['discount_rate'] ?? '100.00',
            'discount_amount' => number_format((float)($quotationData['discount_amount'] ?? 0), 2),
            'tax_rate' => $quotationData['tax_rate'] ?? '0.00',
            'tax_amount' => number_format((float)($quotationData['tax_amount'] ?? 0), 2),
            'final_amount' => number_format((float)($quotationData['final_amount'] ?? 0), 2),
            'valid_days' => $quotationData['valid_days'] ?? '30',
            'expire_date' => $quotationData['expire_date'] ?? '',
            'created_date' => date('Y-m-d'),
            'remark' => $quotationData['remark'] ?? ''
        ];
    }
    
    /**
     * 渲染产品列表
     * 
     * @param string $content 模板内容
     * @param array $products 产品列表
     * @return string 渲染后的内容
     */
    private function renderProductList(string $content, array $products): string
    {
        // 查找产品列表模板
        $pattern = '/{{#products}}(.*?){{\/products}}/s';
        
        if (preg_match($pattern, $content, $matches)) {
            $productTemplate = $matches[1];
            $productHtml = '';
            
            foreach ($products as $index => $product) {
                $productRow = $productTemplate;
                $productRow = str_replace('{{index}}', (string)($index + 1), $productRow);
                $productRow = str_replace('{{product_name}}', $product['product_name'] ?? '', $productRow);
                $productRow = str_replace('{{product_code}}', $product['product_code'] ?? '', $productRow);
                $productRow = str_replace('{{price}}', number_format((float)($product['price'] ?? 0), 2), $productRow);
                $productRow = str_replace('{{quantity}}', $product['quantity'] ?? '0', $productRow);
                $productRow = str_replace('{{unit}}', $product['unit'] ?? '', $productRow);
                $productRow = str_replace('{{subtotal}}', number_format((float)($product['subtotal'] ?? 0), 2), $productRow);
                
                $productHtml .= $productRow;
            }
            
            $content = str_replace($matches[0], $productHtml, $content);
        }
        
        return $content;
    }
    
    /**
     * 获取默认模板
     * 
     * @return array|null 默认模板
     */
    public function getDefaultTemplate(): ?array
    {
        $tenantId = $this->getTenantId();
        
        // 优先获取系统默认模板
        $template = $this->model->where('tenant_id', $tenantId)
            ->where('type', self::TYPE_SYSTEM)
            ->where('status', 1)
            ->order('id asc')
            ->find();
        
        if (!$template) {
            // 获取第一个启用的普通模板
            $template = $this->model->where('tenant_id', $tenantId)
                ->where('type', self::TYPE_NORMAL)
                ->where('status', 1)
                ->order('id asc')
                ->find();
        }
        
        return $template ? $template->toArray() : null;
    }
    
    /**
     * 复制模板
     * 
     * @param int $templateId 源模板ID
     * @param string $newName 新模板名称
     * @return int 新模板ID
     * @throws BusinessException
     */
    public function copyTemplate(int $templateId, string $newName): int
    {
        $template = $this->getTemplateById($templateId);
        if (!$template) {
            throw new BusinessException('源模板不存在');
        }
        
        $newTemplateData = $template;
        unset($newTemplateData['id']);
        $newTemplateData['template_name'] = $newName;
        $newTemplateData['template_code'] = ''; // 清空编码，避免冲突
        $newTemplateData['type'] = self::TYPE_NORMAL; // 复制的模板都是普通模板
        
        return $this->createTemplate($newTemplateData);
    }
    
    /**
     * 获取模板详情
     *
     * @param int $templateId 模板ID
     * @return array|null 模板详情
     */
    public function getTemplateById(int $templateId): ?array
    {
        $cacheKey = self::CACHE_PREFIX . "template:{$templateId}";
        
        $template = Cache::get($cacheKey);
        if ($template === false) {
            $template = $this->model->where('tenant_id', $this->getTenantId())
                ->find($templateId);
            
            if ($template) {
                $template = $template->toArray();
                Cache::set($cacheKey, $template, self::CACHE_TTL);
            }
        }
        
        return $template;
    }
    
    /**
     * 清除模板缓存
     */
    private function clearTemplateCache(): void
    {
        Cache::tag('quotation_template')->clear();
    }
    
    /**
     * 获取租户ID
     */
    private function getTenantId(): int
    {
        return (int)request()->header('tenant-id', '0');
    }

    /**
     * 获取管理员ID
     */
    private function getAdminId(): int
    {
        return (int)request()->header('admin-id', '0');
    }
}
