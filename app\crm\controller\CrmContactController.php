<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmContactService;
use think\response\Json;

/**
 * 联系人表控制器
 */
class CrmContactController extends BaseController
{
    use CrudControllerTrait, ControllerImportExportTrait;
    /**
     * @var CrmContactService
     */
    protected CrmContactService $service;

    /**
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();

        // 使用单例模式获取Service实例
        $this->service = CrmContactService::getInstance();
    }
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		$result = $this->service->search($params,[],[
			'creator','customer'
		]);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id],[
			'creator','customer'
		]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}

    /**
     * 状态切换
     */
    public function status($id)
    {
        $status = $this->request->post('status');
        $result = $this->service->updateField($id, 'status', $status);
        return $this->success('状态更新成功', $result);
    }

}