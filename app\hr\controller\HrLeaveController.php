<?php
declare(strict_types=1);

namespace app\hr\controller;

use app\common\core\base\BaseController;
use app\hr\service\HrLeaveService;
use think\response\Json;

/**
 * 请假申请控制器（公共业务集成在我的申请页面ApplicationController）
 *
 *
 */
class HrLeaveController extends BaseController
{
	/**
	 * 请假申请服务实例
	 *
	 * @var HrLeaveService
	 */
	protected HrLeaveService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = HrLeaveService::getInstance();
	}
	
	/**
	 * 获取请假类型选项
	 *
	 * @return Json 响应结果
	 * 响应数据结构：
	 * {
	 *   "code": 200,
	 *   "message": "success",
	 *   "data": [
	 *     {"value": 1, "label": "年假"},
	 *     {"value": 2, "label": "事假"},
	 *     {"value": 3, "label": "病假"},
	 *     {"value": 4, "label": "婚假"},
	 *     {"value": 5, "label": "产假"},
	 *     {"value": 6, "label": "丧假"},
	 *     {"value": 7, "label": "其他"}
	 *   ]
	 * }
	 */
	public function leaveTypes(): Json
	{
		$types = [
			[
				'value' => 1,
				'label' => '年假'
			],
			[
				'value' => 2,
				'label' => '事假'
			],
			[
				'value' => 3,
				'label' => '病假'
			],
			[
				'value' => 4,
				'label' => '婚假'
			],
			[
				'value' => 5,
				'label' => '产假'
			],
			[
				'value' => 6,
				'label' => '丧假'
			],
			[
				'value' => 7,
				'label' => '其他'
			],
		];
		
		return $this->success('获取成功', $types);
	}
}