<?php
declare(strict_types=1);

namespace app\project\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\project\service\ProjectMemberService;
use think\response\Json;

/**
 * 项目成员表控制器
 */
class ProjectMemberController extends BaseController
{
    use CrudControllerTrait;
    /**
     * @var ProjectMemberService
     */
    protected $service;

    /**
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();

        // 使用单例模式获取Service实例
        $this->service = ProjectMemberService::getInstance();
    }

    /**
     * 状态切换
     */
    public function status($id)
    {
        $status = $this->request->post('status');
        $result = $this->service->updateField($id, 'status', $status);
        return $this->success('状态更新成功', $result);
    }

    /**
     * 获取可添加的用户列表
     * 权限控制：只有项目负责人或租户管理员才可以添加成员
     *
     * @param int $projectId 项目ID
     * @return \think\Response
     */
    public function availableUsers(int $projectId)
    {
        try {
            $result = $this->service->getAvailableUsers($projectId);
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

}