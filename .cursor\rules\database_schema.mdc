---
description: 
globs: 
alwaysApply: true
---
{
  "name": "数据库结构",
  "description": "多租户前后端分离框架系统的数据库表结构说明",
  "glob": "**/*.php"
}

# 数据库结构

本系统采用多租户设计，所有涉及租户的表都包含`tenant_id`字段用于数据隔离。

## 核心表结构

### 用户与权限相关表

- **system_admin** - 系统用户表
  - 主要字段: id, username, password, salt, real_name, avatar, dept_id, status, tenant_id
  - 关联: dept_id -> system_dept.id

- **system_role** - 角色表
  - 主要字段: id, name, data_scope, status, tenant_id
  - data_scope: 数据权限范围(1全部，2本部门，3本部门及以下，4仅本人，5自定义)

- **system_menu** - 菜单表
  - 主要字段: id, parent_id, title, name, path, component, type, icon, sort, status
  - type: 类型(0目录，1菜单，2按钮)

- **system_role_menu** - 角色菜单关联表
  - 主要字段: id, role_id, menu_id, tenant_id

- **system_admin_role** - 用户角色关联表
  - 主要字段: id, admin_id, role_id, tenant_id

### 组织架构相关表

- **system_dept** - 部门表
  - 主要字段: id, parent_id, name, code, leader_name, sort, status, tenant_id

- **system_post** - 岗位表
  - 主要字段: id, name, code, sort, status, tenant_id

### 租户相关表

- **system_tenant** - 租户表
  - 主要字段: id, name, code, domain, logo, package_id, expired_at, status

- **system_tenant_package** - 租户套餐表
  - 主要字段: id, name, price, duration, max_admin_count, menu_ids, status

- **system_tenant_config** - 租户配置表
  - 主要字段: id, group, item_key, item_value, tenant_id

### 日志相关表

- **system_operation_log** - 操作日志表
  - 主要字段: id, admin_id, controller, action, url, method, params, ip, tenant_id

- **system_login_log** - 登录日志表
  - 主要字段: id, admin_id, ip, location, browser, os, log_type, status, tenant_id

### 文件管理相关表

- **system_attachment_category** - 附件分类表
  - 主要字段: id, parent_id, name, sort, tenant_id

- **system_attachment** - 附件表
  - 主要字段: id, cate_id, name, path, extension, size, mime_type, storage, tenant_id

## 通用字段

所有表都包含以下通用字段:
- `id`: 主键
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `deleted_at`: 软删除时间
- `creator_id`: 创建者ID

涉及租户的表都包含:
- `tenant_id`: 租户ID

