<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 综合测试表模型
 */
class SystemTestCrudAllFields extends BaseModel
{
    // 设置表名
    protected $name = 'system_test_crud_all_fields';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段转换
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // 软删除
    protected $deleteTime = 'deleted_at';
} 