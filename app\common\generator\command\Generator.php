<?php
declare(strict_types=1);

namespace app\common\generator\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use app\common\generator\src\CrudGenerator;

/**
 * CRUD代码生成器命令
 */
class Generator extends Command
{
    /**
     * 配置命令
     */
    protected function configure()
    {
        $this->setName('generator:crud')
            ->addArgument('table', Argument::REQUIRED, '表名')
            ->addOption('module', 'm', Option::VALUE_OPTIONAL, '模块名称', 'system')
            ->addOption('frontend', 'f', Option::VALUE_NONE, '是否生成前端代码')
            ->addOption('overwrite', 'o', Option::VALUE_NONE, '是否覆盖已存在的文件')
            ->addOption('debug', 'd', Option::VALUE_NONE, '调试模式，输出生成的数据')
            ->addOption('sql-file', 's', Option::VALUE_OPTIONAL, 'SQL文件路径')
            ->setDescription('生成CRUD代码');
    }

    /**
     * 执行命令
     * 
     * @param Input $input 输入对象
     * @param Output $output 输出对象
     * @return int 退出码
     */
    protected function execute(Input $input, Output $output)
    {
        // 获取表名
        $table = $input->getArgument('table');
        
        // 获取模块名称
        $module = $input->getOption('module');
        
        // 是否生成前端代码
        $generateFrontend = $input->getOption('frontend');
        
        // 是否覆盖已存在的文件
        $overwrite = $input->getOption('overwrite');
        
        // 是否调试模式
        $debug = $input->getOption('debug');
        
        // SQL文件路径
        $sqlFile = $input->getOption('sql-file');
        
        // 输出配置信息
        $output->writeln("开始生成CRUD代码：");
        $output->writeln("表名：{$table}");
        $output->writeln("模块：{$module}");
        $output->writeln("生成前端代码：" . ($generateFrontend ? '是' : '否'));
        $output->writeln("覆盖已存在文件：" . ($overwrite ? '是' : '否'));
        $output->writeln("调试模式：" . ($debug ? '是' : '否'));
        $output->writeln("SQL文件：" . ($sqlFile ?: '无'));
        $output->writeln("");
        
        try {
            // 如果指定了SQL文件，先执行SQL创建表
            if ($sqlFile && file_exists($sqlFile)) {
                $this->executeSqlFile($sqlFile, $output);
            }
            
            // 创建生成器
            $generator = new CrudGenerator();
            
            // 如果是调试模式，先输出表结构信息
            if ($debug) {
                $parser = new \app\common\generator\src\parsers\TableInfoParser();
                $tableInfo = $parser->parse($table);
                $output->writeln("表结构信息：");
                $output->writeln(json_encode($tableInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                $output->writeln("");
            }
            
            // 生成代码
            $files = $generator->generate($table, [
                'module' => $module,
                'frontend' => $generateFrontend,
                'overwrite' => $overwrite,
                'debug' => $debug
            ]);
            
            // 输出生成结果
            $output->writeln("代码生成成功！");
            $output->writeln("生成的文件列表：");
            
            foreach ($files as $file) {
                $output->writeln("{$file['type']}: {$file['path']}");
                
                // 调试模式下输出文件内容
                if ($debug && !empty($file['content']) && empty($file['skipped'])) {
                    $output->writeln("文件内容：");
                    $output->writeln($file['content']);
                    $output->writeln("");
                }
            }
            
            return 0; // 成功
        } catch (\Exception $e) {
            $output->writeln("发生错误：{$e->getMessage()}");
            $output->writeln("文件：{$e->getFile()}:{$e->getLine()}");
            $output->writeln("堆栈：{$e->getTraceAsString()}");
            
            return 1; // 失败
        }
    }
    
    /**
     * 执行SQL文件
     *
     * @param string $sqlFile SQL文件路径
     * @param Output $output 输出对象
     * @return void
     */
    protected function executeSqlFile(string $sqlFile, Output $output): void
    {
        $output->writeln("执行SQL文件：{$sqlFile}");
        
        // 读取SQL文件内容
        $sql = file_get_contents($sqlFile);
        if (!$sql) {
            throw new \Exception("无法读取SQL文件：{$sqlFile}");
        }
        
        // 执行SQL
        try {
            \think\facade\Db::execute($sql);
            $output->writeln("SQL执行成功");
        } catch (\Exception $e) {
            throw new \Exception("SQL执行失败：" . $e->getMessage());
        }
    }
} 