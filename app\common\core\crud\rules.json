{"name": "CRUD模块化设计规则", "version": "1.0.0", "description": "多租户前后端分离框架系统中CRUD模块化设计的规则和指导方针", "author": "Admin System Team", "crudModules": {"core": {"service": "CrudService", "interface": "CrudInterface", "factory": "CrudFactory"}, "traits": {"QueryBuilderTrait": "查询构建相关功能", "DataFilterTrait": "数据过滤相关功能", "SearchTrait": "搜索条件构建功能", "OrderTrait": "排序处理功能", "ModelConfigTrait": "模型配置相关功能", "CrudServiceTrait": "提供标准CRUD服务方法实现", "CrudControllerTrait": "控制器CRUD操作封装"}, "dependencies": {"DataPermissionTrait": "数据权限相关功能", "TransactionTrait": "事务处理相关功能"}}, "standardOperations": {"getList": "获取列表数据", "getPageList": "获取分页列表数据", "getCount": "获取数据总数", "getOne": "获取单条数据", "add": "添加数据", "edit": "编辑数据", "delete": "删除数据", "search": "搜索数据", "getSelectOptions": "获取下拉选项数据", "updateField": "更新单个字段"}, "searchTypes": {"eq": "等值查询", "like": "模糊查询", "between": "范围查询", "in": "集合查询", "date": "日期范围查询", "custom": "自定义查询"}, "validOperators": ["eq", "=", "<>", "!=", ">", ">=", "<", "<=", "like", "not like", "between", "not between", "in", "not in", "null", "not null", "exists", "not exists", "exp", "find_in_set", "time", "between time", "not between time"], "securityMeasures": {"fieldValidation": "验证字段名是否包含危险字符", "operatorValidation": "验证操作符是否在白名单内", "valueFiltering": "过滤输入值，防止SQL注入", "dataPermission": "基于数据权限字段过滤数据"}, "conventions": {"dataRangeField": "creator_id", "defaultOrder": {"id": "desc"}, "forbidUpdateFields": ["id", "password", "salt", "tenant_id", "creator_id", "created_at", "updated_at", "deleted_at"], "pageLimit": {"maxPage": 1000, "maxLimit": 100}}, "codeGeneration": {"tool": "php think crud", "options": {"table": "数据表名称(必填)", "module": "模块名称", "controller": "控制器名称", "model": "模型名称", "service": "服务类名称", "with-frontend": "是否生成前端代码", "with-test": "是否生成单元测试代码", "with-api-doc": "是否生成API文档", "force": "强制覆盖已存在文件"}}, "usageExamples": {"basic": ["// 创建服务实例", "$userModel = new \\app\\model\\User();", "$userService = new \\app\\common\\core\\crud\\CrudService($userModel);", "", "// 获取列表", "$list = $userService->getList(['status' => 1], ['created_at' => 'desc']);", "", "// 分页查询", "$pageList = $userService->getPageList(['status' => 1], ['created_at' => 'desc'], 1, 10);", "", "// 添加数据", "$userService->add([", "    'username' => 'test',", "    'password' => '123456',", "    'status' => 1", "]);"], "search": ["// 定义搜索字段配置", "$searchFields = [", "    'keyword' => [", "        'field' => 'username',", "        'type' => 'like'", "    ],", "    'status' => [", "        'type' => 'eq'", "    ],", "    'created_at' => [", "        'type' => 'date'", "    ]", "];", "", "// 执行搜索", "$result = $userService->search([", "    'keyword' => 'admin',", "    'status' => 1,", "    'created_at' => ['2023-01-01', '2023-12-31'],", "    'page' => 1,", "    'limit' => 10,", "    'sort_field' => 'created_at',", "    'sort_order' => 'desc'", "], $searchFields);"]}}