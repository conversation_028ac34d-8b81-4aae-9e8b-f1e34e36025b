# 消息通知系统数据库表

# 消息模板表  php think crud notice_template --module=notice --with-frontend --frontend-path=frontend/art-design-pro/src/views/notice
DROP TABLE IF EXISTS `notice_template`;
CREATE TABLE `notice_template`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `code`            varchar(50)         NOT NULL COMMENT '模板编码',
    `name`            varchar(100)        NOT NULL COMMENT '模板名称',
    `title`           varchar(200)        NOT NULL COMMENT '消息标题模板',
    `content`         text                NOT NULL COMMENT '消息内容模板',
    `module_code`     varchar(50)         NOT NULL DEFAULT '' COMMENT '业务模块编码：crm,erp,oa等',
    `send_channels`   varchar(50)         NOT NULL DEFAULT 'site' COMMENT '发送渠道：site站内信,email邮件,sms短信,wework微信,dingtalk钉钉',
    `mobile_template` text COMMENT '移动端模板配置，JSON格式',
    `status`          tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
    `creator_id`      bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`, `tenant_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息模板表';

# 消息记录表 php think crud notice_message --module=notice --with-frontend --frontend-path=frontend/art-design-pro/src/views/notice
DROP TABLE IF EXISTS `notice_message`;
CREATE TABLE `notice_message`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息ID',
    `template_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '模板ID',
    `title`         varchar(200)        NOT NULL COMMENT '消息标题',
    `content`       text                NOT NULL COMMENT '消息内容',
    `type`          tinyint(1)         NOT NULL DEFAULT '0' COMMENT '消息大类：0:system系统,1：个人消息',
    `module_code`   varchar(50)         NOT NULL DEFAULT '' COMMENT '所属模块',
    `business_id`   varchar(64)                  DEFAULT NULL COMMENT '业务ID',
    `detail_url`    varchar(255)                 DEFAULT NULL COMMENT 'PC端详情URL',
    `mobile_url`    varchar(255)                 DEFAULT NULL COMMENT '移动端跳转URL',
    `action_config` text                         DEFAULT NULL COMMENT '操作按钮配置JSON',
    `sender_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '发送人ID',
    `sender_name`   varchar(50)                  DEFAULT NULL COMMENT '发送人姓名',
    `send_time`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `priority`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '优先级：0普通，1重要，2紧急',
    `status`        tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态：0待发送，1已发送，2发送失败',
    `send_channels` varchar(50)         NOT NULL DEFAULT 'site' COMMENT '发送渠道：site站内信,email邮件,sms短信,wework微信,dingtalk钉钉',
    `creator_id`    bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_template_id` (`template_id`),
    KEY `idx_business` (`module_code`, `business_id`),
    KEY `idx_send_time` (`send_time`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息记录表';

# 消息接收表 php think crud notice_recipient --module=notice --with-frontend --frontend-path=frontend/art-design-pro/src/views/notice
DROP TABLE IF EXISTS `notice_recipient`;
CREATE TABLE `notice_recipient`
(
    `id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `message_id`         bigint(20) unsigned NOT NULL COMMENT '消息ID',
    `user_id`            bigint(20) unsigned NOT NULL COMMENT '用户ID',
    `user_name`          varchar(50)         NOT NULL COMMENT '用户姓名',
    `read_status`        tinyint(1)          NOT NULL DEFAULT '0' COMMENT '读取状态：0未读，1已读',
    `read_time`          datetime                     DEFAULT NULL COMMENT '阅读时间',
    `read_client`        varchar(20)                  DEFAULT NULL COMMENT '阅读终端：pc桌面端,mobile移动端,wework企业微信,dingtalk钉钉',
    `site_delivered`     tinyint(1)          NOT NULL DEFAULT '0' COMMENT '站内信是否已投递：0否，1是',
    `email_delivered`    tinyint(1)          NOT NULL DEFAULT '0' COMMENT '邮件是否已投递：0否，1是',
    `sms_delivered`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '短信是否已投递：0否，1是',
    `wework_delivered`   tinyint(1)          NOT NULL DEFAULT '0' COMMENT '微信是否已投递：0否，1是',
    `dingtalk_delivered` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '钉钉是否已投递：0否，1是',
    `is_deleted`         tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否删除：0否，1是',
    `creator_id`         bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id`          bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_message_user` (`message_id`, `user_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_read_status` (`read_status`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息接收表';

# todo 废弃 消息通道配置表 php think crud notice_channel_config --module=notice --with-frontend --frontend-path=frontend/art-design-pro/src/views/notice
DROP TABLE IF EXISTS `notice_channel_config`;
CREATE TABLE `notice_channel_config`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `channel`    varchar(20)         NOT NULL COMMENT '通道编码：site,email,sms,wework,dingtalk',
    `name`       varchar(50)         NOT NULL COMMENT '通道名称',
    `config`     text                NOT NULL COMMENT '通道配置，JSON格式',
    `status`     tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
    `creator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_channel` (`channel`, `tenant_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息通道配置表';


# 消息发送队列表 php think crud notice_queue --module=notice --with-frontend --frontend-path=frontend/art-design-pro/src/views/notice
DROP TABLE IF EXISTS `notice_queue`;
CREATE TABLE `notice_queue`
(
    `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `message_id`     bigint(20) unsigned NOT NULL COMMENT '消息ID',
    `channel`        varchar(20)         NOT NULL COMMENT '发送通道：site,email,sms,wework,dingtalk',
    `recipient_ids`  text                NOT NULL COMMENT '接收人ID列表，JSON数组',
    `status`         tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1处理中，2已处理，3处理失败',
    `retry_count`    int(11)             NOT NULL DEFAULT '0' COMMENT '重试次数',
    `error_message`  varchar(255)                 DEFAULT NULL COMMENT '错误信息',
    `scheduled_time` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计划发送时间',
    `process_time`   datetime                     DEFAULT NULL COMMENT '处理时间',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id`      bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_message_channel` (`message_id`, `channel`),
    KEY `idx_status` (`status`),
    KEY `idx_scheduled_time` (`scheduled_time`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息发送队列表';

# 插入默认通道配置
INSERT INTO `notice_channel_config` (`channel`, `name`, `config`, `status`, `creator_id`)
VALUES ('site', '站内信', '{}', 1, 1),
       ('email', '电子邮件',
        '{"smtp_host":"smtp.example.com","smtp_port":465,"smtp_user":"<EMAIL>","smtp_pass":"password","smtp_secure":"ssl","from_email":"<EMAIL>","from_name":"系统通知"}',
        0, 1),
       ('sms', '短信', '{"api_url":"https://api.example.com/sms/send","api_key":"your_api_key","sign_name":"公司名称"}',
        0, 1),
       ('wework', '企业微信', '{"corpid":"your_corpid","agentid":"your_agentid","secret":"your_app_secret"}', 0, 1),
       ('dingtalk', '钉钉', '{"app_key":"your_app_key","app_secret":"your_app_secret","agent_id":"your_agent_id"}', 0,
        1);

# 插入系统默认消息模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `type`, `module_code`, `sub_type`, `send_channels`,
                               `status`, `creator_id`)
VALUES ('system_notice', '系统通知', '系统通知：${title}',
        '<div>尊敬的用户：</div><div>${content}</div><div>如有疑问，请联系系统管理员。</div>', 'system', '', 'notice',
        'site', 1, 1),
       ('workflow_task_approval', '工作流任务审批通知', '您有一个待审批任务：${task_name}',
        '<div>您有一个新的待审批任务</div><div>流程标题：${process_title}</div><div>当前环节：${task_name}</div><div>提交人：${submitter_name}</div><div>提交时间：${submit_time}</div><div>请及时处理！</div>',
        'workflow', 'workflow', 'task', 'site,wework', 1, 1),
       ('workflow_task_approved', '工作流任务审批结果通知', '您的申请已审批完成：${process_title}',
        '<div>您的申请已审批完成</div><div>流程标题：${process_title}</div><div>审批结果：${result}</div><div>审批意见：${opinion}</div><div>审批人：${approver_name}</div><div>审批时间：${approve_time}</div>',
        'workflow', 'workflow', 'result', 'site,email', 1, 1),
       ('workflow_task_urge', '工作流催办通知', '催办提醒：${process_title}',
        '<div>您有一个待办任务被催办</div><div>流程标题：${process_title}</div><div>当前环节：${task_name}</div><div>催办人：${urger_name}</div><div>催办时间：${urge_time}</div><div>催办原因：${urge_reason}</div><div>请尽快处理！</div>',
        'workflow', 'workflow', 'urge', 'site,sms,wework', 1, 1);


# todo 暂不实施 消息归档表（示例，实际使用时按月创建）
DROP TABLE IF EXISTS `notice_message_archive_template`;
CREATE TABLE `notice_message_archive_template`
(
    `id`            bigint(20) unsigned NOT NULL COMMENT '消息ID',
    `template_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '模板ID',
    `title`         varchar(200)        NOT NULL COMMENT '消息标题',
    `content`       text                NOT NULL COMMENT '消息内容',
    `type`          varchar(20)         NOT NULL DEFAULT 'system' COMMENT '消息大类：system系统,workflow工作流,business业务',
    `module_code`   varchar(50)         NOT NULL DEFAULT '' COMMENT '业务模块编码：crm,erp,oa等',
    `sub_type`      varchar(30)                  DEFAULT NULL COMMENT '消息子类型',
    `business_id`   varchar(64)                  DEFAULT NULL COMMENT '业务ID',
    `detail_url`    varchar(255)                 DEFAULT NULL COMMENT 'PC端详情URL',
    `mobile_url`    varchar(255)                 DEFAULT NULL COMMENT '移动端跳转URL',
    `sender_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '发送人ID',
    `sender_name`   varchar(50)                  DEFAULT NULL COMMENT '发送人姓名',
    `send_time`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `priority`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '优先级：0普通，1重要，2紧急',
    `status`        tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态：0待发送，1已发送，2发送失败',
    `send_channels` varchar(50)         NOT NULL DEFAULT 'site' COMMENT '发送渠道：site站内信,email邮件,sms短信,wework微信,dingtalk钉钉',
    `created_at`    datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `tenant_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_template_id` (`template_id`),
    KEY `idx_business` (`module_code`, `business_id`),
    KEY `idx_send_time` (`send_time`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息归档表模板（按月分表）';

# todo 暂不实施  消息阅读状态归档表（示例，实际使用时按月创建）
DROP TABLE IF EXISTS `notice_recipient_archive_template`;

# todo 暂不实施
CREATE TABLE `notice_recipient_archive_template`
(
    `id`          bigint(20) unsigned NOT NULL COMMENT 'ID',
    `message_id`  bigint(20) unsigned NOT NULL COMMENT '消息ID',
    `user_id`     bigint(20) unsigned NOT NULL COMMENT '用户ID',
    `user_name`   varchar(50)         NOT NULL COMMENT '用户姓名',
    `read_status` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '读取状态：0未读，1已读',
    `read_time`   datetime                     DEFAULT NULL COMMENT '阅读时间',
    `read_client` varchar(20)                  DEFAULT NULL COMMENT '阅读终端',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `tenant_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_message_user` (`message_id`, `user_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息接收归档表模板（按月分表）';


DROP TABLE IF EXISTS `notice_template_tenant_config`;
CREATE TABLE `notice_template_tenant_config`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `tenant_id`            bigint(20) unsigned NOT NULL COMMENT '租户ID',
    `template_id`          bigint(20) unsigned NOT NULL COMMENT '模板ID',
    `template_code`        varchar(50)         NOT NULL COMMENT '模板编码',
    `is_enabled`           tinyint(1)          NOT NULL DEFAULT '1' COMMENT '是否启用：0禁用，1启用',
    `site_enabled`         tinyint(1)          NOT NULL DEFAULT '1' COMMENT '站内信是否启用：0禁用，1启用',
    `email_enabled`        tinyint(1)          NOT NULL DEFAULT '0' COMMENT '邮件是否启用：0禁用，1启用',
    `sms_enabled`          tinyint(1)          NOT NULL DEFAULT '0' COMMENT '短信是否启用：0禁用，1启用',
    `wework_enabled`       tinyint(1)          NOT NULL DEFAULT '0' COMMENT '企业微信是否启用：0禁用，1启用',
    `dingtalk_enabled`     tinyint(1)          NOT NULL DEFAULT '0' COMMENT '钉钉是否启用：0禁用，1启用',
    `webhook_enabled`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '自定义Webhook是否启用：0禁用，1启用',
    `sms_template_code`    varchar(50)                  DEFAULT NULL COMMENT '短信模板编码',
    `wework_webhook_url`   varchar(255)                 DEFAULT NULL COMMENT '企业微信Webhook URL',
    `dingtalk_webhook_url` varchar(255)                 DEFAULT NULL COMMENT '钉钉Webhook URL',
    `webhook_url`          varchar(255)                 DEFAULT NULL COMMENT '自定义Webhook URL',
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_template` (`tenant_id`, `template_id`),
    KEY `idx_template_code` (`template_code`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='租户消息模板配置表';

# todo 废弃
DROP TABLE IF EXISTS `notice_channel_tenant_config`;
CREATE TABLE `notice_channel_tenant_config`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `tenant_id`  bigint(20) unsigned NOT NULL COMMENT '租户ID',
    `channel`    varchar(20)         NOT NULL COMMENT '通道类型：email邮件,sms短信',
    `is_enabled` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否启用：0禁用，1启用',
    `config`     text                NOT NULL COMMENT '通道配置，JSON格式',
    `creator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_channel` (`tenant_id`, `channel`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='租户通道全局配置表';

ALTER TABLE `notice_message`
    ADD COLUMN `is_delayed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否延迟发送：0否，1是' AFTER `status`,
    ADD COLUMN `delay_minutes` int(11) NOT NULL DEFAULT '0' COMMENT '延迟发送分钟数' AFTER `is_delayed`;


ALTER TABLE `workflow_task`
    ADD COLUMN `task_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '任务类型：0审批任务，1抄送任务' AFTER `node_type`;