<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 附件模型
 */
class AttachmentModel extends BaseModel
{
    
    // 设置表名
    protected $name = 'system_attachment';
    
    // 设置字段信息
    /*protected $schema = [
        'id'            => 'int',
        'name'          => 'string',
        'path'          => 'string',
        'url'           => 'string',
        'extension'     => 'string',
        'size'          => 'int',
        'mime_type'     => 'string',
        'storage'       => 'string',
        'storage_id'    => 'string',
        'module'        => 'string',
        'business_id'   => 'int',
        'business_type' => 'string',
        'sort'          => 'int',
        'status'        => 'int',
        'creator_id'    => 'int',
        'updater_id'    => 'int',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'deleted_at'    => 'datetime',
        'tenant_id'     => 'int',
    ];*/
	
	public function getPathAttr($value)
	{
		return getImgUrl($value);
	}
    
    // 获取文件大小（格式化）
    public function getSizeAttr($value): string
    {
        $size = $value;
        
        if ($size < 1024) {
            return $size . 'B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . 'KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / (1024 * 1024), 2) . 'MB';
        } else {
            return round($size / (1024 * 1024 * 1024), 2) . 'GB';
        }
    }
    
    // 关联租户
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id', 'id');
    }
} 