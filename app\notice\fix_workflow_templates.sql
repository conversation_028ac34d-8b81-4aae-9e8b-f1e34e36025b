-- 修复工作流消息模板SQL脚本
-- 执行日期：2024-07-12
-- 修复内容：
-- 1. 修复workflow_task_approved模板的变量配置
-- 2. 创建缺失的workflow_task_cc模板
-- 3. 创建其他缺失的工作流模板

-- 1. 修复workflow_task_approved模板的变量配置
UPDATE `notice_template`
SET `variables_config` = '{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "审批结果",
      "code": "审批结果",
      "field": "result",
      "required": true,
      "description": "审批结果：已通过/已驳回"
    },
    {
      "name": "提交时间",
      "code": "提交时间",
      "field": "submit_time",
      "required": false,
      "description": "流程提交时间"
    },
    {
      "name": "完成时间",
      "code": "完成时间",
      "field": "finish_time",
      "required": false,
      "description": "审批完成时间"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "流程详情页链接"
    }
  ]
}',
`content` = '您的申请已审批完成\n流程标题：${流程标题}\n审批结果：${审批结果}\n提交时间：${提交时间}\n完成时间：${完成时间}\n\n点击查看详情：${详情链接}'
WHERE `code` = 'workflow_task_approved';

-- 2. 创建workflow_task_cc抄送通知模板
INSERT INTO `notice_template`
(`code`, `name`, `title`, `content`, `variables_config`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES
('workflow_task_cc', '工作流抄送通知', '您收到一个抄送：${流程标题}',
'您收到一个抄送通知\n流程标题：${流程标题}\n提交人：${提交人}\n节点名称：${节点名称}\n抄送时间：${抄送时间}\n\n请知悉。',
'{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "提交人",
      "code": "提交人",
      "field": "submitter_name",
      "required": true,
      "description": "流程提交人姓名"
    },
    {
      "name": "节点名称",
      "code": "节点名称",
      "field": "node_name",
      "required": true,
      "description": "抄送节点名称"
    },
    {
      "name": "抄送时间",
      "code": "抄送时间",
      "field": "cc_time",
      "required": true,
      "description": "抄送时间"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "流程详情页链接"
    }
  ]
}',
'workflow', 'site,wework', 1, 1, 0)
ON DUPLICATE KEY UPDATE
`title` = VALUES(`title`),
`content` = VALUES(`content`),
`variables_config` = VALUES(`variables_config`);

-- 3. 创建workflow_task_urge催办通知模板
INSERT INTO `notice_template`
(`code`, `name`, `title`, `content`, `variables_config`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES
('workflow_task_urge', '工作流催办通知', '您有一个待处理任务被催办：${流程标题}',
'您有一个待处理任务被催办\n流程标题：${流程标题}\n任务名称：${任务名称}\n催办人：${催办人}\n催办时间：${催办时间}\n催办原因：${催办原因}\n\n请及时处理！',
'{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "任务名称",
      "code": "任务名称",
      "field": "task_name",
      "required": true,
      "description": "待处理任务名称"
    },
    {
      "name": "催办人",
      "code": "催办人",
      "field": "urger_name",
      "required": true,
      "description": "催办人姓名"
    },
    {
      "name": "催办时间",
      "code": "催办时间",
      "field": "created_at",
      "required": true,
      "description": "催办时间"
    },
    {
      "name": "催办原因",
      "code": "催办原因",
      "field": "reason",
      "required": false,
      "description": "催办原因"
    }
  ]
}',
'workflow', 'site,wework', 1, 1, 0)
ON DUPLICATE KEY UPDATE
`title` = VALUES(`title`),
`content` = VALUES(`content`),
`variables_config` = VALUES(`variables_config`);

-- 4. 创建workflow_task_transfer转交通知模板
INSERT INTO `notice_template`
(`code`, `name`, `title`, `content`, `variables_config`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES
('workflow_task_transfer', '工作流转交通知', '您收到一个转交任务：${流程标题}',
'您收到一个转交任务\n流程标题：${流程标题}\n节点名称：${节点名称}\n转交人：${转交人}\n接收人：${接收人}\n转交时间：${转交时间}\n\n请及时处理！',
'{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "节点名称",
      "code": "节点名称",
      "field": "node_name",
      "required": true,
      "description": "转交任务节点名称"
    },
    {
      "name": "转交人",
      "code": "转交人",
      "field": "from_user",
      "required": true,
      "description": "转交人姓名"
    },
    {
      "name": "接收人",
      "code": "接收人",
      "field": "to_user",
      "required": true,
      "description": "接收人姓名"
    },
    {
      "name": "转交时间",
      "code": "转交时间",
      "field": "transfer_time",
      "required": true,
      "description": "转交时间"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "任务详情页链接"
    }
  ]
}',
'workflow', 'site,wework', 1, 1, 0)
ON DUPLICATE KEY UPDATE
`title` = VALUES(`title`),
`content` = VALUES(`content`),
`variables_config` = VALUES(`variables_config`);

-- 5. 创建workflow_task_terminated终止通知模板
INSERT INTO `notice_template`
(`code`, `name`, `title`, `content`, `variables_config`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES
('workflow_task_terminated', '工作流终止通知', '您的申请已被终止：${流程标题}',
'您的申请已被终止\n流程标题：${流程标题}\n审批结果：${审批结果}\n提交时间：${提交时间}\n终止时间：${终止时间}\n终止人：${终止人}\n终止原因：${终止原因}\n\n详情链接：${详情链接}',
'{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "审批结果",
      "code": "审批结果",
      "field": "result",
      "required": true,
      "description": "审批结果"
    },
    {
      "name": "提交时间",
      "code": "提交时间",
      "field": "submit_time",
      "required": false,
      "description": "流程提交时间"
    },
    {
      "name": "终止时间",
      "code": "终止时间",
      "field": "terminate_time",
      "required": true,
      "description": "流程终止时间"
    },
    {
      "name": "终止人",
      "code": "终止人",
      "field": "terminate_by",
      "required": true,
      "description": "终止操作人"
    },
    {
      "name": "终止原因",
      "code": "终止原因",
      "field": "reason",
      "required": false,
      "description": "终止原因"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "流程详情页链接"
    }
  ]
}',
'workflow', 'site,wework', 1, 1, 0)
ON DUPLICATE KEY UPDATE
`title` = VALUES(`title`),
`content` = VALUES(`content`),
`variables_config` = VALUES(`variables_config`);

-- 6. 修复workflow_task_approval任务审批通知模板的变量配置
UPDATE `notice_template`
SET `variables_config` = '{
  "variables": [
    {
      "name": "任务名称",
      "code": "任务名称",
      "field": "task_name",
      "required": true,
      "description": "审批任务名称"
    },
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "提交人",
      "code": "提交人",
      "field": "submitter_name",
      "required": true,
      "description": "流程提交人姓名"
    },
    {
      "name": "创建时间",
      "code": "创建时间",
      "field": "created_at",
      "required": true,
      "description": "任务创建时间"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "任务详情页链接"
    }
  ]
}',
`content` = '您有一个新的待审批任务\n流程标题：${流程标题}\n当前环节：${任务名称}\n提交人：${提交人}\n提交时间：${创建时间}\n\n请及时处理！\n详情链接：${详情链接}'
WHERE `code` = 'workflow_task_approval';

-- 执行完成后的验证查询
-- SELECT code, name, title FROM notice_template WHERE module_code = 'workflow' ORDER BY code;
