<?php

namespace app\common\lib\upload;

interface UploadStorageInterface
{
	/**
	 * 获取上传Token
	 * @param array $config 配置
	 * @param array $params 参数
	 * @return string|array
	 */
	public function getUploadToken(array $config, array $params = []): string|array;
	
	/**
	 * 上传文件
	 * @param array $file 文件信息
	 * @param array $config 配置
	 * @return array
	 */
	public function upload(array $file, array $config): array;
	
	/**
	 * 删除文件
	 * @param string $filePath 文件路径
	 * @param array $config 配置
	 * @return bool
	 */
	public function delete(string $filePath, array $config): bool;
	
	/**
	 * 处理上传回调
	 * @param array $params 回调参数
	 * @param array $config 配置
	 * @return array
	 */
	public function callback(array $params, array $config): array;
	
}