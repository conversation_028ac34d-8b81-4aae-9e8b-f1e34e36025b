<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use think\Model;
use think\Collection;

/**
 * 字段场景处理特性
 * 用于实现不同场景下的字段筛选返回
 */
trait FieldSceneTrait
{
    /**
     * 存储字段场景配置
     */
    protected $fieldScenes = [];
    
    /**
     * 设置字段场景配置
     */
    public function setFieldScenes(array $scenes): self
    {
        $this->fieldScenes = $scenes;
        return $this;
    }
    
    /**
     * 添加字段场景配置
     */
    public function addFieldScene(string $scene, array $fields): self
    {
        $this->fieldScenes[$scene] = $fields;
        return $this;
    }
    
    /**
     * 获取字段场景配置
     */
    public function getFieldScene($scene = 'list')
    {
        // 1. 从服务配置获取
        if (isset($this->fieldScenes[$scene])) {
            return $this->fieldScenes[$scene];
        }
        
        // 2. 从模型获取
        if ($this->model && method_exists($this->model, 'getFieldScene')) {
            $fields = $this->model->getFieldScene($scene);
            if ($fields) return $fields;
        }
        
        // 3. 如果没有找到，返回默认场景或所有字段
        return $this->fieldScenes['list'] ?? ['*'];
    }
    
    /**
     * 应用场景字段过滤
     */
    protected function applyFieldScene($data, $scene = 'list')
    {
        $fields = $this->getFieldScene($scene);
        
        // 如果是 ['*']，返回所有字段
        if ($fields === ['*'] || empty($fields)) {
            return $data;
        }
        
        // 处理集合
        if ($data instanceof Collection) {
            return $data->map(function ($item) use ($fields) {
                return $this->filterItemFields($item, $fields);
            });
        }
        
        // 处理单个模型
        if ($data instanceof Model) {
            return $this->filterItemFields($data, $fields);
        }
        
        // 处理数组
        if (is_array($data)) {
            // 如果是搜索结果数组
            if (isset($data['list']) && $data['list'] instanceof Collection) {
                $data['list'] = $data['list']->map(function ($item) use ($fields) {
                    return $this->filterItemFields($item, $fields);
                });
                return $data;
            }
        }
        
        return $data;
    }
    
    /**
     * 过滤单个数据项的字段
     */
    protected function filterItemFields($item, array $fields)
    {
        // 如果不是模型实例，直接返回
        if (!($item instanceof Model)) {
            return $item;
        }
        
		// todo 这里修改了，看后续是否会有影响
//	    $result = [];
        $result = $item;
        
        // 处理字段
        foreach ($fields as $key => $value) {
            // 处理关联
            if (is_string($key) && is_array($value)) {
                $this->processRelationFields($result, $item, $key, $value);
            }
            // 处理普通字段
            else if (is_string($value)) {
                $result[$value] = $item->getAttr($value);
            }
        }
        
        return $result;
    }
    
    /**
     * 处理关联数据字段
     */
    protected function processRelationFields(&$result, $item, $relation, $fields)
    {
        // 确保关联已加载
        if (!$item->isRelationLoaded($relation)) {
            // 如果关联未加载，则跳过
            return;
        }
        
        $relationData = $item->getRelation($relation);
        if (!$relationData) {
            return;
        }
        
        if ($relationData instanceof Collection) {
            $result[$relation] = $relationData->map(function ($relItem) use ($fields) {
                return $this->filterItemFields($relItem, $fields);
            });
        } else {
            $result[$relation] = $this->filterItemFields($relationData, $fields);
        }
    }
} 