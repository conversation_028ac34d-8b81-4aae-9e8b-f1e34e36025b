<?php
declare(strict_types=1);

namespace app\common\exception;

use Exception;

/**
 * 租户异常类
 */
class TenantException extends Exception
{
    /**
     * 构造方法
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param Exception|null $previous 上一个异常
     */
    public function __construct(string $message = '租户访问受限', int $code = 403, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
} 