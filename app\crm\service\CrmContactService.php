<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmContact;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 联系人表服务类
 */
class CrmContactService extends BaseService
{

    use ExportableTrait,ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmContact();
        parent::__construct();
    }
    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

            'customer_id' => ['type' => 'eq'],

            'name' => ['type' => 'like'],

            'gender' => ['type' => 'eq'],

            'position' => ['type' => 'like'],

            'department' => ['type' => 'like'],

            'mobile' => ['type' => 'like'],

            'email' => ['type' => 'like'],

            'importance' => ['type' => 'eq'],

            'role_type' => ['type' => 'eq'],

            'birthday' => ['type' => 'date'],

            'is_primary' => ['type' => 'eq'],

        ];
    }
    
    /**
     * 获取验证规则 - 基于crm_data.sql字段约束
     *
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            'customer_id' => 'require|integer|gt:0',
            'name' => 'require|max:50',
            'gender' => 'integer|in:0,1,2',
            'position' => 'max:50',
            'phone' => 'max:20',
            'mobile' => 'max:20',
            'email' => 'email|max:100',
            'wechat' => 'max:50',
            'qq' => 'max:20',
            'address' => 'max:500',
            'birthday' => 'date',
            'importance' => 'integer|in:0,1,2,3',
            'role_type' => 'integer|in:0,1,2,3',
            'status' => 'integer|in:0,1',
            'remark' => 'max:500',
        ];

        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('id', $ids)->delete();
    }
} 