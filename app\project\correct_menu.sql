-- 项目任务管理模块菜单配置（修正版）
-- 根据实际生成的控制器命名来配置权限标识
-- 权限标识格式：模块:控制器:方法 (例如: project:project:index)

-- 获取当前最大菜单ID
SET @max_id = (SELECT IFNULL(MAX(id), 0) FROM system_menu);

-- 项目管理主菜单 (目录)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 1, 0, '项目管理', 'project', '/project', '', 0, 'el-icon-folder-opened', 300, 0, 0, 1, 1, '项目任务管理模块', NOW(), NOW());

-- 项目列表 (菜单) - 对应 project:project:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 2, @max_id + 1, '项目列表', 'project:project:index', '/project/project-list', '/project/project/list', 1, 'el-icon-folder', 1, 0, 1, 1, 1, '项目列表管理', NOW(), NOW());

-- 任务管理 (菜单) - 对应 project:projecttask:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 3, @max_id + 1, '任务管理', 'project:projecttask:index', '/project/task-list', '/project/project_task/list', 1, 'el-icon-tickets', 2, 0, 1, 1, 1, '任务列表管理', NOW(), NOW());

-- 项目成员 (菜单) - 对应 project:projectmember:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 4, @max_id + 1, '项目成员', 'project:projectmember:index', '/project/member-list', '/project/project_member/list', 1, 'el-icon-user', 3, 0, 1, 1, 1, '项目成员管理', NOW(), NOW());

-- 任务评论 (菜单) - 对应 project:projecttaskcomment:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 5, @max_id + 1, '任务评论', 'project:projecttaskcomment:index', '/project/comment-list', '/project/project_task_comment/list', 1, 'el-icon-chat-dot-round', 4, 0, 1, 1, 1, '任务评论管理', NOW(), NOW());

-- ================================
-- 按钮权限配置 (基于实际生成的控制器方法)
-- ================================

-- 项目管理按钮权限 (project:project:*)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 10, @max_id + 2, '查看项目', 'project:project:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看项目详情', NOW(), NOW()),
(@max_id + 11, @max_id + 2, '新增项目', 'project:project:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增项目', NOW(), NOW()),
(@max_id + 12, @max_id + 2, '编辑项目', 'project:project:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑项目', NOW(), NOW()),
(@max_id + 13, @max_id + 2, '删除项目', 'project:project:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除项目', NOW(), NOW()),
(@max_id + 14, @max_id + 2, '批量删除项目', 'project:project:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除项目', NOW(), NOW()),
(@max_id + 15, @max_id + 2, '更新项目字段', 'project:project:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新项目字段', NOW(), NOW()),
(@max_id + 16, @max_id + 2, '项目状态', 'project:project:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改项目状态', NOW(), NOW()),
(@max_id + 17, @max_id + 2, '我的项目', 'project:project:myprojects', '', '', 2, '', 8, 0, 0, 1, 1, '我的项目列表', NOW(), NOW()),
(@max_id + 18, @max_id + 2, '项目详情', 'project:project:projectdetail', '', '', 2, '', 9, 0, 0, 1, 1, '项目详情信息', NOW(), NOW()),
(@max_id + 19, @max_id + 2, '项目看板', 'project:project:kanban', '', '', 2, '', 10, 0, 0, 1, 1, '项目看板数据', NOW(), NOW()),
(@max_id + 20, @max_id + 2, '添加成员', 'project:project:addmember', '', '', 2, '', 11, 0, 0, 1, 1, '添加项目成员', NOW(), NOW()),
(@max_id + 21, @max_id + 2, '移除成员', 'project:project:removemember', '', '', 2, '', 12, 0, 0, 1, 1, '移除项目成员', NOW(), NOW());

-- 任务管理按钮权限 (project:projecttask:*)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 30, @max_id + 3, '查看任务', 'project:projecttask:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看任务详情', NOW(), NOW()),
(@max_id + 31, @max_id + 3, '新增任务', 'project:projecttask:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增任务', NOW(), NOW()),
(@max_id + 32, @max_id + 3, '编辑任务', 'project:projecttask:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑任务', NOW(), NOW()),
(@max_id + 33, @max_id + 3, '删除任务', 'project:projecttask:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除任务', NOW(), NOW()),
(@max_id + 34, @max_id + 3, '批量删除任务', 'project:projecttask:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除任务', NOW(), NOW()),
(@max_id + 35, @max_id + 3, '更新任务字段', 'project:projecttask:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新任务字段', NOW(), NOW()),
(@max_id + 36, @max_id + 3, '任务状态', 'project:projecttask:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改任务状态', NOW(), NOW()),
(@max_id + 37, @max_id + 3, '我的任务', 'project:projecttask:mytasks', '', '', 2, '', 8, 0, 0, 1, 1, '我的任务列表', NOW(), NOW()),
(@max_id + 38, @max_id + 3, '更新状态', 'project:projecttask:updatestatus', '', '', 2, '', 9, 0, 0, 1, 1, '更新任务状态', NOW(), NOW()),
(@max_id + 39, @max_id + 3, '分配任务', 'project:projecttask:assign', '', '', 2, '', 10, 0, 0, 1, 1, '分配任务', NOW(), NOW()),
(@max_id + 40, @max_id + 3, '添加评论', 'project:projecttask:addcomment', '', '', 2, '', 11, 0, 0, 1, 1, '添加任务评论', NOW(), NOW());

-- 项目成员按钮权限 (project:projectmember:*)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 50, @max_id + 4, '查看成员', 'project:projectmember:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看成员详情', NOW(), NOW()),
(@max_id + 51, @max_id + 4, '新增成员', 'project:projectmember:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增项目成员', NOW(), NOW()),
(@max_id + 52, @max_id + 4, '编辑成员', 'project:projectmember:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑项目成员', NOW(), NOW()),
(@max_id + 53, @max_id + 4, '删除成员', 'project:projectmember:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除项目成员', NOW(), NOW()),
(@max_id + 54, @max_id + 4, '批量删除成员', 'project:projectmember:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除成员', NOW(), NOW()),
(@max_id + 55, @max_id + 4, '更新成员字段', 'project:projectmember:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新成员字段', NOW(), NOW()),
(@max_id + 56, @max_id + 4, '成员状态', 'project:projectmember:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改成员状态', NOW(), NOW());

-- 任务评论按钮权限 (project:projecttaskcomment:*)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 60, @max_id + 5, '查看评论', 'project:projecttaskcomment:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看评论详情', NOW(), NOW()),
(@max_id + 61, @max_id + 5, '新增评论', 'project:projecttaskcomment:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增任务评论', NOW(), NOW()),
(@max_id + 62, @max_id + 5, '编辑评论', 'project:projecttaskcomment:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑任务评论', NOW(), NOW()),
(@max_id + 63, @max_id + 5, '删除评论', 'project:projecttaskcomment:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除任务评论', NOW(), NOW()),
(@max_id + 64, @max_id + 5, '批量删除评论', 'project:projecttaskcomment:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除评论', NOW(), NOW()),
(@max_id + 65, @max_id + 5, '更新评论字段', 'project:projecttaskcomment:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新评论字段', NOW(), NOW()),
(@max_id + 66, @max_id + 5, '评论状态', 'project:projecttaskcomment:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改评论状态', NOW(), NOW());
