<?php
declare(strict_types=1);

namespace app\system\service;

use app\system\model\NoticeModel;

/**
 * 通知公告服务类
 */
class NoticeService
{
	/**
	 * 获取通知公告列表
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getNoticeList(array $params): array
	{
		$page = isset($params['page']) ? (int)$params['page'] : 1;
		$limit = isset($params['limit']) ? (int)$params['limit'] : 10;
		
		$query = NoticeModel::where('deleted_at', null);
		
		// 租户ID筛选
		if (isset($params['tenant_id'])) {
			$query->where(function ($q) use ($params) {
				$q->where('tenant_id', $params['tenant_id'])
				  ->whereOr(function ($sq) {
					  $sq->where('tenant_id', 0)
					     ->where('is_tenant_visible', 1);
				  });
			});
		}
		
		// 条件筛选
		if (!empty($params['title'])) {
			$query->whereLike('title', '%' . $params['title'] . '%');
		}
		
		if (isset($params['type']) && $params['type'] !== '') {
			$query->where('type', $params['type']);
		}
		
		if (isset($params['status']) && $params['status'] !== '') {
			$query->where('status', $params['status']);
		}
		
		// 获取总数
		$total = $query->count();
		
		// 获取分页数据
		$list = $query->page($page, $limit)
		              ->order('is_top', 'desc')
		              ->order('id', 'desc')
		              ->select()
		              ->toArray();
		
		return [
			'list' => $list,
			'total' => $total
		];
	}
	
	/**
	 * 获取通知公告详情
	 * @param int $id 通知公告ID
	 * @return array|null
	 */
	public function getNoticeDetail(int $id): ?array
	{
		$notice = NoticeModel::where('id', $id)
		                     ->where('deleted_at', null)
		                     ->findOrEmpty();
				  
		return $notice->isEmpty() ? null : $notice->toArray();
	}
	
	/**
	 * 创建通知公告
	 * @param array $params 通知公告信息
	 * @return array|false
	 */
	public function createNotice(array $params)
	{
		try {
			$notice = new NoticeModel();
			$notice->title = $params['title'];
			$notice->content = $params['content'];
			$notice->type = $params['type'];
			$notice->scope_type = $params['scope_type'] ?? 1;
			$notice->scope_ids = $params['scope_ids'] ?? '';
			$notice->status = $params['status'];
			$notice->is_top = $params['is_top'] ?? 0;
			$notice->is_tenant_visible = $params['is_tenant_visible'] ?? 0;
			$notice->remark = $params['remark'] ?? '';
			$notice->tenant_id = $params['tenant_id'] ?? 0;
			$notice->creator_id = $params['creator_id'] ?? 0;
			$notice->updater_id = $params['creator_id'] ?? 0;
			
			if ($notice->save()) {
				return $this->getNoticeDetail($notice->id);
			}
			
			return false;
		} catch (\Exception $e) {
			return false;
		}
	}
	
	/**
	 * 更新通知公告
	 * @param int $id 通知公告ID
	 * @param array $params 通知公告信息
	 * @return bool
	 */
	public function updateNotice(int $id, array $params): bool
	{
		$notice = NoticeModel::where('id', $id)->findOrEmpty();
		
		if ($notice->isEmpty()) {
			return false;
		}
		
		if (isset($params['title'])) {
			$notice->title = $params['title'];
		}
		
		if (isset($params['content'])) {
			$notice->content = $params['content'];
		}
		
		if (isset($params['type'])) {
			$notice->type = $params['type'];
		}
		
		if (isset($params['scope_type'])) {
			$notice->scope_type = $params['scope_type'];
		}
		
		if (isset($params['scope_ids'])) {
			$notice->scope_ids = $params['scope_ids'];
		}
		
		if (isset($params['status'])) {
			$notice->status = $params['status'];
		}
		
		if (isset($params['is_top'])) {
			$notice->is_top = $params['is_top'];
		}
		
		if (isset($params['is_tenant_visible'])) {
			$notice->is_tenant_visible = $params['is_tenant_visible'];
		}
		
		if (isset($params['remark'])) {
			$notice->remark = $params['remark'];
		}
		
		if (isset($params['updater_id'])) {
			$notice->updater_id = $params['updater_id'];
		}
		
		return $notice->save();
	}
	
	/**
	 * 删除通知公告
	 * @param int $id 通知公告ID
	 * @return bool
	 */
	public function deleteNotice(int $id): bool
	{
		$notice = NoticeModel::where('id', $id)->findOrEmpty();
		
		if ($notice->isEmpty()) {
			return false;
		}
		
		$notice->deleted_at = date('Y-m-d H:i:s');
		return $notice->save();
	}
}