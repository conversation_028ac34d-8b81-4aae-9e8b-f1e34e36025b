<?php
declare(strict_types=1);

namespace app\notice\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 租户消息模板配置模型
 */
class NoticeTemplateTenantConfigModel extends BaseModel
{
	/**
	 * 数据表名称
	 *
	 * @var string
	 */
	protected $name = 'notice_template_tenant_config';
	
	/**
	 * 自动写入时间戳
	 *
	 * @var bool
	 */
	protected $autoWriteTimestamp = true;
	
	/**
	 * 创建时间字段
	 *
	 * @var string
	 */
	protected $createTime = 'created_at';
	
	/**
	 * 更新时间字段
	 *
	 * @var string
	 */
	protected $updateTime = 'updated_at';
	
	protected bool $isSoftDelete = false;
	
	/**
	 * 允许写入的字段
	 *
	 * @var array
	 */
	protected $field = [
		'id',
		'template_id',
		'template_code',
		'is_enabled',
		'site_enabled',
		'email_enabled',
		'sms_enabled',
		'wework_enabled',
		'dingtalk_enabled',
		'webhook_enabled',
		'sms_template_code',
		'wework_webhook_url',
		'dingtalk_webhook_url',
		'webhook_url',
		'created_at',
	];
	
	/**
	 * 模板关联
	 *
	 * @return BelongsTo
	 */
	public function template()
	{
		return $this->belongsTo(NoticeTemplateModel::class, 'template_id', 'id');
	}
	
	/**
	 * 获取启用的通道列表
	 *
	 * @return array 启用的通道编码列表
	 */
	public function getEnabledChannels(): array
	{
		$channels = [];
		
		if (!$this->is_enabled) {
			return $channels;
		}
		
		if ($this->site_enabled) {
			$channels[] = 'site';
		}
		
		if ($this->email_enabled) {
			$channels[] = 'email';
		}
		
		if ($this->sms_enabled) {
			$channels[] = 'sms';
		}
		
		if ($this->wework_enabled) {
			$channels[] = 'wework';
		}
		
		if ($this->dingtalk_enabled) {
			$channels[] = 'dingtalk';
		}
		
		if ($this->webhook_enabled) {
			$channels[] = 'webhook';
		}
		
		return $channels;
	}
} 