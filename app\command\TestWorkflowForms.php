<?php
declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\workflow\model\WorkflowType;
use app\workflow\model\WorkflowDefinition;
use app\workflow\factory\DynamicWorkflowFactory;

/**
 * 测试工作流表单配置
 */
class TestWorkflowForms extends Command
{
    protected function configure()
    {
        $this->setName('test:workflow-forms')
             ->setDescription('测试工作流表单配置和Service映射');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始测试工作流表单配置...');
        
        // 测试的业务类型
        $businessCodes = [
            'hr_business_trip',
            'hr_outing', 
            'office_sample_mail',
            'finance_payment_approval',
            'finance_expense_reimbursement',
            'ims_outbound_approval',
            'ims_shipment_approval'
        ];
        
        $output->writeln('');
        $output->writeln('=== 检查workflow_type表配置 ===');
        
        foreach ($businessCodes as $businessCode) {
            $workflowType = WorkflowType::where('business_code', $businessCode)->find();
            
            if ($workflowType) {
                $output->writeln("✅ {$businessCode}: {$workflowType->name} (ID: {$workflowType->id})");
                
                // 检查是否有对应的工作流定义
                $definition = WorkflowDefinition::where('type_id', $workflowType->id)->find();
                if ($definition) {
                    $output->writeln("   ✅ 工作流定义: {$definition->name}");
                } else {
                    $output->writeln("   ❌ 缺少工作流定义");
                }
            } else {
                $output->writeln("❌ {$businessCode}: 未找到工作流类型");
            }
        }
        
        $output->writeln('');
        $output->writeln('=== 检查Service映射 ===');
        
        foreach ($businessCodes as $businessCode) {
            try {
                $service = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
                
                if ($service) {
                    $className = get_class($service);
                    $output->writeln("✅ {$businessCode}: {$className}");
                    
                    // 检查Service是否实现了FormServiceInterface
                    if ($service instanceof \app\workflow\interfaces\FormServiceInterface) {
                        $output->writeln("   ✅ 实现FormServiceInterface接口");
                    } else {
                        $output->writeln("   ❌ 未实现FormServiceInterface接口");
                    }
                } else {
                    $output->writeln("❌ {$businessCode}: Service创建失败");
                }
            } catch (\Exception $e) {
                $output->writeln("❌ {$businessCode}: {$e->getMessage()}");
            }
        }
        
        $output->writeln('');
        $output->writeln('=== 测试完成 ===');
        
        return 0;
    }
}
