<template>
  <div class="payment-form-view">
    <el-descriptions :column="2" border>
      <!--      <el-descriptions-item label="付款单号">
              {{ formData.payment_no || '-' }}
            </el-descriptions-item>-->

      <el-descriptions-item label="支付对象">
        {{ formData.payee_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="收款账号">
        {{ formData.payee_account || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="收款银行">
        {{ formData.payee_bank || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="付款金额">
        ¥{{ formData.payment_amount || 0 }}
      </el-descriptions-item>

      <el-descriptions-item label="支付日期">
        {{ formData.payment_date || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="付款方式">
        {{ getPaymentMethodLabel(formData.payment_method) }}
      </el-descriptions-item>

      <el-descriptions-item label="付款事由" :span="2">
        {{ formData.reason || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        {{ formData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem } from 'element-plus'
  import { getPaymentMethodLabel } from '@/utils/payment'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'finance_payment_approval'
    }
  })
</script>

<style scoped lang="scss"></style>
