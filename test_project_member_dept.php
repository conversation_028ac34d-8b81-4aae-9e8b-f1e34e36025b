<?php
/**
 * 测试项目成员部门关联
 * 验证通过嵌套关联获取用户部门名称
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use app\project\model\ProjectMember;

try {
    // 初始化ThinkPHP应用
    $app = new \think\App();
    $app->initialize();
    
    echo "=== 项目成员部门关联测试 ===\n\n";
    
    // 1. 测试单个项目成员的部门信息获取
    echo "1. 测试单个项目成员的部门信息获取\n";
    echo "-----------------------------------\n";
    
    $member = ProjectMember::with(['user.dept'])->find(1);
    
    if ($member) {
        echo "成员ID: " . $member->id . "\n";
        echo "用户ID: " . $member->user_id . "\n";
        echo "用户名: " . ($member->username ?? 'N/A') . "\n";
        echo "真实姓名: " . ($member->real_name ?? 'N/A') . "\n";
        echo "部门ID: " . ($member->dept_id ?? 'N/A') . "\n";
        echo "部门名称: " . ($member->dept_name ?? 'N/A') . "\n";
        echo "角色: " . $member->getRoleText() . "\n";
        
        // 检查关联数据
        if ($member->user) {
            echo "\n关联用户信息:\n";
            echo "- 用户真实姓名: " . $member->user->real_name . "\n";
            echo "- 用户部门ID: " . $member->user->dept_id . "\n";
            
            if ($member->user->dept) {
                echo "- 部门信息: " . $member->user->dept->name . "\n";
            } else {
                echo "- 部门信息: 未关联部门\n";
            }
        }
    } else {
        echo "未找到项目成员记录\n";
    }
    
    echo "\n";
    
    // 2. 测试项目成员列表的部门信息获取
    echo "2. 测试项目成员列表的部门信息获取\n";
    echo "-----------------------------------\n";
    
    $members = ProjectMember::with(['user.dept'])
                           ->limit(5)
                           ->select();
    
    if ($members->count() > 0) {
        foreach ($members as $member) {
            echo "成员: {$member->real_name} | ";
            echo "部门: " . ($member->dept_name ?? '未分配') . " | ";
            echo "角色: {$member->getRoleText()}\n";
        }
    } else {
        echo "未找到项目成员记录\n";
    }
    
    echo "\n";
    
    // 3. 测试原生SQL查询验证数据结构
    echo "3. 原生SQL查询验证数据结构\n";
    echo "-----------------------------------\n";
    
    $sql = "
        SELECT 
            pm.id as member_id,
            pm.user_id,
            pm.role,
            sa.real_name,
            sa.dept_id,
            sd.name as dept_name
        FROM project_member pm
        LEFT JOIN system_admin sa ON pm.user_id = sa.id
        LEFT JOIN system_dept sd ON sa.dept_id = sd.id
        WHERE pm.deleted_at IS NULL
        LIMIT 5
    ";
    
    $results = Db::query($sql);
    
    if (!empty($results)) {
        foreach ($results as $row) {
            echo "成员ID: {$row['member_id']} | ";
            echo "姓名: {$row['real_name']} | ";
            echo "部门: " . ($row['dept_name'] ?? '未分配') . " | ";
            echo "角色: {$row['role']}\n";
        }
    } else {
        echo "未找到数据\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
