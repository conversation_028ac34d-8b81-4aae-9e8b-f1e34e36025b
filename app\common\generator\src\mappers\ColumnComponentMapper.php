<?php
declare(strict_types=1);

namespace app\common\generator\src\mappers;

/**
 * 列组件映射器
 * 负责将数据库字段映射为前端表格列组件
 */
class ColumnComponentMapper
{
    /**
     * 组件映射配置
     * @var array
     */
    protected array $config;
    
    /**
     * 构造函数
     * 
     * @param array $config 组件映射配置
     */
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * 获取字段对应的列组件
     * 
     * @param array $column 字段信息
     * @return array|null 列组件信息
     */
    public function getColumnComponent(array $column): ?array
    {
        // 获取基本信息
        $field = $column['Field'];
        $type = $column['Type'];
        $fieldLower = strtolower($field);
        
        // 1. 先检查是否在注释中有明确的组件标记
        $componentType = null;
        // 优先检查component标记
        if (isset($column['parsed_comment']['tags']['component'])) {
            $componentType = $column['parsed_comment']['tags']['component'];
        } 
        // 向后兼容column标记
        else if (isset($column['parsed_comment']['tags']['column'])) {
            $componentType = $column['parsed_comment']['tags']['column'];
        }
        
        if ($componentType !== null) {
            // 检查新格式组件映射
            if (isset($this->config['component_mappings'][$componentType])) {
                $mappedType = $this->config['component_mappings'][$componentType];
                return $this->getComponentConfig($mappedType, $column);
            }
            // 直接使用配置的组件
            if (isset($this->config[$componentType])) {
                return $this->getComponentConfig($componentType, $column);
            }
        }
        
        // 2. 检查是否在注释中有明确的格式化标记
        $formatter = null;
        if (isset($column['parsed_comment']['tags']['formatter'])) {
            $formatter = $column['parsed_comment']['tags']['formatter'];
        } else if (isset($column['parsed_comment']['tags']['fmt'])) {
            $formatter = $column['parsed_comment']['tags']['fmt'];
        } else if (isset($column['parsed_comment']['tags']['format'])) {
            $formatter = $column['parsed_comment']['tags']['format'];
        }
        
        if ($formatter !== null) {
            if (isset($this->config['formatter_mappings'][$formatter])) {
                $componentType = $this->config['formatter_mappings'][$formatter];
                return $this->getComponentConfig($componentType, $column);
            }
        }
        
        // 3. 根据字段名称模式匹配
        foreach ($this->config['field_name_patterns'] as $pattern => $componentType) {
            if (stripos($fieldLower, $pattern) !== false) {
                return $this->getComponentConfig($componentType, $column);
            }
        }
        
        // 4. 根据字段类型匹配
        if (isset($this->config['field_type_patterns'][$type])) {
            $componentType = $this->config['field_type_patterns'][$type];
            return $this->getComponentConfig($componentType, $column);
        }
        
        // 5. 针对tinyint(1)类型特殊处理
        if (preg_match('/^tinyint\(1\)/', $type)) {
            return $this->getComponentConfig('switch', $column);
        }
        
        // 6. 针对枚举类型特殊处理
        if (preg_match('/^enum/', $type)) {
            return $this->getComponentConfig('tag', $column);
        }
        
        // 7. 根据字段类型的前缀进行通用处理
        if (preg_match('/^(int|tinyint|smallint|mediumint|bigint)/', $type)) {
            return $this->getComponentConfig('number', $column);
        }
        
        if (preg_match('/^(decimal|float|double)/', $type)) {
            return $this->getComponentConfig('currency', $column);
        }
        
        if (preg_match('/^(text|mediumtext|longtext)/', $type)) {
            return $this->getComponentConfig('long_text', $column);
        }
        
        // 8. 没有匹配到任何规则，返回null
        return null;
    }
    
    /**
     * 获取组件配置
     * 
     * @param string $componentType 组件类型
     * @param array $column 字段信息
     * @return array 组件配置
     */
    protected function getComponentConfig(string $componentType, array $column): array
    {
        if (!isset($this->config[$componentType])) {
            // 如果没有配置，返回默认文本组件
            return [
                'component' => 'text-column',
                'props' => []
            ];
        }
        
        $config = $this->config[$componentType];
        $component = $config['component'];
        $props = $config['props'] ?? [];
        $isDirectComponent = $config['isDirectComponent'] ?? false;
        
        // 处理需要选项的组件
        if (isset($config['needOptions']) && $config['needOptions']) {
            $props = $this->getOptionsProps($props, $column);
        }
        
        // 处理需要API方法的组件
        if (isset($config['needApiMethod']) && $config['needApiMethod']) {
            $props = $this->getApiMethodProps($props, $column);
        }
        
        // 处理自定义属性
        $props = $this->getCustomProps($props, $column);
        
        return [
            'component' => $component,
            'props' => $props,
            'isDirectComponent' => $isDirectComponent
        ];
    }
    
    /**
     * 获取选项属性
     * 
     * @param array $props 原属性
     * @param array $column 字段信息
     * @return array 新属性
     */
    protected function getOptionsProps(array $props, array $column): array
    {
        // 如果字段注释中有选项数据
        if (!empty($column['parsed_comment']['options'])) {
            $options = [];
            
            foreach ($column['parsed_comment']['options'] as $value => $label) {
                $options[] = [
                    'value' => $value,
                    'label' => $label
                ];
            }
            
            // 如果是状态列，设置statusMap
            if (isset($props['statusMap'])) {
                $statusMap = [];
                foreach ($column['parsed_comment']['options'] as $value => $label) {
                    $statusMap[$value] = [
                        'text' => $label,
                        'type' => $this->getStatusType($value)
                    ];
                }
                $props['statusMap'] = $statusMap;
            } else {
                $props['options'] = $options;
            }
        }
        
        return $props;
    }
    
    /**
     * 获取API方法属性
     * 
     * @param array $props 原属性
     * @param array $column 字段信息
     * @return array 新属性
     */
    protected function getApiMethodProps(array $props, array $column): array
    {
        // 如果在字段注释标签中指定了API方法
        if (isset($column['parsed_comment']['tags']['api'])) {
            $props['apiMethod'] = $column['parsed_comment']['tags']['api'];
        } else {
            // 设置一个通用的API方法路径
            $props['apiMethod'] = 'undefined';
        }
        
        return $props;
    }
    
    /**
     * 获取自定义属性
     * 
     * @param array $props 原属性
     * @param array $column 字段信息
     * @return array 新属性
     */
    protected function getCustomProps(array $props, array $column): array
    {
        // 从字段注释的tags中提取自定义属性
        if (isset($column['parsed_comment']['tags'])) {
            $tags = $column['parsed_comment']['tags'];
            
            // 处理常见属性
            $propMappings = [
                'width' => 'width',
                'min-width' => 'minWidth',
                'align' => 'align',
                'sortable' => 'sortable',
                'fixed' => 'fixed',
                'show-overflow-tooltip' => 'showOverflowTooltip'
            ];
            
            foreach ($propMappings as $tagName => $propName) {
                if (isset($tags[$tagName])) {
                    $value = $tags[$tagName];
                    
                    // 处理布尔值
                    if ($value === 'true' || $value === true) {
                        $value = true;
                    } elseif ($value === 'false' || $value === false) {
                        $value = false;
                    }
                    
                    $props[$propName] = $value;
                }
            }
            
            // 处理特定组件的属性
            if (isset($tags['format']) && strpos($props['component'] ?? '', 'datetime') !== false) {
                $props['format'] = $tags['format'];
            }
            
            if (isset($tags['currency']) && strpos($props['component'] ?? '', 'currency') !== false) {
                $props['currency'] = $tags['currency'];
            }
            
            if (isset($tags['digits']) && strpos($props['component'] ?? '', 'currency') !== false) {
                $props['digits'] = (int)$tags['digits'];
            }
        }
        
        return $props;
    }
    
    /**
     * 获取状态类型
     * 
     * @param mixed $value 状态值
     * @return string 状态类型
     */
    protected function getStatusType($value): string
    {
        // 根据值推断状态类型
        if ($value === 1 || $value === '1' || $value === true || $value === 'true' || 
            strcasecmp($value, 'active') === 0 || strcasecmp($value, 'enabled') === 0 || 
            strcasecmp($value, 'yes') === 0) {
            return 'success';
        }
        
        if ($value === 0 || $value === '0' || $value === false || $value === 'false' || 
            strcasecmp($value, 'inactive') === 0 || strcasecmp($value, 'disabled') === 0 || 
            strcasecmp($value, 'no') === 0) {
            return 'danger';
        }
        
        if (strcasecmp($value, 'pending') === 0 || strcasecmp($value, 'wait') === 0 || 
            strcasecmp($value, 'processing') === 0) {
            return 'warning';
        }
        
        if (strcasecmp($value, 'info') === 0 || strcasecmp($value, 'draft') === 0) {
            return 'info';
        }
        
        // 默认使用主题色
        return 'primary';
    }
} 