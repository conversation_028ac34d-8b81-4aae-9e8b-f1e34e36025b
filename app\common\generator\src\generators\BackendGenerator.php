<?php
declare(strict_types=1);

namespace app\common\generator\src\generators;

use app\common\generator\src\interfaces\GeneratorInterface;
use app\common\generator\src\engines\TemplateEngine;

/**
 * 后端代码生成器
 */
class BackendGenerator implements GeneratorInterface
{
	/**
	 * 模板引擎
	 *
	 * @var TemplateEngine
	 */
	protected TemplateEngine $templateEngine;
	
	/**
	 * 配置信息
	 *
	 * @var array
	 */
	protected array $config;
	
	/**
	 * 构造函数
	 *
	 * @param TemplateEngine $templateEngine 模板引擎
	 * @param array          $config         配置信息
	 */
	public function __construct(TemplateEngine $templateEngine, array $config)
	{
		$this->templateEngine = clone $templateEngine;
		$this->config         = $config;
		
		// 设置模板路径
		$this->templateEngine->setTemplatePath(app()->getRootPath() . 'app/common/generator/templates/backend/');
	}
	
	/**
	 * 生成后端代码
	 *
	 * @param array $tableInfo 表结构信息
	 * @param array $options   生成选项
	 * @return array 生成结果
	 */
	public function generate(mixed $tableInfo, array $options = []): array
	{
		// 检查参数类型
		if (!is_array($tableInfo)) {
			throw new \InvalidArgumentException('表结构信息必须是数组类型');
		}
		
		// 生成的文件列表
		$files = [];
		
		// 获取模块名称
		$module = $options['module'] ?? $this->getModuleFromTableComment($tableInfo);
		
		// 获取实体名称
		$entityName = $tableInfo['entity_name'];
		
		// 生成Model文件
		$modelFile = $this->generateModel($tableInfo, $module, $options);
		if ($modelFile) {
			$files[] = $modelFile;
		}
		
		// 生成Service文件
		$serviceFile = $this->generateService($tableInfo, $module, $options);
		if ($serviceFile) {
			$files[] = $serviceFile;
		}
		
		// 生成Controller文件
		$controllerFile = $this->generateController($tableInfo, $module, $options);
		if ($controllerFile) {
			$files[] = $controllerFile;
		}
		
		// 生成路由文件
		$routeFile = $this->generateRoute($tableInfo, $module, $options);
		if ($routeFile) {
			$files[] = $routeFile;
		}
		
		return $files;
	}
	
	/**
	 * 从表注释中获取模块名称
	 *
	 * @param array $tableInfo 表结构信息
	 * @return string 模块名称
	 */
	protected function getModuleFromTableComment(array $tableInfo): string
	{
		if (isset($tableInfo['parsed_comment']['tags']['module'])) {
			return $tableInfo['parsed_comment']['tags']['module'];
		}
		
		// 默认模块名称
		return 'common';
	}
	
	/**
	 * 生成Model文件
	 *
	 * @param array  $tableInfo 表结构信息
	 * @param string $module    模块名称
	 * @param array  $options   生成选项
	 * @return array|null 生成的文件信息
	 */
	protected function generateModel(array $tableInfo, string $module, array $options = []): ?array
	{
		// 模板数据
		$data = [
			'MODULE'      => $module,
			'ENTITY_NAME' => $tableInfo['entity_name'],
			'TABLE_NAME'  => $tableInfo['table_name_without_prefix'],
			'PRIMARY_KEY' => $tableInfo['primary_key'],
			'COMMENT'     => $tableInfo['parsed_comment']['title'],
			'NAMESPACE'   => "app\\{$module}\\model"
		];
		
		// 渲染模板
		$content = $this->templateEngine->render('model', $data);
		
		// 文件保存路径
		$filePath = app()->getRootPath() . "app/{$module}/model/{$tableInfo['entity_name']}.php";
		
		// 创建目录
		$this->makeDirIfNotExists(dirname($filePath));
		
		// 检查文件是否存在
		if (file_exists($filePath) && empty($options['overwrite'])) {
			// 文件存在且不允许覆盖
			return [
				'type'    => 'model',
				'path'    => $filePath,
				'content' => '文件已存在，跳过生成',
				'skipped' => true
			];
		}
		
		// 写入文件
		if (file_put_contents($filePath, $content)) {
			return [
				'type'    => 'model',
				'path'    => $filePath,
				'content' => $content
			];
		}
		
		return null;
	}
	
	/**
	 * 生成Service文件
	 *
	 * @param array  $tableInfo 表结构信息
	 * @param string $module    模块名称
	 * @param array  $options   生成选项
	 * @return array|null 生成的文件信息
	 */
	protected function generateService(array $tableInfo, string $module, array $options = []): ?array
	{
		// 获取服务文件名
		$serviceName = $tableInfo['entity_name'] . 'Service';
		
		// 生成文件路径
		$filePath = app()->getRootPath() . "app/{$module}/service/{$serviceName}.php";
		
		// 创建目录
		$this->makeDirIfNotExists(dirname($filePath));
		
		// 检查文件是否存在
		if (file_exists($filePath) && empty($options['overwrite'])) {
			// 文件存在且不允许覆盖
			return [
				'type' => 'service',
				'path' => $filePath,
				'content' => '文件已存在，跳过生成',
				'skipped' => true
			];
		}
		
		// 准备模板变量
		$namespace = 'app\\' . $module . '\service';
		$modelClass = 'app\\' . $module . '\model\\' . $tableInfo['entity_name'];
		
		// 生成搜索字段
		$searchFields = [];
		foreach ($tableInfo['columns'] as $column) {
			// 调试输出
			file_put_contents(app()->getRootPath() . 'runtime/column_' . $column['Field'] . '.json', 
				json_encode($column['parsed_comment'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
			
			// 处理@search标签
			if (isset($column['parsed_comment']['tags']['search'])) {
				$searchType = $column['parsed_comment']['tags']['search'];
				if ($searchType === true) {
					$searchType = 'eq';
				}
				$searchFields[] = [
					'field' => $column['Field'],
					'type' => $searchType
				];
			}
			// 处理@search:xxx格式
			else if (isset($column['Comment']) && preg_match('/@search:([a-z_]+)(?:\s|$)/i', $column['Comment'], $matches)) {
				$searchType = $matches[1];
				$searchFields[] = [
					'field' => $column['Field'],
					'type' => $searchType
				];
			}
		}
		
		// 调试输出
		file_put_contents(app()->getRootPath() . 'runtime/search_fields_debug.json', 
			json_encode($searchFields, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
		
		// 准备模板数据
		$hasExport = $this->hasExportSupport($tableInfo);
		$hasImport = $this->hasImportSupport($tableInfo);

		$data = [
			'NAMESPACE' => $namespace,
			'MODULE' => $module,
			'ENTITY_NAME' => $tableInfo['entity_name'],
			'COMMENT' => $tableInfo['parsed_comment']['title'],
			'TABLE_NAME' => $tableInfo['table_name'],
			'PRIMARY_KEY' => $tableInfo['primary_key'],
			'search_fields' => $searchFields,
			'HAS_EXPORT' => $hasExport,
			'HAS_IMPORT' => $hasImport,
			'HAS_IMPORT_EXPORT' => $hasExport || $hasImport
		];
		
		// 渲染模板
		$content = $this->templateEngine->render('service', $data);
		
		// 写入文件
		if (file_put_contents($filePath, $content)) {
			return [
				'type' => 'service',
				'path' => $filePath,
				'content' => $content
			];
		}
		
		return null;
	}
	
	/**
	 * 生成Controller文件
	 *
	 * @param array  $tableInfo 表结构信息
	 * @param string $module    模块名称
	 * @param array  $options   生成选项
	 * @return array|null 生成的文件信息
	 */
	protected function generateController(array $tableInfo, string $module, array $options = []): ?array
	{
		// 模板数据
		$hasExport = $this->hasExportSupport($tableInfo);
		$hasImport = $this->hasImportSupport($tableInfo);

		$data = [
			'MODULE'      => $module,
			'ENTITY_NAME' => $tableInfo['entity_name'],
			'TABLE_NAME'  => $tableInfo['table_name_without_prefix'],
			'PRIMARY_KEY' => $tableInfo['primary_key'],
			'COMMENT'     => $tableInfo['parsed_comment']['title'],
			'NAMESPACE'   => "app\\{$module}\\controller",
			'HAS_EXPORT'  => $hasExport,
			'HAS_IMPORT'  => $hasImport,
			'HAS_IMPORT_EXPORT' => $hasExport || $hasImport  // 只要有导入或导出功能就需要引入trait
		];
		
		// 渲染模板
		$content = $this->templateEngine->render('controller', $data);
		
		// 文件保存路径
		$filePath = app()->getRootPath() . "app/{$module}/controller/{$tableInfo['entity_name']}Controller.php";
		
		// 创建目录
		$this->makeDirIfNotExists(dirname($filePath));
		
		// 检查文件是否存在
		if (file_exists($filePath) && empty($options['overwrite'])) {
			// 文件存在且不允许覆盖
			return [
				'type'    => 'controller',
				'path'    => $filePath,
				'content' => '文件已存在，跳过生成',
				'skipped' => true
			];
		}
		
		// 写入文件
		if (file_put_contents($filePath, $content)) {
			return [
				'type'    => 'controller',
				'path'    => $filePath,
				'content' => $content
			];
		}
		
		return null;
	}
	
	/**
	 * 生成路由文件
	 *
	 * @param array  $tableInfo 表结构信息
	 * @param string $module    模块名称
	 * @param array  $options   生成选项
	 * @return array|null 生成的文件信息
	 */
	protected function generateRoute(array $tableInfo, string $module, array $options = []): ?array
	{
		// 模板数据
		$hasExport = $this->hasExportSupport($tableInfo);
		$hasImport = $this->hasImportSupport($tableInfo);

		$data = [
			'MODULE'           => $module,
			'ENTITY_NAME'      => $tableInfo['entity_name'],
			'TABLE_NAME'       => $tableInfo['table_name_without_prefix'],
			'PRIMARY_KEY'      => $tableInfo['primary_key'],
			'COMMENT'          => $tableInfo['parsed_comment']['title'],
			'CONTROLLER_CLASS' => "app\\{$module}\\controller\\{$tableInfo['entity_name']}Controller",
			'HAS_EXPORT'       => $hasExport,
			'HAS_IMPORT'       => $hasImport,
			'HAS_IMPORT_EXPORT' => $hasExport || $hasImport
		];
		
		// 渲染模板
		$content = $this->templateEngine->render('route', $data);
		
		// 文件保存路径 - 修改为以表名命名的独立文件
		$filePath = app()->getRootPath() . "route/{$tableInfo['table_name_without_prefix']}.php";
		
		// 创建目录
		$this->makeDirIfNotExists(dirname($filePath));
		
		// 检查文件是否存在
		if (file_exists($filePath) && empty($options['overwrite'])) {
			// 文件存在且不允许覆盖
			return [
				'type'    => 'route',
				'path'    => $filePath,
				'content' => '路由文件已存在，跳过生成',
				'skipped' => true
			];
		}
		
		// 写入文件
		if (file_put_contents($filePath, $content)) {
			return [
				'type'    => 'route',
				'path'    => $filePath,
				'content' => $content
			];
		}
		
		return null;
	}
	
	/**
	 * 创建目录（如果不存在）
	 *
	 * @param string $dir 目录路径
	 * @return bool
	 */
	protected function makeDirIfNotExists(string $dir): bool
	{
		if (!is_dir($dir)) {
			return mkdir($dir, 0755, true);
		}
		return true;
	}

	/**
	 * 表是否支持导出
	 *
	 * @param array $tableInfo 表结构信息
	 * @return bool
	 */
	protected function hasExportSupport(array $tableInfo): bool
	{
		return isset($tableInfo['parsed_comment']['tags']['exp']) && $tableInfo['parsed_comment']['tags']['exp'];
	}

	/**
	 * 表是否支持导入
	 *
	 * @param array $tableInfo 表结构信息
	 * @return bool
	 */
	protected function hasImportSupport(array $tableInfo): bool
	{
		return isset($tableInfo['parsed_comment']['tags']['imp']) && $tableInfo['parsed_comment']['tags']['imp'];
	}
}