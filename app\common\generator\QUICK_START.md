# CRUD生成器快速开始

## 5分钟快速上手

### 1. 准备数据表

创建一个测试表，注意字段注释的写法：

```sql
CREATE TABLE `test_article` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL COMMENT '标题 @required @max:100 @search:like',
  `content` text COMMENT '内容 @form:editor',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1=发布,0=草稿 @search:eq @component:switch',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览量 @number',
  `price` decimal(10,2) DEFAULT 0.00 COMMENT '价格 @number',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表 @module:system @exp:true @imp:true';
```

### 2. 生成代码

```bash
# 生成前后端代码
php think generator:crud test_article --module=system --frontend --overwrite
```

### 3. 检查生成结果

生成器会创建以下文件：

**后端文件：**
- `app/system/controller/TestArticleController.php` - 控制器
- `app/system/model/TestArticle.php` - 模型
- `app/system/service/TestArticleService.php` - 服务类

**前端文件：**
- `frontend/src/views/system/test_article/list.vue` - 列表页面
- `frontend/src/views/system/test_article/form-dialog.vue` - 表单对话框
- `frontend/src/views/system/test_article/import-export-dialog.vue` - 导入导出对话框

### 4. 验证功能

生成的代码包含以下功能：

✅ **表单验证**：title 字段必填且最大长度100  
✅ **搜索功能**：title 模糊搜索，status 精确搜索  
✅ **组件映射**：status 字段自动使用开关组件  
✅ **数字处理**：view_count 和 price 使用数字输入框  
✅ **日期格式**：published_at 使用正确的日期时间格式  
✅ **导入导出**：支持数据导入和导出功能  

## 常用注释标记

### 验证规则
- `@required` - 必填
- `@max:100` - 最大长度
- `@number` - 数字验证
- `@email` - 邮箱验证

### 搜索配置
- `@search:like` - 模糊搜索
- `@search:eq` - 精确搜索
- `@search:between` - 范围搜索

### 组件指定
- `@component:switch` - 开关组件
- `@component:image` - 图片组件
- `@form:editor` - 富文本编辑器

### 导入导出
- 表注释：`@exp:true @imp:true`
- 字段注释：`@exp @imp`

## 字段类型建议

| 用途 | 推荐类型 | 示例 |
|------|----------|------|
| 主键 | `int(11) AUTO_INCREMENT` | `id` |
| 标题 | `varchar(100)` | `title` |
| 内容 | `text` | `content` |
| 状态 | `tinyint(1)` | `status` |
| 金额 | `decimal(15,2)` | `price`, `amount` |
| 数量 | `int(11)` | `quantity`, `sort` |
| 日期 | `date` | `birthday` |
| 时间 | `datetime` | `created_at` |

## CRM系统专用指南

如果你正在开发CRM系统，我们提供了专门的生成器使用指南：

### CRM数据库表结构
- 位置：`app/crm/database_generator.sql`
- 包含28个CRM核心表，支持多租户架构
- 已优化字段注释，支持一键生成

### CRM生成器命令示例
```bash
# 生成标签管理模块
php think generator:crud crm_tag --module=crm --frontend --overwrite

# 生成线索管理模块
php think generator:crud crm_lead --module=crm --frontend --overwrite

# 生成客户管理模块
php think generator:crud crm_customer --module=crm --frontend --overwrite
```

### 详细指南
查看完整的CRM生成器使用指南：`crm/doc/CRM_CRUD生成器使用指南.md`

## 下一步

1. **通用开发**：查看完整文档 [README.md](README.md)
2. **CRM开发**：查看CRM专用指南 `crm/doc/CRM_CRUD生成器使用指南.md`
3. 了解更多组件：查看表格列组件章节
4. 自定义验证：学习表单验证规则
5. 高级功能：导入导出、搜索配置等

## 常见问题

**Q: 表单提交失败？**  
A: 检查字段注释是否有不必要的验证标记，移除不需要验证的字段上的 `@required` 等标记。

**Q: 日期格式错误？**  
A: 重新生成代码，生成器会自动设置正确的 `value-format` 属性。

**Q: 数字字段有警告？**  
A: 检查默认值是否为数字类型，生成器会自动处理这个问题。

更多问题请查看 [README.md](README.md) 的问题排查章节。
