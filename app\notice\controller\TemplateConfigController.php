<?php
declare(strict_types=1);

namespace app\notice\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\notice\service\NoticeTemplateService;
use app\notice\service\TemplateConfigService;
use think\facade\Log;
use think\Response;

/**
 * 租户模板配置控制器
 */
class TemplateConfigController extends BaseController
{
	use CrudControllerTrait;
	
	/**
	 * 模板配置服务实例
	 *
	 * @var TemplateConfigService
	 */
	protected TemplateConfigService $service;
	
	/**
	 * 模板服务实例
	 *
	 * @var NoticeTemplateService
	 */
	protected NoticeTemplateService $templateService;
	
	/**
	 * 初始化
	 */
	protected function initialize(): void
	{
		parent::initialize();
		$this->service         = TemplateConfigService::getInstance();
		$this->templateService = NoticeTemplateService::getInstance();
	}
	
	/**
	 * 获取租户模板配置列表
	 *
	 * @return Response
	 */
	public function index(): Response
	{
		try {
			// 获取请求参数
			$page         = $this->request->param('page/d', 1);
			$pageSize     = $this->request->param('page_size/d', 15);
			$templateCode = $this->request->param('template_code/s', '');
			
			// 构建过滤条件
			$filters = [];
			if ($templateCode) {
				$filters['template_code'] = $templateCode;
			}
			
			// 获取数据
			$result = $this->service->getTemplateConfigList($this->request->tenantId, $filters, [
				'page'      => $page,
				'page_size' => $pageSize
			]);
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			Log::error('获取租户模板配置列表失败: ' . $e->getMessage());
			return $this->error('获取租户模板配置列表失败');
		}
	}
	
	/**
	 * 获取租户模板配置详情
	 *
	 * @param int $id 配置ID
	 * @return Response
	 */
	public function read(int $id): Response
	{
		try {
			// 获取配置信息
			$config = $this->service->get($id);
			
			if (!$config) {
				return $this->error('模板配置不存在');
			}
			
			// 检查权限
			$tenantId = $this->getCurrentTenantId();
			if ($config['tenant_id'] != $tenantId) {
				return $this->error('无权限访问此配置');
			}
			
			// 关联模板信息
			$template = $this->templateService->getTemplateByCode($config['template_code']);
			if ($template) {
				$config['template'] = $template;
			}
			
			return $this->success('获取成功', $config);
		}
		catch (\Exception $e) {
			Log::error('获取租户模板配置详情失败: ' . $e->getMessage());
			return $this->error('获取租户模板配置详情失败');
		}
	}
	
	/**
	 * 更新租户模板配置
	 *
	 * @param int $id 配置ID
	 * @return Response
	 */
	public function update(int $id): Response
	{
		try {
			// 获取配置信息
			$config = $this->service->get($id);
			
			if (!$config) {
				return $this->error('模板配置不存在');
			}
			
			// 检查权限
			$tenantId = $this->getCurrentTenantId();
			if ($config['tenant_id'] != $tenantId) {
				return $this->error('无权限修改此配置');
			}
			
			// 获取请求数据
			$data = $this->request->only([
				'is_enabled',
				'site_enabled',
				'email_enabled',
				'sms_enabled',
				'wework_enabled',
				'dingtalk_enabled',
				'webhook_enabled',
				'sms_template_code',
				'wework_webhook_url',
				'dingtalk_webhook_url',
				'webhook_url'
			]);
			
			// 更新配置
			$result = $this->service->saveTemplateConfig(array_merge(['id' => $id], $data));
			
			if (!$result) {
				return $this->error('更新模板配置失败');
			}
			
			return $this->success('更新成功');
		}
		catch (\Exception $e) {
			Log::error('更新租户模板配置失败: ' . $e->getMessage());
			return $this->error('更新租户模板配置失败');
		}
	}
	
	/**
	 * 设置模板状态
	 *
	 * @return Response
	 */
	public function setStatus(): Response
	{
		try {
			$id     = $this->request->param('id/d', 0);
			$status = $this->request->param('status/d', 0);
			
			if (!$id) {
				return $this->error('参数错误');
			}
			
			// 获取配置信息
			$config = $this->service->get($id);
			
			if (!$config) {
				return $this->error('模板配置不存在');
			}
			
			// 检查权限
			$tenantId = $this->getCurrentTenantId();
			if ($config['tenant_id'] != $tenantId) {
				return $this->error('无权限修改此配置');
			}
			
			// 更新状态
			$result = $this->service->setTemplateStatus($tenantId, $config['template_id'], $status == 1);
			
			if (!$result) {
				return $this->error('设置模板状态失败');
			}
			
			return $this->success('设置成功');
		}
		catch (\Exception $e) {
			Log::error('设置模板状态失败: ' . $e->getMessage());
			return $this->error('设置模板状态失败');
		}
	}
	
	/**
	 * 设置通道状态
	 *
	 * @return Response
	 */
	public function setChannelStatus(): Response
	{
		try {
			$id      = $this->request->param('id/d', 0);
			$channel = $this->request->param('channel/s', '');
			$status  = $this->request->param('status/d', 0);
			
			if (!$id || !$channel) {
				return $this->error('参数错误');
			}
			
			// 获取配置信息
			$config = $this->service->get($id);
			
			if (!$config) {
				return $this->error('模板配置不存在');
			}
			
			// 检查权限
			$tenantId = $this->getCurrentTenantId();
			if ($config['tenant_id'] != $tenantId) {
				return $this->error('无权限修改此配置');
			}
			
			// 检查通道是否有效
			$validChannels = [
				'site',
				'email',
				'sms',
				'wework',
				'dingtalk',
				'webhook'
			];
			if (!in_array($channel, $validChannels)) {
				return $this->error('无效的通道类型');
			}
			
			// 更新通道状态
			$result = $this->service->setChannelStatus($tenantId, $config['template_id'], $channel, $status == 1);
			
			if (!$result) {
				return $this->error('设置通道状态失败');
			}
			
			return $this->success('设置成功');
		}
		catch (\Exception $e) {
			Log::error('设置通道状态失败: ' . $e->getMessage());
			return $this->error('设置通道状态失败');
		}
	}
	
	/**
	 * 获取当前租户ID
	 *
	 * @return int 租户ID
	 */
	protected function getCurrentTenantId(): int
	{
		// 从当前登录用户中获取租户ID
		// 实际实现可能需要根据具体的用户认证系统调整
		return $this->request->user['tenant_id'] ?? 0;
	}
} 