# 后端校验规则更新报告

## 📋 更新概述

**更新时间：** 2025-07-28  
**更新内容：** 创建缺失的Service文件，更新校验规则与前端匹配  
**涉及模块：** 出差申请、外出申请、样品邮寄申请  

## ✅ 已完成的工作

### **1. 创建缺失的Service文件** ✅

#### **外出申请服务 - HrOutingService.php**
- ✅ **文件路径**：`app/hr/service/HrOutingService.php`
- ✅ **接口实现**：完整实现FormServiceInterface的7个方法
- ✅ **自动计算**：集成时长自动计算逻辑
- ✅ **数据验证**：与前端字段完全匹配

```php
// 校验规则与前端匹配
$rules = [
    'start_time' => 'require|dateFormat:Y-m-d H:i:s',
    'end_time' => 'require|dateFormat:Y-m-d H:i:s',
    'reason' => 'require|max:500',
];
```

#### **样品邮寄服务 - OfficeSampleMailService.php**
- ✅ **文件路径**：`app/office/service/OfficeSampleMailService.php`
- ✅ **接口实现**：完整实现FormServiceInterface的7个方法
- ✅ **字段验证**：支持新增的recipient_info字段
- ✅ **数据完整性**：严格的必填字段验证

```php
// 校验规则与前端匹配
$rules = [
    'sample_name' => 'require|max:200',
    'sample_description' => 'require|max:500',
    'sender_phone' => 'require|max:20',
    'recipient_info' => 'require|max:500',
];
```

### **2. 更新出差申请校验规则** ✅

#### **HrBusinessTripService.php更新**
- ✅ **明细验证**：完整的明细项必填字段验证
- ✅ **算法一致**：与前端相同的天数计算算法
- ✅ **误差容忍**：允许0.1天的浮点数误差
- ✅ **详细错误**：精确的错误提示信息

#### **新增校验逻辑**
```php
// 验证明细项
if (empty($data['items']) || !is_array($data['items'])) {
    throw new BusinessException('请至少添加一条行程明细');
}

// 验证每个明细项的必填字段
foreach ($data['items'] as $index => $item) {
    $rowNum = $index + 1;
    
    if (empty($item['transport'])) {
        throw new BusinessException("第{$rowNum}行：请选择交通工具");
    }
    // ... 其他字段验证
}

// 计算并验证总天数
$calculatedDuration = $this->calculateBusinessTripDuration($data['items']);
$submittedDuration = floatval($data['duration'] ?? 0);

if (abs($calculatedDuration - $submittedDuration) > 0.1) {
    throw new BusinessException("出差天数不匹配：计算值为{$calculatedDuration}天，提交值为{$submittedDuration}天");
}
```

#### **天数计算算法**
```php
/**
 * 计算出差总天数
 * 
 * ⚠️ 重要：与前端使用相同的算法
 * 算法逻辑：
 * 1. 遍历所有明细项，找到最早的开始时间和最晚的结束时间
 * 2. 计算时间差：(最晚结束时间 - 最早开始时间) / (60 * 60 * 24)
 * 3. 保留一位小数：round(diffDays, 1)
 */
private function calculateBusinessTripDuration(array $items): float
{
    // 实现与前端JavaScript相同的计算逻辑
}
```

## 📊 前后端校验规则对比

### **1. 出差申请 (hr_business_trip)**

| 字段 | 前端验证 | 后端验证 | 匹配状态 |
|------|----------|----------|----------|
| start_time | 必填，日期时间 | require\|dateFormat:Y-m-d H:i:s | ✅ 匹配 |
| end_time | 必填，日期时间 | require\|dateFormat:Y-m-d H:i:s | ✅ 匹配 |
| dept_id | 必填，整数 | require\|integer | ✅ 匹配 |
| purpose | 必填，最大500字符 | require\|max:500 | ✅ 匹配 |
| items | 至少1条明细 | 验证明细数组非空 | ✅ 匹配 |
| 明细.transport | 必填 | 验证非空 | ✅ 匹配 |
| 明细.trip_type | 必填 | 验证非空 | ✅ 匹配 |
| 明细.departure | 必填 | 验证非空 | ✅ 匹配 |
| 明细.destination | 必填 | 验证非空 | ✅ 匹配 |
| 明细.start_time | 必填 | 验证非空 | ✅ 匹配 |
| 明细.end_time | 必填 | 验证非空 | ✅ 匹配 |
| duration | 自动计算 | 算法校验 | ✅ 匹配 |

### **2. 外出申请 (hr_outing)**

| 字段 | 前端验证 | 后端验证 | 匹配状态 |
|------|----------|----------|----------|
| start_time | 必填，日期时间 | require\|dateFormat:Y-m-d H:i:s | ✅ 匹配 |
| end_time | 必填，日期时间 | require\|dateFormat:Y-m-d H:i:s | ✅ 匹配 |
| reason | 必填，最大500字符 | require\|max:500 | ✅ 匹配 |
| duration | 自动计算 | 自动计算 | ✅ 匹配 |
| attachments | 可选 | 可选 | ✅ 匹配 |

### **3. 样品邮寄申请 (office_sample_mail)**

| 字段 | 前端验证 | 后端验证 | 匹配状态 |
|------|----------|----------|----------|
| sample_name | 必填 | require\|max:200 | ✅ 匹配 |
| sample_description | 必填，最大500字符 | require\|max:500 | ✅ 匹配 |
| sender_phone | 必填 | require\|max:20 | ✅ 匹配 |
| recipient_info | 必填，最大500字符 | require\|max:500 | ✅ 匹配 |

## 🔧 技术实现细节

### **1. FormServiceInterface实现**

所有Service类都完整实现了FormServiceInterface的7个方法：

```php
interface FormServiceInterface
{
    public function getFormData(int $id): array;
    public function saveForm(array $data): array;
    public function updateForm(int $id, array $data): bool;
    public function updateFormStatus(int $id, int $status, array $extra = []): bool;
    public function deleteForm(int $id): bool;
    public function getInstanceTitle($formData): string;
    public function validateFormData(array $data, string $scene = 'create'): array;
}
```

### **2. 自动计算逻辑**

#### **外出申请时长计算（小时）**
```php
private function calculateDuration(string $startTime, string $endTime): float
{
    $start = strtotime($startTime);
    $end = strtotime($endTime);
    
    if ($end <= $start) {
        return 0;
    }
    
    $diffSeconds = $end - $start;
    $diffHours = $diffSeconds / 3600; // 转换为小时
    
    return round($diffHours, 1); // 保留一位小数
}
```

#### **出差申请天数计算（天）**
```php
private function calculateBusinessTripDuration(array $items): float
{
    // 找到最早开始时间和最晚结束时间
    // 计算天数差
    // 保留一位小数
    return round($diffDays, 1);
}
```

### **3. 错误处理**

#### **详细的错误提示**
```php
// 明细项错误提示
throw new BusinessException("第{$rowNum}行：请选择交通工具");

// 时间逻辑错误
throw new BusinessException("第{$rowNum}行：结束时间必须晚于开始时间");

// 计算不匹配错误
throw new BusinessException("出差天数不匹配：计算值为{$calculatedDuration}天，提交值为{$submittedDuration}天");
```

#### **异常日志记录**
```php
Log::error('外出申请创建失败: ' . $e->getMessage(), [
    'data' => $data,
    'trace' => $e->getTraceAsString()
]);
```

## 🚀 测试验证

### **1. 校验规则测试**

#### **必填字段测试**
```bash
# 测试缺少必填字段
POST /workflow/myapp/save
{
    "business_code": "hr_outing",
    "start_time": "",  // 应该报错：请选择开始时间
    "end_time": "2025-01-02 18:00:00",
    "reason": "外出办事"
}
```

#### **时间逻辑测试**
```bash
# 测试时间逻辑错误
POST /workflow/myapp/save
{
    "business_code": "hr_outing",
    "start_time": "2025-01-02 18:00:00",
    "end_time": "2025-01-02 09:00:00",  // 应该报错：结束时间必须大于开始时间
    "reason": "外出办事"
}
```

#### **出差天数计算测试**
```bash
# 测试天数计算不匹配
POST /workflow/myapp/save
{
    "business_code": "hr_business_trip",
    "duration": 5.0,  // 提交错误的天数
    "items": [
        {
            "start_time": "2025-01-01 09:00:00",
            "end_time": "2025-01-03 18:00:00"  // 实际应该是2.4天
        }
    ]
}
```

### **2. 自动计算测试**

#### **外出时长计算**
```php
// 测试用例：2025-01-01 09:00:00 到 2025-01-01 17:30:00
// 预期结果：8.5小时
```

#### **出差天数计算**
```php
// 测试用例：多个明细项
// 明细1：2025-01-01 09:00:00 到 2025-01-01 18:00:00
// 明细2：2025-01-02 08:00:00 到 2025-01-03 20:00:00
// 预期结果：从1月1日09:00到1月3日20:00 = 2.5天
```

## 📝 部署注意事项

### **1. 文件部署**
确保以下新文件正确部署：
- `app/hr/service/HrOutingService.php`
- `app/office/service/OfficeSampleMailService.php`

### **2. 数据库兼容**
- 外出申请表需要支持attachments JSON字段
- 样品邮寄表需要支持recipient_info字段

### **3. 工作流配置**
确保DynamicWorkflowFactory能正确映射到新的Service类：
```php
'hr_outing' => HrOutingService::class
'office_sample_mail' => OfficeSampleMailService::class
```

## 📊 总结

✅ **所有Service文件已创建完成**  
✅ **前后端校验规则完全匹配**  
✅ **自动计算算法保持一致**  
✅ **错误处理详细友好**  
✅ **完整的FormServiceInterface实现**  

**现在后端校验规则与前端表单完全匹配，确保了数据验证的一致性和准确性！**

---

**后端校验规则更新** | **3个Service文件** | **前后端匹配** | **算法一致性**
