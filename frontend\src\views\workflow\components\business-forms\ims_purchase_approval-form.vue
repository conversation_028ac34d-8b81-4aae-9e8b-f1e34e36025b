<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="采购单号" prop="purchase_no">
              <ElInput
                v-model="formData.purchase_no"
                placeholder="请输入采购单号"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="采购类型" prop="purchase_type">
              <ElSelect
                v-model="formData.purchase_type"
                placeholder="请选择采购类型"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption label="原材料采购" :value="1" />
                <ElOption label="设备采购" :value="2" />
                <ElOption label="办公用品采购" :value="3" />
                <ElOption label="其他采购" :value="4" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="采购日期" prop="purchase_date">
              <ElDatePicker
                v-model="formData.purchase_date"
                type="date"
                placeholder="请选择采购日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="供应商" prop="supplier_id">
              <ElSelect
                v-model="formData.supplier_id"
                placeholder="请选择供应商"
                style="width: 100%"
                filterable
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="supplier in supplierOptions"
                  :key="supplier.value"
                  :label="supplier.label"
                  :value="supplier.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="所在部门" prop="dept_id">
              <ElSelect
                v-model="formData.dept_id"
                placeholder="请选择部门"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="dept in deptOptions"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="联系人" prop="contact_person">
              <ElInput
                v-model="formData.contact_person"
                placeholder="请输入联系人"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="联系电话" prop="contact_phone">
              <ElInput
                v-model="formData.contact_phone"
                placeholder="请输入联系电话"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="预计到货日期">
              <ElDatePicker
                v-model="formData.expected_date"
                type="date"
                placeholder="请选择预计到货日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="采购事由" prop="reason">
          <ElInput
            v-model="formData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入采购事由"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>

        <!-- 采购明细 -->
        <ElFormItem label="采购明细">
          <div class="items-container">
            <ElButton
              v-if="isEditable"
              type="primary"
              size="small"
              @click="addItem"
              style="margin-bottom: 10px"
            >
              添加明细
            </ElButton>
            <ElTable :data="formData.items" border size="small">
              <ElTableColumn label="产品名称" width="200">
                <template #default="{ row, $index }">
                  <ElSelect
                    v-if="isEditable"
                    v-model="row.product_id"
                    placeholder="请选择产品"
                    filterable
                    @change="onProductChange(row)"
                  >
                    <ElOption
                      v-for="product in productOptions"
                      :key="product.value"
                      :label="product.label"
                      :value="product.value"
                    />
                  </ElSelect>
                  <span v-else>{{ row.product_name || '-' }}</span>
                </template>
              </ElTableColumn>
              <ElTableColumn label="数量" width="120">
                <template #default="{ row, $index }">
                  <ElInputNumber
                    v-if="isEditable"
                    v-model="row.quantity"
                    :min="0"
                    :precision="2"
                    size="small"
                    style="width: 100%"
                    @change="calculateItemTotal(row)"
                  />
                  <span v-else>{{ row.quantity || 0 }}</span>
                </template>
              </ElTableColumn>
              <ElTableColumn label="单价" width="120">
                <template #default="{ row, $index }">
                  <ElInputNumber
                    v-if="isEditable"
                    v-model="row.unit_price"
                    :min="0"
                    :precision="2"
                    size="small"
                    style="width: 100%"
                    @change="calculateItemTotal(row)"
                  />
                  <span v-else>{{ row.unit_price || 0 }}</span>
                </template>
              </ElTableColumn>
              <ElTableColumn label="小计" width="120">
                <template #default="{ row }">
                  <span>{{ row.total_amount || 0 }}</span>
                </template>
              </ElTableColumn>
              <ElTableColumn label="操作" width="80" v-if="isEditable">
                <template #default="{ row, $index }">
                  <ElButton type="danger" size="small" @click="removeItem($index)">删除</ElButton>
                </template>
              </ElTableColumn>
            </ElTable>
            <div class="total-summary" style="margin-top: 10px; text-align: right">
              <span>总数量: {{ totalQuantity }}</span>
              <span style="margin-left: 20px">总金额: ¥{{ totalAmount }}</span>
            </div>
          </div>
        </ElFormItem>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface ImsPurchaseFormData {
    id?: number
    purchase_no: string
    purchase_type: number | null
    purchase_date: string
    supplier_id: number | null
    dept_id: number | null
    contact_person: string
    contact_phone: string
    expected_date: string
    reason: string
    remark: string
    items: any[]
    total_quantity: number
    total_amount: number
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `采购申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起采购申请'
  })

  /** 表单数据 */
  const formData = reactive<ImsPurchaseFormData & any>({
    purchase_no: '',
    purchase_type: null,
    purchase_date: '',
    supplier_id: null,
    dept_id: null,
    contact_person: '',
    contact_phone: '',
    expected_date: '',
    reason: '',
    remark: '',
    items: [],
    total_quantity: 0,
    total_amount: 0,
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  // 选项数据
  const supplierOptions = ref([])
  const deptOptions = ref([])
  const productOptions = ref([])

  // 计算总数量和总金额
  const totalQuantity = computed(() => {
    return formData.items.reduce((sum, item) => sum + (item.quantity || 0), 0)
  })

  const totalAmount = computed(() => {
    return formData.items.reduce((sum, item) => sum + (item.total_amount || 0), 0)
  })

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    purchase_no: [{ required: true, message: '请输入采购单号', trigger: 'blur' }],
    purchase_type: [{ required: true, message: '请选择采购类型', trigger: 'change' }],
    purchase_date: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
    supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
    dept_id: [{ required: true, message: '请选择所在部门', trigger: 'change' }],
    reason: [{ required: true, message: '请输入采购事由', trigger: 'blur' }]
  }

  // ==================== 方法定义 ====================

  /**
   * 添加明细项
   */
  const addItem = () => {
    formData.items.push({
      product_id: null,
      product_name: '',
      quantity: 0,
      unit_price: 0,
      total_amount: 0
    })
  }

  /**
   * 删除明细项
   */
  const removeItem = (index: number) => {
    formData.items.splice(index, 1)
  }

  /**
   * 计算明细小计
   */
  const calculateItemTotal = (item: any) => {
    item.total_amount = (item.quantity || 0) * (item.unit_price || 0)
  }

  /**
   * 产品选择变化
   */
  const onProductChange = (item: any) => {
    const product = productOptions.value.find((p) => p.value === item.product_id)
    if (product) {
      item.product_name = product.label
      item.unit_price = product.price || 0
      calculateItemTotal(item)
    }
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('ims_purchase_approval-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      purchase_no: '',
      purchase_type: null,
      purchase_date: '',
      supplier_id: null,
      dept_id: null,
      contact_person: '',
      contact_phone: '',
      expected_date: '',
      reason: '',
      remark: '',
      items: [],
      total_quantity: 0,
      total_amount: 0,
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        // 确保items是数组
        if (!Array.isArray(formData.items)) {
          formData.items = []
        }

        console.log('采购申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('ims_purchase_approval-form setFormData called with:', data)
    Object.assign(formData, data)
    if (!Array.isArray(formData.items)) {
      formData.items = []
    }
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('ims_purchase_approval-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: ImsPurchaseFormData = {
        purchase_no: formData.purchase_no,
        purchase_type: formData.purchase_type,
        purchase_date: formData.purchase_date,
        supplier_id: formData.supplier_id,
        dept_id: formData.dept_id,
        contact_person: formData.contact_person,
        contact_phone: formData.contact_phone,
        expected_date: formData.expected_date,
        reason: formData.reason,
        remark: formData.remark,
        items: formData.items,
        total_quantity: totalQuantity.value,
        total_amount: totalAmount.value
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('采购申请保存数据:', submitData)
      emit('save', submitData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      if (formData.items.length === 0) {
        ElMessage.warning('请至少添加一条采购明细')
        return
      }

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: ImsPurchaseFormData = {
        purchase_no: formData.purchase_no,
        purchase_type: formData.purchase_type,
        purchase_date: formData.purchase_date,
        supplier_id: formData.supplier_id,
        dept_id: formData.dept_id,
        contact_person: formData.contact_person,
        contact_phone: formData.contact_phone,
        expected_date: formData.expected_date,
        reason: formData.reason,
        remark: formData.remark,
        items: formData.items,
        total_quantity: totalQuantity.value,
        total_amount: totalAmount.value
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('采购申请提交数据:', submitData)
      emit('submit', submitData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 监听总数量和总金额变化
  watch([totalQuantity, totalAmount], () => {
    formData.total_quantity = totalQuantity.value
    formData.total_amount = totalAmount.value
  })

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }

  .items-container {
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 15px;
    background-color: var(--el-bg-color-page);
  }

  .total-summary {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }
</style>
