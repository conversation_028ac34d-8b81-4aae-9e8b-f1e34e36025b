
# 字段注释驱动的CRUD生成方案

## 1. 背景与目的

当前CRUD框架中的代码生成器主要依赖表注释中的标签信息来指导代码生成，这导致以下问题：

- 表注释变得过长且难以维护
- 配置信息与字段定义分离，降低了代码可读性
- 修改单个字段配置需要调整整个表注释
- 表注释长度可能受数据库系统限制

字段注释驱动方案旨在将配置信息迁移到各个字段的注释中，使配置与字段定义紧密关联，提高可维护性和可读性。

## 2. 注释标记语法

### 2.1 字段注释格式

```sql
`field_name` type [constraints] COMMENT '字段描述 | @标记1 @标记2=值 @标记3=值1,值2'
```

其中：
- 字段描述与标记配置使用 `|` 符号分隔
- 每个标记以 `@` 开头
- 标记可以有值也可以无值
- 标记值可以是单个值或逗号分隔的多个值

### 2.2 表注释格式

表注释仅保留全局配置项：

```sql
COMMENT='表描述 @标记1=值 @标记2=值'
```

## 3. 字段级标记详解

| 标记 | 完整写法 | 简写 | 说明 | 可能的值 | 示例 |
|------|---------|-----|------|---------|------|
| @search | @search=type | @s=type | 搜索字段及类型 | eq, neq, gt, lt, gte, lte, like, between, in, date, custom | @search=like 或 @s=like |
| @hide | @hide | @h | 默认隐藏字段 | - | @hide 或 @h |
| @edit | @edit | @e | 允许单独编辑 | - | @edit 或 @e |
| @forbid | @forbid | @f | 禁止单独编辑 | - | @forbid 或 @f |
| @order | @order | @o | 允许排序 | - | @order 或 @o |
| @permission | @permission | @p | 数据权限字段 | - | @permission 或 @p |
| @scene | @scene=场景1,场景2 | - | 字段所属场景 | 任意场景名称 | @scene=list,detail,select |
| @validate | @validate=规则1,规则2 | @v=规则1,规则2 | 验证规则 | 任意验证规则 | @validate=required,min:3,max:50 |
| @relation | @relation=类型:模型:外键:本地键 | @r=类型:模型:外键:本地键 | 关联关系 | belongsTo, hasOne, hasMany, belongsToMany | @relation=belongsTo:Role:role_id:id |
| @default | @default=值 | @d=值 | 前端表单默认值 | 任意值 | @default=1 或 @d=1 |
| @control | @control=类型 | @c=类型 | 表单控件类型 | input, select, radio, checkbox, date, datetime, textarea, switch, upload | @control=select 或 @c=select |
| @options | @options=选项1:值1,选项2:值2 | @opt=选项1:值1,选项2:值2 | 选择控件选项 | key:value格式 | @options=启用:1,禁用:0 |
| @width | @width=宽度 | @w=宽度 | 表格列宽度 | 数字 | @width=120 或 @w=120 |
| @fixed | @fixed=位置 | - | 表格固定列 | left, right | @fixed=left |
| @formatter | @formatter=类型 | @fmt=类型 | 表格格式化方式 | date, datetime, status, tag, image | @formatter=status 或 @fmt=status |
| @required | @required | @req | 是否必填 | - | @required 或 @req |
| @export | @export | @exp | 是否导出 | - | @export 或 @exp |
| @import | @import | @imp | 是否导入 | - | @import 或 @imp |
| @sortable | @sortable | @sort | 支持后端排序 | - | @sortable 或 @sort |

## 4. 表级标记详解

| 标记 | 说明 | 可能的值 | 示例 |
|------|------|----------|------|
| @module | 模块名称 | 任意模块名 | @module:admin |
| @order | 默认排序 | 字段:方向 | @order:id:desc |
| @enable_permission | 启用数据权限 | true, false | @enable_permission:true |
| @with | 默认关联 | 关联名称列表 | @with:roles,department |
| @tenant | 多租户字段 | 字段名 | @tenant:tenant_id |
| @tree | 树形表格配置 | 父ID字段,名称字段 | @tree:parent_id,name |
| @soft_delete | 软删除字段 | 字段名 | @soft_delete:deleted_at |
| @timestamp | 时间戳字段 | 创建时间字段,更新时间字段 | @timestamp:created_at,updated_at |
| @creator | 创建者字段 | 字段名 | @creator:creator_id |
| @cache | 启用缓存 | true, false | @cache:true |
| @path | 前端路由路径 | 路径名称 | @path:system/user |
| @icon | 前端图标 | 图标名称 | @icon:user |
| @auth | 权限前缀 | 权限标识 | @auth:system:user |

## 5. 完整示例

```sql
CREATE TABLE `system_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID | @o @scene=list,detail,select',
  `username` varchar(50) NOT NULL COMMENT '用户名 | @s=like @e @o @req @scene=list,detail,select @v=required,min:3,max:50 @w=120',
  `password` varchar(100) NOT NULL COMMENT '密码 | @h @f @req @v=required,min:6,max:20 @scene=add',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称 | @s=like @e @scene=list,detail @c=input',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像 | @e @scene=detail @c=upload',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱 | @s=like @e @scene=list,detail @v=email',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号 | @s=like @e @scene=detail @v=mobile',
  `dept_id` int unsigned DEFAULT NULL COMMENT '部门ID | @e @scene=detail @r=belongsTo:Dept:dept_id:id',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用 | @s=eq @e @o @scene=list,detail @c=switch @fmt=status',
  `tenant_id` int unsigned DEFAULT NULL COMMENT '租户ID | @scene=detail',
  `creator_id` int unsigned DEFAULT NULL COMMENT '创建人ID | @p @scene=detail @r=belongsTo:User:creator_id:id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间 | @s=date @o @scene=list,detail @fmt=datetime',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间 | @o @scene=detail @fmt=datetime',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间 | @h',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
COMMENT='用户表 @module:admin @order:id:desc @enable_permission:true @with:dept,roles @tree:parent_id,nickname @soft_delete:deleted_at @timestamp:created_at,updated_at';
```

## 6. 代码调整建议

为支持字段注释驱动的CRUD生成方案，需要对现有代码进行以下调整：

### 6.1 添加字段注释解析方法

```php
/**
 * 解析字段注释
 *
 * @param string $comment 注释内容
 * @return array 解析结果
 */
protected function parseFieldComment(string $comment): array
{
    $parts = explode(' | ', $comment, 2);
    $description = $parts[0];
    $tags = [];
    
    if (count($parts) > 1 && !empty($parts[1])) {
        preg_match_all('/@([a-z_]+)(?:=([^@\s]+))?/', $parts[1], $matches, PREG_SET_ORDER);
        foreach ($matches as $match) {
            $tagName = $match[1];
            $tagValue = $match[2] ?? true;
            
            // 处理简写形式
            $tagName = $this->expandTagShortName($tagName);
            
            // 处理多值标记
            if (is_string($tagValue) && strpos($tagValue, ',') !== false) {
                $tagValue = explode(',', $tagValue);
            }
            
            $tags[$tagName] = $tagValue;
        }
    }
    
    return [
        'description' => $description,
        'tags' => $tags
    ];
}

/**
 * 展开标记简写名称
 *
 * @param string $shortName 简写名称
 * @return string 完整名称
 */
protected function expandTagShortName(string $shortName): string
{
    $map = [
        's' => 'search',
        'h' => 'hide',
        'e' => 'edit',
        'f' => 'forbid',
        'o' => 'order',
        'p' => 'permission',
        'v' => 'validate',
        'r' => 'relation',
        'd' => 'default',
        'c' => 'control',
        'opt' => 'options',
        'w' => 'width',
        'fmt' => 'formatter',
        'req' => 'required',
        'exp' => 'export',
        'imp' => 'import',
        'sort' => 'sortable'
    ];
    
    return $map[$shortName] ?? $shortName;
}
```

### 6.2 修改表信息收集方法

```php
/**
 * 获取表结构信息
 *
 * @param string $tableName 表名
 * @return array
 */
protected function getTableInfo(string $tableName): array
{
    // 获取表字段信息
    $columns = Db::query("SHOW FULL COLUMNS FROM `{$tableName}`");
    $processedColumns = [];
    
    // 处理每个字段的注释
    foreach ($columns as $column) {
        $parsedComment = $this->parseFieldComment($column['Comment'] ?? '');
        $column['parsed_comment'] = $parsedComment;
        $processedColumns[] = $column;
    }
    
    // 获取表注释
    $tableComment = '';
    $tableInfo = Db::query("SELECT TABLE_COMMENT FROM information_schema.TABLES WHERE TABLE_NAME = '{$tableName}' AND TABLE_SCHEMA = DATABASE()");
    if (!empty($tableInfo)) {
        $tableComment = $tableInfo[0]['TABLE_COMMENT'];
    }
    
    return [
        'columns' => $processedColumns,
        'comment' => $tableComment,
        'primary_key' => $this->getPrimaryKey($processedColumns),
        'parsed_comment' => $this->helper->parseTableComment($tableComment)
    ];
}
```

### 6.3 修改字段场景生成方法

```php
/**
 * 获取字段场景配置
 *
 * @param array $columns 字段信息
 * @param array $parsedComment 解析后的表注释
 * @return array
 */
protected function getFieldScenes(array $columns, array $parsedComment): array
{
    $scenes = [
        'list' => [],
        'detail' => [],
        'select' => []
    ];
    
    // 从字段注释中收集场景信息
    foreach ($columns as $column) {
        $field = $column['Field'];
        $parsedFieldComment = $column['parsed_comment'] ?? ['tags' => []];
        
        if (isset($parsedFieldComment['tags']['scene'])) {
            $fieldScenes = is_array($parsedFieldComment['tags']['scene']) 
                ? $parsedFieldComment['tags']['scene'] 
                : explode(',', $parsedFieldComment['tags']['scene']);
                
            foreach ($fieldScenes as $scene) {
                if (isset($scenes[$scene])) {
                    $scenes[$scene][] = $field;
                }
            }
        }
    }
    
    // 如果场景为空，设置默认值
    if (empty($scenes['list'])) {
        $scenes['list'] = $this->getDefaultListFields($columns);
    }
    
    if (empty($scenes['detail'])) {
        $scenes['detail'] = $this->getDefaultDetailFields($columns);
    }
    
    if (empty($scenes['select'])) {
        $scenes['select'] = $this->getDefaultSelectFields($columns);
    }
    
    return $scenes;
}
```

### 6.4 修改搜索字段收集方法

```php
/**
 * 获取搜索字段
 *
 * @param array $columns 字段信息
 * @param array $parsedComment 解析后的表注释
 * @return array
 */
protected function getSearchFields(array $columns, array $parsedComment): array
{
    $searchFields = [];
    
    // 从字段注释中收集搜索字段信息
    foreach ($columns as $column) {
        $field = $column['Field'];
        $parsedFieldComment = $column['parsed_comment'] ?? ['tags' => []];
        
        if (isset($parsedFieldComment['tags']['search'])) {
            $searchType = $parsedFieldComment['tags']['search'];
            if ($searchType === true) {
                // 如果没有指定搜索类型，根据字段类型推断
                $searchType = $this->inferSearchType($column['Type'], $field);
            }
            
            $searchFields[$field] = ['type' => $searchType];
        }
    }
    
    return $searchFields;
}

/**
 * 推断搜索类型
 *
 * @param string $dbType 数据库类型
 * @param string $fieldName 字段名称
 * @return string
 */
protected function inferSearchType(string $dbType, string $fieldName): string
{
    if (strpos($fieldName, 'status') !== false || strpos($fieldName, 'type') !== false || strpos($dbType, 'tinyint') !== false) {
        return 'eq';
    }
    
    if (strpos($fieldName, 'time') !== false || strpos($fieldName, 'date') !== false || strpos($fieldName, 'at') !== false) {
        return 'date';
    }
    
    if (strpos($dbType, 'int') !== false) {
        return 'eq';
    }
    
    return 'like';
}
```

## 7. 迁移策略

从现有的表注释驱动迁移到字段注释驱动，建议采取以下策略：

1. **代码兼容阶段**：修改CRUD生成器代码，使其同时支持表注释和字段注释标记
2. **渐进式迁移**：新表使用字段注释标记，旧表在修改时逐步迁移
3. **双向同步**：在迁移期间，保持表注释和字段注释的一致性
4. **迁移工具**：开发一个工具，将表注释中的配置自动转换为字段注释格式
5. **文档更新**：更新开发文档，明确新的注释标记规范

## 8. 最佳实践

### 8.1 注释规范

- 保持字段描述简洁明了
- 标记按重要性排序
- 相关标记放在一起，如`@s=like @o`
- 使用简写形式提高可读性
- 不要在注释中使用`|`字符(除了分隔符)

### 8.2 字段设计规范

- 主键：`id` (int unsigned AUTO_INCREMENT)
- 创建时间：`created_at` (datetime)
- 更新时间：`updated_at` (datetime)
- 软删除：`deleted_at` (datetime)
- 创建者：`creator_id` (int unsigned)
- 租户ID：`tenant_id` (int unsigned)
- 状态字段：`status` (tinyint(1) DEFAULT '1')

### 8.3 注释示例

| 字段类型 | 注释示例 |
|---------|---------|
| 主键 | `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID \| @o @scene=list,detail,select' |
| 名称字段 | `name` varchar(50) NOT NULL COMMENT '名称 \| @s=like @e @req @scene=list,detail,select' |
| 状态字段 | `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用 \| @s=eq @e @o @fmt=status @c=switch @scene=list,detail' |
| 时间字段 | `created_at` datetime DEFAULT NULL COMMENT '创建时间 \| @o @s=date @fmt=datetime @scene=list,detail' |
| 关联字段 | `dept_id` int unsigned DEFAULT NULL COMMENT '部门ID \| @e @r=belongsTo:Dept:dept_id:id @scene=detail' |
| 图片字段 | `avatar` varchar(255) DEFAULT NULL COMMENT '头像 \| @e @c=upload @scene=detail' |
| 富文本 | `content` text COMMENT '内容 \| @e @c=editor @scene=detail' |

## 9. 结论

字段注释驱动的CRUD生成方案通过将配置信息与字段定义紧密结合，提高了代码的可读性和可维护性。这种方案既能满足复杂业务场景的需求，又能保持代码的简洁性，是CRUD框架进一步优化的重要方向。

通过标准化的字段注释格式，开发人员可以快速理解每个字段的业务含义和技术属性，减少沟通成本，提高开发效率。同时，CRUD生成器可以根据这些注释信息生成更符合业务需求的代码，减少后期的手动调整工作。
