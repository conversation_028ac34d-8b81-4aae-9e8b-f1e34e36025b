<?php
declare(strict_types=1);

namespace app\finance\model;

use app\common\core\base\BaseModel;
use app\system\model\SystemAdmin;
use app\workflow\constants\WorkflowStatusConstant;

/**
 * 报销申请表模型
 */
class FinanceExpenseReimbursement extends BaseModel
{
	// 设置表名
	protected $name = 'finance_expense_reimbursement';
	
	
	// 字段类型转换
	protected $type = [
		'workflow_instance_id' => 'integer',
		'approval_status'      => 'integer',
		'submitter_id'         => 'integer',
		'expense_type'         => 'integer',
		'total_amount'         => 'float',
		'creator_id'           => 'integer',
	];
	
	protected $append = [
		'expense_type_text',
		'items'
	];
	
	public function getAttachmentAttr($value)
	{
		return empty($value)
			? []
			: explode(',', $value);
	}
	
	
	// 审批状态常量
	const STATUS_DRAFT      = WorkflowStatusConstant::STATUS_DRAFT;
	const STATUS_PROCESSING = WorkflowStatusConstant::STATUS_PROCESSING;
	const STATUS_COMPLETED  = WorkflowStatusConstant::STATUS_COMPLETED;
	const STATUS_REJECTED   = WorkflowStatusConstant::STATUS_REJECTED;
	const STATUS_TERMINATED = WorkflowStatusConstant::STATUS_TERMINATED;
	const STATUS_RECALLED   = WorkflowStatusConstant::STATUS_RECALLED;
	const STATUS_VOID       = WorkflowStatusConstant::STATUS_VOID;
	
	// 报销类型常量
	const TYPE_TRAVEL        = 1;          // 差旅费
	const TYPE_TRANSPORT     = 2;          // 交通费
	const TYPE_MEAL          = 3;          // 餐费
	const TYPE_ACCOMMODATION = 4;          // 住宿费
	const TYPE_OFFICE        = 5;          // 办公费
	const TYPE_COMMUNICATION = 6;          // 通讯费
	const TYPE_OTHER         = 7;          // 其他
	
	/**
	 * 获取默认搜索字段
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'reimbursement_no' => ['type' => 'like'],
			'expense_type'     => ['type' => 'eq'],
			'approval_status'  => ['type' => 'eq'],
			'total_amount'     => ['type' => 'between'],
			'submit_time'      => ['type' => 'datetime'],
			'approval_time'    => ['type' => 'datetime'],
			'created_at'       => ['type' => 'date'],
		];
	}
	
	/**
	 * 关联报销明细
	 */
	public function items()
	{
		return $this->hasMany(FinanceExpenseItem::class, 'reimbursement_id', 'id');
	}
	
	/**
	 * 关联提交人
	 */
	public function submitter()
	{
		return $this->belongsTo(SystemAdmin::class, 'submitter_id', 'id')
		            ->bind([
			            'submitter_name' => 'username'
		            ]);
	}
	
	/**
	 * 关联创建人
	 */
	public function creator()
	{
		return $this->belongsTo(SystemAdmin::class, 'creator_id', 'id')
		            ->bind([
			            'creator_name' => 'username'
		            ]);
	}
	
	/**
	 * 获取报销类型文本
	 */
	public function getExpenseTypeTextAttr($value, $data)
	{
		$types = [
			self::TYPE_TRAVEL        => '差旅费',
			self::TYPE_TRANSPORT     => '交通费',
			self::TYPE_MEAL          => '餐费',
			self::TYPE_ACCOMMODATION => '住宿费',
			self::TYPE_OFFICE        => '办公费',
			self::TYPE_COMMUNICATION => '通讯费',
			self::TYPE_OTHER         => '其他',
		];
		
		return $types[$data['expense_type']] ?? '未知';
	}
	
	/**
	 * 获取审批状态文本
	 */
	public function getApprovalStatusTextAttr($value, $data)
	{
		$statuses = [
			self::STATUS_DRAFT      => '草稿',
			self::STATUS_PROCESSING => '审批中',
			self::STATUS_COMPLETED  => '已通过',
			self::STATUS_REJECTED   => '已拒绝',
			self::STATUS_TERMINATED => '已终止',
			self::STATUS_RECALLED   => '已撤回',
			self::STATUS_VOID       => '已作废',
		];
		
		return $statuses[$data['approval_status']] ?? '未知';
	}
}
