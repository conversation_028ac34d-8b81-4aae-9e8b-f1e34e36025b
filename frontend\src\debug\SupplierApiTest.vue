<template>
  <div style="padding: 20px">
    <el-card header="供应商API测试">
      <div style="margin-bottom: 20px">
        <h3>1. 直接API调用测试</h3>
        <el-button @click="testDirectApi" :loading="directLoading">测试直接API调用</el-button>
        <div v-if="directResult" style="margin-top: 10px">
          <h4>API响应结果：</h4>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px">{{
            JSON.stringify(directResult, null, 2)
          }}</pre>
        </div>
        <div v-if="directError" style="margin-top: 10px; color: red">
          <h4>错误信息：</h4>
          <pre>{{ directError }}</pre>
        </div>
      </div>

      <div style="margin-bottom: 20px">
        <h3>2. ApiSelect组件测试</h3>
        <ApiSelect
          v-model="selectedSupplier"
          :api="{ url: '/ims/ims_supplier/options' }"
          placeholder="请选择供应商"
          clearable
          :auto-load="true"
          :load-on-focus="false"
          style="width: 300px"
          @load-success="handleLoadSuccess"
          @load-error="handleLoadError"
          @change="handleChange"
        />
        <div style="margin-top: 10px">
          <p><strong>选中值：</strong>{{ selectedSupplier }}</p>
          <p><strong>加载状态：</strong>{{ apiSelectStatus }}</p>
        </div>
      </div>

      <div style="margin-bottom: 20px">
        <h3>3. 简化版供应商选择器测试</h3>
        <SupplierSelectorSimple
          v-model="selectedSupplierSimple"
          @change="handleSimpleChange"
          style="width: 300px"
        />
        <div style="margin-top: 10px">
          <p><strong>选中值：</strong>{{ selectedSupplierSimple }}</p>
        </div>
      </div>

      <div>
        <h3>4. 网络请求日志</h3>
        <el-button @click="clearLogs" size="small">清空日志</el-button>
        <div
          v-for="(log, index) in requestLogs"
          :key="index"
          style="margin-top: 5px; font-size: 12px"
        >
          <span :style="{ color: log.type === 'error' ? 'red' : 'green' }">
            [{{ log.time }}] {{ log.message }}
          </span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { ImsSupplierApi } from '@/api/ims/imsSupplier'
  import ApiSelect from '@/components/core/forms/ApiSelect/index.vue'
  import SupplierSelectorSimple from '@/components/business/SupplierSelector.vue'
  import { ElMessage } from 'element-plus'

  // 响应式数据
  const directLoading = ref(false)
  const directResult = ref(null)
  const directError = ref(null)
  const selectedSupplier = ref(null)
  const selectedSupplierSimple = ref(null)
  const apiSelectStatus = ref('未加载')
  const requestLogs = ref([])

  // 添加日志
  const addLog = (message, type = 'info') => {
    requestLogs.value.push({
      time: new Date().toLocaleTimeString(),
      message,
      type
    })
  }

  // 清空日志
  const clearLogs = () => {
    requestLogs.value = []
  }

  // 测试直接API调用
  const testDirectApi = async () => {
    try {
      directLoading.value = true
      directResult.value = null
      directError.value = null

      addLog('开始调用 /ims/ims_supplier/options 接口')

      const response = await ImsSupplierApi.options()
      directResult.value = response

      addLog(`API调用成功，返回 ${response.data?.length || 0} 条数据`, 'success')
      ElMessage.success('API调用成功')
    } catch (error) {
      directError.value = error.message || '未知错误'
      addLog(`API调用失败: ${error.message}`, 'error')
      ElMessage.error('API调用失败')
      console.error('API调用错误:', error)
    } finally {
      directLoading.value = false
    }
  }

  // ApiSelect加载成功
  const handleLoadSuccess = (data) => {
    apiSelectStatus.value = `加载成功，${data.length} 条数据`
    addLog(`ApiSelect加载成功，${data.length} 条数据`, 'success')
  }

  // ApiSelect加载失败
  const handleLoadError = (error) => {
    apiSelectStatus.value = `加载失败: ${error.message}`
    addLog(`ApiSelect加载失败: ${error.message}`, 'error')
  }

  // ApiSelect值变化
  const handleChange = (value) => {
    addLog(`ApiSelect值变化: ${value}`)
  }

  // 简化版选择器值变化
  const handleSimpleChange = (value) => {
    addLog(`简化版选择器值变化: ${value}`)
  }

  // 页面加载时自动测试
  // testDirectApi()
</script>

<style scoped>
  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
  }
</style>
