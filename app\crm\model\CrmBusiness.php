<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 商机表模型 - 基于crm_data.sql表结构
 */
class CrmBusiness extends BaseModel
{
	use CreatorTrait;

	// 设置表名
	protected $name = 'crm_business';

	// 字段类型转换
	protected $type = [
		'customer_id' => 'integer',
		'amount' => 'float',
		'probability' => 'float',
		'status' => 'integer',
		'owner_user_id' => 'integer',
		'expected_date' => 'date',
		'last_followed_at' => 'datetime',
		'next_followed_at' => 'datetime',
		'closed_date' => 'date',
	];

	// 关联客户
	public function customer()
	{
		return $this->belongsTo(CrmCustomer::class, 'customer_id', 'id');
	}

	// 关联商机产品
	public function businessProducts()
	{
		return $this->hasMany(CrmBusinessProduct::class, 'business_id', 'id');
	}

	// 关联合同
	public function contracts()
	{
		return $this->hasMany(CrmContract::class, 'business_id', 'id');
	}

	// 关联跟进记录
	public function followRecords()
	{
		return $this->hasMany(CrmFollowRecord::class, 'related_id', 'id')
			->where('related_type', 'business');
	}

	// 获取状态文本
	public function getStatusTextAttr($value, $data)
	{
		$statusMap = [
			0 => '失败',
			1 => '进行中',
			2 => '成功'
		];
		return $statusMap[$data['status']] ?? '进行中';
	}

	public function getImpSceneFields(): array
	{
		return [
			'customer_id' => [
				'label' => '客户ID',
				'type'  => 'number',
			],
			'business_name' => [
				'label' => '商机名称',
				'type'  => 'text',
			],
			'amount' => [
				'label' => '商机金额',
				'type'  => 'number',
			],
			'stage' => [
				'label' => '商机阶段',
				'type'  => 'text',
			],
			'probability' => [
				'label' => '成功概率(%)',
				'type'  => 'number',
			],
			'expected_date' => [
				'label' => '预计成交日期',
				'type'  => 'date',
			],
			'source' => [
				'label' => '商机来源',
				'type'  => 'text',
			],
			'status' => [
				'label' => '状态',
				'type'  => 'select',
				'options' => [
					0 => '失败',
					1 => '进行中',
					2 => '成功'
				]
			],
			'type' => [
				'label' => '商机类型',
				'type'  => 'text',
			],
			'description' => [
				'label' => '商机描述',
				'type'  => 'textarea',
			]
		];
	}
}