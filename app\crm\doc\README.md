# CRM商机-合同流程设计文档总览

## 文档结构

本目录包含了CRM系统中商机到合同完整流程的设计文档，包括以下文件：

### 📋 主要文档

1. **[商机合同流程设计文档.md](./商机合同流程设计文档.md)**
   - 完整的业务流程设计
   - 数据表结构设计
   - 业务规则定义
   - API接口设计
   - 实施建议

2. **[商机合同流程图.md](./商机合同流程图.md)**
   - 详细的流程图和状态图
   - 数据流转关系图
   - 时间轴流程图
   - 决策流程图
   - 权限控制流程图

3. **[商机阶段表初始化脚本.sql](./商机阶段表初始化脚本.sql)**
   - 完整的数据库表创建脚本
   - 初始化数据插入
   - 数据迁移脚本
   - 统计视图创建

4. **[商机阶段API接口文档.md](./商机阶段API接口文档.md)**
   - 详细的API接口说明
   - 请求响应示例
   - 错误码定义
   - 使用示例

## 🎯 设计核心要点

### 1. 业务流程支持

#### 多样化流程模式
- **标准流程**: 客户 → 商机 → 合同 → 执行
- **简化流程**: 客户 → 合同 → 执行
- **混合流程**: 灵活适配不同业务场景

#### 阶段管理
- **标准化阶段**: 7个默认阶段，覆盖完整销售周期
- **多租户支持**: 每个租户可自定义阶段配置
- **流程控制**: 明确的阶段流转规则和权限控制

### 2. 数据表设计

#### 核心表结构
```
crm_business_stage          # 商机阶段表（多租户）
crm_business_stage_flow     # 阶段流转规则表
crm_business_stage_record   # 阶段变更记录表
crm_business               # 商机表（优化）
crm_contract              # 合同表（优化）
```

#### 关键设计原则
- **向前兼容**: 保持现有数据结构，增量优化
- **多租户支持**: 所有表支持租户隔离
- **可扩展性**: 预留扩展字段和配置能力
- **数据完整性**: 外键约束和业务规则验证

### 3. 技术特性

#### 阶段管理特性
- ✅ **可配置阶段**: 支持自定义阶段名称、颜色、图标
- ✅ **流转控制**: 基于规则的阶段流转验证
- ✅ **历史追踪**: 完整的阶段变更记录
- ✅ **统计分析**: 阶段停留时间、转化率分析
- ✅ **自动化规则**: 支持自动提醒、自动推进等

#### 合同管理特性
- ✅ **多来源支持**: 商机转化、直接创建、续约等
- ✅ **状态管理**: 完整的合同生命周期状态
- ✅ **审批流程**: 支持合同审批工作流
- ✅ **关联管理**: 灵活的商机-合同关联关系

## 🚀 实施路径

### 阶段一：基础功能（2周）
- [ ] 创建商机阶段相关表
- [ ] 实现阶段管理CRUD功能
- [ ] 商机表增加stage_id字段
- [ ] 基础的阶段变更功能

### 阶段二：流程控制（2周）
- [ ] 实现阶段流转规则
- [ ] 阶段变更记录功能
- [ ] 权限控制和验证
- [ ] 自动化规则基础框架

### 阶段三：高级功能（2周）
- [ ] 商机转合同功能
- [ ] 统计分析功能
- [ ] 自动化提醒和通知
- [ ] 数据迁移工具

### 阶段四：优化完善（1周）
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 文档完善
- [ ] 测试和修复

## 📊 关键指标

### 业务指标
- **阶段转化率**: 各阶段之间的转化效率
- **平均停留时间**: 商机在各阶段的平均停留时间
- **成功率**: 从初始阶段到成功签约的整体成功率
- **周期时长**: 从商机创建到合同签署的平均周期

### 技术指标
- **响应时间**: API接口响应时间 < 200ms
- **并发支持**: 支持100+并发用户操作
- **数据一致性**: 99.9%的数据一致性保证
- **可用性**: 99.5%的系统可用性

## 🔧 配置说明

### 环境变量配置
```env
# 商机阶段功能开关
CRM_STAGE_IMPLEMENTATION=database  # dict_array | database
CRM_ENABLE_STAGE_RECORD=true       # 是否启用阶段变更记录
CRM_ENABLE_STAGE_FLOW=true          # 是否启用阶段流程控制
CRM_AUTO_STAGE_REMINDER=true        # 是否启用自动阶段提醒
```

### 数据库配置
```sql
-- 启用外键约束检查
SET FOREIGN_KEY_CHECKS = 1;

-- 设置默认字符集
ALTER DATABASE crm CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📝 使用指南

### 管理员操作
1. **阶段配置**: 在系统设置中配置商机阶段
2. **流转规则**: 设置阶段间的流转规则和条件
3. **权限分配**: 为不同角色分配阶段操作权限
4. **数据监控**: 定期检查阶段统计数据和异常情况

### 销售人员操作
1. **商机创建**: 创建商机时自动设置为初始阶段
2. **阶段推进**: 根据业务进展推进商机阶段
3. **记录维护**: 填写阶段变更原因和备注信息
4. **合同转换**: 在合适的阶段将商机转为合同

### 销售经理操作
1. **团队监控**: 查看团队商机在各阶段的分布
2. **异常处理**: 处理长时间停留在某阶段的商机
3. **数据分析**: 分析团队的销售漏斗和转化效率
4. **流程优化**: 根据数据反馈优化销售流程

## 🔍 常见问题

### Q1: 如何处理现有商机数据？
A: 执行数据迁移脚本，将现有的stage字段值映射到新的stage_id字段。

### Q2: 是否可以跳过某些阶段？
A: 可以，通过配置阶段流转规则允许跨阶段流转。

### Q3: 如何自定义阶段？
A: 在商机阶段管理界面创建新阶段，并配置相应的流转规则。

### Q4: 阶段变更记录会影响性能吗？
A: 记录表设计了合理的索引，对性能影响很小。可定期归档历史数据。

### Q5: 如何实现自动化规则？
A: 在阶段配置中设置auto_rules字段，系统会根据配置执行相应的自动化操作。

## 📞 技术支持

如有技术问题或建议，请联系：
- 开发团队: <EMAIL>
- 产品经理: <EMAIL>
- 技术文档: https://docs.company.com/crm

## 📄 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的商机阶段管理功能
- 基础的流程控制和统计分析

### 计划更新
- v1.1.0: 增加更多自动化规则
- v1.2.0: 支持自定义字段配置
- v1.3.0: 集成工作流引擎

---

**注意**: 本文档会随着系统功能的更新而持续维护，请关注最新版本。
