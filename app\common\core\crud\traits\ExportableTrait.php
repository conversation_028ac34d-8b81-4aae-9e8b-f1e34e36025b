<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use think\facade\Db;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

/**
 * 数据导出功能特性
 *
 * 提供数据导出功能，可独立使用，不依赖于导入功能
 */
trait ExportableTrait
{
    use FormatableFieldsTrait;
	
	/**
	 * 导入导出相关配置
	 */
	protected bool $enableExport = false;   // 是否启用导出功能
	
	
	protected int $exportBatchSize = 1000;  // 导出批处理大小
	
	protected string $exportScene = 'export'; // 默认导出场景
    
    /**
     * 获取导出字段配置
     * 
     * @param string $scene 导出场景，默认使用model中定义或默认配置
     * @return array 导出字段配置
     */
    public function getExportFields(string $scene = ''): array
    {
        // 从模型的场景中获取导出字段列表
        $fields = $this->getSceneFields($scene, 'exp');
        
        // 获取字段配置信息
        $fieldsConfig = $this->getFieldsConfig($fields, 'exp');
        
        // 解析字段格式配置
        return $this->parseFieldFormats($fieldsConfig);
    }
    
    /**
     * 导出数据
     * 
     * @param array $params 查询参数
     * @param string $scene 导出场景
     * @param string $format 导出格式(xlsx, csv等)
     * @return string 生成的文件路径
     * @throws \Exception 导出失败时抛出异常
     */
    public function exportData(array $params = [], string $scene = '', string $format = 'xlsx'): string
    {
        // 获取导出字段配置
        $fields = $this->getExportFields($scene);
        if (empty($fields)) {
            throw new \Exception('没有可导出的字段');
        }
        
        // 查询数据
        $data = $this->searchForExport($params);
        if (empty($data)) {
            throw new \Exception('没有可导出的数据');
        }
        
        // 生成导出文件
        return $this->generateExportFile($data, $fields, $format);
    }
    
    /**
     * 查询用于导出的数据
     *
     * @param array $params 查询参数
     * @return array 查询结果
     */
    public function searchForExport(array $params): array
    {
        // 过滤前端专用参数，防止传递到数据库查询
        $filteredParams = $this->filterExportParams($params);

        // 限制导出数量，防止服务器压力过大
        $limit = (int)($params['limit'] ?? 10000);
        $limit = min($limit, 50000); // 最大导出50000条

        // 处理导出类型
        $exportType = $params['export_type'] ?? 'all';

        switch ($exportType) {
            case 'selected':
                // 勾选数据导出
                $ids = $params['ids'] ?? [];
                if (empty($ids)) {
                    throw new \Exception('请先勾选要导出的数据');
                }
                return $this->exportSelectedData($ids);

            case 'current':
                // 当前页数据导出
                if (method_exists($this, 'search')) {
                    $page = (int)($params['page'] ?? 1);
                    $pageLimit = (int)($params['page_limit'] ?? 10);
                    $result = $this->search($filteredParams, [], [], false, $page, $pageLimit);
                    return $result['data'] ?? [];
                }
                break;

            case 'all':
            default:
                // 全部数据导出
                break;
        }

        // 使用通用查询方法
        if (method_exists($this, 'search')) {
            $result = $this->search($filteredParams, [], [], false);
            return $result['data'] ?? [];
        }

        // 基础查询
        $query = $this->buildBaseQuery($filteredParams);

        // 添加排序
        $order = $params['order'] ?? '';
        if ($order) {
            $orderField = $params['order_field'] ?? 'id';
            $query->order($orderField, $order);
        } else {
            $query->order('id', 'desc');
        }

        // 执行查询
        return $query->limit($limit)->select()->toArray();
    }

    /**
     * 导出勾选的数据
     *
     * @param array $ids 选中的ID数组
     * @return array 查询结果
     */
    protected function exportSelectedData(array $ids): array
    {
        if (empty($ids)) {
            return [];
        }

        // 基础查询
        $query = $this->buildBaseQuery([]);

        // 添加ID条件
        $query->whereIn('id', $ids);

        // 执行查询
        return $query->select()->toArray();
    }

    /**
     * 过滤导出参数，移除前端专用参数
     *
     * @param array $params 原始参数
     * @return array 过滤后的参数
     */
    protected function filterExportParams(array $params): array
    {
        // 前端专用参数列表，这些参数不应该传递到数据库查询
        $frontendOnlyParams = [
            'export_type', // 导出类型
            'format',      // 导出格式
            'page',        // 分页页码
            'limit',       // 每页数量
            'page_limit',  // 每页数量(别名)
            'scene',       // 导出场景
            'order',       // 排序方向
            'order_field', // 排序字段
            'ids'          // 选中的ID数组
        ];

        $filtered = [];
        foreach ($params as $key => $value) {
            if (!in_array($key, $frontendOnlyParams)) {
                $filtered[$key] = $value;
            }
        }

        return $filtered;
    }
    
    /**
     * 生成导出文件
     * 
     * @param array $data 数据
     * @param array $fields 字段配置
     * @param string $format 导出格式
     * @return string 生成的文件路径
     */
    protected function generateExportFile(array $data, array $fields, string $format): string
    {
        // 创建工作表
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置标题行
        $col = 1;
        foreach ($fields as $field) {
            $sheet->setCellValueByColumnAndRow($col++, 1, $field['title']);
        }
        
        // 设置粗体样式
        $sheet->getStyle('A1:' . $this->getColumnLetter(count($fields)) . '1')
            ->getFont()->setBold(true);
        
        // 写入数据
        $row = 2;
        foreach ($data as $item) {
            $col = 1;
            foreach ($fields as $field) {
                $value = $item[$field['name']] ?? '';
                
                // 格式化数据
                if (isset($field['format'])) {
                    $this->initFormatProcessor();
                    $options = [];
                    
                    // 处理枚举选项
                    if ($field['format'] === 'enum' || $field['format'] === 'status') {
                        $options['map'] = $this->getOptionsMap($field);
                    }
                    
                    $value = $this->formatProcessor->formatForExport($value, $field['format'], $options);
                }
                
                $sheet->setCellValueByColumnAndRow($col++, $row, $value);
            }
            $row++;
        }
        
        // 自动调整列宽
        foreach (range(1, count($fields)) as $col) {
            $sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
        }
        
        // 生成文件
        $filename = $this->getExportFilename($format);
        $filepath = $this->getSavePath() . '/' . $filename;
        
        // 保存文件
        switch ($format) {
            case 'csv':
                $writer = new Csv($spreadsheet);
                break;
            case 'xlsx':
            default:
                $writer = new Xlsx($spreadsheet);
                break;
        }
        
        $writer->save($filepath);
        
        return $filepath;
    }
    
    
    /**
     * 获取导出文件名
     * 
     * @param string $format 导出格式
     * @return string 文件名
     */
    protected function getExportFilename(string $format): string
    {
        $table = '';
        if ($this->model) {
            $table = $this->model->getTable();
        }
        
        return $table . '_export_' . date('YmdHis') . '.' . $format;
    }
    
    
} 