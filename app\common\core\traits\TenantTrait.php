<?php
declare(strict_types=1);

namespace app\common\core\traits;

use think\db\BaseQuery;
use think\db\concern\WhereQuery;

/**
 * 租户隔离特性
 * 处理多租户数据隔离
 */
trait TenantTrait
{
	/**
	 * 应用租户隔离
	 *
	 * @param mixed $query       查询构造器 BaseQuery
	 * @param string               $tenantField 租户字段名
	 * @return BaseQuery
	 */
    protected function applyTenantIsolation(mixed $query, string $tenantField = 'tenant_id'): BaseQuery
    {
        // 使用新的权限函数进行统一判断
        if (!should_apply_tenant_isolation()) {
            // 系统超级管理员在系统模式下跳过租户隔离
            return $query;
        }

        // 获取有效的租户ID（考虑租户切换）
        $effectiveTenantId = get_effective_tenant_id();

        // 断言：TokenAuthMiddleware已经确保了tenant_id >= 0
        assert($effectiveTenantId >= 0, 'Effective tenant ID should be >= 0 after TokenAuthMiddleware validation');

        // 应用租户隔离（tenant_id=0也是有效租户）
        $query->where($tenantField, $effectiveTenantId);

        return $query;
    }
    
    /**
     * 获取当前租户ID
     *
     * @return int
     */
    protected function getCurrentTenantId(): int
    {
        $request = request();
        return $request->tenantId ?? 0;
    }
} 