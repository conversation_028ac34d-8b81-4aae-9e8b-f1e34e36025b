<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\api\exception\TenantException;
use app\system\service\TenantService;
use Closure;
use think\Request;
use think\Response;

/**
 * 租户验证中间件
 */
class TenantMiddleware
{
    /**
     * 处理请求
     *
     * @param Request $request 请求对象
     * @param Closure $next    中间件对象
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 如果是超级管理员，直接放行
        if (isset($request->isSuperAdmin) && $request->isSuperAdmin) {
            return $next($request);
        }
        
        // 获取当前租户ID
        $tenantId = $request->tenantId ?? 0;
        
        if ($tenantId <= 0) {
            throw new TenantException('租户不存在或已被禁用');
        }
        
        // 验证租户状态
        $tenantService = new TenantService();
        $tenant = $tenantService->getTenantById($tenantId);
        
        if (empty($tenant)) {
            throw new TenantException('租户不存在或已被禁用');
        }
        
        if ($tenant['status'] != 1) {
            throw new TenantException('租户已被禁用');
        }
        
        // 验证租户是否过期
        if (!empty($tenant['expired_at']) && strtotime($tenant['expired_at']) < time()) {
            throw new TenantException('租户已过期');
        }
        
        // 将租户信息绑定到请求对象
        $request->tenant = $tenant;
        
        // 继续执行
        return $next($request);
    }
} 