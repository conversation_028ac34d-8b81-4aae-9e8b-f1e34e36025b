-- =====================================================
-- 插入测试供应商数据
-- 用于测试产品关联供应商功能
-- =====================================================

-- 清空现有测试数据（可选）
-- DELETE FROM ims_supplier WHERE name LIKE '测试供应商%';

-- 插入测试供应商数据
INSERT INTO `ims_supplier` (
    `tenant_id`, 
    `name`, 
    `code`, 
    `contact_name`, 
    `contact_phone`, 
    `contact_email`, 
    `address`, 
    `remark`, 
    `status`, 
    `created_at`, 
    `updated_at`
) VALUES 
(1, '测试供应商A', 'SUP001', '张三', '13800138001', 'zhang<PERSON>@example.com', '北京市朝阳区测试地址1号', '主要供应商A', 1, NOW(), NOW()),
(1, '测试供应商B', 'SUP002', '李四', '13800138002', '<EMAIL>', '上海市浦东新区测试地址2号', '主要供应商B', 1, NOW(), NOW()),
(1, '测试供应商C', 'SUP003', '王五', '13800138003', '<EMAIL>', '广州市天河区测试地址3号', '备用供应商C', 1, NOW(), NOW()),
(1, '测试供应商D', 'SUP004', '赵六', '13800138004', '<EMAIL>', '深圳市南山区测试地址4号', '新合作供应商D', 1, NOW(), NOW()),
(1, '停用供应商E', 'SUP005', '钱七', '13800138005', '<EMAIL>', '杭州市西湖区测试地址5号', '已停用的供应商', 0, NOW(), NOW());

-- 验证插入结果
SELECT 
    id,
    name,
    code,
    contact_name,
    contact_phone,
    status,
    created_at
FROM ims_supplier 
WHERE name LIKE '测试供应商%' OR name LIKE '停用供应商%'
ORDER BY id;

-- 统计信息
SELECT 
    COUNT(*) as total_suppliers,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_suppliers,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive_suppliers
FROM ims_supplier 
WHERE name LIKE '测试供应商%' OR name LIKE '停用供应商%';

-- 测试options接口应该返回的数据
SELECT 
    id,
    name,
    code
FROM ims_supplier 
WHERE status = 1 
ORDER BY name ASC;

-- 执行完成提示
SELECT 
    '测试供应商数据插入完成' as 状态,
    '可以测试产品关联供应商功能了' as 说明,
    NOW() as 执行时间;
