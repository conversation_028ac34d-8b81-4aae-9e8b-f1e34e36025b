<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
import {
  ElMessage,
  ElMessageBox,
  ElDescriptions,
  ElDescriptionsItem
} from 'element-plus'
import { useCheckedColumns } from '@/composables/useCheckedColumns'
import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
import ArtTable from '@/components/core/tables/ArtTable.vue'
import ArtTableHeader from '@/components/core/tables/ArtTableHeader.vue'
import ArtTableFullScreen from '@/components/core/tables/ArtTableFullScreen.vue'
import { BgColorEnum } from '@/enums/appEnum'
import { {{EntityName}}Api } from '@/api/{{moduleName}}/{{entityName}}'
import { ApiStatus } from '@/utils/http/status'
{{#importColumns}}
import { {{columns}} } from '@/components/core/tables/columns'
{{/importColumns}}
{{#useDrawer}}
import FormDrawer from './form-drawer.vue'
{{/useDrawer}}
{{^useDrawer}}
import FormDialog from './form-dialog.vue'
{{/useDrawer}}
{{#hasImport}}
import ImportExportDialog from './import-export-dialog.vue'
{{/hasImport}}
{{^hasImport}}{{#hasExport}}
import ImportExportDialog from './import-export-dialog.vue'
{{/hasExport}}{{/hasImport}}


// 表格数据与分页
const tableData = ref<any[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

{{#hasBatchDelete}}
// 批量删除相关
const tableRef = ref()
const selectedRows = ref<any[]>([])
{{/hasBatchDelete}}

// 详情对话框
const detailDialogVisible = ref(false)
const detailData = ref<any>({})

// 定义表单搜索初始值
const initialSearchState = {
{{#searchFields}}
  {{name}}: {{defaultValue}}, // {{comment}}
{{/searchFields}}
}

// 响应式表单数据
const formFilters = reactive({ ...initialSearchState })

// 重置表单
const handleReset = () => {
  Object.assign(formFilters, { ...initialSearchState })
  getTableData()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

// 表单项变更处理
const handleFormChange = (params: SearchChangeParams): void => {
  console.log('表单项变更:', params)
}

// 表单配置项
const formItems: SearchFormItem[] = [
{{searchItems}}
]

// 列配置
const columnOptions = [{ label: '操作', prop: 'operation' }]

// 动态列配置
const { columnChecks, columns } = useCheckedColumns(() => [
{{#tableColumns}}
  {
    prop: '{{prop}}',
    label: '{{label}}',
    {{#width}}width: {{width}},{{/width}}
    {{#component}}component: {{component}},{{/component}}
    {{#componentProps}}componentProps: {{{componentProps}}},{{/componentProps}}
    {{#isSpecialColumn}}isSpecialColumn: {{isSpecialColumn}},{{/isSpecialColumn}}
  },
{{/tableColumns}}
])

onMounted(() => {
  getTableData()
})

// 处理分页页码变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  getTableData()
}

// 处理每页条数变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getTableData()
}

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
    const res = await {{EntityName}}Api.list({
      page: currentPage.value,
      limit: pageSize.value,
      ...formFilters
    })

    if (res.code === ApiStatus.success) {
      total.value = res.data.total || 0
      currentPage.value = res.data.page || 1
      pageSize.value = res.data.limit || 10
      tableData.value = res.data.list || []
    }
  } finally {
    loading.value = false
  }
}

// 刷新表格
const handleRefresh = () => {
  getTableData()
}

// 显示详情
const showDetail = async (id: number) => {
  try {
    loading.value = true
    const res = await {{EntityName}}Api.detail(id)
    if (res.code === ApiStatus.success) {
      detailData.value = res.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(res.message || '获取详情失败')
    }
  } finally {
    loading.value = false
  }
}

// 这个函数已经在下面的条件块中定义了

// 删除记录
const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true
    const res = await {{EntityName}}Api.delete(id)

    if (res.code === ApiStatus.success) {
      ElMessage.success('删除成功')
      getTableData()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  } finally {
    loading.value = false
  }
}

{{#hasBatchDelete}}
// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条记录吗？`, '批量删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true
    const ids = selectedRows.value.map(row => row.id)
    const res = await {{EntityName}}Api.batchDelete(ids)

    if (res.code === ApiStatus.success) {
      ElMessage.success('批量删除成功')
      selectedRows.value = []
      tableRef.value?.clearSelection()
      getTableData()
    } else {
      ElMessage.error(res.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  } finally {
    loading.value = false
  }
}
{{/hasBatchDelete}}



{{#hasImport}}
// 导入导出对话框引用
const importExportDialogRef = ref()

// 显示导入对话框
const showImportDialog = () => {
  importExportDialogRef.value?.showDialog('import')
}

// 导入导出成功回调
const handleImportExportSuccess = () => {
  getTableData()
}
{{/hasImport}}
{{^hasImport}}{{#hasExport}}
// 导入导出对话框引用
const importExportDialogRef = ref()

// 导入导出成功回调
const handleImportExportSuccess = () => {
  getTableData()
}
{{/hasExport}}{{/hasImport}}

{{#hasExport}}
// 显示导出对话框
const showExportDialog = () => {
  importExportDialogRef.value?.showDialog('export')
}
{{/hasExport}}

{{#useDrawer}}
// 表单抽屉引用
const formDrawerRef = ref()

// 显示表单抽屉
const showFormDialog = (type: string, id?: number) => {
  formDrawerRef.value?.showDrawer(type, id)
}

// 表单提交成功回调
const handleFormSubmitSuccess = () => {
  getTableData()
}
{{/useDrawer}}
{{^useDrawer}}
// 表单对话框引用
const formDialogRef = ref()

// 显示表单对话框
const showFormDialog = (type: string, id?: number) => {
  formDialogRef.value?.showDialog(type, id)
}

// 表单提交成功回调
const handleFormSubmitSuccess = () => {
  getTableData()
}
{{/useDrawer}}
</script>

<template>
  <ArtTableFullScreen>
    <div class="{{moduleName}}-{{entityName}}-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton type="primary" @click="showFormDialog('add')">新增</ElButton>
            {{#hasBatchDelete}}
            <ElButton
              type="danger"
              :disabled="selectedRows.length === 0"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedRows.length }})
            </ElButton>
            {{/hasBatchDelete}}
            {{#hasImport}}
            <ElButton type="success" @click="showImportDialog">导入</ElButton>
            {{/hasImport}}
            {{#hasExport}}
            <ElButton type="warning" @click="showExportDialog">导出</ElButton>
            {{/hasExport}}
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :marginTop="10"
          {{#hasBatchDelete}}
          ref="tableRef"
          @selection-change="handleSelectionChange"
          {{/hasBatchDelete}}
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          {{#hasBatchDelete}}
          <!-- 选择列 -->
          <ElTableColumn type="selection" width="55" />
          {{/hasBatchDelete}}

          <!-- 普通列 -->
          <template v-for="col in columns.filter(col => !col.isSpecialColumn)" :key="col.prop">
            <ElTableColumn v-bind="col" />
          </template>
          
          <!-- 特殊组件列 -->
          <template v-for="col in columns.filter(col => col.component)" :key="col.prop">
            <component
              :is="col.component"
              :prop="col.prop"
              :label="col.label"
              :width="col.width"
              :align="col.align"
              v-bind="col.componentProps || {}"
            />
          </template>

          <!-- 操作列 -->
          <ElTableColumn prop="operation" label="操作" fixed="right" width="240">
            <template #default="scope">
              <div>
                <ArtButtonTable text="详情" @click="showDetail(scope.row.id)" />
                <ArtButtonTable
                  text="编辑"
                  :iconClass="BgColorEnum.PRIMARY"
                  @click="showFormDialog('edit', scope.row.id)"
                />
                <ArtButtonTable
                  text="删除"
                  :iconClass="BgColorEnum.DANGER"
                  @click="handleDelete(scope.row.id)"
                />
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 详情对话框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="{{entityTitle}}详情"
          width="700px"
          destroy-on-close
          class="detail-dialog"
        >
          <div class="detail-content" style="height: 500px; overflow-y: auto; padding-right: 10px;">
            <ElDescriptions :column="2" border>
            {{#detailItems}}
            <ElDescriptionsItem label="{{label}}">
              {{#isImage}}
              <img v-if="detailData.{{prop}}" :src="detailData.{{prop}}" class="detail-image" />
              <span v-else>-</span>
              {{/isImage}}
              {{#isTag}}
              <ElTag :type="{{tagTypeExpr}}">
                {{tagLabelExpr}}
              </ElTag>
              {{/isTag}}
              {{#isStatus}}
              <ElTag :type="detailData.{{prop}} === 1 ? 'success' : 'danger'">
                {{ detailData.{{prop}} === 1 ? '启用' : '禁用' }}
              </ElTag>
              {{/isStatus}}
              {{#isSelect}}
              <span>{{ {{selectOptionsExpr}}[detailData.{{prop}}] || detailData.{{prop}} || '-' }}</span>
              {{/isSelect}}
              {{#isRadio}}
              <span>{{ {{radioOptionsExpr}}[detailData.{{prop}}] || detailData.{{prop}} || '-' }}</span>
              {{/isRadio}}
              {{#isColor}}
              <div style="display: flex; align-items: center; gap: 8px;">
                <div v-if="detailData.{{prop}}" :style="{ width: '20px', height: '20px', backgroundColor: detailData.{{prop}}, border: '1px solid #ddd', borderRadius: '4px' }"></div>
                <span>{{ detailData.{{prop}} || '-' }}</span>
              </div>
              {{/isColor}}
              {{#isNormal}}
              {{ detailData.{{prop}} || '-' }}
              {{/isNormal}}
            </ElDescriptionsItem>
            {{/detailItems}}
          </ElDescriptions>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 表单组件 -->
        {{#useDrawer}}
        <FormDrawer
          ref="formDrawerRef"
          @success="handleFormSubmitSuccess"
        />
        {{/useDrawer}}
        {{^useDrawer}}
        <FormDialog
          ref="formDialogRef"
          @success="handleFormSubmitSuccess"
        />
        {{/useDrawer}}

        <!-- 导入导出对话框 -->
        {{#hasImport}}
        <ImportExportDialog
          ref="importExportDialogRef"
          @success="handleImportExportSuccess"
        />
        {{/hasImport}}
        {{^hasImport}}{{#hasExport}}
        <ImportExportDialog
          ref="importExportDialogRef"
          @success="handleImportExportSuccess"
        />
        {{/hasExport}}{{/hasImport}}


      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .{{moduleName}}-{{entityName}}-page {
    width: 100%;

    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }
    }

    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }

    /* 详情对话框固定高度样式 */
    :deep(.detail-dialog) {
      .el-dialog__body {
        height: 500px !important;
        padding: 20px !important;
        overflow: hidden !important;
      }

      .detail-content {
        height: 100%;
        overflow-y: auto;
        padding-right: 10px;
      }

      /* 滚动条样式优化 */
      .detail-content::-webkit-scrollbar {
        width: 6px;
      }

      .detail-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    }
  }
</style>