<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmBusinessService;
use app\crm\service\CrmCustomerService;
use app\crm\service\CrmFollowRecordService;
use app\crm\service\CrmLeadService;
use think\response\Json;

/**
 * 跟进记录表控制器
 */
class CrmFollowRecordController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CrmFollowRecordService
	 */
	protected CrmFollowRecordService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = CrmFollowRecordService::getInstance();
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$result = $this->service->getFollowRecordList($params = $this->request->param());
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id], [
			'creator'
		]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		// 处理关联数据
		$related_type = $info->related_type;
		$related_name = '';
		if ($related_type == 'lead') {
			$related_name = $info->lead->lead_name ?? '';
		}
		elseif ($related_type == 'customer') {
			$related_name = $info->customer->customer_name ?? '';
		}
		elseif ($related_type == 'business') {
			$related_name = $info->business->business_name ?? '';
		}
		$info['related_name'] = $related_name;
		return $this->success('获取成功', $info);
	}
	
	/**
	 * 状态切换
	 */
	public function status($id): Json
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	
	/**
	 * 获取关联下拉选项
	 *
	 * @return Json
	 */
	public function relatedOptions(): Json
	{
		$related_type = $this->request->param('related_type');
		$params       = $this->request->param();
		$where        = $params['where'] ?? [];
		$service      = null;
		$field        = '';
		switch ($related_type) {
			case 'lead':
				$service = CrmLeadService::getInstance();
				$field   = 'lead_name';
				break;
			case 'customer':
				$service = CrmCustomerService::getInstance();
				$field   = 'customer_name';
				break;
			case 'business':
				$service = CrmBusinessService::getInstance();
				$field   = 'business_name';
				break;
		}
		
		if (empty($service)) {
			return $this->error('参数错误');
		}
		
		$list = $service->getCrudService()
		                ->getList($where, [
			                'created_at' => 'desc'
		                ]);
		
		foreach ($list as &$item) {
			$item['label'] = $item[$field];
			$item['value'] = $item['id'];
		}
		
		return $this->success('获取成功', $list);
	}
	
}