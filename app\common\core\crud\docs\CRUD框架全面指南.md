# CRUD框架全面指南

## 第一部分：框架概述与架构设计

### 1. 框架概述

本CRUD框架是基于ThinkPHP开发的一套完整的增删改查操作封装，旨在简化后端开发流程，实现快速构建高质量API接口。框架通过各种Trait实现功能分离，提供了一种灵活且可扩展的代码组织方式。

#### 1.1 核心特性

- **组件化设计**：通过Trait机制实现功能模块化，便于维护和扩展
- **标准化CRUD操作**：统一封装常用的增删改查方法
- **字段场景管理**：支持不同场景下的字段筛选，避免数据冗余
- **灵活的查询构建**：支持多种复杂查询条件构建
- **数据权限控制**：内置数据权限过滤功能
- **自动数据过滤**：防止SQL注入，提高安全性
- **自动数据验证**：基于字段场景和数据库结构自动生成验证规则
- **导入导出功能**：支持字段注释驱动的Excel数据导入导出
- **代码生成器**：一键生成CRUD相关代码，提高开发效率

#### 1.2 应用场景

- 管理后台快速开发
- API接口标准化
- 多租户应用开发
- 需要严格数据权限控制的系统
- 需要数据导入导出功能的业务系统

### 2. 架构设计

#### 2.1 核心组件

```
CrudService (核心服务类)
├── CrudOperationsTrait (基础CRUD操作)
├── FieldSceneTrait (字段场景管理)
├── SearchTrait (搜索功能)
├── QueryBuilderTrait (查询构建)
├── DataPermissionTrait (数据权限)
├── TransactionTrait (事务处理)
├── DataFilterTrait (数据过滤)
├── ModelConfigTrait (模型配置)
├── OrderTrait (排序处理)
├── ValidatorTrait (数据验证)
└── 导入导出相关
    ├── FormatableFieldsTrait (字段格式化)
    ├── ExportableTrait (数据导出)
    └── ImportableTrait (数据导入)
```

#### 2.2 类关系图

```
┌─────────────────┐     ┌───────────────┐
│   CrudService   │◄────│   Model类     │
└─────────────────┘     └───────────────┘
        ▲
        │
┌───────┴───────────┐
│                   │
│   各种Service类   │
└───────────────────┘
        ▲
        │
┌───────┴───────────┐     ┌───────────────────────────┐
│                   │     │                           │
│     Controller    │◄────│ ImportExportController    │
└───────────────────┘     └───────────────────────────┘
```

#### 2.3 数据流程

1. Controller接收请求并传递给Service
2. Service调用CrudService的方法处理业务逻辑
3. CrudService使用Model进行数据库操作
4. 结果经过字段场景处理后返回给Controller
5. Controller将结果响应给客户端

## 第二部分：核心组件详解

### 1. CrudService

`CrudService`是框架的核心类，通过组合多个Trait实现完整的CRUD功能。

```php
class CrudService implements CrudInterface
{
    use DataPermissionTrait, TransactionTrait, DataFilterTrait, 
        ModelConfigTrait, OrderTrait, QueryBuilderTrait, 
        SearchTrait, CrudOperationsTrait, FieldSceneTrait, ValidatorTrait;
    
    // 核心属性
    protected ?Model $model = null;                     // 当前操作的模型实例
    protected string $dataRangeField = 'creator_id';    // 数据权限字段
    protected bool $enableDataPermission = true;        // 是否启用数据权限
    protected array $hiddenFields = [];                 // 默认隐藏字段
    protected array $defaultOrder = ['id' => 'desc'];   // 默认排序
    protected array $defaultSearchFields = [];          // 默认搜索字段
    protected array $allowUpdateFields = [];            // 允许单字段编辑的字段
    protected array $forbidUpdateFields = [];           // 禁止单字段编辑的字段
    protected array $allowSortFields = [];              // 允许排序的字段
    protected array $validateRules = [];                // 验证规则
    protected array $validateScene = [];                // 验证场景
    
    // 构造函数
    public function __construct(?Model $model = null)
    {
        if ($model) {
            $this->setModel($model);
        }
    }
}
```

### 2. 核心Trait功能详解

#### 2.1 CrudOperationsTrait

提供基础的CRUD操作方法：

| 方法名 | 功能描述 | 参数说明 | 返回值 |
|-------|---------|---------|-------|
| getList | 获取列表数据 | where, order, with, applyDataPermission, scene | 数据集合 |
| getPageList | 获取分页列表 | where, order, page, limit, with, applyDataPermission, scene | 分页数据 |
| getCount | 获取记录数量 | where, applyDataPermission | 整数 |
| getOne | 获取单条记录 | where, with, applyDataPermission, scene | 单条记录 |
| getDetail | 获取详情 | id, with, scene | 单条记录 |
| add | 添加记录 | data | 新记录ID |
| edit | 编辑记录 | data, where | 布尔值 |
| delete | 删除记录 | where | 布尔值 |
| updateField | 更新单个字段 | id, field, value | 布尔值 |
| getSelectOptions | 获取下拉选项 | where, labelField, valueField, order, scene | 选项数组 |

#### 2.2 FieldSceneTrait

管理不同场景下的字段筛选：

| 方法名 | 功能描述 | 参数说明 | 
|-------|---------|---------|
| setFieldScenes | 设置多个字段场景 | scenes |
| addFieldScene | 添加单个字段场景 | name, fields |
| getFieldScene | 获取指定场景的字段 | scene |
| applyFieldScene | 应用字段场景过滤 | data, scene |

字段场景功能允许根据不同业务场景返回不同的字段集合，例如：

```php
$this->setFieldScenes([
    'list' => ['id', 'name', 'status', 'created_at'],
    'detail' => [
        'id', 'name', 'description', 'status', 'created_at',
        // 关联数据
        'category' => ['id', 'name']
    ]
]);
```

#### 2.3 SearchTrait

提供搜索相关功能：

| 方法名 | 功能描述 | 参数说明 |
|-------|---------|---------|
| search | 搜索记录 | params, searchFields, with, applyDataPermission, scene |
| buildSearchWhere | 构建搜索条件 | params, searchFields |
| applySearchType | 应用搜索类型 | field, value, type, params |

支持的搜索类型：

| 类型 | 说明 | 查询方式 |
|-----|------|---------|
| eq | 等于 | = |
| neq | 不等于 | != |
| gt | 大于 | > |
| lt | 小于 | < |
| gte | 大于等于 | >= |
| lte | 小于等于 | <= |
| like | 模糊查询 | LIKE %value% |
| between | 区间查询 | BETWEEN |
| in | IN查询 | IN |
| date | 日期区间 | BETWEEN |
| custom | 自定义处理 | 通过handler回调处理 |

#### 2.4 DataPermissionTrait

提供数据权限控制：

| 方法名 | 功能描述 | 参数说明 |
|-------|---------|---------|
| setEnableDataPermission | 设置是否启用数据权限 | enable |
| getDataPermissionScope | 获取数据权限范围 | - |
| applyDataPermission | 应用数据权限 | query |

#### 2.5 TransactionTrait

提供事务处理方法：

| 方法名 | 功能描述 | 参数说明 |
|-------|---------|---------|
| startTrans | 启动事务 | - |
| commit | 提交事务 | - |
| rollback | 回滚事务 | - |

#### 2.6 ValidatorTrait

提供数据验证功能：

| 方法名 | 功能描述 | 参数说明 | 返回值 |
|-------|---------|---------|-------|
| validateData | 验证数据 | data, scene | 验证后的数据 |
| getValidateRules | 获取验证规则 | - | 验证规则数组 |
| getValidateScene | 获取验证场景 | - | 验证场景数组 |
| generateValidateSceneFromFieldScene | 从字段场景生成验证场景 | - | 验证场景数组 |
| generateValidateRulesFromTableSchema | 从数据库结构生成验证规则 | - | 验证规则数组 |

ValidatorTrait支持多种方式获取验证规则：

1. 从模型方法获取：模型中实现`getValidateRules()`和`getValidateScene()`方法
2. 在服务类中设置：通过`$validateRules`和`$validateScene`属性
3. 自动生成：通过字段场景和数据库结构自动生成

数据验证会在添加(add)、编辑(edit)和更新单字段(updateField)等操作中自动进行。

## 第三部分：使用指南

### 1. 快速开始

#### 1.1 创建模型

```php
<?php
namespace app\model;

use think\Model;

class User extends Model
{
    // 设置数据表名
    protected $name = 'user';
    
    // 类型转换
    protected $type = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    // 获取默认搜索字段
    public function getDefaultSearchFields()
    {
        return [
            'username' => ['type' => 'like'],
            'mobile' => ['type' => 'like'],
            'status' => ['type' => 'eq']
        ];
    }
    
    // 获取允许单独更新的字段
    public function getAllowUpdateFields()
    {
        return ['username', 'nickname', 'avatar', 'status', 'mobile', 'email'];
    }
    
    // 获取允许排序的字段
    public function getAllowSortFields()
    {
        return ['id', 'created_at', 'updated_at', 'sort'];
    }
    
    // 获取验证规则
    public function getValidateRules()
    {
        return [
            'username' => 'require|unique:user|length:3,20',
            'password' => 'require|length:6,20',
            'email' => 'email|unique:user',
            'mobile' => 'mobile|unique:user',
            'status' => 'in:0,1'
        ];
    }
    
    // 获取验证场景
    public function getValidateScene()
    {
        return [
            'add' => ['username', 'password', 'email', 'mobile', 'status'],
            'edit' => ['username', 'email', 'mobile', 'status']
        ];
    }
}
```

#### 1.2 创建服务类

```php
<?php
namespace app\service;

use app\common\core\crud\CrudService;
use app\model\User;

class UserService extends CrudService
{
    public function __construct()
    {
        // 初始化模型
        parent::__construct(new User());
        
        // 设置字段场景
        $this->setFieldScenes([
            // 列表场景
            'list' => [
                'id', 'username', 'nickname', 'status', 'email', 'created_at'
            ],
            // 详情场景
            'detail' => [
                'id', 'username', 'nickname', 'avatar', 'email', 
                'mobile', 'status', 'remark', 'created_at', 'updated_at',
                // 关联
                'roles' => ['id', 'name']
            ],
            // 下拉选择场景
            'select' => [
                'id', 'username'
            ]
        ]);
        
        // 设置验证规则
        $this->validateRules = [
            'username' => 'require|unique:user|length:3,20',
            'password' => 'require|length:6,20|confirm',
            'email' => 'email|unique:user'
        ];
        
        // 设置验证场景
        $this->validateScene = [
            'add' => ['username', 'password', 'email'],
            'edit' => ['username', 'email']
        ];
    }
    
    // 自定义业务方法
    public function resetPassword($userId)
    {
        // 业务逻辑实现
    }
}
```

#### 1.3 创建控制器

```php
<?php
namespace app\controller;

use app\service\UserService;
use think\Request;

class UserController extends BaseController
{
    protected $userService;
    
    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }
    
    // 列表
    public function index(Request $request)
    {
        $params = $request->param();
        $result = $this->userService->search($params, [], ['roles'], null, 'list');
        return $this->success('获取成功', $result);
    }
    
    // 详情
    public function detail($id)
    {
        $info = $this->userService->getDetail($id, ['roles'], 'detail');
        return $this->success('获取成功', $info);
    }
    
    // 添加
    public function add(Request $request)
    {
        $data = $request->param();
        $result = $this->userService->add($data);
        return $this->success('添加成功', $result);
    }
    
    // 编辑
    public function edit(Request $request)
    {
        $id = $request->param('id/d');
        $data = $request->param();
        $result = $this->userService->edit($data, ['id' => $id]);
        return $this->success('编辑成功', $result);
    }
    
    // 删除
    public function delete($id)
    {
        $result = $this->userService->delete(['id' => $id]);
        return $this->success('删除成功', $result);
    }
}
```

### 2. 常用API详解

#### 2.1 获取列表数据

```php
/**
 * 获取列表数据
 * @param array|callable $where 查询条件
 * @param array|string $order 排序规则
 * @param array|string $with 关联预载入
 * @param bool|null $applyDataPermission 是否应用数据权限
 * @param string $scene 字段场景
 * @return \think\Collection
 */
public function getList($where = [], $order = null, $with = [], ?bool $applyDataPermission = null, string $scene = 'list')
```

使用示例：

```php
// 基础用法
$list = $service->getList(['status' => 1]);

// 带排序和关联
$list = $service->getList(
    ['status' => 1], 
    ['created_at' => 'desc'], 
    ['department', 'roles']
);

// 带字段场景
$list = $service->getList(
    ['status' => 1], 
    ['created_at' => 'desc'], 
    ['department'], 
    true, 
    'admin_list'
);
```

#### 2.2 搜索功能

```php
/**
 * 搜索记录
 * @param array $params 搜索参数
 * @param array $searchFields 搜索字段配置
 * @param array $with 关联预载入
 * @param bool|null $applyDataPermission 是否应用数据权限
 * @param string $scene 字段场景
 * @return array
 */
public function search(array $params, array $searchFields = [], array $with = [], ?bool $applyDataPermission = null, string $scene = 'list')
```

使用示例：

```php
// 基础搜索
$result = $service->search([
    'username' => '张三',
    'status' => 1,
    'page' => 1,
    'limit' => 20
]);

// 自定义搜索字段
$result = $service->search(
    ['keyword' => '张三', 'status' => 1],
    [
        'keyword' => [
            'type' => 'custom',
            'handler' => function($value) {
                return function($query) use($value) {
                    $query->where('username|mobile|email', 'like', "%{$value}%");
                };
            }
        ]
    ]
);
```

#### 2.3 字段场景使用

```php
// 设置多个场景
$this->setFieldScenes([
    'list' => ['id', 'name', 'status'],
    'detail' => ['id', 'name', 'status', 'description', 'created_at']
]);

// 添加单个场景
$this->addFieldScene('export', ['id', 'name', 'mobile', 'created_at']);

// 使用场景
$list = $this->getList($where, $order, $with, true, 'list');
$detail = $this->getDetail($id, $with, 'detail');
```

#### 2.4 数据权限控制

```php
// 设置数据权限字段
protected $dataRangeField = 'creator_id';

// 启用/禁用数据权限
$service->setEnableDataPermission(true);

// 查询时指定是否应用数据权限
$list = $service->getList($where, $order, $with, true); // 应用
$list = $service->getList($where, $order, $with, false); // 不应用
```

## 第四部分：导入导出功能

### 1. 功能概述

导入导出功能是CRUD框架的重要扩展，通过解析字段注释中的标记来自动生成导入导出配置，实现数据的高效导入导出。该功能分为导入和导出两个独立的模块，可以按需集成。

#### 1.1 主要特性

- **字段注释驱动**：通过字段注释中的标记配置导入导出功能
- **按需引入**：导入和导出功能相互独立，可按需使用
- **自动格式推断**：根据字段类型和注释自动推断数据格式
- **模板自动生成**：自动生成导入模板，支持下拉选择、单元格验证
- **批量处理**：支持大量数据分批处理，提高性能和稳定性
- **ArtTable集成**：与前端ArtTable组件无缝集成，实现前后端一体化导入导出

#### 1.2 功能架构

导入导出功能被拆分为三个独立特性（Trait）：

1. **FormatableFieldsTrait**：基础格式处理特性，提供字段格式解析和推断功能
2. **ExportableTrait**：导出功能特性，处理数据导出相关业务
3. **ImportableTrait**：导入功能特性，处理数据导入相关业务

### 2. 安装与配置

#### 2.1 安装依赖

导入导出功能依赖PhpSpreadsheet库：

```bash
composer require phpoffice/phpspreadsheet
```

#### 2.2 配置字段标注

通过在字段注释中添加标记配置导入导出功能：

```sql
CREATE TABLE `system_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID | @exp',
  `username` varchar(50) NOT NULL COMMENT '用户名 | @s=like @e @exp @imp @val=required',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱 | @s=like @e @exp @imp @val=email',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用 | @s=eq @e @exp @imp @fmt=status',
  `role_id` int unsigned DEFAULT NULL COMMENT '角色ID | @exp @imp @opt=api:system/role/options',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间 | @exp @fmt=datetime',
  PRIMARY KEY (`id`)
) COMMENT='用户表 @module:admin @exp:true @imp:true';
```

支持的标记：

| 标记 | 简写 | 说明 | 示例 |
|-----|------|-----|------|
| @export | @exp | 标记字段可导出 | @exp |
| @import | @imp | 标记字段可导入 | @imp |
| @formatter | @fmt | 数据格式化方式 | @fmt=datetime |
| @validator | @val | 导入数据验证规则 | @val=required,email |
| @options | @opt | 下拉选项数据 | @opt=1:男,2:女 |

表级配置示例：
```
@exp:true - 启用表导出功能
@imp:true - 启用表导入功能
@exp_scene:username,email,status - 定义导出场景包含的字段
@imp_scene:username,email,status,role_id - 定义导入场景包含的字段
@imp_batch:100 - 导入批次大小设置
```

### 3. 集成导出功能

#### 3.1 在服务类中集成导出功能

```php
<?php
namespace app\service;

use app\common\core\crud\CrudService;
use app\common\core\crud\traits\ExportableTrait;

class UserService extends CrudService
{
    // 引入导出功能特性
    use ExportableTrait;
    
    // 设置字段场景
    protected array $fieldScenes = [
        'list' => ['id', 'username', 'email', 'status', 'created_at'],
        'detail' => ['id', 'username', 'email', 'mobile', 'status', 'created_at'],
        'export' => ['id', 'username', 'email', 'status', 'created_at'] // 导出场景
    ];
    
    // 其他方法...
}
```

#### 3.2 自定义导出数据查询

您可以重写`searchForExport`方法定制导出数据的查询逻辑：

```php
protected function searchForExport(array $params): array
{
    // 自定义查询逻辑
    $query = $this->model->alias('u')
        ->join('role r', 'r.id = u.role_id', 'LEFT')
        ->field(['u.*', 'r.name as role_name']);
        
    // 添加查询条件
    if (!empty($params['keyword'])) {
        $query->where('u.username|u.email', 'like', "%{$params['keyword']}%");
    }
    
    // 执行查询并返回结果
    return $query->select()->toArray();
}
```

### 4. 集成导入功能

#### 4.1 在服务类中集成导入功能

```php
<?php
namespace app\service;

use app\common\core\crud\CrudService;
use app\common\core\crud\traits\ImportableTrait;

class UserService extends CrudService
{
    // 引入导入功能特性
    use ImportableTrait;
    
    // 导入前处理钩子
    protected function beforeImport(array &$data)
    {
        foreach ($data as &$item) {
            // 处理导入数据
            $item['created_at'] = date('Y-m-d H:i:s');
        }
    }
    
    // 导入后处理钩子
    protected function afterImport(array &$result)
    {
        // 可以进行缓存清理、通知发送等后续处理
        cache('user_list', null);
    }
}
```

### 5. 控制器集成

#### 5.1 继承ImportExportController

最简单的方式是让控制器继承`ImportExportController`：

```php
<?php
namespace app\controller\admin;

use app\common\core\crud\controller\ImportExportController;
use app\service\UserService;

class UserController extends ImportExportController
{
    protected function initialize()
    {
        // 初始化服务实例
        $this->service = new UserService();
    }
    
    // 其他控制器方法...
}
```

#### 5.2 使用ControllerImportExportTrait

如果已有控制器基类，可以使用Trait方式集成：

```php
<?php
namespace app\controller\admin;

use app\common\controller\BaseController;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\service\UserService;

class UserController extends BaseController
{
    use ControllerImportExportTrait;
    
    protected function initialize()
    {
        parent::initialize();
        $this->service = new UserService();
    }
}
```

### 6. 与前端ArtTable集成

#### 6.1 ArtTable组件简介

ArtTable是一个基于ElementPlus的高级表格组件，提供了丰富的功能，包括：

- 数据列表展示
- 搜索表单
- 排序筛选
- 批量操作
- 导入导出

#### 6.2 后端配置

在CrudService中集成导入导出功能：

```php
class UserService extends CrudService
{
    // 引入导入导出功能特性
    use ExportableTrait, ImportableTrait;
    
    // 其他代码...
}
```

#### 6.3 前端调用

在ArtTable组件中配置导入导出按钮：

```vue
<template>
  <ArtTable
    :columns="columns"
    :fetch-data="fetchData"
    :search-form-items="searchFormItems"
    :operation-buttons="operationButtons"
  />
</template>

<script setup>
import { ref } from 'vue';
import { getList, exportData, importData } from '@/api/system/user';

// 操作按钮配置
const operationButtons = [
  {
    label: '导出',
    icon: 'Download',
    onClick: handleExport,
    auth: 'system:user:export'
  },
  {
    label: '导入',
    icon: 'Upload',
    onClick: handleImport,
    auth: 'system:user:import'
  }
];

// 导出处理
const handleExport = async () => {
  try {
    const params = { ...searchForm.value };
    const res = await exportData(params);
    // 处理下载
    const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = '用户数据.xlsx';
    link.click();
  } catch (error) {
    console.error('导出失败', error);
  }
};

// 导入处理
const handleImport = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';
  input.onchange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await importData(formData);
      ElMessage.success(`导入成功，成功${res.data.success}条，失败${res.data.fail}条`);
      fetchData();
    } catch (error) {
      ElMessage.error('导入失败');
    }
  };
  input.click();
};
</script>
```

#### 6.4 API接口定义

在前端API文件中定义导入导出接口：

```typescript
// 导出数据
export function exportData(params) {
  return request({
    url: '/api/system/users/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 下载导入模板
export function getImportTemplate() {
  return request({
    url: '/api/system/users/importTemplate',
    method: 'get',
    responseType: 'blob'
  });
}

// 导入数据
export function importData(data) {
  return request({
    url: '/api/system/users/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
```

更多详细的集成方法请参考 [CRUD框架与ArtTable集成指南](./CRUD框架与ArtTable集成指南.md)。

## 第五部分：进阶功能

### 1. 自定义搜索逻辑

```php
// 在服务类中重写搜索方法
public function search(array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null, string $scene = 'list')
{
    // 自定义搜索条件处理
    if (isset($params['keyword']) && !empty($params['keyword'])) {
        $params['custom_search'] = $params['keyword'];
        // 自定义搜索字段
        $searchFields['custom_search'] = [
            'type' => 'custom',
            'handler' => function($value) {
                return function($query) use($value) {
                    $query->where('username|mobile|email', 'like', "%{$value}%");
                };
            }
        ];
    }
    
    // 调用父类方法完成搜索
    return parent::search($params, $searchFields, $with, $applyDataPermission, $scene);
}
```

### 2. 事务处理

```php
public function createUserWithRoles($userData, $roleIds)
{
    // 启动事务
    $this->startTrans();
    
    try {
        // 创建用户
        $user = $this->add($userData);
        
        // 关联角色
        $this->attachRoles($user->id, $roleIds);
        
        // 提交事务
        $this->commit();
        return $user;
    } catch (\Exception $e) {
        // 回滚事务
        $this->rollback();
        throw $e;
    }
}
```

### 3. 关联数据处理

```php
// 在字段场景中定义关联
$this->setFieldScenes([
    'detail' => [
        'id', 'name', 'status',
        // 一级关联
        'department' => ['id', 'name'],
        // 多级关联
        'roles' => [
            'id', 'name', 
            'permissions' => ['id', 'name', 'code']
        ]
    ]
]);

// 使用关联查询
$detail = $service->getDetail($id, ['department', 'roles.permissions'], 'detail');
```

### 4. 自定义验证

```php
public function add(array $data)
{
    // 添加前验证
    $validate = validate('User');
    if (!$validate->check($data)) {
        throw new \Exception($validate->getError());
    }
    
    // 添加数据
    return parent::add($data);
}
```

### 5. 数据验证功能

#### 5.1 基本使用

框架提供了多种方式配置验证规则：

**方式一：在模型中定义验证规则**

```php
class User extends Model
{
    // 获取验证规则
    public function getValidateRules()
    {
        return [
            'username' => 'require|unique:user|length:3,20',
            'password' => 'require|length:6,20',
            'email' => 'email|unique:user',
            'mobile' => 'mobile|unique:user',
            'status' => 'in:0,1'
        ];
    }
    
    // 获取验证场景
    public function getValidateScene()
    {
        return [
            'add' => ['username', 'password', 'email', 'mobile', 'status'],
            'edit' => ['username', 'email', 'mobile', 'status']
        ];
    }
}
```

**方式二：在服务类中设置验证规则**

```php
class UserService extends CrudService
{
    public function __construct()
    {
        parent::__construct(new User());
        
        // 设置验证规则
        $this->validateRules = [
            'username' => 'require|unique:user|length:3,20',
            'password' => 'require|length:6,20|confirm',
            'email' => 'email|unique:user'
        ];
        
        // 设置验证场景
        $this->validateScene = [
            'add' => ['username', 'password', 'email'],
            'edit' => ['username', 'email']
        ];
    }
}
```

**方式三：自动生成验证规则**

```php
class UserService extends CrudService
{
    public function __construct()
    {
        parent::__construct(new User());
        
        // 设置字段场景
        $this->setFieldScenes([
            'list' => ['id', 'username', 'status'],
            'detail' => ['id', 'username', 'email', 'status']
        ]);
        
        // 从数据库结构自动生成验证规则
        $this->validateRules = $this->generateValidateRulesFromTableSchema();
        
        // 从字段场景自动生成验证场景
        // 会自动调用generateValidateSceneFromFieldScene()方法
    }
}
```

#### 5.2 手动验证

如果需要在特定位置手动验证数据，可以直接调用验证方法：

```php
// 在自定义方法中验证数据
public function resetPassword($userId, $password, $confirmPassword)
{
    // 手动验证
    $this->validateData([
        'password' => $password,
        'password_confirm' => $confirmPassword
    ], 'resetPassword');
    
    // 处理业务逻辑
    return $this->edit(['password' => password_hash($password, PASSWORD_DEFAULT)], ['id' => $userId]);
}
```

#### 5.3 自定义验证场景

```php
// 在服务类中添加自定义验证场景
public function __construct()
{
    parent::__construct(new User());
    
    // 添加自定义验证场景
    $this->validateScene['resetPassword'] = ['password', 'password_confirm'];
}

// 或在模型中定义
public function getValidateScene()
{
    return [
        'add' => ['username', 'password', 'email'],
        'edit' => ['username', 'email'],
        'resetPassword' => ['password', 'password_confirm']
    ];
}
```

#### 5.4 支持的验证规则

框架支持ThinkPHP内置的所有验证规则，常用规则包括：

| 规则 | 说明 | 示例 |
|-----|------|------|
| require | 必须填写 | 'name' => 'require' |
| number | 数字 | 'age' => 'number' |
| integer | 整数 | 'age' => 'integer' |
| float | 浮点数 | 'price' => 'float' |
| boolean | 布尔值 | 'status' => 'boolean' |
| email | 邮箱 | 'email' => 'email' |
| array | 数组 | 'tags' => 'array' |
| date | 日期 | 'birthday' => 'date' |
| alpha | 只允许字母 | 'name' => 'alpha' |
| alphaNum | 只允许字母和数字 | 'name' => 'alphaNum' |
| alphaDash | 只允许字母、数字和下划线_及破折号- | 'name' => 'alphaDash' |
| chs | 只允许汉字 | 'name' => 'chs' |
| chsAlpha | 只允许汉字、字母 | 'name' => 'chsAlpha' |
| chsAlphaNum | 只允许汉字、字母和数字 | 'name' => 'chsAlphaNum' |
| chsDash | 只允许汉字、字母、数字和下划线_及破折号- | 'name' => 'chsDash' |
| url | URL地址 | 'url' => 'url' |
| ip | IP地址 | 'ip' => 'ip' |
| in | 在范围内 | 'status' => 'in:0,1,2' |
| between | 在区间内 | 'age' => 'between:1,120' |
| length | 长度范围 | 'name' => 'length:3,25' |
| max | 最大长度 | 'name' => 'max:25' |
| min | 最小长度 | 'name' => 'min:3' |
| unique | 唯一值 | 'name' => 'unique:user' |
| regex | 正则表达式 | 'zip' => 'regex:/^\d{6}$/' |
| confirm | 字段值一致 | 'password' => 'confirm' |

## 第六部分：常见问题解答

### 1. 数据权限不生效

**问题**: 设置了数据权限，但查询结果不受限制。

**解决方案**:
1. 检查`$dataRangeField`是否设置正确
2. 确认`$enableDataPermission`已设为true
3. 查询时确保`applyDataPermission`参数为true或null
4. 检查`getDataPermissionScope`方法返回是否正确

### 2. 字段场景过滤不生效

**问题**: 设置了字段场景，但返回的数据字段未被过滤。

**解决方案**:
1. 确认`setFieldScenes`方法调用正确
2. 检查场景名称是否匹配
3. 验证查询方法中是否传入了正确的场景名
4. 检查关联数据的预载入是否正确

### 3. 搜索条件不正确

**问题**: 搜索时条件未按预期应用。

**解决方案**:
1. 检查模型中`getDefaultSearchFields`方法的返回值
2. 验证搜索类型是否支持
3. 检查`search`方法的`$searchFields`参数
4. 使用`buildSearchWhere`方法单独测试搜索条件构建

### 4. 代码生成不符合预期

**问题**: 生成的代码不符合项目需求。

**解决方案**:
1. 在表注释中添加特殊标签配置
2. 自定义模板文件
3. 使用`--force`参数覆盖已有文件
4. 修改生成器的默认配置

### 5. 验证器相关问题

**问题**: 自定义的验证规则不生效。

**解决方案**:
1. 检查验证规则格式是否正确
2. 确认是否在正确的类中定义了验证规则
3. 验证场景名称是否与调用方法中的一致
4. 模型方法命名是否为`getValidateRules`和`getValidateScene`

**问题**: 自动生成的验证规则不符合预期。

**解决方案**:
1. 检查数据库表结构是否完整
2. 可以通过调试查看自动生成的规则
3. 考虑手动设置验证规则以覆盖自动生成的规则

**问题**: 字段场景与验证场景不一致。

**解决方案**:
1. 确保字段场景中包含了需要验证的字段
2. 检查`generateValidateSceneFromFieldScene`方法的实现
3. 手动设置验证场景以覆盖自动生成的场景

### 6. 导入导出功能相关问题

**问题**: 导出数据为空或格式不正确。

**解决方案**:
1. 检查字段注释中的导出标记是否正确（`@exp` 或 `@export`）
2. 验证服务类中是否正确引入了 `ExportableTrait`
3. 检查表注释中是否启用了导出功能（`@exp:true`）
4. 确认服务层中的导出场景字段配置是否正确
5. 检查自定义格式化设置是否正确

**问题**: 导入模板生成失败或下载出错。

**解决方案**:
1. 确保 PhpSpreadsheet 库已正确安装
2. 检查导入文件存储目录（runtime/import）是否有写入权限
3. 验证导入模板路径和URL生成是否正确
4. 检查控制器中的导入方法是否正确配置

**问题**: 导入数据失败或有部分错误。

**解决方案**:
1. 检查Excel模板格式是否正确，字段名是否与系统匹配
2. 验证必填字段是否已填写，数据格式是否符合要求
3. 查看日志了解具体错误信息
4. 检查`beforeImport`和`afterImport`钩子是否有异常

**问题**: 下拉选项数据不显示。

**解决方案**:
1. 检查`@opt`或`@options`配置是否正确
2. 验证API接口或静态选项数据是否可访问
3. 检查格式处理器中的选项解析逻辑

## 总结

本CRUD框架通过模块化设计和灵活的配置，为ThinkPHP项目提供了强大的CRUD操作支持。开发者可以通过继承CrudService并利用其丰富的功能，快速构建高质量的后端API。框架的字段场景管理、数据权限控制、验证功能和导入导出能力，满足了企业级应用开发的各种需求。同时，代码生成器功能使得开发效率得到显著提升。

通过本文档的学习，开发者应能深入理解框架的封装逻辑以及使用方法，能够在实际项目中灵活应用和扩展本框架。对于需要数据导入导出功能的项目，框架提供的字段注释驱动解决方案可以大大减少重复代码编写，提高开发效率。
