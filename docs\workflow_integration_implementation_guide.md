# 工作流对接代码适配实施指南

## 📋 实施概述

**实施时间：** 2025-07-28  
**实施范围：** 9个业务审批表的模式二工作流对接  
**技术架构：** DynamicWorkflowFactory + FormServiceInterface + ApplicationController  

## ✅ 已完成工作

### 1. 数据库配置 ✅
- ✅ **workflow_type表配置**：已生成9个业务类型的配置SQL
- ✅ **工作流字段验证**：所有业务表均包含必需的工作流字段
- ✅ **业务代码映射**：确认DynamicWorkflowFactory映射规则正确

### 2. Model模型类创建 ✅
- ✅ **IMS模块**：ImsOutboundApproval、ImsInboundApproval、ImsShipmentApproval、ImsPurchaseApproval
- ✅ **Finance模块**：FinancePaymentApproval、FinanceExpenseReimbursement
- ✅ **HR模块**：HrBusinessTrip（已更新）、HrOuting
- ✅ **Office模块**：OfficeSampleMail
- ✅ **明细表模型**：所有相关的Item模型类

### 3. FormService实现类 ✅
- ✅ **接口实现**：所有Service类实现FormServiceInterface的7个方法
- ✅ **示例实现**：ImsOutboundApprovalService、FinancePaymentApprovalService
- ✅ **模板文档**：FormService_template.php供其他Service参考

## 🔧 剩余适配工作

### 阶段一：完成所有FormService实现

#### 1. IMS模块剩余Service（3个）
```bash
# 需要创建的Service类
app/ims/service/ImsInboundApprovalService.php
app/ims/service/ImsShipmentApprovalService.php  
app/ims/service/ImsPurchaseApprovalService.php
```

#### 2. Finance模块剩余Service（1个）
```bash
# 需要创建的Service类
app/finance/service/FinanceExpenseReimbursementService.php
```

#### 3. HR模块剩余Service（1个）
```bash
# 需要创建的Service类
app/hr/service/HrOutingService.php
```

#### 4. Office模块剩余Service（1个）
```bash
# 需要创建的Service类
app/office/service/OfficeSampleMailService.php
```

### 阶段二：ApplicationController集成优化

#### 1. 验证DynamicWorkflowFactory映射
```php
// 确保以下映射正确工作
'ims_outbound_approval' -> ImsOutboundApprovalService
'ims_inbound_approval' -> ImsInboundApprovalService
'ims_shipment_approval' -> ImsShipmentApprovalService
'ims_purchase_approval' -> ImsPurchaseApprovalService
'finance_payment_approval' -> FinancePaymentApprovalService
'finance_expense_reimbursement' -> FinanceExpenseReimbursementService
'hr_business_trip' -> HrBusinessTripService
'hr_outing' -> HrOutingService
'office_sample_mail' -> OfficeSampleMailService
```

#### 2. ApplicationController方法优化
- ✅ 现有方法已支持通用业务类型处理
- 🔧 需要完善错误处理和日志记录
- 🔧 需要添加业务类型验证

### 阶段三：前端页面适配

#### 1. "我的申请"页面扩展
```javascript
// 需要在前端添加9个新的业务类型支持
const businessTypes = [
  { code: 'ims_outbound_approval', name: '出库申请' },
  { code: 'ims_inbound_approval', name: '入库申请' },
  { code: 'ims_shipment_approval', name: '出货申请' },
  { code: 'ims_purchase_approval', name: '采购申请' },
  { code: 'finance_payment_approval', name: '付款申请' },
  { code: 'finance_expense_reimbursement', name: '报销申请' },
  { code: 'hr_business_trip', name: '出差申请' },
  { code: 'hr_outing', name: '外出申请' },
  { code: 'office_sample_mail', name: '样品邮寄申请' }
];
```

#### 2. 表单组件适配
- 🔧 为每个业务类型创建对应的表单组件
- 🔧 支持明细表格编辑（出库、入库、出货、采购、报销）
- 🔧 集成工作流操作按钮（提交、撤回、作废）

### 阶段四：测试验证

#### 1. 单元测试
```bash
# 测试FormService接口实现
php think test:form-service-interface

# 测试DynamicWorkflowFactory映射
php think test:dynamic-workflow-factory

# 测试工作流集成
php think test:workflow-integration
```

#### 2. 集成测试
- 🔧 测试完整的申请->审批->通过流程
- 🔧 测试撤回、拒绝、终止等异常流程
- 🔧 测试明细数据的保存和更新

## 📊 技术架构验证

### DynamicWorkflowFactory映射机制
```php
// 映射算法验证
business_code: 'ims_outbound_approval'
↓
module_code: 'ims' (从workflow_type表获取)
↓  
business_name: 'ImsOutboundApproval' (PascalCase转换)
↓
service_class: 'app\ims\service\ImsOutboundApprovalService'
```

### FormServiceInterface实现标准
```php
// 必须实现的7个方法
1. getFormData(int $id): array
2. saveForm(array $data): array  
3. updateForm(int $id, array $data): bool
4. updateFormStatus(int $id, int $status, array $extra = []): bool
5. deleteForm(int $id): bool
6. getInstanceTitle($formData): string
7. validateFormData(array $data, string $scene = 'create'): array
8. afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
```

## 🎯 实施优先级

### 高优先级（立即实施）
1. ✅ 完成所有FormService实现类
2. ✅ 执行workflow_type表配置SQL
3. ✅ 验证DynamicWorkflowFactory映射

### 中优先级（本周完成）
1. 🔧 ApplicationController集成优化
2. 🔧 前端"我的申请"页面适配
3. 🔧 基础功能测试

### 低优先级（下周完成）
1. 🔧 完整的前端表单组件
2. 🔧 全面的测试用例
3. 🔧 性能优化和错误处理

## 📝 实施检查清单

### 后端检查项
- [ ] 9个FormService类全部创建完成
- [ ] workflow_type表配置已执行
- [ ] DynamicWorkflowFactory映射测试通过
- [ ] ApplicationController方法测试通过
- [ ] 工作流状态同步测试通过

### 前端检查项  
- [ ] "我的申请"页面支持9个业务类型
- [ ] 表单创建和编辑功能正常
- [ ] 工作流操作按钮功能正常
- [ ] 明细表格编辑功能正常
- [ ] 审批状态显示正确

### 集成检查项
- [ ] 完整申请流程测试通过
- [ ] 异常流程处理正确
- [ ] 数据一致性验证通过
- [ ] 权限控制验证通过
- [ ] 性能测试达标

## 🚀 下一步行动

1. **立即执行**：完成剩余6个FormService实现类
2. **今日完成**：执行workflow_type配置SQL，验证映射机制
3. **本周完成**：ApplicationController优化和基础测试
4. **下周完成**：前端适配和全面测试

通过以上系统性的适配工作，可以实现9个业务审批表与工作流系统的完美集成，支持模式二通用页面集成对接。
