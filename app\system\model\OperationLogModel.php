<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 操作日志模型
 */
class OperationLogModel extends BaseModel
{
    
    /**
     * 表名
     * @var string
     */
    protected $name = 'system_operation_log';
	
	protected $json = ['params', 'result'];
	
	protected $jsonAssoc = true;
    
    /**
     * 用户关联
     * @return BelongsTo
     */
    public function admin()
    {
        return $this->belongsTo(AdminModel::class, 'admin_id', 'id')->bind(['username']);
    }

    /**
     * 菜单关联
     * @return BelongsTo
     */
    public function menu()
    {
        return $this->belongsTo(MenuModel::class, 'menu_id', 'id');
    }
} 