<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 产品分类表模型
 */
class CrmProductCategory extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_product_category';
	
	// 设置主键
	protected $pk = 'id';
	
	protected $append = [
		'creator',
	];

	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'name' => ['type' => 'like'],
			'code' => ['type' => 'like'],
			'status' => ['type' => 'eq'],
			'created_at' => ['type' => 'date'],
			'updated_at' => ['type' => 'date'],
		];
	}
}