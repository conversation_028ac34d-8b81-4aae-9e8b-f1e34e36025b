<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmContractReceivableService;
use think\response\Json;

/**
 * 回款记录表控制器
 */
class CrmContractReceivableController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CrmContractReceivableService
	 */
	protected CrmContractReceivableService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = CrmContractReceivableService::getInstance();
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * 删除回款记录（重写CrudControllerTrait的delete方法）
	 * 只允许删除草稿状态的回款
	 */
	public function delete(): Json
	{
		$id = (int)$this->request->param('id');

		if (!$id) {
			return $this->error('参数错误');
		}

		try {
			// 获取回款信息
			$receivable = $this->service->getOne(['id' => $id]);
			if ($receivable->isEmpty()) {
				return $this->error('回款记录不存在');
			}

			// 检查审批状态 - 只允许删除草稿状态
			if ($receivable->approval_status !== 0) {
				return $this->error('只有草稿状态的回款才能删除，其他状态请使用作废功能');
			}

			// 执行删除
			$result = $this->service->delete([$id]);
			return $result
				? $this->success('删除成功')
				: $this->error('删除失败');

		} catch (\Exception $e) {
			return $this->error('删除失败：' . $e->getMessage());
		}
	}

	/**
	 * 作废回款记录
	 */
	public function void(int $id): Json
	{
		try {
			$reason = $this->request->post('reason', '');
			if (empty(trim($reason))) {
				return $this->error('作废原因不能为空');
			}

			$result = $this->service->voidReceivable($id, trim($reason));
			return $this->success('作废成功', ['result' => $result]);
		} catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}





}