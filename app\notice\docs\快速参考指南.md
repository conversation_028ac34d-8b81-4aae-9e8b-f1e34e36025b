# 消息中心快速参考指南

## 🚀 快速开始

### 1. 基本发送消息
```php
use app\notice\service\NoticeDispatcherService;

// 最简单的发送方式 - 注意：数据使用英文键名
NoticeDispatcherService::getInstance()->send(
    'workflow',              // 模块
    'task_approval',         // 动作
    [                        // 数据 - 使用英文键名
        'title' => '张三的请假申请',
        'task_name' => '部门经理审批',
        'submitter_name' => '张三',
        'created_at' => date('Y-m-d H:i:s')
    ],
    [1, 2, 3],              // 接收人
    ['creator_id' => 1]     // 选项
);
```

### 2. 获取未读消息
```php
use app\notice\service\NoticeMessageService;

$count = NoticeMessageService::getInstance()->getUnreadCount($userId);
```

### 3. 标记已读
```php
NoticeMessageService::getInstance()->markAsRead($messageId, $userId);
```

## � 变量映射机制

### 重要说明
- **代码传入**: 必须使用英文键名（如 `title`、`task_name`）
- **模板内容**: 使用中文变量名（如 `${流程标题}`、`${任务名称}`）
- **自动映射**: 消息中心会自动将英文键名映射为中文变量名

### 示例
```php
// 代码传入英文键名
$variables = [
    'title' => '张三的请假申请',        // 映射为 ${流程标题}
    'task_name' => '部门经理审批',      // 映射为 ${任务名称}
    'approver_name' => '李四'          // 映射为 ${审批人}
];
```

## �📋 模板编码对照表

### 工作流模块 (workflow)
| 编码 | 名称 | 用途 |
|------|------|------|
| `workflow_task_approval` | 任务审批通知 | 提交审批时通知审批人 |
| `workflow_task_approved` | 审批结果通知 | 审批完成后通知申请人 |
| `workflow_task_cc` | 抄送通知 | 流程抄送给相关人员 |
| `workflow_task_urge` | 催办通知 | 催办超时未处理的任务 |
| `workflow_task_transfer` | 转交通知 | 任务转交给其他人 |
| `workflow_task_terminated` | 终止通知 | 流程被终止 |
| `workflow_task_void` | 作废通知 | 流程被作废 |

### CRM模块 (crm)
| 编码 | 名称 | 用途 |
|------|------|------|
| `crm_lead_convert` | 线索转化通知 | 线索转化为客户 |
| `crm_customer_assign` | 客户分配通知 | 客户分配给销售 |
| `crm_business_stage_change` | 商机阶段变更 | 商机阶段发生变更 |
| `crm_quotation_create` | 报价单创建 | 创建新报价单 |
| `crm_contract_approval` | 合同审批 | 合同提交审批 |

### 系统模块 (system)
| 编码 | 名称 | 用途 |
|------|------|------|
| `system_notice` | 系统通知 | 系统公告、维护通知等 |

## 🔧 常用变量对照

### 工作流变量
```php
[
    'title' => '流程标题',
    'task_name' => '任务名称',
    'submitter_name' => '提交人姓名',
    'created_at' => '提交时间',
    'approver_name' => '审批人姓名',
    'result' => '审批结果',
    'opinion' => '审批意见',
    'detail_url' => '详情链接'
]
```

### CRM变量
```php
[
    'customer_name' => '客户名称',
    'customer_phone' => '客户电话',
    'business_name' => '商机名称',
    'contract_no' => '合同编号',
    'contract_amount' => '合同金额',
    'lead_name' => '线索名称'
]
```

## 📨 发送渠道配置

| 渠道 | 编码 | 说明 | 配置要求 |
|------|------|------|----------|
| 站内信 | `site` | 系统内部消息 | 无需配置 |
| 邮件 | `email` | 邮件通知 | 需配置SMTP |
| 短信 | `sms` | 短信通知 | 需配置短信API |
| 企业微信 | `wework` | 企业微信通知 | 需配置企业微信 |
| 钉钉 | `dingtalk` | 钉钉通知 | 需配置钉钉应用 |
| Webhook | `webhook` | 自定义回调 | 需配置回调URL |

### 多渠道发送
```php
// 同时发送到多个渠道
'send_channels' => 'site,email,sms'
```

## ⚙️ 选项参数说明

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `creator_id` | int | 发送者ID | `1` |
| `business_id` | int | 业务数据ID | `123` |
| `module_code` | string | 模块编码 | `'workflow'` |
| `send_channels` | string | 发送渠道 | `'site,email'` |
| `priority` | int | 优先级(0-2) | `1` |
| `delay_minutes` | int | 延迟分钟数 | `30` |

### 优先级说明
- `0`: 普通 (默认)
- `1`: 重要
- `2`: 紧急

## 🔍 常用查询方法

### 消息查询
```php
// 获取用户消息列表
$messages = NoticeMessageService::getInstance()->getUserMessages($userId, [
    'read_status' => 0,        // 0=未读, 1=已读
    'module_code' => 'workflow', // 模块筛选
    'start_date' => '2025-07-01', // 开始日期
    'end_date' => '2025-07-16',   // 结束日期
    'page' => 1,               // 页码
    'limit' => 10              // 每页数量
]);

// 获取未读消息数
$count = NoticeMessageService::getInstance()->getUnreadCount($userId);

// 标记消息已读
NoticeMessageService::getInstance()->markAsRead($messageId, $userId);

// 批量标记已读
NoticeMessageService::getInstance()->markMultipleAsRead($messageIds, $userId);
```

### 模板查询
```php
// 获取模板信息
$template = NoticeTemplateService::getInstance()->getTemplateByCode('workflow_task_approval');

// 获取模板列表
$templates = NoticeTemplateService::getInstance()->getTemplateList([
    'module_code' => 'workflow',
    'status' => 1
]);
```

## 🚨 错误处理

### 常见错误及解决方案

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| 模板不存在 | 模板编码错误 | 检查模板编码是否正确 |
| 变量缺失 | 必填变量未提供 | 检查并提供所有必填变量 |
| 接收人为空 | 接收人数组为空 | 确保接收人ID数组有效 |
| 发送失败 | 渠道配置错误 | 检查对应渠道配置 |

### 调试代码
```php
// 开启调试日志
Log::info('发送消息调试', [
    'module' => $module,
    'action' => $action,
    'data' => $data,
    'recipients' => $recipients
]);

// 检查模板是否存在
$template = NoticeTemplateService::getInstance()->getTemplateByCode("{$module}_{$action}");
if (!$template) {
    Log::error("模板不存在: {$module}_{$action}");
    return false;
}

// 检查接收人是否有效
$validRecipients = array_filter($recipients, function($userId) {
    return AdminModel::where('id', $userId)->count() > 0;
});

if (empty($validRecipients)) {
    Log::error('没有有效的接收人', ['recipients' => $recipients]);
    return false;
}
```

## 📊 队列管理

### 队列处理命令
```bash
# 处理消息队列
php think notice:process-queue

# 处理延迟消息
php think notice:process-delayed
```

### 队列状态查询
```sql
-- 查看队列状态
SELECT 
    channel,
    status,
    COUNT(*) as count
FROM notice_queue 
GROUP BY channel, status;

-- 查看失败的队列项
SELECT * FROM notice_queue 
WHERE status = 3 
ORDER BY created_at DESC 
LIMIT 10;
```

## 🔧 配置文件

### 队列配置 (config/queue.php)
```php
return [
    'default' => 'redis',
    'connections' => [
        'redis' => [
            'driver' => 'redis',
            'queue' => 'notice',
            'retry_after' => 90,
        ],
    ],
];
```

### 缓存配置
```php
// 模板缓存时间: 1小时
// 未读消息缓存: 实时更新
```

## 📞 技术支持

### 日志文件位置
- 消息发送日志: `runtime/log/notice.log`
- 队列处理日志: `runtime/log/queue.log`
- 系统错误日志: `runtime/log/error.log`

### 数据库表
- `notice_template`: 通知模板
- `notice_message`: 消息记录
- `notice_recipient`: 接收人记录
- `notice_queue`: 发送队列
- `notice_template_tenant_config`: 租户配置

### 常用SQL查询
```sql
-- 查看最近的消息
SELECT * FROM notice_message 
ORDER BY created_at DESC 
LIMIT 10;

-- 查看用户未读消息
SELECT m.*, r.read_status 
FROM notice_message m
JOIN notice_recipient r ON m.id = r.message_id
WHERE r.user_id = 1 AND r.read_status = 0;

-- 查看队列处理情况
SELECT 
    DATE(created_at) as date,
    status,
    COUNT(*) as count
FROM notice_queue 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), status
ORDER BY date DESC;
```

## 🎯 最佳实践

1. **使用调度器发送**: 推荐使用 `NoticeDispatcherService` 而不是直接调用消息服务
2. **错误处理**: 始终包含 try-catch 错误处理
3. **变量验证**: 发送前验证所有必填变量
4. **批量发送**: 避免在循环中发送消息，应收集接收人后批量发送
5. **日志记录**: 重要操作记录详细日志便于调试
6. **队列监控**: 定期检查队列处理状态，及时处理失败项
