<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\identifier;

use think\facade\Request;
use app\common\middleware\AuthMiddleware;

/**
 * 用户ID标识实现
 */
class UserIdentifier implements IdentifierInterface
{
    /**
     * 获取标识值
     *
     * @return string 标识值（用户ID）
     */
    public function getValue(): string
    {
        // 从请求中获取用户ID，如果未登录则使用IP地址作为标识
        $userId = Request::middleware('user_id');
        
        if (empty($userId)) {
            // 尝试从JWT中获取用户ID
            try {
                $userId = app()->jwt->getUser()->id ?? 0;
            } catch (\Throwable $e) {
                $userId = 0;
            }
        }
        
        // 如果无法获取用户ID，则使用IP作为标识
        if (empty($userId)) {
            return 'ip:' . Request::ip();
        }
        
        return 'user:' . $userId;
    }
} 