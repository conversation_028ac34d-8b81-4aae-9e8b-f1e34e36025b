# CRUD框架使用手册

## 1. 框架概述

本框架是一个基于ThinkPHP的CRUD（Create, Read, Update, Delete）操作封装，旨在简化后端开发，实现快速构建API接口。框架具有以下特点：

- **标准化CRUD操作**：统一封装了常用的增删改查方法
- **自动数据过滤**：防止SQL注入，提高安全性
- **灵活的查询构建**：支持各种复杂查询条件
- **字段场景管理**：支持不同场景下的字段筛选，避免数据冗余
- **数据权限控制**：内置数据权限过滤功能
- **代码生成器**：一键生成CRUD相关代码，提高开发效率

## 2. 快速开始

### 2.1 创建模型

模型需要继承自`think\Model`并实现相关方法：

```php
<?php
namespace app\model;

use think\Model;

class User extends Model
{
    // 设置数据表名
    protected $name = 'user';
    
    // 类型转换
    protected $type = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    // 获取默认搜索字段
    public function getDefaultSearchFields()
    {
        return [
            'username' => ['type' => 'like'],
            'mobile' => ['type' => 'like'],
            'status' => ['type' => 'eq']
        ];
    }
    
    // 获取允许单独更新的字段
    public function getAllowUpdateFields()
    {
        return ['username', 'nickname', 'avatar', 'status', 'mobile', 'email'];
    }
    
    // 获取允许排序的字段
    public function getAllowSortFields()
    {
        return ['id', 'created_at', 'updated_at', 'sort'];
    }
}
```

### 2.2 创建服务类

服务类继承`CrudService`，实现具体业务逻辑和字段场景定义：

```php
<?php
namespace app\service;

use app\common\core\crud\CrudService;
use app\model\User;

class UserService extends CrudService
{
    public function __construct()
    {
        // 初始化模型
        parent::__construct(new User());
        
        // 设置字段场景
        $this->setFieldScenes([
            // 列表场景
            'list' => [
                'id', 'username', 'nickname', 'status', 'email', 'created_at'
            ],
            // 详情场景
            'detail' => [
                'id', 'username', 'nickname', 'avatar', 'email', 
                'mobile', 'status', 'remark', 'created_at', 'updated_at',
                // 关联
                'roles' => ['id', 'name']
            ]
        ]);
    }
    
    // 自定义业务方法
    public function resetPassword($userId)
    {
        // 业务逻辑实现
    }
}
```

### 2.3 创建控制器

控制器调用服务类实现接口：

```php
<?php
namespace app\controller;

use app\service\UserService;
use think\Request;

class UserController extends BaseController
{
    protected $userService;
    
    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }
    
    // 列表
    public function index(Request $request)
    {
        $params = $request->param();
        $result = $this->userService->search($params, [], ['roles'], null, 'list');
        return $this->success('获取成功', $result);
    }
    
    // 详情
    public function detail($id)
    {
        $info = $this->userService->getDetail($id, ['roles'], 'detail');
        return $this->success('获取成功', $info);
    }
    
    // 添加
    public function add(Request $request)
    {
        $data = $request->param();
        $result = $this->userService->add($data);
        return $this->success('添加成功', $result);
    }
    
    // 编辑
    public function edit(Request $request)
    {
        $id = $request->param('id/d');
        $data = $request->param();
        $result = $this->userService->edit($data, ['id' => $id]);
        return $this->success('编辑成功', $result);
    }
    
    // 删除
    public function delete($id)
    {
        $result = $this->userService->delete(['id' => $id]);
        return $this->success('删除成功', $result);
    }
}
```

## 3. 核心功能说明

### 3.1 CrudService 基础方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| getList | 获取列表 | where: 查询条件<br>order: 排序规则<br>with: 关联查询<br>applyDataPermission: 是否应用数据权限<br>scene: 字段场景 |
| getPageList | 获取分页列表 | where: 查询条件<br>order: 排序规则<br>page: 页码<br>limit: 每页数量<br>with: 关联查询<br>applyDataPermission: 是否应用数据权限<br>scene: 字段场景 |
| getCount | 获取记录数 | where: 查询条件<br>applyDataPermission: 是否应用数据权限 |
| getOne | 获取单条记录 | where: 查询条件<br>with: 关联查询<br>applyDataPermission: 是否应用数据权限<br>scene: 字段场景 |
| getDetail | 获取详情 | id: 记录ID<br>with: 关联查询<br>scene: 字段场景 |
| search | 搜索记录 | params: 搜索参数<br>searchFields: 搜索字段配置<br>with: 关联查询<br>applyDataPermission: 是否应用数据权限<br>scene: 字段场景 |
| add | 添加记录 | data: 记录数据 |
| edit | 编辑记录 | data: 更新数据<br>where: 更新条件 |
| delete | 删除记录 | where: 删除条件 |
| updateField | 更新单个字段 | id: 记录ID<br>field: 更新字段<br>value: 更新值 |
| getSelectOptions | 获取下拉选项 | where: 查询条件<br>labelField: 标签字段<br>valueField: 值字段<br>order: 排序规则<br>scene: 字段场景 |

### 3.2 字段场景功能

字段场景功能允许根据不同业务场景返回不同的字段集合，避免数据冗余，提高接口性能。

#### 3.2.1 场景定义方式

1. **在CrudService子类中定义**：

```php
$this->setFieldScenes([
    'list' => ['id', 'name', 'status', 'created_at'],
    'detail' => [
        'id', 'name', 'description', 'status', 'created_at', 'updated_at',
        'category' => ['id', 'name']  // 关联字段
    ]
]);
```

2. **添加单个场景**：

```php
$this->addFieldScene('export', ['id', 'name', 'mobile', 'email', 'created_at']);
```

3. **在Model类中定义**：

```php
public function getFieldScene($scene = 'list')
{
    $scenes = [
        'list' => ['id', 'username', 'status'],
        'detail' => ['id', 'username', 'email', 'mobile', 'status']
    ];
    
    return $scenes[$scene] ?? null;
}
```

#### 3.2.2 使用场景

在调用CRUD方法时传入场景名称：

```php
// 列表场景
$list = $service->getList($where, $order, $with, true, 'list');

// 详情场景
$detail = $service->getDetail($id, $with, 'detail');

// 搜索场景
$result = $service->search($params, [], $with, true, 'admin_list');
```

#### 3.2.3 关联数据处理

字段场景支持嵌套的关联数据处理：

```php
$this->setFieldScenes([
    'detail' => [
        'id', 'name', 'status',
        // 关联表字段筛选
        'department' => ['id', 'name'],
        'roles' => ['id', 'name', 'permissions' => ['id', 'name', 'code']]
    ]
]);
```

### 3.3 查询条件构建

#### 3.3.1 基础查询条件

```php
// 等值查询
$where = ['status' => 1];

// 表达式查询
$where = [
    ['id', '>', 10],
    ['create_at', 'between', [
        '2023-01-01', 
        '2023-12-31'
    ]]
];

// 混合条件
$where = [
    'status' => 1,
    ['name', 'like', '%测试%']
];
```

#### 3.3.2 搜索条件配置

在模型中定义默认搜索字段：

```php
public function getDefaultSearchFields()
{
    return [
        'username' => ['type' => 'like'],
        'mobile' => ['type' => 'like'],
        'status' => ['type' => 'eq'],
        'created_at' => ['type' => 'date'],
        'category_ids' => ['type' => 'in'],
        'custom_field' => [
            'type' => 'custom',
            'handler' => function($value, $params) {
                return ['field', '=', $value];
            }
        ]
    ];
}
```

支持的搜索类型：

| 类型 | 说明 | 示例 |
|------|------|------|
| eq | 等于 | status=1 |
| like | 模糊查询 | name=张三 -> %张三% |
| between | 区间查询 | price=[10, 100] |
| in | IN 查询 | ids=[1,2,3] |
| date | 日期区间 | created_at=['2023-01-01', '2023-12-31'] |
| custom | 自定义处理 | 通过handler回调处理 |

### 3.4 数据权限控制

数据权限用于控制用户只能看到自己有权限的数据。

```php
// 设置数据权限字段
protected $dataRangeField = 'creator_id';

// 启用/禁用数据权限
$service->setEnableDataPermission(true);

// 在查询时指定是否应用数据权限
$list = $service->getList($where, $order, $with, true); // 应用
$list = $service->getList($where, $order, $with, false); // 不应用
```

## 4. 代码生成器使用

### 4.1 命令行生成CRUD代码

```bash
php think crud user --module=admin --with-frontend
```

### 4.2 参数说明

| 参数 | 简写 | 说明 | 默认值 |
|------|------|------|--------|
| table | - | 数据表名称(必填) | - |
| --module | -m | 模块名称 | system |
| --controller | -c | 控制器名称 | 表名驼峰化 |
| --model | -o | 模型名称 | 表名驼峰化+Model |
| --service | -s | 服务类名称 | 表名驼峰化+Service |
| --controller-base | -b | 控制器基类 | BaseController |
| --with-frontend | -f | 是否生成前端代码 | false |
| --frontend-path | -p | 前端代码生成路径 | frontend/src/views |
| --with-test | -t | 是否生成单元测试 | false |
| --with-api-doc | -a | 是否生成API文档 | false |
| --api-doc-path | - | API文档生成路径 | docs/api |
| --force | - | 强制覆盖已存在文件 | false |

### 4.3 表注释配置

在数据表注释中可以添加特殊标签配置代码生成：

```sql
CREATE TABLE `user` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(64) NOT NULL COMMENT '密码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表 @module:admin @search:username,status';
```

支持的标签：

- `@module:xxx` - 指定模块名
- `@search:field1,field2` - 指定搜索字段
- `@sort:field1,field2` - 指定排序字段
- `@update:field1,field2` - 指定可更新字段

### 4.4 生成文件说明

#### 4.4.1 后端文件生成

运行代码生成命令后，将生成以下后端文件：

1. **模型文件**：`app/{module}/model/{ModelName}.php`
2. **服务类文件**：`app/{module}/service/{ServiceName}.php`
3. **控制器文件**：`app/{module}/controller/{ControllerName}Controller.php`
4. **路由文件**：`route/{Module}.php`

路由文件会生成在项目根目录下的`route`目录中，以模块名命名（首字母大写）。如`route/Admin.php`、`route/System.php`等。生成器会自动将路由代码插入到对应模块的路由文件中。如果路由文件不存在，则会创建新文件并初始化基本结构。

#### 4.4.2 前端文件生成

当使用`--with-frontend`选项时，将生成以下前端文件：

1. **视图文件**：`{frontend-path}/{table}/list.vue`
2. **API接口文件**：`frontend/art-design-pro/src/api/{module}/{ModuleName}{EntityName}Api.ts`

前端API文件命名规则：
- 使用帕斯卡命名法(PascalCase)
- 前缀为模块名(首字母大写)
- 实体名为表名转换为帕斯卡命名法
- 后缀为`Api`
- 例如：`SystemUserApi.ts`、`AdminRoleApi.ts`等

API文件会生成在前端目录的对应模块子目录下，如果目录不存在会自动创建。

### 4.5 路由规则说明

#### 4.5.1 后端路由格式

后端路由遵循ThinkPHP 8的路由规则，路由文件生成在项目根目录的`route`目录下，以模块命名，如`route/System.php`。路由结构示例：

```php
// 控制器命名空间
$nameSpace = '\app\system\controller'; 

// 用户表路由
Route::get('user/index', $nameSpace . '\User@index');
Route::get('user/detail/:id', $nameSpace . '\User@detail');
Route::post('user/add', $nameSpace . '\User@add');
Route::post('user/edit/:id', $nameSpace . '\User@edit');
Route::post('user/delete', $nameSpace . '\User@delete');
Route::post('user/update_field', $nameSpace . '\User@updateField');
Route::get('user/options', $nameSpace . '\User@options');
```

所有路由都会被包含在一个模块级别的`Route::group`中，并配置必要的中间件：

```php
Route::group('api/system', function () {
    // 这里是各个表的路由定义
})
->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class,
]);
```

#### 4.5.2 前端API接口URL格式

前端API接口的URL请求路径格式统一为：

```
/api/{module}/{table}/{action}
```

例如：
- 获取用户列表：`/api/system/user/index`
- 获取用户详情：`/api/system/user/detail/1`
- 添加用户：`/api/system/user/add`
- 编辑用户：`/api/system/user/edit/1`

前端API接口文件按模块分类存放在`src/api/{module}`目录下，文件名采用帕斯卡命名法，前缀为模块名，后缀为`Api`，例如：`SystemUserApi.ts`。

#### 4.5.3 标准CRUD接口

每个实体都会自动生成以下标准CRUD接口：

| 操作 | 请求方法 | URL | 控制器方法 | 参数 |
|------|---------|-----|-----------|------|
| 列表 | GET | /api/{module}/{table}/index | index | 查询参数 |
| 详情 | GET | /api/{module}/{table}/detail/{id} | detail | 路径中的ID |
| 添加 | POST | /api/{module}/{table}/add | add | 表单数据 |
| 编辑 | POST | /api/{module}/{table}/edit/{id} | edit | 路径中的ID和表单数据 |
| 删除 | POST | /api/{module}/{table}/delete | delete | ID数组 |
| 更新字段 | POST | /api/{module}/{table}/update_field | updateField | ID、字段名和字段值 |
| 选项 | GET | /api/{module}/{table}/options | options | 无 |

## 5. 进阶用法

### 5.1 自定义搜索逻辑

```php
// 在服务类中重写搜索方法
public function search(array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null, string $scene = 'list')
{
    // 自定义搜索条件处理
    if (isset($params['keyword'])) {
        $params['username'] = $params['keyword'];
        $params['mobile'] = $params['keyword'];
        // 自定义搜索字段
        $searchFields = [
            'username' => ['type' => 'like'],
            'mobile' => ['type' => 'like']
        ];
    }
    
    // 调用父类方法完成搜索
    return parent::search($params, $searchFields, $with, $applyDataPermission, $scene);
}
```

### 5.2 自定义查询构建

```php
// 在服务类中创建方法
public function getActiveUsers(array $where = [])
{
    // 合并条件
    $where = array_merge($where, [
        'status' => 1,
        ['login_time', '>', date('Y-m-d H:i:s', strtotime('-30 days'))]
    ]);
    
    // 使用基础查询构建器
    $query = $this->buildBaseQuery($where, ['department', 'roles']);
    $query = $this->applyOrder($query, ['login_time' => 'desc']);
    
    // 执行自定义查询
    return $query->limit(100)->select();
}
```

### 5.3 事务处理

```php
public function createUserWithRoles($userData, $roleIds)
{
    // 启动事务
    $this->startTrans();
    
    try {
        // 创建用户
        $user = $this->add($userData);
        
        // 关联角色
        $this->attachRoles($user->id, $roleIds);
        
        // 提交事务
        $this->commit();
        return $user;
    } catch (\Exception $e) {
        // 回滚事务
        $this->rollback();
        throw $e;
    }
}
```

### 5.4 自定义字段场景处理

```php
// 在服务类中自定义场景处理
protected function customFieldScene($data, $scene)
{
    // 获取字段配置
    $fields = $this->getFieldScene($scene);
    
    // 特殊场景处理
    if ($scene === 'sensitive_data') {
        // 移除敏感数据
        foreach ($data as &$item) {
            unset($item['password'], $item['salt']);
        }
    }
    
    // 应用标准场景过滤
    return $this->applyFieldScene($data, $scene);
}
```

## 6. 最佳实践

### 6.1 代码组织

推荐的项目结构：

```
app/
├── common/
│   ├── core/
│   │   ├── crud/           # CRUD框架核心
│   │   └── ...
│   ├── exception/          # 异常处理
│   └── ...
├── controller/             # 控制器
│   ├── BaseController.php
│   ├── admin/              # 后台控制器
│   └── api/                # API控制器
├── model/                  # 模型
├── service/                # 服务层
│   ├── BaseService.php
│   └── ...
└── ...
```

### 6.2 安全最佳实践

1. **始终使用过滤后的数据**：
```php
$safeData = $this->filterData($data);
```

2. **避免直接使用原始查询**：
```php
// 不要这样做
$result = $this->model->where('id = ' . $id)->find();

// 应该这样做
$result = $this->model->where('id', '=', $id)->find();
```

3. **使用数据权限过滤敏感数据**：
```php
$this->setEnableDataPermission(true);
```

### 6.3 性能优化

1. **只查询需要的字段**：
```php
$query->field(['id', 'name', 'status'])->select();
```

2. **合理使用关联查询**：
```php
// 避免n+1问题
$list = $service->getList([], [], ['roles', 'department']);
```

3. **使用字段场景减少数据传输**：
```php
$list = $service->getList($where, $order, $with, true, 'list');
```

4. **合理设置分页大小**：
```php
$result = $service->search($params, [], [], null, 'list');
// 已内置最大分页限制：$page=1000, $limit=100
```

## 7. 常见问题

### 7.1 数据权限不生效

检查以下配置：
- 是否设置了正确的`$dataRangeField`
- 是否启用了数据权限`$enableDataPermission = true`
- 查询时是否传入了正确的权限参数

### 7.2 关联查询失败

可能原因：
- 模型中未正确定义关联关系
- 关联表不存在或字段名不匹配
- 权限问题导致无法查询关联表

### 7.3 字段场景过滤不起作用

检查以下问题：
- 场景名称是否正确
- 场景定义是否包含了所需字段
- 是否正确应用了字段场景方法

## 8. 扩展开发

### 8.1 添加自定义Trait

创建自定义特性以扩展功能：

```php
<?php
namespace app\common\core\crud\traits;

trait CustomOperationTrait
{
    // 自定义方法
    public function batchUpdate(array $idList, array $data)
    {
        // 实现批量更新逻辑
    }
    
    // 自定义导出功能
    public function export(array $params, array $fields)
    {
        // 实现导出逻辑
    }
}
```

然后在服务类中使用：

```php
<?php
namespace app\service;

use app\common\core\crud\CrudService;
use app\common\core\crud\traits\CustomOperationTrait;

class UserService extends CrudService
{
    use CustomOperationTrait;
    
    // ...
}
```

### 8.2 扩展字段场景处理

自定义字段场景处理器：

```php
<?php
namespace app\common\core\crud\scene;

class FieldSceneHandler
{
    // 处理字段场景
    public function process($data, $scene, $fields)
    {
        // 自定义处理逻辑
        return $processedData;
    }
}
```

## 9. 参考

- [ThinkPHP 官方文档](https://www.kancloud.cn/manual/thinkphp6_0/content)
- [代码生成器命令说明](../command/CrudGenerator.php) 

## 10. 最新更新

### 10.1 CRUD生成器优化 (2023-V2)

本次更新对CRUD生成器进行了多项优化，主要包括：

1. **改进路由生成**：
   - 后端路由文件现在统一生成在项目根目录的`route`目录下，按模块命名
   - 路由格式适配ThinkPHP 8标准，使用模块级的路由分组和中间件
   - 控制器方法调用使用`@`符号而非`/`分隔符

2. **前端API文件组织优化**：
   - API文件现在按模块分类存放在`frontend/art-design-pro/src/api/{module}`目录下
   - API文件命名采用帕斯卡命名法，格式为`{ModuleName}{EntityName}Api.ts`，例如`SystemUserApi.ts`
   - API请求URL路径统一为`/api/{module}/{table}/{action}`格式

3. **API类型定义增强**：
   - 增加了查询参数接口`{EntityName}QueryParams`
   - 增加了表单数据接口`{EntityName}FormData`
   - 改进字段类型根据数据库字段特性自动映射

4. **文档完善**：
   - 添加了详细的路由规则说明
   - 提供了各类生成文件的路径和命名约定
   - 列出了标准CRUD接口及其URL格式

这些优化使CRUD生成器更好地适应多租户前后端分离框架系统的需求，提高了代码组织的一致性和可维护性。 