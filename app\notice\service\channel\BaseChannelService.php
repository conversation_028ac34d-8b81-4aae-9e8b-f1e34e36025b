<?php
declare(strict_types=1);

namespace app\notice\service\channel;

use app\notice\service\interfaces\ChannelServiceInterface;
use think\facade\Log;

/**
 * 通道服务基类
 */
abstract class BaseChannelService implements ChannelServiceInterface
{
	/**
	 * 通道编码
	 *
	 * @var string
	 */
	protected string $channelCode = '';
	
	
	/**
	 * 记录发送日志
	 *
	 * @param string $action   动作
	 * @param array  $data     数据
	 * @param int    $tenantId 租户ID
	 * @return void
	 */
	protected function log(string $action, array $data, int $tenantId = 0): void
	{
		$logData = [
			'channel'   => $this->channelCode,
			'tenant_id' => $tenantId,
			'action'    => $action,
			'data'      => $data,
			'time'      => date('Y-m-d H:i:s'),
		];
		
		Log::info("通道[{$this->channelCode}]操作: {$action}", $logData);
	}
	
	/**
	 * 获取通道配置
	 *
	 * @param string $channel 通道编码
	 * @return array|null 通道配置
	 */
	public function getChannelConfig(string $channel): ?array
	{
		// 默认实现，子类可以覆盖
		return [
			'enabled' => true,
			'config' => [],
		];
	}
	
	/**
	 * 更新通道配置
	 *
	 * @param string $channel 通道编码
	 * @param array $config 通道配置
	 * @return bool 是否成功
	 */
	public function updateChannelConfig(string $channel, array $config): bool
	{
		// 默认实现，子类可以覆盖
		return true;
	}
	
	/**
	 * 启用通道
	 *
	 * @param string $channel 通道编码
	 * @return bool 是否成功
	 */
	public function enableChannel(string $channel): bool
	{
		// 默认实现，子类可以覆盖
		return true;
	}
	
	/**
	 * 禁用通道
	 *
	 * @param string $channel 通道编码
	 * @return bool 是否成功
	 */
	public function disableChannel(string $channel): bool
	{
		// 默认实现，子类可以覆盖
		return true;
	}
	
	/**
	 * 获取所有可用通道
	 *
	 * @return array 通道列表
	 */
	public function getAvailableChannels(): array
	{
		// 默认实现，子类可以覆盖
		return [$this->channelCode];
	}
	
	/**
	 * 测试通道配置
	 *
	 * @param string $channel 通道编码
	 * @param array  $config  通道配置
	 * @return array 测试结果
	 */
	abstract public function testChannelConfig(string $channel, array $config): array;
	
	/**
	 * 发送消息
	 *
	 * @param array $message    消息数据
	 * @param array $recipients 接收人列表
	 * @param array $options    选项参数
	 * @return bool 是否发送成功
	 */
	abstract public function send(array $message, array $recipients, array $options = []): bool;
} 