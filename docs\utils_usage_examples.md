# 通用工具方法使用指南

## 📋 概述

为了提高代码复用性和一致性，我们封装了天数/时长自动计算和数字转中文大写的通用方法，前后端都可以使用。

## 🎯 前端工具方法

### 1. 日期计算工具 (`frontend/src/utils/date.ts`)

#### **天数计算**
```typescript
import { calculateDays } from '@/utils/date'

// 计算出差天数
const days = calculateDays('2025-07-29', '2025-07-31') // 返回 3
const sameDayDays = calculateDays('2025-07-29 09:00', '2025-07-29 18:00') // 返回 1
const invalidDays = calculateDays('2025-07-29', '2025-07-28') // 返回 0（结束日期早于开始日期）

// 在Vue组件中使用
const formData = reactive({
  start_time: '',
  end_time: '',
  days: 0
})

// 监听日期变化自动计算天数
watch([() => formData.start_time, () => formData.end_time], () => {
  formData.days = calculateDays(formData.start_time, formData.end_time)
})
```

#### **时长计算**
```typescript
import { calculateHours } from '@/utils/date'

// 计算外出时长
const hours = calculateHours('2025-07-28 14:00', '2025-07-28 17:00') // 返回 3.0
const preciseHours = calculateHours('2025-07-28 14:30', '2025-07-28 17:15') // 返回 2.8
const invalidHours = calculateHours('2025-07-28 17:00', '2025-07-28 14:00') // 返回 0

// 在Vue组件中使用
const formData = reactive({
  start_time: '',
  end_time: '',
  duration: 0
})

// 监听时间变化自动计算时长
watch([() => formData.start_time, () => formData.end_time], () => {
  formData.duration = calculateHours(formData.start_time, formData.end_time)
})
```

### 2. 数字转换工具 (`frontend/src/utils/number.ts`)

#### **数字转中文大写**
```typescript
import { convertToChineseNumber } from '@/utils/number'

// 基本用法
convertToChineseNumber(0) // "零元整"
convertToChineseNumber(123.45) // "壹佰贰拾叁元肆角伍分"
convertToChineseNumber(1000) // "壹仟元整"
convertToChineseNumber(10000.50) // "壹万元伍角"
convertToChineseNumber(-100) // "负壹佰元整"

// 在Vue组件中使用
const formData = reactive({
  total_amount: 0,
  total_amount_chinese: ''
})

// 监听金额变化自动转换大写
watch(() => formData.total_amount, (newAmount) => {
  formData.total_amount_chinese = convertToChineseNumber(newAmount)
})

// 在明细表格中使用
const items = ref([
  { product_name: '产品A', quantity: 10, unit_price: 100, total_amount: 1000 }
])

// 计算总金额和大写
const totalAmount = computed(() => {
  return items.value.reduce((sum, item) => sum + item.total_amount, 0)
})

const totalAmountChinese = computed(() => {
  return convertToChineseNumber(totalAmount.value)
})
```

#### **其他数字工具**
```typescript
import { 
  formatNumberWithCommas, 
  formatCurrency, 
  safeAdd, 
  safeMultiply,
  roundToDecimals 
} from '@/utils/number'

// 千分位格式化
formatNumberWithCommas(1234567.89) // "1,234,567.89"

// 货币格式化
formatCurrency(1234.56) // "¥1,234.56"

// 安全计算（避免浮点数精度问题）
safeAdd(0.1, 0.2) // 0.3
safeMultiply(0.1, 3) // 0.3

// 四舍五入
roundToDecimals(1.2356, 2) // 1.24
```

## 🔧 后端工具方法

### 1. 日期计算工具 (`app/common/utils/DateCalculator.php`)

#### **天数计算**
```php
use app\common\utils\DateCalculator;

// 计算出差天数
$days = DateCalculator::calculateDays('2025-07-29', '2025-07-31'); // 返回 3
$sameDayDays = DateCalculator::calculateDays('2025-07-29 09:00', '2025-07-29 18:00'); // 返回 1
$invalidDays = DateCalculator::calculateDays('2025-07-29', '2025-07-28'); // 返回 0

// 在Service中使用
class HrBusinessTripService extends BaseService
{
    public function updateSummaryFields(int $businessTripId): void
    {
        $businessTrip = $this->model->find($businessTripId);
        
        // 自动计算天数
        $days = DateCalculator::calculateDays(
            $businessTrip->start_time, 
            $businessTrip->end_time
        );
        
        $businessTrip->save(['days' => $days]);
    }
}
```

#### **时长计算**
```php
// 计算外出时长
$hours = DateCalculator::calculateHours('2025-07-28 14:00', '2025-07-28 17:00'); // 返回 3.0
$preciseHours = DateCalculator::calculateHours('2025-07-28 14:30', '2025-07-28 17:15'); // 返回 2.8

// 在Service中使用
class HrOutingService extends BaseService
{
    public function calculateDuration(string $startTime, string $endTime): float
    {
        return DateCalculator::calculateHours($startTime, $endTime);
    }
}
```

#### **其他日期工具**
```php
// 计算工作日
$workdays = DateCalculator::calculateWorkdays('2025-07-28', '2025-08-01');

// 格式化时长
$formatted = DateCalculator::formatDuration(2.5); // "2小时30分钟"

// 检查是否为工作日
$isWorkday = DateCalculator::isWorkday('2025-07-28'); // true/false
```

### 2. 数字转换工具 (`app/common/utils/NumberConverter.php`)

#### **数字转中文大写**
```php
use app\common\utils\NumberConverter;

// 基本用法
NumberConverter::convertToChineseNumber(0); // "零元整"
NumberConverter::convertToChineseNumber(123.45); // "壹佰贰拾叁元肆角伍分"
NumberConverter::convertToChineseNumber(1000); // "壹仟元整"
NumberConverter::convertToChineseNumber(-100); // "负壹佰元整"

// 在Service中使用
class ImsOutboundApprovalService extends BaseService
{
    public function saveForm(array $data): array
    {
        // 计算总金额
        $totalAmount = array_sum(array_column($data['items'], 'total_amount'));
        
        // 转换为中文大写
        $data['total_amount'] = $totalAmount;
        $data['total_amount_chinese'] = NumberConverter::convertToChineseNumber($totalAmount);
        
        // 保存数据...
    }
}
```

#### **其他数字工具**
```php
// 千分位格式化
$formatted = NumberConverter::formatNumberWithCommas(1234567.89); // "1,234,567.89"

// 货币格式化
$currency = NumberConverter::formatCurrency(1234.56); // "¥1,234.56"

// 安全计算
$sum = NumberConverter::safeAdd(0.1, 0.2); // 0.3
$product = NumberConverter::safeMultiply(0.1, 3); // 0.3

// 数字验证
$isValid = NumberConverter::isValidNumber('123'); // true
$safeNum = NumberConverter::toSafeNumber('abc', 100); // 100.0
```

## 📊 实际应用场景

### 1. 出差申请表单
```typescript
// 前端 Vue 组件
const formData = reactive({
  start_time: '',
  end_time: '',
  days: 0
})

// 自动计算天数
watch([() => formData.start_time, () => formData.end_time], () => {
  formData.days = calculateDays(formData.start_time, formData.end_time)
})
```

```php
// 后端 Service
class HrBusinessTripService extends BaseService
{
    public function validateFormData(array $data, string $scene = 'create'): array
    {
        // 自动计算天数
        if (!empty($data['start_time']) && !empty($data['end_time'])) {
            $data['days'] = DateCalculator::calculateDays($data['start_time'], $data['end_time']);
        }
        
        return $data;
    }
}
```

### 2. 出库申请表单
```typescript
// 前端明细计算
const items = ref([])
const totalAmount = computed(() => {
  return items.value.reduce((sum, item) => safeAdd(sum, item.total_amount), 0)
})
const totalAmountChinese = computed(() => {
  return convertToChineseNumber(totalAmount.value)
})
```

```php
// 后端明细处理
class ImsOutboundApprovalService extends BaseService
{
    private function calculateTotalAmount(array $items): array
    {
        $totalAmount = 0;
        
        foreach ($items as $item) {
            $totalAmount = NumberConverter::safeAdd($totalAmount, $item['total_amount']);
        }
        
        return [
            'total_amount' => $totalAmount,
            'total_amount_chinese' => NumberConverter::convertToChineseNumber($totalAmount)
        ];
    }
}
```

## 🎯 最佳实践

### 1. 前端使用建议
- 使用 `watch` 监听数据变化自动计算
- 使用 `computed` 属性进行响应式计算
- 在表单验证前进行数据计算
- 使用 TypeScript 类型检查确保数据安全

### 2. 后端使用建议
- 在 Service 层进行业务计算
- 在数据保存前进行自动计算
- 使用异常处理确保计算安全
- 在模型事件中自动更新计算字段

### 3. 性能优化
- 前端避免在循环中重复计算
- 后端缓存复杂计算结果
- 使用防抖处理频繁的计算操作

## 📝 注意事项

1. **日期格式**：支持标准的日期时间格式，建议使用 ISO 格式
2. **数字精度**：使用安全计算方法避免浮点数精度问题
3. **错误处理**：所有方法都有错误处理，返回安全的默认值
4. **性能考虑**：避免在高频操作中重复调用计算方法
5. **数据验证**：在使用前验证输入数据的有效性

**这些工具方法已经在工作流表单中得到验证，可以安全地在项目中使用！**
