<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 跟进记录表模型 - 基于crm_data.sql表结构
 */
class CrmFollowRecord extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_follow_record';
	
	// 字段类型转换
	protected $type = [
		'related_id' => 'integer',
		'contact_id' => 'integer',
	];
	
	// 关联客户（当related_type为customer时）
	public function customer()
	{
		return $this->belongsTo(CrmCustomer::class, 'related_id', 'id')->bind ([
			'customer_name'
		]);
	}
	
	// 关联商机（当related_type为business时）
	public function business()
	{
		return $this->belongsTo(CrmBusiness::class, 'related_id', 'id')->bind ([
			'business_name'
		]);
	}
	
	// 关联线索（当related_type为lead时）
	public function lead()
	{
		return $this->belongsTo(CrmLead::class, 'related_id', 'id')->bind ([
			'lead_name'
		]);
	}
	
	// 获取跟进类型文本
	public function getFollowTypeTextAttr($value, $data)
	{
		$typeMap = [
			1 => '电话',
			2 => '邮件',
			3 => '微信',
			4 => '面谈',
			5 => '其他'
		];
		return $typeMap[$data['follow_type']] ?? '其他';
	}
	
	// 获取关联类型文本
	public function getRelatedTypeTextAttr($value, $data)
	{
		$typeMap = [
			'customer' => '客户',
			'business' => '商机',
			'contract' => '合同'
		];
		return $typeMap[$data['related_type']] ?? '';
	}
	
	public function getImpSceneFields(): array
	{
		return [
			'related_type'        => [
				'label'   => '关联类型',
				'type'    => 'select',
				'options' => [
					'customer' => '客户',
					'business' => '商机',
					'contract' => '合同'
				]
			],
			'related_id'          => [
				'label' => '关联ID',
				'type'  => 'number',
			],
			'contact_id'          => [
				'label' => '联系人ID',
				'type'  => 'number',
			],
			'follow_type'         => [
				'label'   => '跟进方式',
				'type'    => 'select',
				'options' => [
					1 => '电话',
					2 => '邮件',
					3 => '微信',
					4 => '面谈',
					5 => '其他'
				]
			],
			'follow_date'         => [
				'label' => '跟进日期',
				'type'  => 'date',
			],
			'content'             => [
				'label' => '跟进内容',
				'type'  => 'textarea',
			],
			'next_follow_date'    => [
				'label' => '下次跟进日期',
				'type'  => 'date',
			],
			'next_follow_content' => [
				'label' => '下次跟进计划',
				'type'  => 'textarea',
			]
		];
	}
}