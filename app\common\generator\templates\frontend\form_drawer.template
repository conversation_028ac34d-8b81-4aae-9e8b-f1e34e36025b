<script setup lang="ts">
import { ref, reactive, defineExpose, defineEmits } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import { {{EntityName}}Api } from '@/api/{{moduleName}}/{{entityName}}'
import { ApiStatus } from '@/utils/http/status'
import type { FormItemRule } from 'element-plus'
{{customImports}}

const emit = defineEmits(['success'])

// 抽屉状态
const drawerVisible = ref(false)
const drawerType = ref('add') // add或edit
const loading = ref(false)

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
{{defaultFormData}}
})

// 表单验证规则
const rules = {{formRules}}

// 显示抽屉
const showDrawer = async (type: string, id?: number) => {
  drawerType.value = type
  drawerVisible.value = true

  // 重置表单数据
  Object.assign(formData, {
{{defaultFormData}}
  })

  // 编辑模式下获取详情数据
  if (type === 'edit' && id) {
    try {
      loading.value = true
      const res = await {{EntityName}}Api.detail(id)
      if (res.code === ApiStatus.success) {
        Object.assign(formData, res.data)
      } else {
        ElMessage.error(res.message || '获取详情失败')
      }
    } finally {
      loading.value = false
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        let res

        if (drawerType.value === 'add') {
          res = await {{EntityName}}Api.add(formData)
        } else {
          res = await {{EntityName}}Api.update(formData)
        }

        if (res.code === ApiStatus.success) {
          ElMessage.success(drawerType.value === 'add' ? '添加成功' : '更新成功')
          drawerVisible.value = false
          emit('success')
        } else {
          ElMessage.error(res.message || '操作失败')
        }
      } finally {
        loading.value = false
      }
    }
  })
}

// 暴露方法给父组件
defineExpose({
  showDrawer
})
</script>

<template>
  <ElDrawer
    v-model="drawerVisible"
    :title="drawerType === 'add' ? '新增{{entityTitle}}' : '编辑{{entityTitle}}'"
    size="500px"
    destroy-on-close
  >
    <div class="drawer-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        {{formItems}}
      </ElForm>
    </div>
    
    <template #footer>
      <div class="drawer-footer">
        <ElButton @click="drawerVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitForm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
.drawer-content {
  padding: 20px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  border-top: 1px solid #e4e7ed;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.avatar-uploader .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  line-height: 100px;
}
</style>