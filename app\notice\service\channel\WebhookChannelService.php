<?php
declare(strict_types=1);

namespace app\notice\service\channel;

use think\facade\Log;

/**
 * 自定义Webhook通道服务
 * 注意：这是一个示例实现，将在后续开发中完善
 */
class WebhookChannelService extends BaseChannelService
{
    /**
     * 通道编码
     * @var string
     */
    protected string $channelCode = 'webhook';
    
    /**
     * 发送Webhook消息
     *
     * @param array $message 消息数据
     * @param array $recipients 接收人列表
     * @param array $options 选项参数
     * @return bool 是否发送成功
     */
    public function send(array $message, array $recipients, array $options = []): bool
    {
        // 这里只是一个基本实现，后续开发中会完善功能
        Log::info('自定义Webhook通道发送消息，后续开发中会完善功能');
        return true;
    }
    
    /**
     * 测试Webhook通道配置
     *
     * @param string $channel 通道编码
     * @param array $config 通道配置
     * @return array 测试结果
     */
    public function testChannelConfig(string $channel, array $config): array
    {
        // 这里只是一个基本实现，后续开发中会完善功能
        return [
            'success' => true,
            'message' => '自定义Webhook通道配置测试成功 (后续开发中会完善功能)'
        ];
    }
} 