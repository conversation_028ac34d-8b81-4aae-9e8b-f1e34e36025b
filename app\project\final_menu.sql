-- 项目任务管理模块菜单配置（最终版）
-- 与前端UI设计完全一致的菜单权限配置
-- 菜单结构：项目管理 -> 项目列表 + 任务管理

-- 获取当前最大菜单ID
SET @max_id = (SELECT IFNULL(MAX(id), 0) FROM system_menu);

-- ================================
-- 主菜单结构（2个菜单项）
-- ================================

-- 项目管理主菜单 (目录)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 1, 0, '项目管理', 'project', '/project', '', 0, 'el-icon-folder-opened', 300, 0, 0, 1, 1, '项目任务管理模块', NOW(), NOW());

-- 1. 项目列表 (主菜单)
-- 前端路径: /project/list -> ProjectList.vue
-- 包含功能: 项目卡片视图、项目详情页、项目成员管理
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 2, @max_id + 1, '项目列表', 'project:project:index', '/project/list', '/project/ProjectList', 1, 'el-icon-folder', 1, 0, 1, 1, 1, '项目列表管理（包含项目详情、成员管理）', NOW(), NOW());

-- 2. 任务管理 (主菜单)
-- 前端路径: /project/tasks -> TaskManagement.vue
-- 包含功能: 我的任务、全部任务、任务详情、任务评论
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 3, @max_id + 1, '任务管理', 'project:task:index', '/project/tasks', '/project/TaskManagement', 1, 'el-icon-tickets', 2, 0, 1, 1, 1, '任务列表管理（包含任务详情、评论功能）', NOW(), NOW());

-- ================================
-- 项目相关按钮权限（挂在项目列表下）
-- 对应后端API: /api/project/project/*
-- ================================

INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
-- 基础CRUD权限
(@max_id + 10, @max_id + 2, '查看项目', 'project:project:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看项目详情', NOW(), NOW()),
(@max_id + 11, @max_id + 2, '新增项目', 'project:project:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增项目', NOW(), NOW()),
(@max_id + 12, @max_id + 2, '编辑项目', 'project:project:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑项目', NOW(), NOW()),
(@max_id + 13, @max_id + 2, '删除项目', 'project:project:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除项目', NOW(), NOW()),
(@max_id + 14, @max_id + 2, '批量删除项目', 'project:project:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除项目', NOW(), NOW()),
(@max_id + 15, @max_id + 2, '更新项目字段', 'project:project:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新项目字段', NOW(), NOW()),
(@max_id + 16, @max_id + 2, '项目状态', 'project:project:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改项目状态', NOW(), NOW()),

-- 自定义业务权限
(@max_id + 17, @max_id + 2, '我的项目', 'project:project:myprojects', '', '', 2, '', 8, 0, 0, 1, 1, '我的项目列表', NOW(), NOW()),
(@max_id + 18, @max_id + 2, '项目详情', 'project:project:projectdetail', '', '', 2, '', 9, 0, 0, 1, 1, '项目详情信息', NOW(), NOW()),
(@max_id + 19, @max_id + 2, '项目看板', 'project:project:kanban', '', '', 2, '', 10, 0, 0, 1, 1, '项目看板数据', NOW(), NOW()),

-- 项目成员管理权限（集成在项目详情页中）
(@max_id + 20, @max_id + 2, '添加成员', 'project:project:addmember', '', '', 2, '', 11, 0, 0, 1, 1, '添加项目成员', NOW(), NOW()),
(@max_id + 21, @max_id + 2, '移除成员', 'project:project:removemember', '', '', 2, '', 12, 0, 0, 1, 1, '移除项目成员', NOW(), NOW()),
(@max_id + 22, @max_id + 2, '查看成员', 'project:member:detail', '', '', 2, '', 13, 0, 0, 1, 1, '查看成员详情', NOW(), NOW()),
(@max_id + 23, @max_id + 2, '成员管理', 'project:member:index', '', '', 2, '', 14, 0, 0, 1, 1, '项目成员管理', NOW(), NOW()),
(@max_id + 24, @max_id + 2, '编辑成员', 'project:member:edit', '', '', 2, '', 15, 0, 0, 1, 1, '编辑项目成员', NOW(), NOW()),
(@max_id + 25, @max_id + 2, '删除成员', 'project:member:delete', '', '', 2, '', 16, 0, 0, 1, 1, '删除项目成员', NOW(), NOW());

-- ================================
-- 任务相关按钮权限（挂在任务管理下）
-- 对应后端API: /api/project/task/*
-- ================================

INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
-- 基础CRUD权限
(@max_id + 30, @max_id + 3, '查看任务', 'project:task:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看任务详情', NOW(), NOW()),
(@max_id + 31, @max_id + 3, '新增任务', 'project:task:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增任务', NOW(), NOW()),
(@max_id + 32, @max_id + 3, '编辑任务', 'project:task:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑任务', NOW(), NOW()),
(@max_id + 33, @max_id + 3, '删除任务', 'project:task:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除任务', NOW(), NOW()),
(@max_id + 34, @max_id + 3, '批量删除任务', 'project:task:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除任务', NOW(), NOW()),
(@max_id + 35, @max_id + 3, '更新任务字段', 'project:task:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新任务字段', NOW(), NOW()),
(@max_id + 36, @max_id + 3, '任务状态', 'project:task:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改任务状态', NOW(), NOW()),

-- 自定义业务权限
(@max_id + 37, @max_id + 3, '我的任务', 'project:task:mytasks', '', '', 2, '', 8, 0, 0, 1, 1, '我的任务列表', NOW(), NOW()),
(@max_id + 38, @max_id + 3, '更新状态', 'project:task:updatestatus', '', '', 2, '', 9, 0, 0, 1, 1, '更新任务状态', NOW(), NOW()),
(@max_id + 39, @max_id + 3, '分配任务', 'project:task:assign', '', '', 2, '', 10, 0, 0, 1, 1, '分配任务', NOW(), NOW()),

-- 任务评论权限（集成在任务详情弹窗中）
(@max_id + 40, @max_id + 3, '添加评论', 'project:task:addcomment', '', '', 2, '', 11, 0, 0, 1, 1, '添加任务评论', NOW(), NOW()),
(@max_id + 41, @max_id + 3, '查看评论', 'project:taskcomment:detail', '', '', 2, '', 12, 0, 0, 1, 1, '查看评论详情', NOW(), NOW()),
(@max_id + 42, @max_id + 3, '评论管理', 'project:taskcomment:index', '', '', 2, '', 13, 0, 0, 1, 1, '任务评论管理', NOW(), NOW()),
(@max_id + 43, @max_id + 3, '编辑评论', 'project:taskcomment:edit', '', '', 2, '', 14, 0, 0, 1, 1, '编辑任务评论', NOW(), NOW()),
(@max_id + 44, @max_id + 3, '删除评论', 'project:taskcomment:delete', '', '', 2, '', 15, 0, 0, 1, 1, '删除任务评论', NOW(), NOW());

-- ================================
-- 菜单与前端UI设计对应关系说明
-- ================================

/*
前端页面结构：
1. 项目列表页 (/project/list -> ProjectList.vue)
   - 项目卡片视图 (我的项目 + 全部项目)
   - 项目详情页 (/project/detail/:id -> ProjectDetail.vue)
     - 项目看板视图 (TaskKanban.vue)
     - 项目成员管理 (ProjectMembers.vue)
     - 项目统计报表 (ProjectStatistics.vue)

2. 任务管理页 (/project/tasks -> TaskManagement.vue)
   - 我的任务列表
   - 全部任务列表
   - 任务详情弹窗 (TaskDetail.vue)
     - 任务评论功能 (TaskComments.vue)

权限控制：
- 所有按钮权限都通过权限中间件控制
- 前端根据用户权限显示/隐藏功能按钮
- 后端API根据权限标识进行访问控制

路由映射：
- 前端路由: /project/list -> 后端API: /api/project/project/*
- 前端路由: /project/tasks -> 后端API: /api/project/task/*
- 项目详情页: /project/detail/:id -> 后端API: /api/project/project/project-detail/:id
- 项目看板: 前端组件 -> 后端API: /api/project/project/kanban
*/
