<?php
declare(strict_types=1);

namespace app\system\controller;

use app\common\core\base\BaseController;
use app\common\service\TenantSwitchService;
use think\Response;

/**
 * 租户切换测试控制器
 * 用于测试租户切换功能是否正常工作
 */
class TenantSwitchTestController extends BaseController
{
    protected TenantSwitchService $tenantSwitchService;
    
    public function initialize()
    {
        parent::initialize();
        $this->tenantSwitchService = app(TenantSwitchService::class);
    }
    
    /**
     * 测试当前状态
     */
    public function testCurrentStatus(): Response
    {
        $data = [
            'admin_id' => request()->adminId ?? 0,
            'tenant_id' => request()->tenantId ?? 0,
            'is_super_admin' => is_super_admin(),
            'is_tenant_super_admin' => is_tenant_super_admin(),
            'current_mode' => $this->tenantSwitchService->getCurrentMode(),
            'switched_tenant_id' => $this->tenantSwitchService->getCurrentSwitchedTenantId(),
            'should_apply_tenant_isolation' => should_apply_tenant_isolation(),
            'effective_tenant_id' => get_effective_tenant_id(),
            'permission_context' => $this->tenantSwitchService->getPermissionContext(),
            'token_info' => $this->getTokenInfo()
        ];
        
        return $this->success('当前状态', $data);
    }
    
    /**
     * 测试切换到系统模式
     */
    public function testSwitchToSystemMode(): Response
    {
        if (!is_super_admin()) {
            return $this->error('只有系统超级管理员才能测试此功能');
        }
        
        try {
            $result = $this->tenantSwitchService->switchToSystemMode();
            
            $data = [
                'switch_result' => $result,
                'current_mode' => $this->tenantSwitchService->getCurrentMode(),
                'switched_tenant_id' => $this->tenantSwitchService->getCurrentSwitchedTenantId(),
                'should_apply_tenant_isolation' => should_apply_tenant_isolation(),
                'effective_tenant_id' => get_effective_tenant_id()
            ];
            
            return $this->success('切换到系统模式成功', $data);
        } catch (\Exception $e) {
            return $this->error('切换失败：' . $e->getMessage());
        }
    }
    
    /**
     * 测试切换到租户模式
     */
    public function testSwitchToTenantMode(): Response
    {
        if (!is_super_admin()) {
            return $this->error('只有系统超级管理员才能测试此功能');
        }
        
        $tenantId = (int)input('tenant_id', 0);
        if ($tenantId <= 0) {
            return $this->error('请提供有效的租户ID');
        }
        
        try {
            $result = $this->tenantSwitchService->switchToTenantMode($tenantId);
            
            $data = [
                'switch_result' => $result,
                'target_tenant_id' => $tenantId,
                'current_mode' => $this->tenantSwitchService->getCurrentMode(),
                'switched_tenant_id' => $this->tenantSwitchService->getCurrentSwitchedTenantId(),
                'should_apply_tenant_isolation' => should_apply_tenant_isolation(),
                'effective_tenant_id' => get_effective_tenant_id(),
                'tenant_info' => $this->tenantSwitchService->getCurrentTenantInfo()
            ];
            
            return $this->success('切换到租户模式成功', $data);
        } catch (\Exception $e) {
            return $this->error('切换失败：' . $e->getMessage());
        }
    }
    
    /**
     * 测试获取可用租户列表
     */
    public function testGetAvailableTenants(): Response
    {
        if (!is_super_admin()) {
            return $this->error('只有系统超级管理员才能测试此功能');
        }
        
        $tenants = $this->tenantSwitchService->getAvailableTenants();
        
        return $this->success('获取可用租户列表', [
            'tenants' => $tenants,
            'count' => count($tenants)
        ]);
    }
    
    /**
     * 测试缓存一致性
     */
    public function testCacheConsistency(): Response
    {
        $data = [
            'cache_vs_function' => [
                'cache_mode' => $this->tenantSwitchService->getCurrentMode(),
                'cache_tenant_id' => $this->tenantSwitchService->getCurrentSwitchedTenantId(),
                'function_should_apply_isolation' => should_apply_tenant_isolation(),
                'function_effective_tenant_id' => get_effective_tenant_id()
            ],
            'token_expire_info' => $this->getTokenExpireInfo(),
            'cache_keys' => $this->getCacheKeys()
        ];
        
        return $this->success('缓存一致性测试', $data);
    }
    
    /**
     * 获取 Token 信息
     */
    protected function getTokenInfo(): array
    {
        $token = request()->header('Authorization');
        if (!$token) {
            return ['error' => 'No token found'];
        }
        
        $tokenInfo = \app\common\utils\TokenUtil::getTokenInfo($token);
        if (!$tokenInfo) {
            return ['error' => 'Invalid token'];
        }
        
        return [
            'admin_id' => $tokenInfo['admin_id'] ?? 0,
            'create_time' => $tokenInfo['create_time'] ?? 0,
            'create_time_readable' => date('Y-m-d H:i:s', $tokenInfo['create_time'] ?? 0),
            'data_keys' => array_keys($tokenInfo['data'] ?? [])
        ];
    }
    
    /**
     * 获取 Token 过期信息
     */
    protected function getTokenExpireInfo(): array
    {
        $token = request()->header('Authorization');
        if (!$token) {
            return ['error' => 'No token found'];
        }
        
        $tokenInfo = \app\common\utils\TokenUtil::getTokenInfo($token);
        if (!$tokenInfo) {
            return ['error' => 'Invalid token'];
        }
        
        $createTime = $tokenInfo['create_time'] ?? time();
        $currentTime = time();
        $defaultExpire = 7200; // 2小时
        
        $elapsedTime = $currentTime - $createTime;
        $remainingTime = $defaultExpire - $elapsedTime;
        
        return [
            'create_time' => $createTime,
            'current_time' => $currentTime,
            'elapsed_seconds' => $elapsedTime,
            'remaining_seconds' => $remainingTime,
            'remaining_minutes' => round($remainingTime / 60, 2),
            'is_expired' => $remainingTime <= 0
        ];
    }
    
    /**
     * 获取缓存键信息
     */
    protected function getCacheKeys(): array
    {
        $adminId = request()->adminId ?? 0;
        
        return [
            'mode_key' => 'tenant_switch:mode:' . $adminId,
            'tenant_key' => 'tenant_switch:tenant:' . $adminId,
            'admin_id' => $adminId
        ];
    }
}
