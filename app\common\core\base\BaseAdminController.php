<?php
declare(strict_types=1);

namespace app\common\core\base;

use think\App;
use think\facade\Cache;
use think\Model;

/**
 * 管理控制器基类
 */
abstract class BaseAdminController extends BaseController
{
	/**
	 * 用户ID
	 *
	 * @var Model|null
	 */
	protected ?Model $model = null;
	
	/**
	 * 用户ID
	 *
	 * @var int
	 */
	protected int $adminId = 0;
	
	/**
	 * 用户信息
	 *
	 * @var array
	 */
	protected array $adminInfo = [];
	
	/**
	 * 租户ID
	 *
	 * @var int
	 */
	protected int $tenantId = 0;
	
	/**
	 * token
	 *
	 * @var string
	 */
	protected string $token = '';
	
//	protected bool   $is_super;
	
	/**
	 * 构造方法
	 *
	 * @param App $app 应用实例
	 */
	public function __construct(App $app)
	{
		parent::__construct($app);
	}
	
	/**
	 * 初始化
	 */
	protected function initialize()
	{
		parent::initialize();
		
		$token = $this->request->header('Authorization');
		if (!empty($token)) {
			$this->token    = $token;
			$adminInfoData  = Cache::get($token);
			$this->adminId   = $adminInfoData['admin_id'] ?? 0;
			$adminInfo      = $adminInfoData['data'] ?? [];
			$this->tenantId = $adminInfo['tenant_id'] ?? 0;
//			$this->is_super  = !empty($adminInfo['is_super_admin']);
			$this->adminInfo = $adminInfo;
		}
		
	}
} 