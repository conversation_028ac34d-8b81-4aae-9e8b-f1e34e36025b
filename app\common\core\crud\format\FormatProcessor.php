<?php
declare(strict_types=1);

namespace app\common\core\crud\format;

/**
 * 数据格式化处理器
 * 
 * 处理字段格式推断和数据格式转换
 */
class FormatProcessor
{
    /**
     * 格式化处理器映射
     */
    protected $formatters = [
        'datetime' => 'formatDatetime',
        'date' => 'formatDate',
        'money' => 'formatMoney',
        'status' => 'formatStatus',
        'image' => 'formatImage',
        'enum' => 'formatEnum',
        'number' => 'formatNumber',
        'decimal' => 'formatDecimal',
        'email' => 'formatEmail',
        'phone' => 'formatPhone',
        'url' => 'formatUrl',
        'html' => 'formatHtml',
    ];
    
    /**
     * 字段类型映射
     */
    protected $typeMap = [
        // 数值类型
        'int' => 'number',
        'tinyint' => 'number',
        'smallint' => 'number',
        'mediumint' => 'number',
        'bigint' => 'number',
        'float' => 'decimal',
        'double' => 'decimal',
        'decimal' => 'decimal',
        
        // 日期时间类型
        'date' => 'date',
        'datetime' => 'datetime',
        'timestamp' => 'datetime',
        'time' => 'time',
        'year' => 'number',
        
        // 字符串类型
        'char' => 'string',
        'varchar' => 'string',
        'tinytext' => 'string',
        'text' => 'string',
        'mediumtext' => 'string',
        'longtext' => 'string',
        
        // 二进制类型
        'blob' => 'string',
        'tinyblob' => 'string',
        'mediumblob' => 'string',
        'longblob' => 'string',
        
        // 其他类型
        'enum' => 'enum',
        'set' => 'string',
        'json' => 'json',
    ];
    
    /**
     * 根据字段配置推断格式
     * 
     * @param array $field 字段信息
     * @return string 格式类型
     */
    public function inferFormat(array $field): string
    {
        // 1. 检查是否有显式指定的格式化器
        if (!empty($field['formatter'])) {
            return $field['formatter'];
        }
        
        $comment = $field['comment'] ?? '';
        $name = $field['name'] ?? '';
        $type = $field['type'] ?? 'string';
        
        // 2. 从注释中解析格式
        $format = $this->inferFormatFromComment($comment);
        if ($format) {
            return $format;
        }
        
        // 3. 根据字段名称推断
        $format = $this->inferFormatFromName($name);
        if ($format) {
            return $format;
        }
        
        // 4. 根据字段类型推断
        return $this->inferFormatFromType($type);
    }
    
    /**
     * 从注释中推断格式
     * 
     * @param string $comment 字段注释
     * @return string|null 格式类型
     */
    protected function inferFormatFromComment(string $comment): ?string
    {
        if (empty($comment)) {
            return null;
        }
        
        // 检查是否有格式注解
        if (preg_match('/@fmt=(\w+)/', $comment, $matches) || 
            preg_match('/@formatter=(\w+)/', $comment, $matches)) {
            return $matches[1];
        }
        
        // 检查是否为枚举选项
        if (preg_match('/[\w\s]+:[\w\d]+=[\w\s]+,[\w\d]+=[\w\s]+/', $comment)) {
            return 'enum';
        }
        
        // 根据注释内容关键词推断
        $lowerComment = strtolower($comment);
        
        if (strpos($lowerComment, '图片') !== false || 
            strpos($lowerComment, '照片') !== false || 
            strpos($lowerComment, 'image') !== false || 
            strpos($lowerComment, 'photo') !== false) {
            return 'image';
        }
        
        if (strpos($lowerComment, '邮箱') !== false || 
            strpos($lowerComment, 'email') !== false) {
            return 'email';
        }
        
        if (strpos($lowerComment, '手机') !== false || 
            strpos($lowerComment, '电话') !== false || 
            strpos($lowerComment, 'phone') !== false || 
            strpos($lowerComment, 'mobile') !== false) {
            return 'phone';
        }
        
        if (strpos($lowerComment, '链接') !== false || 
            strpos($lowerComment, 'url') !== false || 
            strpos($lowerComment, 'link') !== false) {
            return 'url';
        }
        
        if (strpos($lowerComment, '富文本') !== false || 
            strpos($lowerComment, 'html') !== false || 
            strpos($lowerComment, 'editor') !== false) {
            return 'html';
        }
        
        if (strpos($lowerComment, '金额') !== false || 
            strpos($lowerComment, '价格') !== false || 
            strpos($lowerComment, 'money') !== false || 
            strpos($lowerComment, 'price') !== false) {
            return 'money';
        }
        
        if (strpos($lowerComment, '状态') !== false || 
            strpos($lowerComment, 'status') !== false || 
            strpos($lowerComment, 'state') !== false) {
            return 'status';
        }
        
        return null;
    }
    
    /**
     * 根据字段名称推断格式
     * 
     * @param string $name 字段名
     * @return string|null 格式类型
     */
    protected function inferFormatFromName(string $name): ?string
    {
        $lowerName = strtolower($name);
        
        // 时间日期相关
        if (strpos($lowerName, '_at') !== false || 
            strpos($lowerName, '_time') !== false || 
            in_array($lowerName, ['created_at', 'updated_at', 'deleted_at'])) {
            return 'datetime';
        }
        
        if (strpos($lowerName, '_date') !== false || 
            in_array($lowerName, ['date', 'birthday'])) {
            return 'date';
        }
        
        // 金额相关
        if (strpos($lowerName, 'amount') !== false || 
            strpos($lowerName, 'price') !== false || 
            strpos($lowerName, 'money') !== false || 
            strpos($lowerName, 'cost') !== false || 
            strpos($lowerName, 'fee') !== false) {
            return 'money';
        }
        
        // 联系方式相关
        if (strpos($lowerName, 'email') !== false) {
            return 'email';
        }
        
        if (strpos($lowerName, 'phone') !== false || 
            strpos($lowerName, 'mobile') !== false || 
            strpos($lowerName, 'tel') !== false) {
            return 'phone';
        }
        
        // 状态相关
        if (strpos($lowerName, 'status') !== false || 
            strpos($lowerName, 'state') !== false || 
            $lowerName === 'is_enabled' || 
            $lowerName === 'is_active') {
            return 'status';
        }
        
        // 图片相关
        if (strpos($lowerName, 'image') !== false || 
            strpos($lowerName, 'photo') !== false || 
            strpos($lowerName, 'avatar') !== false || 
            strpos($lowerName, 'picture') !== false || 
            strpos($lowerName, 'icon') !== false) {
            return 'image';
        }
        
        // 链接相关
        if (strpos($lowerName, 'url') !== false || 
            strpos($lowerName, 'link') !== false || 
            strpos($lowerName, 'website') !== false || 
            strpos($lowerName, 'site') !== false) {
            return 'url';
        }
        
        return null;
    }
    
    /**
     * 根据字段类型推断格式
     * 
     * @param string $type 字段类型
     * @return string 格式类型
     */
    protected function inferFormatFromType(string $type): string
    {
        // 移除可能的长度限制
        $baseType = preg_replace('/\(\d+(\,\d+)?\)/', '', $type);
        
        return $this->typeMap[$baseType] ?? 'string';
    }
    
    /**
     * 格式化数据用于导出
     * 
     * @param mixed $value 原始值
     * @param string $format 格式类型
     * @param array $options 格式化选项
     * @return mixed 格式化后的值
     */
    public function formatForExport($value, string $format, array $options = [])
    {
        if ($value === null || $value === '') {
            return '';
        }
        
        $method = $this->formatters[$format] ?? null;
        
        if ($method && method_exists($this, $method)) {
            return $this->{$method}($value, $options, 'export');
        }
        
        return $value;
    }
    
    /**
     * 解析导入数据
     * 
     * @param mixed $value 原始值
     * @param string $format 格式类型
     * @param array $options 格式化选项
     * @return mixed 解析后的值
     */
    public function parseForImport($value, string $format, array $options = [])
    {
        if ($value === null || $value === '') {
            return null;
        }
        
        $method = $this->formatters[$format] ?? null;
        
        if ($method && method_exists($this, $method)) {
            return $this->{$method}($value, $options, 'import');
        }
        
        return $value;
    }
    
    /**
     * 格式化日期时间
     */
    protected function formatDatetime($value, array $options = [], string $mode = 'export')
    {
        if ($mode === 'export') {
            return is_numeric($value) ? date('Y-m-d H:i:s', (int)$value) : $value;
        } else {
            return strtotime($value) ?: $value;
        }
    }
    
    /**
     * 格式化日期
     */
    protected function formatDate($value, array $options = [], string $mode = 'export')
    {
        if ($mode === 'export') {
            return is_numeric($value) ? date('Y-m-d', (int)$value) : $value;
        } else {
            return strtotime($value) ?: $value;
        }
    }
    
    /**
     * 格式化金额
     */
    protected function formatMoney($value, array $options = [], string $mode = 'export')
    {
        $decimals = $options['decimals'] ?? 2;
        $decPoint = $options['dec_point'] ?? '.';
        $thousandsSep = $options['thousands_sep'] ?? ',';
        
        if ($mode === 'export') {
            return number_format((float)$value, $decimals, $decPoint, $thousandsSep);
        } else {
            // 移除非数字字符
            return (float)preg_replace('/[^\d\.\-]/', '', $value);
        }
    }
    
    /**
     * 格式化状态
     */
    protected function formatStatus($value, array $options = [], string $mode = 'export')
    {
        $statusMap = $options['map'] ?? [
            1 => '启用',
            0 => '禁用'
        ];
        
        if ($mode === 'export') {
            return $statusMap[$value] ?? $value;
        } else {
            $reverseMap = array_flip($statusMap);
            return $reverseMap[$value] ?? $value;
        }
    }
    
    /**
     * 格式化图片
     */
    protected function formatImage($value, array $options = [], string $mode = 'export')
    {
        return $value;
    }
    
    /**
     * 格式化枚举
     */
    protected function formatEnum($value, array $options = [], string $mode = 'export')
    {
        $enumMap = $options['map'] ?? [];
        
        if ($mode === 'export') {
            return $enumMap[$value] ?? $value;
        } else {
            $reverseMap = array_flip($enumMap);
            return $reverseMap[$value] ?? $value;
        }
    }
    
    /**
     * 格式化整数
     */
    protected function formatNumber($value, array $options = [], string $mode = 'export')
    {
        if ($mode === 'import') {
            return intval($value);
        }
        return $value;
    }
    
    /**
     * 格式化小数
     */
    protected function formatDecimal($value, array $options = [], string $mode = 'export')
    {
        if ($mode === 'import') {
            return floatval($value);
        }
        return $value;
    }
    
    /**
     * 格式化邮箱
     */
    protected function formatEmail($value, array $options = [], string $mode = 'export')
    {
        return $value;
    }
    
    /**
     * 格式化电话
     */
    protected function formatPhone($value, array $options = [], string $mode = 'export')
    {
        return $value;
    }
    
    /**
     * 格式化URL
     */
    protected function formatUrl($value, array $options = [], string $mode = 'export')
    {
        return $value;
    }
    
    /**
     * 格式化HTML
     */
    protected function formatHtml($value, array $options = [], string $mode = 'export')
    {
        if ($mode === 'export') {
            return strip_tags($value);
        }
        return $value;
    }
} 