<template>
  <div class="procurement-form-view">
    <el-descriptions :column="2" border>
      <!-- 基本信息 -->
      <el-descriptions-item label="采购类型">
        {{ getProcurementTypeText(formData.procurement_type) }}
      </el-descriptions-item>

      <el-descriptions-item label="交付日期">
        {{ formData.delivery_date || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="物品名称">
        {{ formData.item_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="采购来源单位">
        {{ formData.supplier_name || '-' }}
      </el-descriptions-item>

      <!-- 金额信息 -->
      <el-descriptions-item label="单价(元)">
        {{ formatCurrency(formData.unit_price) }}
      </el-descriptions-item>

      <el-descriptions-item label="数量">
        {{ formData.quantity || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="付款金额" :span="2">
        <div>
          <div>{{ formatCurrency(formData.payment_amount) }}</div>
          <div style="color: #909399; font-size: 12px; margin-top: 4px">
            大写：{{ formData.payment_amount_words || '-' }}
          </div>
        </div>
      </el-descriptions-item>

      <!-- 收款信息 -->
      <el-descriptions-item label="收款人">
        {{ formData.payee_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="支付方式">
        {{ getPaymentMethodText(formData.payment_method) }}
      </el-descriptions-item>

      <el-descriptions-item label="开户行">
        {{ formData.bank_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="收款账号">
        {{ formData.bank_account || '-' }}
      </el-descriptions-item>

      <!-- 备注信息 -->
      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        <div style="white-space: pre-wrap">{{ formData.remark }}</div>
      </el-descriptions-item>

      <!-- 附件信息 -->
      <el-descriptions-item
        label="图片"
        :span="2"
        v-if="formData.attachment && formData.attachment.length > 0"
      >
        <div class="attachment-list">
          <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
            <el-image
              :src="file"
              style="width: 100px; height: 100px; object-fit: cover; border-radius: 4px"
              :preview-src-list="attachmentList"
              :initial-index="index"
              preview-teleported
              :hide-on-click-modal="true"
              fit="cover"
            />
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem, ElImage } from 'element-plus'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'office_procurement'
    }
  })

  // 采购类型映射
  const procurementTypeMap = {
    1: '办公用品',
    2: '设备采购',
    3: '服务采购',
    4: '其他'
  }

  // 支付方式映射
  const paymentMethodMap = {
    1: '银行转账',
    2: '现金支付',
    3: '支票',
    4: '支付宝',
    5: '微信',
    6: '其他'
  }

  /**
   * 获取采购类型文本
   */
  const getProcurementTypeText = (type: number) => {
    return procurementTypeMap[type] || '-'
  }

  /**
   * 获取支付方式文本
   */
  const getPaymentMethodText = (method: number) => {
    return paymentMethodMap[method] || '-'
  }

  /**
   * 格式化货币
   */
  const formatCurrency = (amount: number) => {
    if (amount === undefined || amount === null) return '-'
    return `¥${Number(amount).toFixed(2)}`
  }

  /**
   * 处理附件列表
   */
  const attachmentList = computed(() => {
    if (!props.formData.attachment) return []

    try {
      // 如果是字符串，尝试解析JSON
      if (typeof props.formData.attachment === 'string') {
        return JSON.parse(props.formData.attachment)
      }
      // 如果已经是数组，直接返回
      if (Array.isArray(props.formData.attachment)) {
        return props.formData.attachment
      }
    } catch (error) {
      console.error('解析附件数据失败:', error)
    }

    return []
  })
</script>

<style scoped lang="scss">
  .attachment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .attachment-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .el-icon {
      color: #909399;
    }

    span {
      font-size: 14px;
      color: #606266;
    }
  }
</style>
