# 第二阶段：业务逻辑开发任务清单

## 📋 阶段概述
**阶段目标**：基于已生成的CRUD代码，开发核心业务逻辑和工作流集成  
**预计时间**：2-3天  
**前置条件**：✅ 第一阶段已完成（数据表创建 + CRUD生成）

## 🎯 核心任务分解

### 任务2.1：工作流服务开发 ⭐⭐⭐
**负责人**：后端开发  
**预计时间**：1.5天  
**优先级**：高

#### 📝 具体任务
- [ ] **创建工作流服务文件**
  - 文件路径：`app/daily/service/DailyPriceOrderWorkflowService.php`
  - 继承：`WorkflowableService`
  - 业务代码：`daily_price_order`

- [ ] **实现核心方法**
  ```php
  ✅ getBusinessCode()           # 返回 'daily_price_order'
  ✅ getApprovalTitle()          # 审批标题格式化
  ✅ validateForApproval()       # 审批前验证逻辑
  ✅ afterApprovalComplete()     # 审批完成后处理
  ✅ validatePriceChanges()      # 价格变动验证
  ✅ handleOrderApproved()       # 处理审批通过
  ✅ handleOrderRejected()       # 处理审批拒绝
  ✅ handleOrderTerminated()     # 处理审批终止
  ✅ handleOrderVoided()         # 处理作废
  ✅ generatePriceHistory()      # 生成价格历史记录
  ```

- [ ] **业务验证规则**
  - 报价单必须包含产品明细（total_items > 0）
  - 当日不能重复提交已通过的报价单
  - 产品价格不能为负数
  - 自动生成标题：日期 + "报价"（如：2025-07-21报价）

- [ ] **审批结果处理**
  - **审批通过(2)**：生成历史记录 + 清除当日其他草稿
  - **审批拒绝(3)**：记录拒绝原因到备注
  - **审批终止(4)**：软删除所有明细
  - **审批作废(6)**：软删除报价单和明细

#### 🧪 测试要点
- [ ] 提交审批功能测试
- [ ] 各种审批结果的处理测试
- [ ] 业务验证规则测试
- [ ] 历史记录生成测试
- [ ] 事务回滚测试

---

### 任务2.2：业务服务扩展 ⭐⭐
**负责人**：后端开发  
**预计时间**：1天  
**优先级**：高

#### 📝 具体任务
- [ ] **扩展DailyPriceOrderService**
  - 文件路径：`app/daily/service/DailyPriceOrderService.php`
  - 基于生成器生成的基础服务进行扩展

- [ ] **实现核心业务方法**
  ```php
  ✅ createOrderWithDefaults()   # 创建报价单（设置默认值）
  ✅ saveItems()                # 批量保存明细
  ✅ copyFromYesterday()        # 从昨日复制
  ✅ getDateStatistics()        # 获取日期统计
  ```

- [ ] **报价单标题生成规则**
  - 格式：YYYY-MM-DD + "报价"
  - 示例：2025-07-21报价
  - 基于price_date自动生成
  - 无需用户输入

- [ ] **明细保存逻辑**
  - 验证报价单状态（只有草稿、已拒绝、已撤回可编辑）
  - 软删除原有明细
  - 批量插入新明细
  - 自动计算价格变动（price_change、change_rate）
  - 更新产品总数（total_items）
  - 事务处理确保数据一致性

- [ ] **从昨日复制功能**
  - 查找昨日已通过的报价单
  - 复制明细数据，价格作为old_price
  - 创建新的报价单
  - 标记为自动继承价格（is_manual_price = 0）

#### 🧪 测试要点
- [ ] 报价单标题自动生成测试
- [ ] 明细保存和价格计算测试
- [ ] 从昨日复制功能测试
- [ ] 状态权限控制测试
- [ ] 事务回滚测试

---

### 任务2.3：控制器扩展 ⭐
**负责人**：后端开发  
**预计时间**：0.5天  
**优先级**：中

#### 📝 具体任务
- [ ] **扩展DailyPriceOrderController**
  - 文件路径：`app/daily/controller/DailyPriceOrderController.php`
  - 基于生成器生成的基础控制器进行扩展

- [ ] **添加审批相关接口**
  ```php
  ✅ submitApproval()           # POST /daily-price-order/{id}/submit-approval
  ✅ withdrawApproval()         # POST /daily-price-order/{id}/withdraw-approval
  ✅ voidOrder()               # POST /daily-price-order/{id}/void
  ```

- [ ] **添加业务操作接口**
  ```php
  ✅ saveItems()               # POST /daily-price-order/{id}/items
  ✅ copyFromYesterday()       # POST /daily-price-order/copy-yesterday
  ✅ export()                  # GET /daily-price-order/{id}/export
  ✅ getStatistics()           # GET /daily-price-order/statistics
  ```

- [ ] **完善异常处理**
  - 统一的错误响应格式
  - 详细的错误日志记录
  - 友好的用户提示信息

#### 🧪 测试要点
- [ ] 所有新增接口的功能测试
- [ ] 参数验证测试
- [ ] 异常处理测试
- [ ] 权限控制测试

---

## 🔧 配置任务

### 任务2.4：路由配置
**负责人**：后端开发  
**预计时间**：0.2天

#### 📝 具体任务
- [ ] **创建路由文件**
  - 文件路径：`route/daily.php`
  - 配置模块路由

- [ ] **添加扩展路由**
  ```php
  Route::group('daily-price-order', function () {
      Route::post(':id/submit-approval', 'DailyPriceOrderController@submitApproval');
      Route::post(':id/withdraw-approval', 'DailyPriceOrderController@withdrawApproval');
      Route::post(':id/void', 'DailyPriceOrderController@voidOrder');
      Route::post(':id/items', 'DailyPriceOrderController@saveItems');
      Route::post('copy-yesterday', 'DailyPriceOrderController@copyFromYesterday');
      Route::get(':id/export', 'DailyPriceOrderController@export');
      Route::get('statistics', 'DailyPriceOrderController@getStatistics');
  });
  ```

### 任务2.5：工作流配置
**负责人**：后端开发  
**预计时间**：0.3天

#### 📝 具体任务
- [ ] **配置工作流类型**
  ```sql
  INSERT INTO workflow_type (code, name, description, status, created_at) 
  VALUES ('daily_price_order', '每日报价审批', '每日报价单审批流程', 1, NOW());
  ```

- [ ] **测试工作流基础功能**
  - 工作流实例创建
  - 审批节点流转
  - 状态更新机制

---

## 📊 质量保证

### 代码质量要求
- [ ] **代码规范**：遵循PSR-12编码规范
- [ ] **注释完整**：关键方法添加详细注释
- [ ] **异常处理**：完善的try-catch机制
- [ ] **日志记录**：关键操作记录日志
- [ ] **事务处理**：数据操作使用事务

### 测试覆盖要求
- [ ] **单元测试**：核心业务方法测试覆盖率 > 80%
- [ ] **接口测试**：所有新增接口功能测试
- [ ] **集成测试**：工作流集成测试
- [ ] **异常测试**：边界条件和异常情况测试

---

## 🚀 验收标准

### 功能验收
- [ ] ✅ 工作流服务正常运行，审批流程完整
- [ ] ✅ 报价单创建、编辑、保存功能正常
- [ ] ✅ 明细批量保存和价格计算准确
- [ ] ✅ 从昨日复制功能正常
- [ ] ✅ 所有审批状态处理正确
- [ ] ✅ 历史记录生成准确

### 技术验收
- [ ] ✅ 代码通过静态分析检查
- [ ] ✅ 单元测试通过率 > 80%
- [ ] ✅ 接口响应时间 < 500ms
- [ ] ✅ 数据库事务正确处理
- [ ] ✅ 异常情况处理完善

### 业务验收
- [ ] ✅ 审批流程符合业务需求
- [ ] ✅ 价格变动验证规则合理
- [ ] ✅ 权限控制准确
- [ ] ✅ 数据安全性保障

---

## 📝 开发注意事项

1. **继承原则**：基于生成器代码扩展，不要重写基础功能
2. **事务处理**：涉及多表操作的地方必须使用数据库事务
3. **异常处理**：提供友好的错误提示，记录详细的错误日志
4. **权限控制**：基于审批状态控制操作权限
5. **数据验证**：前后端双重验证，确保数据安全
6. **性能考虑**：合理使用索引，避免N+1查询问题

---

## 📞 协作方式

- **每日站会**：上午9:30，汇报进度和问题
- **代码审查**：每个任务完成后进行代码审查
- **测试协作**：开发完成后立即进行功能测试
- **问题反馈**：及时沟通技术难点和业务疑问

---

**任务开始时间**：第一阶段完成后  
**预期完成时间**：2-3个工作日  
**下一阶段**：前端页面开发
