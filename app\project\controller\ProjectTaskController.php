<?php
declare(strict_types=1);

namespace app\project\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\project\service\ProjectTaskRecordService;
use app\project\service\ProjectTaskService;
use think\response\Json;

/**
 * 任务表控制器
 */
class ProjectTaskController extends BaseController
{
	use CrudControllerTrait;
	
	/**
	 * @var ProjectTaskService
	 */
	protected ProjectTaskService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = ProjectTaskService::getInstance();
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		$result = $this->service->search($params, [], ['assignee']);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id], ['assignee']);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * 获取我的任务列表
	 */
	public function myTasks()
	{
		try {
			$userId = get_user_id();
			$params = $this->request->param();
			$result = $this->service->getMyTasks($userId, $params);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 更新任务状态
	 */
	public function updateStatus()
	{
		try {
			$id     = $this->request->param('id');
			$status = $this->request->param('status');
			$userId = get_user_id();
			
			$result = $this->service->updateTaskStatus($id, $status, $userId);
			return $this->success('状态更新成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 分配任务
	 */
	public function assign()
	{
		try {
			$id         = $this->request->param('id');
			$assigneeId = $this->request->param('assignee_id');
			$userId     = get_user_id();
			
			$result = $this->service->assignTask($id, $assigneeId, $userId);
			return $this->success('分配成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 添加任务评论
	 */
	public function addComment()
	{
		try {
			$taskId      = $this->request->param('task_id');
			$content     = $this->request->param('content');
			$attachments = $this->request->param('attachments', []);
			
			// 使用新的记录服务
			$recordService = ProjectTaskRecordService::getInstance();
			$result        = $recordService->addComment($taskId, $content, $attachments);
			
			return $this->success('评论添加成功', ['id' => $result]);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 添加任务跟进
	 */
	public function addFollow()
	{
		try {
			$data   = $this->request->post();
			$taskId = $data['task_id'] ?? $this->request->param('task_id');
			
			// 使用新的记录服务
			$recordService = ProjectTaskRecordService::getInstance();
			$result        = $recordService->addFollow($taskId, $data);
			
			return $this->success('跟进记录添加成功', ['id' => $result]);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取任务评论列表
	 */
	public function getComments(int $id): Json
	{
		try {
			$page  = $this->request->param('page', 1, 'int');
			$limit = $this->request->param('limit', 10, 'int');
			
			$recordService = ProjectTaskRecordService::getInstance();
			$result        = $recordService->getTaskComments($id, $page, $limit);
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取任务跟进列表
	 */
	public function getFollows(int $id): Json
	{
		try {
			$page  = $this->request->param('page', 1, 'int');
			$limit = $this->request->param('limit', 20, 'int');
			
			$recordService = ProjectTaskRecordService::getInstance();
			$result        = $recordService->getTaskFollows($id, $page, $limit);
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 编辑记录（评论或跟进）
	 */
	public function editRecord($recordId)
	{
		try {
			$recordService = ProjectTaskRecordService::getInstance();
			
			// 检查操作权限
			if (!$recordService->checkRecordPermission($recordId, 'edit')) {
				return $this->error('无权限编辑此记录');
			}
			
			$data = $this->request->post();
			unset($data['id'], $data['task_id'], $data['record_type']); // 不允许修改这些字段
			
			$result = $recordService->update($recordId, $data);
			return $this->success('记录更新成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 删除记录（评论或跟进）
	 */
	public function deleteRecord($recordId)
	{
		try {
			$recordService = ProjectTaskRecordService::getInstance();
			
			// 检查操作权限
			if (!$recordService->checkRecordPermission($recordId, 'delete')) {
				return $this->error('无权限删除此记录');
			}
			
			$result = $recordService->delete($recordId);
			return $this->success('记录删除成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
}