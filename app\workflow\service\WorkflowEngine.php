<?php
declare(strict_types=1);

namespace app\workflow\service;

use app\common\exception\BusinessException;
use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\factory\FormServiceFactory;
use app\workflow\model\WorkflowTask;
use app\workflow\model\WorkflowInstance;
use app\workflow\service\node\NodeHandlerFactory;
use app\workflow\service\WorkflowUrlService;
use app\workflow\service\WorkflowStatusSyncService;
use app\system\model\AdminModel;
use think\facade\Db;
use think\facade\Log;

// WorkflowOperationConstant已合并到WorkflowStatusConstant

/**
 * 工作流引擎核心
 */
class WorkflowEngine
{
	/**
	 * 工作流上下文
	 *
	 * @var WorkflowContext
	 */
	protected WorkflowContext $context;
	
	/**
	 * 启动工作流
	 *
	 * @param array $instance 工作流实例
	 * @return bool
	 */
	public function startWorkflow(array $instance): bool
	{
		Log::info('开始启动工作流: 实例ID=' . $instance['id'] . ', 流程定义ID=' . $instance['definition_id']);
		
		// 获取流程定义
		/*$definition = WorkflowDefinitionService::getInstance()
		                                       ->getModel()
		                                       ->where(['id' => $instance['definition_id']])
		                                       ->findOrEmpty();
		if ($definition->isEmpty()) {
			Log::error('流程定义不存在: ' . $instance['definition_id']);
			return false;
		}*/
		
		// 解析流程配置
		$flowConfig = $instance['process_data'];
		if (empty($flowConfig)) {
			Log::error('流程配置为空');
			return false;
		}
		
		// 验证流程定义
		$validator = new WorkflowValidator();
		$errors    = $validator->validateWorkflow($flowConfig);
		
		if (!empty($errors)) {
			Log::error('流程定义验证失败: ' . implode('; ', $errors));
			return false;
		}
		
		// 创建工作流上下文
		$this->context = new WorkflowContext($instance);
		$this->context->initialize($flowConfig);
		
		// 获取根节点
		$nodeConfig = $flowConfig['nodeConfig'] ?? null;
		if (empty($nodeConfig)) {
			Log::error('节点配置为空');
			return false;
		}
		
		// 更新当前节点
		WorkflowInstanceService::getInstance()
		                       ->edit(['current_node' => $nodeConfig['nodeId']], ['id' => $instance['id']]);
		
		// 处理发起人节点
		$handler = NodeHandlerFactory::create(WorkflowStatusConstant::NODE_TYPE_PROMOTER);
		if ($handler === null) {
			Log::error('未找到发起人节点处理器');
			return false;
		}
		
		return $handler->handle($instance, $nodeConfig, $this->context);
	}
	
	/**
	 * 处理审批结果
	 *
	 * @param array $instance   工作流实例
	 * @param array $task       当前任务
	 * @param bool  $isApproved 是否通过
	 * @return bool
	 */
	public function processApprovalResult(array $instance, array $task, bool $isApproved): bool
	{
		
		// 检查实例和任务是否匹配
		if ($task['instance_id'] != $instance['id']) {
			Log::error('任务与实例不匹配: 任务的实例ID=' . $task['instance_id'] . ', 实际实例ID=' . $instance['id']);
			return false;
		}
		
		// 检查任务节点是否为当前节点
		if ($task['node_id'] != $instance['current_node']) {
			Log::warning('任务节点与当前节点不匹配: 任务节点=' . $task['node_id'] . ', 当前节点=' . $instance['current_node']);
			// 可以根据业务需求决定是否继续，这里允许继续
		}
		
		// 如果驳回，则更新实例状态为已驳回
		if (!$isApproved) {
			return $this->rejectWorkflow($instance);
		}
		
		Log::info('开始获取流程定义信息, 定义ID: ' . $instance['definition_id']);
		
		// 获取流程定义
		/*$definition = WorkflowDefinitionService::getInstance()
		                                       ->getModel()
		                                       ->where(['id' => $instance['definition_id']])
		                                       ->findOrEmpty();
		if ($definition->isEmpty()) {
			Log::error('流程定义不存在: ' . $instance['definition_id']);
			return false;
		}
		
		// 解析流程配置
		$flowConfig = $definition->flow_config;
		if (empty($flowConfig)) {
			Log::error('流程配置为空');
			return false;
		}*/
		
		$flowConfig = $instance['process_data'] ?? [];
		if (empty($flowConfig)) {
			throw new BusinessException('流程配置为空');
		}
		Log::info('成功获取流程配置，创建工作流上下文');
		
		// 创建工作流上下文
		$this->context = new WorkflowContext($instance);
		$this->context->initialize($flowConfig);
		
		// 获取当前节点
		$currentNodeId = $task['node_id'] ?? '';
		
		Log::info('查找当前节点: ' . $currentNodeId);
		
		$currentNode = $this->context->getNodeFinder()
		                             ->findById($currentNodeId);
		
		if (empty($currentNode)) {
			Log::error('当前节点在流程配置中不存在: ' . $currentNodeId);
			return false;
		}
		
		// 更新当前审批任务状态为已完成
		Log::info('更新任务状态为已完成，任务ID: ' . $task['id']);
		$workflowTaskModel = new WorkflowTask();
		$taskInfo          = $workflowTaskModel->where('id', $task['id'])
		                                       ->findOrEmpty();
		if ($taskInfo->isEmpty()) {
			throw new \Exception('任务不存在');
		}
		$updateResult = $taskInfo->save(['status' => 1]);  // 1表示已完成状态
		
		if (!$updateResult) {
			throw new \Exception('任务状态更新失败');
		}
		
		// 获取节点配置
		$nodeConfig = json_decode($instance['node_config'] ?? '{}', true);
		//		$approvalMode = $nodeConfig['approval_mode'] ?? WorkflowStatusConstant::APPROVAL_MODE_ALL;
		$approvalMode = $nodeConfig['approvalMode'] ?? WorkflowStatusConstant::APPROVAL_MODE_ALL;
		
		
		Log::info('当前审批模式: ' . $approvalMode);
		
		// 根据不同的审批模式处理
		switch ($approvalMode) {
			case WorkflowStatusConstant::APPROVAL_MODE_ANY:
				// 任意一人通过即可，直接继续流程
				Log::info('任意一人通过模式：一名审批人已通过，继续流程');
				
				// 取消其他待处理任务
				$this->cancelOtherPendingTasks($instance['id'], $task['id'], $currentNodeId);
				
				// 继续流程
				return $this->continueWorkflow($instance, $currentNode);
			
			case WorkflowStatusConstant::APPROVAL_MODE_SEQUENCE:
				// 按顺序依次审批
				return $this->processSequentialApproval($instance, $task, $currentNode);
			
			case WorkflowStatusConstant::APPROVAL_MODE_ALL:
			default:
				// 所有人通过（默认模式）
				return $this->processAllApproval($instance, $task, $currentNode);
		}
	}
	
	/**
	 * 处理所有人通过模式的审批结果
	 *
	 * @param array $instance    工作流实例
	 * @param array $task        当前任务
	 * @param array $currentNode 当前节点
	 * @return bool
	 */
	private function processAllApproval(array $instance, array $task, array $currentNode): bool
	{
		$currentNodeId = $task['node_id'] ?? '';
		$currentTaskId = $task['id'] ?? 0;
		
		// 检查是否还有相同节点的未完成任务（排除当前已处理的任务）
		$pendingTasks = WorkflowTaskService::getInstance()
		                                   ->getModel()
		                                   ->where([
			                                   'instance_id' => $instance['id'],
			                                   'node_id'     => $currentNodeId,
			                                   'status'      => 0,
			                                   // 0表示待处理状态
			                                   'id'          => [
				                                   '<>',
				                                   $currentTaskId
			                                   ]
			                                   // 排除当前已处理的任务
		                                   ])
		                                   ->select()
		                                   ->toArray();
		
		Log::info('同节点待处理任务数: ' . count($pendingTasks) . '（已排除当前任务ID: ' . $currentTaskId . '）');
		
		// 如果还有未完成的任务，不继续流转
		if (!empty($pendingTasks)) {
			Log::info('当前节点还有未处理的任务，暂不流转到下一节点');
			foreach ($pendingTasks as $pendingTask) {
				Log::info('  - 待处理任务ID: ' . $pendingTask['id'] . ', 审批人: ' . $pendingTask['approver_name']);
			}
			return true;
		}
		
		// 所有任务都完成，继续流程
		Log::info('当前节点所有任务都已完成，继续流程');
		return $this->continueWorkflow($instance, $currentNode, $currentTaskId);
	}
	
	/**
	 * 处理按顺序依次审批模式的审批结果
	 *
	 * @param array $instance    工作流实例
	 * @param array $task        当前任务
	 * @param array $currentNode 当前节点
	 * @return bool
	 */
	private function processSequentialApproval(array $instance, array $task, array $currentNode): bool
	{
		// 获取节点配置
		$nodeConfig         = json_decode($instance['node_config'] ?? '{}', true);
		$remainingApprovers = $nodeConfig['remaining_approvers'] ?? [];
		
		Log::info('顺序审批模式：剩余审批人数: ' . count($remainingApprovers));
		
		// 如果还有剩余审批人，创建下一个审批任务
		if (!empty($remainingApprovers)) {
			$nextApprover       = $remainingApprovers[0];
			$remainingApprovers = array_slice($remainingApprovers, 1);
			
			Log::info('顺序审批模式：创建下一个审批人任务: ' . ($nextApprover['name'] ?? '未知'));
			
			// 更新剩余审批人列表
			WorkflowInstanceService::getInstance()
			                       ->edit([
				                       'node_config' => json_encode([
					                       'approval_mode'       => WorkflowStatusConstant::APPROVAL_MODE_SEQUENCE,
					                       'remaining_approvers' => $remainingApprovers
				                       ])
			                       ], ['id' => $instance['id']]);
			
			// ✅ 优化：直接创建任务模型，避免单例状态污染
			$result = $this->createTaskDirectly($instance, $currentNode, $nextApprover);
			
			if (!$result) {
				Log::error('创建审批任务失败: 用户ID=' . ($nextApprover['id'] ?? '未知'));
				return false;
			}
			
			Log::info('成功创建审批任务: 用户ID=' . ($nextApprover['id'] ?? '未知'));
			
			// 发送审批通知
			$this->sendApprovalNotification($instance, $currentNode, $nextApprover);
			
			return true;
		}
		
		// 没有剩余审批人，继续流程
		Log::info('顺序审批模式：所有审批人都已处理，继续流程');
		return $this->continueWorkflow($instance, $currentNode, $task['id'] ?? 0);
	}
	
	/**
	 * 继续工作流处理
	 *
	 * @param array $instance      工作流实例
	 * @param array $currentNode   当前节点
	 * @param int   $currentTaskId 当前任务ID（用于排除检查）
	 * @return bool
	 */
	private function continueWorkflow(array $instance, array $currentNode, int $currentTaskId = 0): bool
	{
		// 找到下一个节点
		$nextNode = $currentNode['childNode'] ?? null;
		
		// 如果当前节点没有子节点，需要检查是否在条件分支中，如果是则需要处理条件分支的主流程
		if (empty($nextNode)) {
			Log::info('当前节点没有子节点，检查是否在条件分支中');
			
			// 查找当前节点所在的条件分支节点
			$branchNode = $this->context->getNodeFinder()
			                            ->findParentBranchNode($currentNode['nodeId']);
			
			if ($branchNode && !empty($branchNode['childNode'])) {
				Log::info('当前节点在条件分支中，处理条件分支的主流程节点');
				
				// 更新当前节点为条件分支的子节点（主流程节点）
				WorkflowInstanceService::getInstance()
				                       ->edit(['current_node' => $branchNode['childNode']['nodeId']], ['id' => $instance['id']]);
				
				// 处理条件分支的主流程节点
				$branchHandler = NodeHandlerFactory::create($branchNode['childNode']['type'] ?? 0);
				if ($branchHandler === null) {
					Log::error('未找到条件分支主流程节点处理器: ' . ($branchNode['childNode']['type'] ?? '未知'));
					return false;
				}
				
				Log::info('开始处理条件分支主流程节点');
				return $branchHandler->handle($instance, $branchNode['childNode'], $this->context);
			}
			
			// 如果不在条件分支中或条件分支没有主流程节点，则完成工作流
			Log::info('当前节点没有子节点且不在条件分支中或条件分支没有主流程节点，流程将结束');
			return $this->completeWorkflow($instance, $currentTaskId);
		}
		
		Log::info('找到下一节点: ' . ($nextNode['nodeName'] ?? '未知') . ', 类型: ' . ($nextNode['type'] ?? '未知'));
		
		// 更新当前节点为下一节点（流转前先更新）
		WorkflowInstanceService::getInstance()
		                       ->edit(['current_node' => $nextNode['nodeId']], ['id' => $instance['id']]);
		
		// 处理下一个节点
		$handler = NodeHandlerFactory::create($nextNode['type'] ?? 0);
		if ($handler === null) {
			Log::error('未找到节点类型的处理器: ' . ($nextNode['type'] ?? '未知'));
			return false;
		}
		
		Log::info('开始处理下一节点');
		return $handler->handle($instance, $nextNode, $this->context);
	}
	
	/**
	 * 取消其他待处理任务
	 *
	 * @param int    $instanceId      实例ID
	 * @param int    $completedTaskId 已完成任务ID
	 * @param string $nodeId          节点ID
	 * @return bool
	 */
	private function cancelOtherPendingTasks(int $instanceId, int $completedTaskId, string $nodeId): bool
	{
		try {
			// 查找同一节点的其他待处理任务
			$pendingTasks = WorkflowTaskService::getInstance()
			                                   ->getModel()
			                                   ->where([
				                                   'instance_id' => $instanceId,
				                                   'node_id'     => $nodeId,
				                                   'status'      => 0,
				                                   // 待处理状态
				                                   'id'          => [
					                                   '<>',
					                                   $completedTaskId
				                                   ]
				                                   // 排除已完成的任务
			                                   ])
			                                   ->select();
			
			if ($pendingTasks->isEmpty()) {
				Log::info('没有需要取消的其他待处理任务');
				return true;
			}
			
			Log::info('取消其他待处理任务数: ' . count($pendingTasks));
			
			// 更新任务状态为已取消
			foreach ($pendingTasks as $task) {
				WorkflowTaskService::getInstance()
				                   ->edit([
					                   'status'      => 9,
					                   // 9表示已取消状态
					                   'handle_time' => date('Y-m-d H:i:s'),
					                   'opinion'     => '任意一人通过模式：其他审批人已通过，自动取消'
				                   ], ['id' => $task['id']]);
				
				Log::info('已取消任务: ID=' . $task['id'] . ', 审批人ID=' . $task['approver_id']);
			}
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('取消其他待处理任务异常: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 发送审批通知
	 *
	 * @param array $instance 工作流实例
	 * @param array $node     节点数据
	 * @param array $user     审批人
	 * @return bool
	 */
	private function sendApprovalNotification(array $instance, array $node, array $user): bool
	{
		try {
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			            // 准备变量 - 需要同时支持中英文键名以兼容不同的模板格式
            $variables = [
                // 英文键名（用于代码逻辑）
                'task_name'      => $node['nodeName'] ?? '审批任务',
                'title'          => $instance['title'],
                'submitter_name' => $instance['submitter_name'],
                'created_at'     => $instance['created_at'],
                'detail_url'     => '/workflow/task/detail?instance_id=' . $instance['id'],
                
                // 中文键名（用于模板渲染）
                '任务名称'        => $node['nodeName'] ?? '审批任务',
                '流程标题'        => $instance['title'],
                '提交人'          => $instance['submitter_name'],
                '提交时间'        => $instance['created_at'],
                '紧急程度'        => '普通', // 数据库模板中需要但当前未实现的字段
            ];
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('task.approval', [
				'task_id' => $task['id'] ?? $instance['id']
			]);
			
			// 发送审批任务通知
			$noticeService->send(WorkflowStatusConstant::MODULE_NAME, WorkflowStatusConstant::MESSAGE_TASK_APPROVAL, $variables, [$user['id']], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url']
			]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送审批通知失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 驳回工作流
	 *
	 * @param array $instance 工作流实例
	 * @return bool
	 */
	public function rejectWorkflow(array $instance): bool
	{
		try {
			// 使用统一状态同步服务
			$syncService = new WorkflowStatusSyncService();
			$result      = $syncService->syncAllWorkflowStatus($instance['id'], WorkflowStatusConstant::STATUS_REJECTED, '流程已被拒绝', request()->adminId ?? 0);
			
			if (!$result) {
				throw new \Exception('状态同步失败');
			}
			
			// 记录流程历史（流程级别的结束记录）
			$historyData = [
				'instance_id'    => $instance['id'],
				'process_id'     => $instance['process_id'],
				'task_id'        => '',
				'node_id'        => $instance['current_node'],
				'node_name'      => '流程结束',
				'node_type'      => 'end',
				'prev_node_id'   => $instance['current_node'],
				'operator_id'    => request()->adminId ?? 0,
				'operation'      => WorkflowStatusConstant::OPERATION_PROCESS_END,
				'operation_time' => date('Y-m-d H:i:s'),
				'opinion'        => '因驳回而结束流程',
				'tenant_id'      => $instance['tenant_id'] ?? 0
			];
			
			// 使用safeAddHistory替代直接add
			WorkflowHistoryService::getInstance()
			                      ->safeAddHistory($historyData);
			
			// 发送驳回通知给申请人
			$this->sendResultNotification($instance, false);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('驳回工作流失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 完成工作流
	 *
	 * @param array $instance      工作流实例
	 * @param int   $excludeTaskId 要排除的任务ID（用于避免检查当前正在处理的任务）
	 * @return bool
	 */
	public function completeWorkflow(array $instance, int $excludeTaskId = 0): bool
	{
		// 先检查是否有未完成的审批任务（排除指定的任务）
		$query = (new WorkflowTask())::where([
			'instance_id' => $instance['id'],
			'task_type'   => WorkflowStatusConstant::TASK_TYPE_APPROVAL,
			'status'      => 0
			// 待处理状态
		]);
		
		// 如果指定了要排除的任务ID，则排除该任务
		if ($excludeTaskId > 0) {
			$query->where('id', '<>', $excludeTaskId);
			Log::info('完成工作流检查时排除任务ID: ' . $excludeTaskId);
		}
		
		$pendingApprovalTasks = $query->select()
		                              ->toArray();
		
		if (!empty($pendingApprovalTasks)) {
			Log::warning('工作流存在未完成的审批任务，不能自动完成，实例ID: ' . $instance['id'] . '，待处理任务数: ' . count($pendingApprovalTasks));
			foreach ($pendingApprovalTasks as $pendingTask) {
				Log::warning('  - 待处理任务ID: ' . $pendingTask['id'] . ', 审批人: ' . $pendingTask['approver_name'] . ', 节点: ' . $pendingTask['node_name']);
			}
			return false;
		}
		
		Db::startTrans();
		try {
			Log::info('完成工作流，实例ID: ' . $instance['id']);
			
			// 更新实例状态为已通过
			WorkflowInstanceService::getInstance()
			                       ->edit([
				                       'status'       => WorkflowStatusConstant::APPROVED,
				                       'current_node' => null,
				                       // 清空当前节点，表示流程已结束
				                       'end_time'     => date('Y-m-d H:i:s')
			                       ], ['id' => $instance['id']]);
			
			// 使用BusinessWorkflowService同步状态
			$businessWorkflowService = new BusinessWorkflowService();
			$businessWorkflowService->syncBusinessStatus($instance['business_code'], $instance['business_id'], [
				'approval_status' => WorkflowStatusConstant::APPROVED
			]);
			$businessWorkflowService->syncWorkflowStatus($instance['id'], WorkflowStatusConstant::APPROVED);
			
			// 记录流程历史
			$historyData = [
				'instance_id'    => $instance['id'],
				'process_id'     => $instance['process_id'],
				'task_id'        => '',
				'node_id'        => $instance['current_node'] ?? '',
				'node_name'      => '流程结束',
				'node_type'      => 'end',
				'prev_node_id'   => $instance['current_node'] ?? '',
				'operator_id'    => request()->adminId ?? $instance['submitter_id'] ?? 0,
				'operation'      => WorkflowStatusConstant::OPERATION_PROCESS_END,
				'operation_time' => date('Y-m-d H:i:s'),
				'tenant_id'      => $instance['tenant_id'] ?? 0
			];
			
			// 使用safeAddHistory替代直接add
			WorkflowHistoryService::getInstance()
			                      ->safeAddHistory($historyData);
			
			Db::commit();
			Log::info('工作流完成处理成功');
			
			// 发送审批通过通知给申请人（移到事务外，避免事务冲突）
			try {
				$this->sendResultNotification($instance, true);
			}
			catch (\Exception $e) {
				Log::warning('发送工作流结果通知失败，但不影响主流程: ' . $e->getMessage());
			}
			
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('完成工作流失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 终止工作流
	 *
	 * @param array  $instance   工作流实例
	 * @param string $reason     终止原因
	 * @param int    $operatorId 操作人ID
	 * @return bool
	 */
	public function terminateWorkflow(array $instance, string $reason, int $operatorId = 0): bool
	{
		Db::startTrans();
		try {
			// 更新实例状态为已终止
			WorkflowInstanceService::getInstance()
			                       ->edit(['status' => WorkflowStatusConstant::TERMINATED], ['id' => $instance['id']]);
			
			// 使用BusinessWorkflowService同步状态
			$businessWorkflowService = new BusinessWorkflowService();
			$businessWorkflowService->syncWorkflowStatus($instance['id'], WorkflowStatusConstant::TERMINATED);
			
			// 取消所有待处理任务
			WorkflowTaskService::getInstance()
			                   ->getModel()
			                   ->where([
				                   'instance_id' => $instance['id'],
				                   'status'      => 0
				                   // 待处理
			                   ])
			                   ->update([
				                   'status'      => WorkflowStatusConstant::TERMINATED,
				                   'handle_time' => date('Y-m-d H:i:s'),
				                   'opinion'     => '流程已被终止: ' . $reason
			                   ]);
			
			// 记录流程历史
			$historyData = [
				'instance_id'    => $instance['id'],
				'process_id'     => $instance['process_id'],
				'task_id'        => '',
				'node_id'        => $instance['current_node'],
				'node_name'      => '流程终止',
				'node_type'      => 'terminate',
				'prev_node_id'   => $instance['current_node'],
				'operator_id'    => $operatorId
					?: (request()->adminId ?? $instance['submitter_id']),
				'operation'      => WorkflowStatusConstant::OPERATION_TERMINATE,
				'opinion'        => $reason,
				'operation_time' => date('Y-m-d H:i:s')
			];
			WorkflowHistoryService::getInstance()
			                      ->getCrudService()
			                      ->add($historyData);
			
			// 发送流程终止通知
			$this->sendTerminationNotification($instance, $reason, $operatorId);
			
			Db::commit();
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('终止工作流失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 发送工作流结果通知
	 *
	 * @param array $instance   工作流实例
	 * @param bool  $isApproved 是否通过
	 * @return bool
	 */
	protected function sendResultNotification(array $instance, bool $isApproved): bool
	{
		try {
			// 获取申请人
			$submitterId = $instance['submitter_id'] ?? 0;
			if (empty($submitterId)) {
				Log::error('申请人ID为空，无法发送通知');
				return false;
			}
			
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 需要同时支持中英文键名以兼容不同的模板格式
			$variables = [
				// 英文键名（用于代码逻辑）
				'title'         => $instance['title'],
				'result'        => $isApproved ? '已通过' : '已拒绝',
				'submit_time'   => $instance['created_at'],
				'finish_time'   => date('Y-m-d H:i:s'),
				'detail_url'    => '/workflow/detail?id=' . $instance['id'],
				'approver_name' => request()->adminInfo['data']['real_name'] ?? '系统',
				'completed_at'  => date('Y-m-d H:i:s'),
				'opinion'       => '',
				
				// 中文键名（用于模板渲染）
				'流程标题'       => $instance['title'],
				'审批结果'       => $isApproved ? '已通过' : '已拒绝',
				'提交时间'       => $instance['created_at'],
				'完成时间'       => date('Y-m-d H:i:s'),
				'审批人'         => request()->adminInfo['data']['real_name'] ?? '系统',
				'审批时间'       => date('Y-m-d H:i:s'),
				'审批意见'       => '',
			];
			
			Log::info('发送工作流结果通知, 通知变量: ' . json_encode($variables));
			
			// 发送工作流结果通知
			$noticeService->send(WorkflowStatusConstant::MODULE_NAME, WorkflowStatusConstant::MESSAGE_TASK_APPROVED, $variables, [$submitterId]);
			
			Log::info('工作流结果通知发送成功');
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送工作流结果通知失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
			return false;
		}
	}
	
	/**
	 * 发送工作流终止通知
	 *
	 * @param array  $instance   工作流实例
	 * @param string $reason     终止原因
	 * @param int    $operatorId 操作人ID
	 * @return bool
	 */
	protected function sendTerminationNotification(array $instance, string $reason, int $operatorId): bool
	{
		try {
			// 获取申请人
			$submitterId = $instance['submitter_id'] ?? 0;
			if (empty($submitterId)) {
				Log::error('申请人ID为空，无法发送通知');
				return false;
			}
			
			// 获取操作人信息
			$operatorName = '系统管理员';
			if ($operatorId > 0) {
				$admin = AdminModel::where('id', $operatorId)
				                   ->find();
				if ($admin) {
					$operatorName = $admin['realname'] ?? $operatorName;
				}
			}
			
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$variables = [
				'title'          => $instance['title'],
				'result'         => '已终止',
				'submit_time'    => $instance['created_at'],
				'terminate_time' => date('Y-m-d H:i:s'),
				'terminate_by'   => $operatorName,
				'reason'         => $reason,
				'detail_url'     => '/workflow/detail?id=' . $instance['id']
			];
			
			Log::info('发送工作流终止通知, 通知变量: ' . json_encode($variables));
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('instance.detail', [
				'instance_id' => $instance['id']
			]);
			
			// 发送工作流终止通知
			$noticeService->send(WorkflowStatusConstant::MODULE_NAME, WorkflowStatusConstant::MESSAGE_TASK_TERMINATED, $variables, [$submitterId], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url']
			]);
			
			Log::info('工作流终止通知发送成功');
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送工作流终止通知失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
			return false;
		}
	}
	
	/**
	 * 作废工作流实例（废弃方法，请使用voidApprovedInstance）
	 *
	 * @param int    $instanceId 实例ID
	 * @param string $reason     作废原因
	 * @param int    $operatorId 操作人ID
	 * @return bool
	 * @deprecated 此方法已废弃，请使用voidApprovedInstance方法
	 */
	public function voidInstance(int $instanceId, string $reason = '', int $operatorId = 0): bool
	{
		Log::warning('voidInstance方法已废弃，请使用voidApprovedInstance方法');
		return $this->voidApprovedInstance($instanceId, $reason, $operatorId);
	}
	
	/**
	 * 作废已通过的工作流实例（供其他模块调用）
	 *
	 * @param int    $instanceId 实例ID
	 * @param string $reason     作废原因
	 * @param int    $operatorId 操作人ID
	 * @return bool
	 */
	public function voidApprovedInstance(int $instanceId, string $reason = '', int $operatorId = 0): bool
	{
		Db::startTrans();
		try {
			// 获取实例信息
			$instance = WorkflowInstance::where('id', $instanceId)
			                            ->find();
			if (!$instance) {
				Log::error('工作流实例不存在: ' . $instanceId);
				return false;
			}
			
			// 检查实例状态 - 必须是已通过状态
			if ($instance['status'] != WorkflowStatusConstant::APPROVED) {
				Log::error('只能作废已通过状态的工作流实例，当前状态：' . $instance['status']);
				return false;
			}
			
			// 获取操作人信息
			$operatorName = '系统';
			if ($operatorId > 0) {
				$operator = AdminModel::where('id', $operatorId)
				                      ->find();
				if ($operator) {
					$operatorName = $operator['real_name'] ?? $operator['username'] ?? '';
				}
			}
			
			// 更新实例状态为作废
			$instance->status   = WorkflowStatusConstant::VOID;
			$instance->end_time = date('Y-m-d H:i:s');
			$instance->save();
			
			// 使用BusinessWorkflowService同步状态
			$businessWorkflowService = new BusinessWorkflowService();
			$businessWorkflowService->syncWorkflowStatus($instance['id'], WorkflowStatusConstant::VOID);
			
			// 记录作废操作历史
			$historyData = [
				'instance_id'    => $instance['id'],
				'process_id'     => $instance['process_id'],
				'task_id'        => '',
				'node_id'        => '',
				'node_name'      => '流程作废',
				'node_type'      => 'void',
				'prev_node_id'   => '',
				'operator_id'    => $operatorId
					?: (request()->adminId ?? $instance['submitter_id']),
				'operation'      => WorkflowStatusConstant::OPERATION_TERMINATE,
				// 复用终止操作类型
				'opinion'        => $reason
					?: '流程已作废',
				'operation_time' => date('Y-m-d H:i:s')
			];
			
			WorkflowHistoryService::getInstance()
			                      ->getCrudService()
			                      ->add($historyData);
			
			Log::info('工作流实例作废成功: ' . $instanceId . ', 操作人: ' . $operatorName);
			
			// 发送作废通知
			$this->sendVoidNotification($instance->toArray(), $reason
				?: '流程已作废', $operatorName);
			
			Db::commit();
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('作废工作流实例失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
			return false;
		}
	}
	
	/**
	 * 发送作废通知
	 *
	 * @param array  $instance     实例信息
	 * @param string $reason       作废原因
	 * @param string $operatorName 操作人姓名
	 * @return bool
	 */
	private function sendVoidNotification(array $instance, string $reason, string $operatorName): bool
	{
		try {
			// 获取提交人ID
			$submitterId = $instance['submitter_id'] ?? 0;
			if ($submitterId <= 0) {
				Log::warning('工作流实例提交人ID无效，跳过作废通知');
				return true;
			}
			
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$variables = [
				'title'       => $instance['title'],
				'result'      => '已作废',
				'submit_time' => $instance['created_at'],
				'void_time'   => date('Y-m-d H:i:s'),
				'void_by'     => $operatorName,
				'reason'      => $reason,
				'detail_url'  => '/workflow/detail?id=' . $instance['id']
			];
			
			Log::info('发送工作流作废通知, 通知变量: ' . json_encode($variables));
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('instance.detail', [
				'instance_id' => $instance['id']
			]);
			
			// 发送工作流作废通知
			$noticeService->send(WorkflowStatusConstant::MODULE_NAME, 'task_void', $variables, [$submitterId], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url'],
				'sender_name' => $operatorName
			]);
			
			Log::info('工作流作废通知发送成功');
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送工作流作废通知失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
			return false;
		}
	}
	
	/**
	 * 直接创建任务，避免单例状态污染
	 *
	 * @param array $instance 工作流实例
	 * @param array $node     节点数据
	 * @param array $user     审批人
	 * @return bool
	 */
	private function createTaskDirectly(array $instance, array $node, array $user): bool
	{
		try {
			$userId = isset($user['id'])
				? intval($user['id'])
				: 0;
			if ($userId <= 0) {
				Log::error('无效的用户ID: ' . json_encode($user));
				return false;
			}
			
			// 检查是否已存在相同的任务
			$existingTask = \app\workflow\model\WorkflowTask::where([
				'instance_id' => $instance['id'],
				'node_id'     => $node['nodeId'],
				'approver_id' => $userId,
				'status'      => 0,
				'deleted_at'  => null
			])
			                                                ->findOrEmpty();
			
			if (!$existingTask->isEmpty()) {
				Log::info('已存在相同的待处理任务，跳过创建: 实例ID=' . $instance['id'] . ', 节点ID=' . $node['nodeId'] . ', 用户ID=' . $userId);
				return true;
			}
			
			// 创建新任务
			$taskId   = md5($instance['id'] . $node['nodeId'] . $userId . microtime(true));
			$taskData = [
				'task_id'       => $taskId,
				'instance_id'   => $instance['id'],
				'process_id'    => $instance['process_id'],
				'node_id'       => $node['nodeId'],
				'node_name'     => $node['nodeName'] ?? '审批节点',
				'node_type'     => isset($node['type']) && $node['type'] == WorkflowStatusConstant::NODE_TYPE_COPYER
					? WorkflowStatusConstant::TASK_TYPE_CC
					: WorkflowStatusConstant::TASK_TYPE_APPROVAL,
				'task_type'     => isset($node['type']) && $node['type'] == WorkflowStatusConstant::NODE_TYPE_COPYER
					? WorkflowStatusConstant::TASK_TYPE_CC
					: WorkflowStatusConstant::TASK_TYPE_APPROVAL,
				'approver_id'   => $userId,
				'approver_name' => $user['real_name'] ?? $user['username'] ?? '',
				'status'        => 0,
				'tenant_id'     => $instance['tenant_id'] ?? 0
			];
			
			// 直接实例化模型创建任务
			$taskModel = new \app\workflow\model\WorkflowTask();
			$result    = $taskModel->saveByCreate($taskData);
			
			if ($result) {
				Log::info('成功创建审批任务: 实例ID=' . $instance['id'] . ', 节点ID=' . $node['nodeId'] . ', 用户ID=' . $userId);
				return true;
			}
			else {
				Log::error('创建审批任务失败: ' . json_encode($taskData));
				return false;
			}
			
		}
		catch (\Exception $e) {
			Log::error('创建审批任务异常: ' . $e->getMessage());
			return false;
		}
	}
} 