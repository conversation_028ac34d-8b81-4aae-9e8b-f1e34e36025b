# CRM商机-合同流程设计文档

## 文档概述

本文档详细描述了CRM系统中商机到合同的完整业务流程，包括状态转换、数据表设计、业务规则等。

## 目录

1. [业务流程概述](#业务流程概述)
2. [商机阶段设计](#商机阶段设计)
3. [状态转换图](#状态转换图)
4. [数据表设计](#数据表设计)
5. [业务规则](#业务规则)
6. [API接口设计](#API接口设计)

## 业务流程概述

### 1. 核心业务流程

CRM系统支持多种业务流程模式，适应不同规模企业的实际需求：

#### 流程模式A：标准销售流程（大客户/复杂项目）
```
客户管理 → 商机创建 → 商机推进 → 合同签署 → 项目执行 → 收款管理
```

#### 流程模式B：简化销售流程（小客户/标准产品）
```
客户管理 → 合同签署 → 项目执行 → 收款管理
```

#### 流程模式C：混合流程（灵活适配）
```
客户管理 → [可选]商机管理 → 合同签署 → 项目执行 → 收款管理
```

### 2. 业务对象关系

```
客户(Customer) 1:N 商机(Business)
客户(Customer) 1:N 合同(Contract)
商机(Business) 1:1 合同(Contract) [可选关联]
```

## 商机阶段设计

### 1. 阶段定义原则

- **标准化**：提供系统默认阶段，保证数据一致性
- **可配置**：支持多租户个性化阶段配置
- **流程化**：明确阶段间的流转规则
- **可追溯**：记录完整的阶段变更历史

### 2. 默认商机阶段

| 阶段编码 | 阶段名称 | 排序 | 描述 | 建议停留时间 |
|---------|---------|------|------|-------------|
| initial | 初步接洽 | 1 | 首次接触客户，了解基本需求 | 3-7天 |
| demand | 需求确认 | 2 | 明确客户具体需求和预算范围 | 5-10天 |
| proposal | 方案报价 | 3 | 提供解决方案和详细报价 | 7-14天 |
| negotiation | 商务谈判 | 4 | 价格和条款谈判阶段 | 5-15天 |
| contract | 合同签署 | 5 | 准备和签署合同文件 | 3-7天 |
| closed_won | 成功签约 | 6 | 合同签署完成，商机成功 | - |
| closed_lost | 失败关闭 | 7 | 商机失败或主动放弃 | - |

### 3. 阶段属性说明

每个阶段包含以下属性：
- **基础信息**：名称、编码、描述
- **流程控制**：排序号、是否最终阶段、是否成功阶段
- **统计信息**：历史成功率、平均停留时间
- **显示配置**：颜色、图标
- **业务规则**：必填字段、自动化规则

## 状态转换图

### 1. 商机状态转换

```mermaid
stateDiagram-v2
    [*] --> initial : 创建商机
    
    initial --> demand : 需求明确
    initial --> closed_lost : 无效商机
    
    demand --> proposal : 需求确认
    demand --> initial : 需求不明确
    demand --> closed_lost : 客户放弃
    
    proposal --> negotiation : 方案认可
    proposal --> demand : 方案调整
    proposal --> closed_lost : 方案被拒
    
    negotiation --> contract : 谈判成功
    negotiation --> proposal : 重新报价
    negotiation --> closed_lost : 谈判失败
    
    contract --> closed_won : 合同签署
    contract --> negotiation : 合同修改
    contract --> closed_lost : 合同终止
    
    closed_won --> [*] : 流程结束
    closed_lost --> [*] : 流程结束
```

### 2. 合同状态转换

```mermaid
stateDiagram-v2
    [*] --> draft : 创建合同
    
    draft --> pending_approval : 提交审批
    draft --> cancelled : 取消合同
    
    pending_approval --> approved : 审批通过
    pending_approval --> rejected : 审批拒绝
    pending_approval --> cancelled : 取消审批
    
    rejected --> draft : 修改重提
    rejected --> cancelled : 放弃合同
    
    approved --> executing : 开始执行
    approved --> cancelled : 执行前取消
    
    executing --> completed : 执行完成
    executing --> terminated : 提前终止
    
    completed --> [*] : 流程结束
    terminated --> [*] : 流程结束
    cancelled --> [*] : 流程结束
```

### 3. 商机-合同关联流程

```mermaid
flowchart TD
    A[客户需求] --> B{业务类型判断}
    
    B -->|复杂项目| C[创建商机]
    B -->|标准产品| D[直接创建合同]
    B -->|老客户续约| E[基于历史合同创建]
    
    C --> F[商机阶段推进]
    F --> G{商机结果}
    
    G -->|成功| H[转为合同]
    G -->|失败| I[关闭商机]
    
    H --> J[合同执行]
    D --> J
    E --> J
    
    J --> K[项目交付]
    K --> L[收款管理]
    
    I --> M[流程结束]
    L --> M
```

## 数据表设计

### 1. 商机阶段表 (crm_business_stage)

```sql
CREATE TABLE `crm_business_stage` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '阶段ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID(0=系统默认)',
    `stage_name` varchar(50) NOT NULL COMMENT '阶段名称',
    `stage_code` varchar(50) NOT NULL COMMENT '阶段编码',
    `order_num` int(11) UNSIGNED NOT NULL COMMENT '排序号',
    `description` text DEFAULT NULL COMMENT '阶段描述',
    `color` varchar(20) NOT NULL DEFAULT '#409EFF' COMMENT '显示颜色',
    `icon` varchar(50) NOT NULL DEFAULT '' COMMENT '图标',
    `is_initial` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否初始阶段',
    `is_final` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否最终阶段',
    `is_success` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否成功阶段',
    `avg_duration_days` int(11) UNSIGNED DEFAULT NULL COMMENT '平均停留天数',
    `success_rate` decimal(5,2) UNSIGNED DEFAULT 0 COMMENT '历史成功率(%)',
    `required_fields` json DEFAULT NULL COMMENT '必填字段配置',
    `auto_rules` json DEFAULT NULL COMMENT '自动化规则配置',
    `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_id`, `stage_code`),
    KEY `idx_tenant_order` (`tenant_id`, `order_num`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='商机阶段表';
```

### 2. 商机表优化 (crm_business)

```sql
CREATE TABLE `crm_business` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商机ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL COMMENT '客户ID',
    `business_name` varchar(200) NOT NULL COMMENT '商机名称',
    `amount` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商机金额',
    `stage_id` bigint(20) UNSIGNED NOT NULL COMMENT '当前阶段ID',
    `expected_date` date DEFAULT NULL COMMENT '预计成交日期',
    `source` varchar(50) NOT NULL DEFAULT '' COMMENT '商机来源',
    `type` varchar(30) NOT NULL DEFAULT '' COMMENT '商机类型',
    `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal' COMMENT '优先级',
    `status` enum('active','won','lost','invalid') NOT NULL DEFAULT 'active' COMMENT '商机状态',
    `description` text COMMENT '商机描述',
    `remark` text COMMENT '备注',
    `owner_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '负责人ID',
    `last_followed_at` datetime DEFAULT NULL COMMENT '最后跟进时间',
    `next_followed_at` datetime DEFAULT NULL COMMENT '下次跟进时间',
    `stage_entered_at` datetime DEFAULT NULL COMMENT '进入当前阶段时间',
    `closed_date` date DEFAULT NULL COMMENT '关闭日期',
    `closed_reason` varchar(255) DEFAULT '' COMMENT '关闭原因',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_tenant_stage` (`tenant_id`, `stage_id`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_user_id`),
    KEY `idx_status_priority` (`status`, `priority`),
    KEY `idx_expected_date` (`expected_date`),
    KEY `idx_deleted_at` (`deleted_at`),
    FOREIGN KEY (`stage_id`) REFERENCES `crm_business_stage` (`id`)
) ENGINE=InnoDB COMMENT='商机表';
```

### 3. 阶段流转规则表 (crm_business_stage_flow)

```sql
CREATE TABLE `crm_business_stage_flow` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规则ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `from_stage_id` bigint(20) UNSIGNED NOT NULL COMMENT '源阶段ID',
    `to_stage_id` bigint(20) UNSIGNED NOT NULL COMMENT '目标阶段ID',
    `is_allowed` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否允许流转',
    `conditions` json DEFAULT NULL COMMENT '流转条件',
    `auto_actions` json DEFAULT NULL COMMENT '自动执行动作',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_flow` (`tenant_id`, `from_stage_id`, `to_stage_id`),
    KEY `idx_from_stage` (`from_stage_id`),
    KEY `idx_to_stage` (`to_stage_id`),
    FOREIGN KEY (`from_stage_id`) REFERENCES `crm_business_stage` (`id`),
    FOREIGN KEY (`to_stage_id`) REFERENCES `crm_business_stage` (`id`)
) ENGINE=InnoDB COMMENT='商机阶段流转规则表';
```

### 4. 阶段变更记录表 (crm_business_stage_record)

```sql
CREATE TABLE `crm_business_stage_record` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT '租户ID',
    `business_id` bigint(20) UNSIGNED NOT NULL COMMENT '商机ID',
    `from_stage_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '原阶段ID',
    `to_stage_id` bigint(20) UNSIGNED NOT NULL COMMENT '新阶段ID',
    `duration_days` int(11) UNSIGNED DEFAULT NULL COMMENT '在原阶段停留天数',
    `change_reason` varchar(255) DEFAULT '' COMMENT '变更原因',
    `remark` text COMMENT '变更备注',
    `creator_id` bigint(20) UNSIGNED NOT NULL COMMENT '操作人ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_business` (`tenant_id`, `business_id`),
    KEY `idx_from_stage` (`from_stage_id`),
    KEY `idx_to_stage` (`to_stage_id`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`business_id`) REFERENCES `crm_business` (`id`),
    FOREIGN KEY (`from_stage_id`) REFERENCES `crm_business_stage` (`id`),
    FOREIGN KEY (`to_stage_id`) REFERENCES `crm_business_stage` (`id`)
) ENGINE=InnoDB COMMENT='商机阶段变更记录表';
```

### 5. 合同表优化 (crm_contract)

```sql
CREATE TABLE `crm_contract` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '合同ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `customer_id` bigint(20) UNSIGNED NOT NULL COMMENT '客户ID',
    `business_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '关联商机ID(可选)',
    `contract_no` varchar(100) NOT NULL COMMENT '合同编号',
    `contract_name` varchar(200) NOT NULL COMMENT '合同名称',
    `contract_amount` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '合同金额',
    `contract_type` varchar(50) NOT NULL DEFAULT '' COMMENT '合同类型',
    `source` enum('opportunity','direct','renewal','referral') NOT NULL DEFAULT 'direct' COMMENT '合同来源',
    `status` enum('draft','pending_approval','approved','executing','completed','terminated','cancelled') NOT NULL DEFAULT 'draft' COMMENT '合同状态',
    `sign_date` date DEFAULT NULL COMMENT '签约日期',
    `start_date` date DEFAULT NULL COMMENT '合同开始日期',
    `end_date` date DEFAULT NULL COMMENT '合同结束日期',
    `payment_terms` varchar(200) DEFAULT '' COMMENT '付款条件',
    `delivery_terms` varchar(200) DEFAULT '' COMMENT '交付条件',
    `description` text COMMENT '合同描述',
    `remark` text COMMENT '备注',
    `owner_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '负责人ID',
    `approver_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '审批人ID',
    `approved_at` datetime DEFAULT NULL COMMENT '审批时间',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_contract_no` (`contract_no`),
    KEY `idx_tenant_customer` (`tenant_id`, `customer_id`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_source` (`source`),
    KEY `idx_sign_date` (`sign_date`),
    KEY `idx_deleted_at` (`deleted_at`),
    FOREIGN KEY (`customer_id`) REFERENCES `crm_customer` (`id`),
    FOREIGN KEY (`business_id`) REFERENCES `crm_business` (`id`)
) ENGINE=InnoDB COMMENT='合同表';
```

## 业务规则

### 1. 商机阶段流转规则

#### 1.1 基本流转规则
- **顺序流转**：一般按照阶段顺序推进，但允许跳跃
- **逆向流转**：允许回退到前一阶段，需要填写回退原因
- **跨越流转**：特殊情况下可跨越多个阶段，需要权限控制
- **最终阶段**：到达最终阶段后不允许再次流转

#### 1.2 自动化规则
- **超时提醒**：阶段停留时间超过预设值时自动提醒
- **自动推进**：满足特定条件时自动推进到下一阶段
- **数据同步**：阶段变更时自动更新相关字段
- **通知机制**：阶段变更时自动通知相关人员

#### 1.3 权限控制
- **阶段权限**：不同角色可操作的阶段范围
- **金额权限**：大额商机的阶段变更需要审批
- **客户权限**：重要客户的商机变更需要特殊权限

### 2. 商机-合同转换规则

#### 2.1 转换条件
- 商机状态必须为"active"
- 商机阶段必须达到"contract"或更高
- 商机金额必须大于0
- 必须有明确的负责人

#### 2.2 转换流程
1. **数据验证**：检查商机数据完整性
2. **创建合同**：基于商机信息创建合同草稿
3. **建立关联**：设置商机与合同的关联关系
4. **状态更新**：更新商机状态为"won"
5. **通知相关人**：通知商机负责人和合同负责人

#### 2.3 数据映射
```
商机 → 合同 数据映射关系：
- business_name → contract_name
- customer_id → customer_id
- amount → contract_amount
- owner_user_id → owner_user_id
- expected_date → sign_date
- description → description
```

### 3. 合同状态管理规则

#### 3.1 状态流转规则
- **草稿 → 待审批**：合同信息完整后可提交审批
- **待审批 → 已审批**：审批通过后进入执行准备
- **已审批 → 执行中**：合同生效日期到达后自动或手动开始执行
- **执行中 → 已完成**：合同到期或项目完成后结束
- **任意状态 → 已取消**：特殊情况下可取消合同

#### 3.2 自动化处理
- **自动编号**：合同创建时自动生成唯一编号
- **到期提醒**：合同到期前自动提醒相关人员
- **状态同步**：合同状态变更时同步更新商机状态
- **收款提醒**：根据付款条件自动生成收款提醒

## API接口设计

### 1. 商机阶段管理接口

#### 1.1 获取阶段列表
```
GET /api/crm/business-stage/list
参数：
- tenant_id: 租户ID（可选，默认当前租户）
- status: 状态筛选（可选）

响应：
{
    "code": 1,
    "data": [
        {
            "id": 1,
            "stage_name": "初步接洽",
            "stage_code": "initial",
            "order_num": 1,
            "color": "#909399",
            "icon": "el-icon-phone",
            "is_initial": 1,
            "is_final": 0,
            "success_rate": 15.5
        }
    ]
}
```

#### 1.2 创建/更新阶段
```
POST /api/crm/business-stage/save
参数：
{
    "stage_name": "阶段名称",
    "stage_code": "阶段编码",
    "order_num": 排序号,
    "description": "阶段描述",
    "color": "#颜色值",
    "icon": "图标类名"
}
```

#### 1.3 删除阶段
```
DELETE /api/crm/business-stage/{id}
注意：删除前需检查是否有商机使用该阶段
```

### 2. 商机管理接口

#### 2.1 商机阶段变更
```
POST /api/crm/business/change-stage
参数：
{
    "business_id": 商机ID,
    "to_stage_id": 目标阶段ID,
    "change_reason": "变更原因",
    "remark": "备注"
}

响应：
{
    "code": 1,
    "message": "阶段变更成功",
    "data": {
        "business_id": 1,
        "from_stage": "初步接洽",
        "to_stage": "需求确认",
        "duration_days": 5
    }
}
```

#### 2.2 获取可流转阶段
```
GET /api/crm/business/{id}/next-stages
响应：
{
    "code": 1,
    "data": [
        {
            "stage_id": 2,
            "stage_name": "需求确认",
            "is_allowed": true,
            "conditions": []
        }
    ]
}
```

#### 2.3 商机转合同
```
POST /api/crm/business/convert-to-contract
参数：
{
    "business_id": 商机ID,
    "contract_data": {
        "contract_name": "合同名称",
        "contract_type": "合同类型",
        "sign_date": "签约日期",
        "start_date": "开始日期",
        "end_date": "结束日期"
    }
}
```

### 3. 合同管理接口

#### 3.1 合同状态变更
```
POST /api/crm/contract/change-status
参数：
{
    "contract_id": 合同ID,
    "status": "新状态",
    "remark": "变更备注"
}
```

#### 3.2 合同审批
```
POST /api/crm/contract/approve
参数：
{
    "contract_id": 合同ID,
    "action": "approve|reject",
    "remark": "审批意见"
}
```

## 初始化数据

### 1. 系统默认阶段数据

```sql
-- 插入系统默认商机阶段
INSERT INTO `crm_business_stage` (`tenant_id`, `stage_name`, `stage_code`, `order_num`, `description`, `color`, `icon`, `is_initial`, `is_final`, `is_success`, `status`) VALUES
(0, '初步接洽', 'initial', 1, '首次接触客户，了解基本需求', '#909399', 'el-icon-phone', 1, 0, 0, 1),
(0, '需求确认', 'demand', 2, '明确客户具体需求和预算范围', '#E6A23C', 'el-icon-edit', 0, 0, 0, 1),
(0, '方案报价', 'proposal', 3, '提供解决方案和详细报价', '#409EFF', 'el-icon-document', 0, 0, 0, 1),
(0, '商务谈判', 'negotiation', 4, '价格和条款谈判阶段', '#F56C6C', 'el-icon-chat-dot-round', 0, 0, 0, 1),
(0, '合同签署', 'contract', 5, '准备和签署合同文件', '#67C23A', 'el-icon-document-checked', 0, 0, 0, 1),
(0, '成功签约', 'closed_won', 6, '合同签署完成，商机成功', '#67C23A', 'el-icon-success', 0, 1, 1, 1),
(0, '失败关闭', 'closed_lost', 7, '商机失败或主动放弃', '#F56C6C', 'el-icon-error', 0, 1, 0, 1);
```

### 2. 默认阶段流转规则

```sql
-- 插入默认阶段流转规则
INSERT INTO `crm_business_stage_flow` (`tenant_id`, `from_stage_id`, `to_stage_id`, `is_allowed`) VALUES
-- 从初步接洽可以流转到的阶段
(0, 1, 2, 1), -- 初步接洽 → 需求确认
(0, 1, 7, 1), -- 初步接洽 → 失败关闭

-- 从需求确认可以流转到的阶段
(0, 2, 1, 1), -- 需求确认 → 初步接洽（回退）
(0, 2, 3, 1), -- 需求确认 → 方案报价
(0, 2, 7, 1), -- 需求确认 → 失败关闭

-- 从方案报价可以流转到的阶段
(0, 3, 2, 1), -- 方案报价 → 需求确认（回退）
(0, 3, 4, 1), -- 方案报价 → 商务谈判
(0, 3, 7, 1), -- 方案报价 → 失败关闭

-- 从商务谈判可以流转到的阶段
(0, 4, 3, 1), -- 商务谈判 → 方案报价（回退）
(0, 4, 5, 1), -- 商务谈判 → 合同签署
(0, 4, 7, 1), -- 商务谈判 → 失败关闭

-- 从合同签署可以流转到的阶段
(0, 5, 4, 1), -- 合同签署 → 商务谈判（回退）
(0, 5, 6, 1), -- 合同签署 → 成功签约
(0, 5, 7, 1); -- 合同签署 → 失败关闭
```

## 实施建议

### 1. 分阶段实施计划

#### 第一阶段：基础功能（2周）
- 创建商机阶段表和基础数据
- 实现阶段管理CRUD功能
- 商机表增加stage_id字段
- 基础的阶段变更功能

#### 第二阶段：流程控制（2周）
- 实现阶段流转规则
- 阶段变更记录功能
- 权限控制和验证
- 自动化规则基础框架

#### 第三阶段：高级功能（2周）
- 商机转合同功能
- 统计分析功能
- 自动化提醒和通知
- 数据迁移工具

#### 第四阶段：优化完善（1周）
- 性能优化
- 用户体验优化
- 文档完善
- 测试和修复

### 2. 数据迁移策略

#### 2.1 现有数据处理
```sql
-- 为现有商机数据设置默认阶段
UPDATE crm_business
SET stage_id = (
    SELECT id FROM crm_business_stage
    WHERE stage_code = 'initial' AND tenant_id = 0
)
WHERE stage_id IS NULL OR stage_id = 0;
```

#### 2.2 阶段映射
```sql
-- 根据现有stage字段值映射到新的stage_id
UPDATE crm_business b
SET stage_id = (
    SELECT s.id FROM crm_business_stage s
    WHERE s.stage_code = CASE
        WHEN b.stage LIKE '%接洽%' THEN 'initial'
        WHEN b.stage LIKE '%需求%' THEN 'demand'
        WHEN b.stage LIKE '%方案%' OR b.stage LIKE '%报价%' THEN 'proposal'
        WHEN b.stage LIKE '%谈判%' THEN 'negotiation'
        WHEN b.stage LIKE '%合同%' THEN 'contract'
        WHEN b.stage LIKE '%成功%' OR b.stage LIKE '%签约%' THEN 'closed_won'
        WHEN b.stage LIKE '%失败%' OR b.stage LIKE '%关闭%' THEN 'closed_lost'
        ELSE 'initial'
    END
    AND s.tenant_id = b.tenant_id
);
```

### 3. 注意事项

#### 3.1 性能考虑
- 为常用查询字段添加索引
- 阶段变更记录表定期归档
- 统计数据可考虑缓存机制

#### 3.2 数据一致性
- 使用事务确保阶段变更的原子性
- 定期检查数据完整性
- 建立数据修复机制

#### 3.3 用户体验
- 提供阶段变更的可视化界面
- 支持批量操作
- 提供操作历史查看功能

---

## 总结

本文档详细描述了CRM系统中商机到合同的完整流程设计，包括：

1. **多样化的业务流程支持**：适应不同规模企业的实际需求
2. **标准化的阶段管理**：提供可配置的多租户阶段体系
3. **完整的状态转换**：清晰的状态流转规则和控制机制
4. **灵活的数据结构**：支持复杂业务场景的数据表设计
5. **规范的接口设计**：便于前端集成和第三方对接
6. **可操作的实施方案**：分阶段的实施计划和数据迁移策略

通过这套设计，系统既能满足标准化管理的需求，又保持了足够的灵活性来适应不同的业务场景。
```
