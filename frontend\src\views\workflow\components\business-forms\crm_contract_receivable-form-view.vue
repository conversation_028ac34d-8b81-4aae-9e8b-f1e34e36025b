<template>
  <div class="crm-receivable-form-view">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="合同信息">
        {{ formData.contract_name || formData.contract_id || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="客户名称">
        {{ formData.customer_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="金额">
        <span class="amount-text"> ¥{{ formatAmount(formData.amount) }} </span>
      </el-descriptions-item>

      <el-descriptions-item label="日期">
        {{ formatDate(formData.received_date) }}
      </el-descriptions-item>

      <el-descriptions-item label="付款方式">
        <el-tag :type="getPaymentMethodColor(formData.payment_method)">
          {{ getPaymentMethodName(formData.payment_method) }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="收款银行">
        {{ formData.bank_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="银行账号">
        <span class="bank-account">
          {{ formatBankAccount(formData.bank_account) }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="收款人">
        {{ formData.owner_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="创建时间">
        {{ formatDateTime(formData.created_at) }}
      </el-descriptions-item>

      <el-descriptions-item v-if="formData.remark" label="备注" :span="2">
        <div class="remark-text">
          {{ formData.remark }}
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  // 组件属性
  interface Props {
    formData: any
  }

  const props = withDefaults(defineProps<Props>(), {
    formData: () => ({})
  })

  // 格式化金额
  const formatAmount = (amount: number | string) => {
    if (!amount) return '0.00'
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  // 格式化日期
  const formatDate = (date: string) => {
    if (!date) return '-'
    return new Date(date).toLocaleDateString('zh-CN')
  }

  // 格式化日期时间
  const formatDateTime = (dateTime: string) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 格式化银行账号（隐藏中间部分）
  const formatBankAccount = (account: string) => {
    if (!account) return '-'
    if (account.length <= 8) return account
    const start = account.substring(0, 4)
    const end = account.substring(account.length - 4)
    return `${start}****${end}`
  }

  // 获取付款方式名称
  const getPaymentMethodName = (method: string) => {
    const methods: Record<string, string> = {
      cash: '现金支付',
      transfer: '银行转账',
      check: '支票支付',
      online: '在线支付',
      other: '其他方式'
    }
    return methods[method] || method || '-'
  }

  // 获取付款方式颜色
  const getPaymentMethodColor = (method: string) => {
    const colors: Record<string, string> = {
      cash: 'success',
      transfer: 'primary',
      check: 'warning',
      online: 'info',
      other: ''
    }
    return colors[method] || ''
  }

  // 获取审批状态名称
  const getApprovalStatusName = (status: number) => {
    const statusMap: Record<number, string> = {
      0: '草稿',
      1: '审批中',
      2: '已通过',
      3: '已驳回',
      4: '已终止',
      5: '已撤回'
    }
    return statusMap[status] || '未知状态'
  }

  // 获取审批状态颜色
  const getApprovalStatusColor = (status: number) => {
    const colorMap: Record<number, string> = {
      0: 'info',
      1: 'warning',
      2: 'success',
      3: 'danger',
      4: 'info',
      5: 'warning'
    }
    return colorMap[status] || 'info'
  }
</script>

<style scoped lang="scss">
  .crm-receivable-form-view {
    //padding: 20px;
  }

  .amount-text {
    font-weight: bold;
    color: #e6a23c;
    font-size: 16px;
  }

  .bank-account {
    font-family: 'Courier New', monospace;
    color: #606266;
  }

  .remark-text,
  .approval-comment {
    line-height: 1.6;
    color: #606266;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .bank-details,
  .approval-details {
    margin-top: 20px;
  }

  .section-title {
    font-weight: bold;
    color: #409eff;
  }

  .action-buttons {
    margin-top: 20px;
    text-align: center;
  }

  .el-descriptions {
    margin-bottom: 20px;
  }

  :deep(.el-descriptions__label) {
    font-weight: 500;
    color: #303133;
  }

  :deep(.el-descriptions__content) {
    color: #606266;
  }
</style>
