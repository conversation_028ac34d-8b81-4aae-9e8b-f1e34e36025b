<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\crm\model\CrmBusiness;
use app\crm\model\CrmContract;
use app\crm\model\CrmContractProduct;
use app\crm\model\CrmBusinessProduct;
use think\facade\Db;
use think\facade\Log;

/**
 * 商机转化业务服务类
 * 负责处理商机转合同的核心业务逻辑
 */
class BusinessConversionService extends BaseService
{
    /**
     * 构造函数
     */
    protected function __construct()
    {
        $this->model = new CrmBusiness();
        parent::__construct();
    }

    /**
     * 商机转化为合同
     * 
     * @param int $businessId 商机ID
     * @param array $contractData 合同数据
     * @return array 转化结果
     * @throws BusinessException
     */
    public function convertToContract(int $businessId, array $contractData = []): array
    {
        // 开启数据库事务
        Db::startTrans();
        
        try {
            // 1. 验证商机状态和权限
            $business = $this->validateBusinessForConversion($businessId);
            
            // 2. 准备合同数据
            $preparedContractData = $this->prepareContractData($business, $contractData);
            
            // 3. 创建合同记录
            $contract = $this->createContract($preparedContractData);
            
            // 4. 复制商机产品到合同产品
            $this->copyBusinessProductsToContract($businessId, $contract['id']);
            
            // 5. 更新商机状态
            $this->updateBusinessStatus($businessId, $contract['id']);
            
            // 6. 记录转化历史
            $this->recordConversionHistory($businessId, $contract['id']);
            
            // 提交事务
            Db::commit();
            
            // 7. 发送转化通知 (异步)
            $this->sendConversionNotification($business, $contract);
            
            return [
                'success' => true,
                'message' => '商机转化成功',
                'data' => [
                    'contract_id' => $contract['id'],
                    'contract_no' => $contract['contract_no'],
                    'contract_amount' => $contract['contract_amount']
                ]
            ];
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            // 记录错误日志
            Log::error('商机转化失败', [
                'business_id' => $businessId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new BusinessException('商机转化失败：' . $e->getMessage());
        }
    }

    /**
     * 验证商机转化条件
     * 
     * @param int $businessId 商机ID
     * @return array 检查结果
     */
    public function checkConversionConditions(int $businessId): array
    {
        try {
            $business = $this->validateBusinessForConversion($businessId);
            
            return [
                'can_convert' => true,
                'message' => '商机可以转化',
                'business_info' => [
                    'business_name' => $business['business_name'],
                    'customer_name' => $business['customer_name'] ?? '',
                    'amount' => $business['amount'],
                    'expected_date' => $business['expected_date']
                ]
            ];
            
        } catch (BusinessException $e) {
            return [
                'can_convert' => false,
                'message' => $e->getMessage(),
                'business_info' => []
            ];
        }
    }

    /**
     * 验证商机是否可以转化
     * 
     * @param int $businessId 商机ID
     * @return array 商机数据
     * @throws BusinessException
     */
    private function validateBusinessForConversion(int $businessId): array
    {
        // ✅ 优化：使用直接模型查询，避免单例状态污染
        $business = \app\crm\model\CrmBusiness::where('id', $businessId)->findOrEmpty();

        if ($business->isEmpty()) {
            throw new BusinessException('商机不存在');
        }

        $businessData = $business->toArray();

        // 检查商机状态
        if ($businessData['status'] != 1) {
            throw new BusinessException('商机状态不允许转化');
        }

        // 检查是否已经转化
        if (!empty($businessData['contract_id'])) {
            throw new BusinessException('商机已经转化为合同');
        }

        // 检查权限
        if (!$this->checkBusinessPermission($businessData)) {
            throw new BusinessException('没有权限操作此商机');
        }

        return $businessData;
    }

    /**
     * 检查商机操作权限
     * 
     * @param array $business 商机数据
     * @return bool
     */
    private function checkBusinessPermission(array $business): bool
    {
        $adminId = $this->getAdminId();
        
        // 商机负责人可以操作
        if ($business['owner_id'] == $adminId) {
            return true;
        }
        
        // 超级管理员可以操作
        if ($this->isSuperAdmin()) {
            return true;
        }
        
        // 其他权限检查...
        return false;
    }

    /**
     * 准备合同数据
     * 
     * @param array $business 商机数据
     * @param array $contractData 额外合同数据
     * @return array 准备好的合同数据
     */
    private function prepareContractData(array $business, array $contractData): array
    {
        $defaultData = [
            'customer_id' => $business['customer_id'],
            'business_id' => $business['id'],
            'contract_no' => $this->generateContractNo(),
            'contract_name' => $contractData['contract_name'] ?? $business['business_name'] . '合同',
            'contract_amount' => $contractData['contract_amount'] ?? $business['expected_amount'],
            'contract_type' => $contractData['contract_type'] ?? 1, // 默认销售合同
            'sign_date' => $contractData['sign_date'] ?? date('Y-m-d'),
            'start_date' => $contractData['start_date'] ?? date('Y-m-d'),
            'end_date' => $contractData['end_date'] ?? date('Y-m-d', strtotime('+1 year')),
            'status' => 0, // 草稿状态
            'owner_id' => $business['owner_id'],
            'tenant_id' => $business['tenant_id'],
            'created_id' => $this->getAdminId(),
            'updated_id' => $this->getAdminId()
        ];
        
        // 合并用户提供的额外数据
        return array_merge($defaultData, $contractData);
    }

    /**
     * 生成合同编号
     * 
     * @return string
     */
    private function generateContractNo(): string
    {
        $prefix = 'HT';
        $date = date('Ymd');
        $tenantId = $this->getTenantId();
        
        // 查询当天最大编号
        $maxNo = Db::table('crm_contract')
            ->where('tenant_id', $tenantId)
            ->where('contract_no', 'like', $prefix . $date . '%')
            ->max('contract_no');
        
        if ($maxNo) {
            $sequence = intval(substr($maxNo, -4)) + 1;
        } else {
            $sequence = 1;
        }
        
        return $prefix . $date . str_pad((string)$sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 创建合同记录
     * 
     * @param array $contractData 合同数据
     * @return array 创建的合同数据
     */
    private function createContract(array $contractData): array
    {
        // ✅ 优化：使用直接模型实例化，避免单例状态污染
        $contractModel = new \app\crm\model\CrmContract();
        $contractId = $contractModel->saveByCreate($contractData);

        if (!$contractId) {
            throw new BusinessException('创建合同失败');
        }

        // 获取创建的合同详情
        $createdContract = \app\crm\model\CrmContract::where('id', $contractId)->findOrEmpty();
        if ($createdContract->isEmpty()) {
            throw new BusinessException('获取合同详情失败');
        }

        return $createdContract->toArray();
    }

    /**
     * 复制商机产品到合同产品
     * 
     * @param int $businessId 商机ID
     * @param int $contractId 合同ID
     */
    private function copyBusinessProductsToContract(int $businessId, int $contractId): void
    {
        // ✅ 优化：使用直接模型查询和实例化，避免循环中的单例状态污染

        // 获取商机产品列表 - 使用直接模型查询
        $businessProducts = \app\crm\model\CrmBusinessProduct::where([
            'business_id' => $businessId,
            'status' => 1
        ])->select()->toArray();

        // 复制到合同产品 - 在循环中使用直接模型实例化
        foreach ($businessProducts as $businessProduct) {
            $contractProductData = [
                'contract_id' => $contractId,
                'product_id' => $businessProduct['product_id'],
                'product_spec_id' => $businessProduct['product_spec_id'],
                'quantity' => $businessProduct['quantity'],
                'unit_price' => $businessProduct['unit_price'],
                'total_amount' => $businessProduct['total_amount'],
                'discount_rate' => $businessProduct['discount_rate'] ?? 0,
                'discount_amount' => $businessProduct['discount_amount'] ?? 0,
                'final_amount' => $businessProduct['final_amount'] ?? $businessProduct['total_amount'],
                'remark' => $businessProduct['remark'] ?? '',
                'status' => 1,
                'tenant_id' => $businessProduct['tenant_id'],
                'created_id' => $this->getAdminId(),
                'updated_id' => $this->getAdminId()
            ];

            // 每次循环创建新的模型实例，避免状态污染
            $contractProductModel = new \app\crm\model\CrmContractProduct();
            $contractProductModel->saveByCreate($contractProductData);
        }
    }

    /**
     * 更新商机状态
     * 
     * @param int $businessId 商机ID
     * @param int $contractId 合同ID
     */
    private function updateBusinessStatus(int $businessId, int $contractId): void
    {
        // ✅ 优化：使用直接模型更新，避免单例状态污染
        $businessModel = \app\crm\model\CrmBusiness::where('id', $businessId)->findOrEmpty();
        if (!$businessModel->isEmpty()) {
            $businessModel->saveByUpdate([
                'contract_id' => $contractId,
                'is_converted' => 1,
                'converted_at' => date('Y-m-d H:i:s'),
                'status' => 3, // 已成交状态
                'updated_id' => $this->getAdminId()
            ]);
        }
    }

    /**
     * 记录转化历史
     * 
     * @param int $businessId 商机ID
     * @param int $contractId 合同ID
     */
    private function recordConversionHistory(int $businessId, int $contractId): void
    {
        // 记录转化日志
        Log::info('商机转化成功', [
            'business_id' => $businessId,
            'contract_id' => $contractId,
            'operator_id' => $this->getAdminId(),
            'tenant_id' => $this->getTenantId(),
            'converted_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 发送转化通知
     * 
     * @param array $business 商机数据
     * @param array $contract 合同数据
     */
    private function sendConversionNotification(array $business, array $contract): void
    {
        try {
            // ✅ 优化：通知服务风险较低，但为了一致性也进行优化
            // 注意：这里可以考虑保留单例，因为通知服务通常是无状态的
            $notificationService = new \app\crm\service\CrmNotificationService();
            $notificationService->sendBusinessConvertNotification($business, $contract, $this->getAdminId());
        } catch (\Exception $e) {
            Log::error('发送商机转化通知失败: ' . $e->getMessage());
        }
    }
}
