<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use think\facade\Log;
use think\response\Json;

/**
 * CRUD控制器特性
 * 提供标准的CRUD控制器方法实现
 */
trait CrudControllerTrait
{
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		$result = $this->service->search($params);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}
	
	/**
	 * 新增
	 *
	 * @return Json
	 */
	public function add(): <PERSON><PERSON>
	{
		$params = $this->request->post();
		
		try {
			$result = $this->service->add($params);
			return $result
				? $this->success('添加成功')
				: $this->error('添加失败');
		}
		catch (\Exception $e) {
			Log::error('CRUD Add Debug - Exception: ' . $e->getMessage());
			return $this->error('添加失败');
		}
	}
	
	/**
	 * 更新
	 *
	 * @param int $id
	 * @return Json
	 */
	public function edit(int $id): Json
	{
		$params = $this->request->post();
		
		try {
			$result = $this->service->edit($params, ['id' => $id]);
			return $result
				? $this->success('更新成功')
				: $this->error('更新失败');
		}
		catch (\Exception $e) {
			Log::error('CRUD Edit Debug - Exception: ' . $e->getMessage());
			return $this->error('更新失败');
		}
	}
	
	/**
	 * 删除单个记录
	 *
	 * @return Json
	 */
	public function delete(): Json
	{
		// 获取URL参数中的ID
		$id = $this->request->param('id');
		
		if (empty($id)) {
			return $this->error('参数错误');
		}
		
		return $this->service->delete([$id])
			? $this->success('更新成功')
			: $this->error('更新失败');
	}
	
	/**
	 * 批量删除
	 *
	 * @return Json
	 */
	public function batchDelete(): Json
	{
		// 获取POST body中的ids数组
		$ids = $this->request->post('ids/a');
		
		if (empty($ids) || !is_array($ids)) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->delete($ids);
		
		if ($result) {
			return $this->success('批量删除成功');
		}
		else {
			return $this->error('批量删除失败');
		}
	}
	
	/**
	 * 更新字段值
	 *
	 * @return Json
	 */
	public function updateField(): Json
	{
		$id    = $this->request->post('id');
		$field = $this->request->post('field');
		$value = $this->request->post('value');
		
		if (empty($id) || empty($field)) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->updateField($id, $field, $value);
		if ($result) {
			return $this->success('更新成功');
		}
		else {
			return $this->error('更新失败');
		}
	}
	
	/**
	 * 获取下拉选项
	 *
	 * @return Json
	 */
	public function options(): Json
	{
		$params     = $this->request->param();
		$labelField = $params['label_field'] ?? 'name';
		$valueField = $params['value_field'] ?? 'id';
		$where      = $params['where'] ?? [];
		
		$result = $this->service->getSelectOptions($where, $labelField, $valueField);
		return $this->success('获取成功', $result);
	}
} 