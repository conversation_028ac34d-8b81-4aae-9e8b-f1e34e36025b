-- 消息中心通知模板变量配置修复脚本
-- 执行时间: 2025-07-16
-- 说明: 修复变量配置中的中文code字段和格式问题

-- 1. 修复 workflow_task_urge 模板 (ID: 17)
-- 问题: 变量code使用中文，应该使用英文
UPDATE notice_template SET 
    variables_config = '{
        "variables": [
            {
                "name": "流程标题",
                "code": "title",
                "field": "title",
                "required": true,
                "description": "工作流程标题"
            },
            {
                "name": "任务名称",
                "code": "task_name",
                "field": "task_name",
                "required": true,
                "description": "待处理任务名称"
            },
            {
                "name": "催办人",
                "code": "urger_name",
                "field": "urger_name",
                "required": true,
                "description": "催办人姓名"
            },
            {
                "name": "催办时间",
                "code": "created_at",
                "field": "created_at",
                "required": true,
                "description": "催办时间"
            },
            {
                "name": "催办原因",
                "code": "reason",
                "field": "reason",
                "required": false,
                "description": "催办原因"
            }
        ]
    }',
    updated_at = NOW()
WHERE id = 17;

-- 2. 修复 workflow_request 模板 (ID: 18)
-- 问题: required字段为空字符串，应该是布尔值
UPDATE notice_template SET 
    variables_config = '{
        "variables": [
            {
                "name": "审批事项",
                "code": "title",
                "field": "title",
                "required": true,
                "description": "审批的事项标题"
            },
            {
                "name": "申请人",
                "code": "submitter_name",
                "field": "submitter_name",
                "required": true,
                "description": "申请人姓名"
            },
            {
                "name": "申请时间",
                "code": "created_at",
                "field": "created_at",
                "required": true,
                "description": "申请提交时间"
            }
        ]
    }',
    updated_at = NOW()
WHERE id = 18;

-- 3. 修复 workflow_task_transfer 模板 (ID: 59)
-- 问题: 变量code使用中文
UPDATE notice_template SET 
    variables_config = '{
        "variables": [
            {
                "name": "流程标题",
                "code": "title",
                "field": "title",
                "required": true,
                "description": "工作流程标题"
            },
            {
                "name": "节点名称",
                "code": "node_name",
                "field": "node_name",
                "required": true,
                "description": "转交任务节点名称"
            },
            {
                "name": "转交人",
                "code": "from_user",
                "field": "from_user",
                "required": true,
                "description": "转交人姓名"
            },
            {
                "name": "接收人",
                "code": "to_user",
                "field": "to_user",
                "required": true,
                "description": "接收人姓名"
            },
            {
                "name": "转交时间",
                "code": "transfer_time",
                "field": "transfer_time",
                "required": true,
                "description": "转交时间"
            },
            {
                "name": "详情链接",
                "code": "detail_url",
                "field": "detail_url",
                "required": false,
                "description": "任务详情页链接"
            }
        ]
    }',
    updated_at = NOW()
WHERE id = 59;

-- 4. 修复 workflow_task_terminated 模板 (ID: 60)
-- 问题: 变量code使用中文，且变量名称不一致
UPDATE notice_template SET 
    variables_config = '{
        "variables": [
            {
                "name": "流程标题",
                "code": "title",
                "field": "title",
                "required": true,
                "description": "工作流程标题"
            },
            {
                "name": "终止结果",
                "code": "result",
                "field": "result",
                "required": true,
                "description": "终止结果"
            },
            {
                "name": "提交时间",
                "code": "submit_time",
                "field": "submit_time",
                "required": false,
                "description": "流程提交时间"
            },
            {
                "name": "终止时间",
                "code": "terminate_time",
                "field": "terminate_time",
                "required": true,
                "description": "流程终止时间"
            },
            {
                "name": "终止人",
                "code": "terminate_by",
                "field": "terminate_by",
                "required": true,
                "description": "终止操作人"
            },
            {
                "name": "终止原因",
                "code": "reason",
                "field": "reason",
                "required": false,
                "description": "终止原因"
            },
            {
                "name": "详情链接",
                "code": "detail_url",
                "field": "detail_url",
                "required": false,
                "description": "流程详情页链接"
            }
        ]
    }',
    updated_at = NOW()
WHERE id = 60;

-- 5. 验证修复结果
-- 查询所有工作流相关模板的变量配置
SELECT 
    id,
    code,
    name,
    JSON_EXTRACT(variables_config, '$.variables[0].code') as first_var_code,
    JSON_EXTRACT(variables_config, '$.variables[0].required') as first_var_required,
    updated_at
FROM notice_template 
WHERE module_code = 'workflow' 
    AND status = 1 
ORDER BY id;

-- 6. 检查是否还有中文code字段的模板
SELECT 
    id,
    code,
    name,
    variables_config
FROM notice_template 
WHERE status = 1 
    AND (
        variables_config LIKE '%"code":"流程标题"%' 
        OR variables_config LIKE '%"code":"任务名称"%'
        OR variables_config LIKE '%"code":"催办人"%'
        OR variables_config LIKE '%"code":"节点名称"%'
        OR variables_config LIKE '%"code":"转交人"%'
        OR variables_config LIKE '%"code":"接收人"%'
        OR variables_config LIKE '%"code":"转交时间"%'
        OR variables_config LIKE '%"code":"终止时间"%'
        OR variables_config LIKE '%"code":"终止人"%'
        OR variables_config LIKE '%"code":"终止原因"%'
        OR variables_config LIKE '%"code":"审批结果"%'
    );

-- 7. 检查required字段格式是否正确
SELECT 
    id,
    code,
    name,
    variables_config
FROM notice_template 
WHERE status = 1 
    AND (
        variables_config LIKE '%"required":""%' 
        OR variables_config LIKE '%"required":"""%'
    );

-- 执行完成后的验证说明:
-- 1. 第5步查询应该显示所有工作流模板的第一个变量code都是英文
-- 2. 第6步查询应该返回空结果，表示没有中文code字段
-- 3. 第7步查询应该返回空结果，表示没有空字符串的required字段
-- 4. 如果以上验证都通过，说明修复成功
