<?php
declare(strict_types=1);

namespace app\crm\service;

use app\notice\service\NoticeDispatcherService;
use think\facade\Log;

/**
 * CRM消息通知服务
 * 负责CRM业务事件的消息通知发送
 */
class CrmNotificationService
{
    /**
     * 消息分发服务
     */
    private NoticeDispatcherService $noticeDispatcher;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->noticeDispatcher = NoticeDispatcherService::getInstance();
    }
    
    /**
     * 获取单例实例
     */
    public static function getInstance(): self
    {
        static $instance = null;
        if ($instance === null) {
            $instance = new self();
        }
        return $instance;
    }
    
    /**
     * 发送线索转化通知
     * 
     * @param array $leadData 线索数据
     * @param array $customerData 客户数据
     * @param int $operatorId 操作人ID
     * @return bool
     */
    public function sendLeadConvertNotification(array $leadData, array $customerData, int $operatorId): bool
    {
        try {
            // 构建符合模板字段映射的数据结构
            $variables = [
                'lead' => [
                    'lead_name' => $leadData['lead_name'] ?? ''
                ],
                'customer' => [
                    'customer_name' => $customerData['customer_name'] ?? ''
                ],
                'convert_time' => date('Y-m-d H:i:s'),
                'operator_name' => $this->getOperatorName($operatorId)
            ];

            // 发送给客户负责人
            $recipientIds = [$customerData['owner_user_id'] ?? $operatorId];

            // 调试信息
            echo "  [DEBUG] 发送数据: " . json_encode($variables, JSON_UNESCAPED_UNICODE) . "\n";
            echo "  [DEBUG] 接收人: " . json_encode($recipientIds) . "\n";

            $result = $this->sendNotification(
                'crm_lead_convert',
                $recipientIds,
                $variables,
                'crm',
                (string)($customerData['id'] ?? 0)
            );

            echo "  [DEBUG] 发送结果: " . ($result ? "成功 (ID: $result)" : "失败") . "\n";
            return $result;

        } catch (\Exception $e) {
            Log::error('发送线索转化通知失败: ' . $e->getMessage(), [
                'lead_data' => $leadData,
                'customer_data' => $customerData,
                'operator_id' => $operatorId
            ]);
            return false;
        }
    }

    /**
     * 发送商机转化通知
     *
     * @param array $businessData 商机数据
     * @param array $contractData 合同数据
     * @param int $operatorId 操作人ID
     * @return bool|int 发送结果
     */
    public function sendBusinessConvertNotification(array $businessData, array $contractData, int $operatorId)
    {
        try {
            echo "[DEBUG] 开始发送商机转化通知\n";
            echo "  [DEBUG] 商机数据: " . json_encode($businessData, JSON_UNESCAPED_UNICODE) . "\n";
            echo "  [DEBUG] 合同数据: " . json_encode($contractData, JSON_UNESCAPED_UNICODE) . "\n";

            // 构建通知变量
            $variables = [
                'business_name' => $businessData['business_name'] ?? '',
                'customer_name' => $businessData['customer_name'] ?? '',
                'contract_no' => $contractData['contract_no'] ?? '',
                'contract_amount' => $contractData['contract_amount'] ?? 0,
                'operator_name' => $this->getOperatorName($operatorId),
                'convert_time' => date('Y-m-d H:i:s')
            ];

            // 发送给合同负责人
            $recipientIds = [$contractData['owner_id'] ?? $operatorId];

            // 调试信息
            echo "  [DEBUG] 发送数据: " . json_encode($variables, JSON_UNESCAPED_UNICODE) . "\n";
            echo "  [DEBUG] 接收人: " . json_encode($recipientIds) . "\n";

            $result = $this->sendNotification(
                'crm_business_convert',
                $recipientIds,
                $variables,
                'crm',
                (string)($contractData['id'] ?? 0)
            );

            echo "  [DEBUG] 发送结果: " . ($result ? "成功 (ID: $result)" : "失败") . "\n";
            return $result;

        } catch (\Exception $e) {
            Log::error('发送商机转化通知失败: ' . $e->getMessage(), [
                'business_data' => $businessData,
                'contract_data' => $contractData,
                'operator_id' => $operatorId
            ]);
            return false;
        }
    }

    /**
     * 发送回款审批提交通知
     *
     * @param int $receivableId 回款记录ID
     * @param int $submitterId 提交人ID
     * @param int $instanceId 工作流实例ID
     * @return bool|int 发送结果
     */
    public function sendReceivableApprovalSubmitNotification(int $receivableId, int $submitterId, int $instanceId)
    {
        try {
            // TODO: 实现回款审批提交通知
            return true;
        } catch (\Exception $e) {
            Log::error('发送回款审批提交通知失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送回款审批通过通知
     *
     * @param int $receivableId 回款记录ID
     * @param string $remark 审批意见
     * @return bool|int 发送结果
     */
    public function sendReceivableApprovalPassNotification(int $receivableId, string $remark)
    {
        try {
            // TODO: 实现回款审批通过通知
            return true;
        } catch (\Exception $e) {
            Log::error('发送回款审批通过通知失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送回款审批驳回通知
     *
     * @param int $receivableId 回款记录ID
     * @param string $remark 审批意见
     * @return bool|int 发送结果
     */
    public function sendReceivableApprovalRejectNotification(int $receivableId, string $remark)
    {
        try {
            // TODO: 实现回款审批驳回通知
            return true;
        } catch (\Exception $e) {
            Log::error('发送回款审批驳回通知失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 发送客户分配通知
     * 
     * @param array $customerData 客户数据
     * @param int $fromUserId 原负责人ID
     * @param int $toUserId 新负责人ID
     * @param int $operatorId 操作人ID
     * @return bool
     */
    public function sendCustomerAssignNotification(array $customerData, int $fromUserId, int $toUserId, int $operatorId): bool
    {
        try {
            // 构建符合模板字段映射的数据结构
            $variables = [
                'customer' => [
                    'customer_name' => $customerData['customer_name'] ?? '',
                    'phone' => $customerData['phone'] ?? ''
                ],
                'assign_time' => date('Y-m-d H:i:s'),
                'assigner_name' => $this->getOperatorName($operatorId)
            ];

            // 发送给新负责人
            $recipientIds = [$toUserId];

            return $this->sendNotification(
                'crm_customer_assign',
                $recipientIds,
                $variables,
                'crm',
                (string)($customerData['id'] ?? 0)
            );
            
        } catch (\Exception $e) {
            Log::error('发送客户分配通知失败: ' . $e->getMessage(), [
                'customer_data' => $customerData,
                'from_user_id' => $fromUserId,
                'to_user_id' => $toUserId,
                'operator_id' => $operatorId
            ]);
            return false;
        }
    }
    
    /**
     * 发送商机阶段变更通知
     * 
     * @param array $businessData 商机数据
     * @param string $oldStage 原阶段名称
     * @param string $newStage 新阶段名称
     * @param string $changeReason 变更原因
     * @param int $operatorId 操作人ID
     * @return bool
     */
    public function sendBusinessStageChangeNotification(array $businessData, string $oldStage, string $newStage, string $changeReason, int $operatorId): bool
    {
        try {
            // 构建符合模板字段映射的数据结构
            $variables = [
                'business' => [
                    'business_name' => $businessData['business_name'] ?? ''
                ],
                'old_stage' => $oldStage,
                'new_stage' => $newStage,
                'change_reason' => $changeReason,
                'operator_name' => $this->getOperatorName($operatorId)
            ];
            
            // 发送给商机负责人
            $recipientIds = [$businessData['owner_user_id'] ?? $operatorId];
            
            return $this->sendNotification(
                'crm_business_stage_change',
                $recipientIds,
                $variables,
                'crm',
                (string)$businessData['id']
            );
            
        } catch (\Exception $e) {
            Log::error('发送商机阶段变更通知失败: ' . $e->getMessage(), [
                'business_data' => $businessData,
                'old_stage' => $oldStage,
                'new_stage' => $newStage,
                'operator_id' => $operatorId
            ]);
            return false;
        }
    }
    
    /**
     * 发送报价单创建通知
     * 
     * @param array $quotationData 报价单数据
     * @param int $operatorId 操作人ID
     * @return bool
     */
    public function sendQuotationCreateNotification(array $quotationData, int $operatorId): bool
    {
        try {
            // 构建符合模板字段映射的数据结构
            $variables = [
                'quotation' => [
                    'quotation_no' => $quotationData['quotation_no'] ?? '',
                    'final_amount' => number_format((float)($quotationData['final_amount'] ?? 0), 2)
                ],
                'customer' => [
                    'customer_name' => $quotationData['customer_name'] ?? ''
                ],
                'create_time' => date('Y-m-d H:i:s'),
                'creator_name' => $this->getOperatorName($operatorId)
            ];
            
            // 发送给报价单负责人和相关人员
            $recipientIds = [$quotationData['owner_user_id'] ?? $operatorId];
            
            return $this->sendNotification(
                'crm_quotation_create',
                $recipientIds,
                $variables,
                'crm',
                (string)$quotationData['id']
            );
            
        } catch (\Exception $e) {
            Log::error('发送报价单创建通知失败: ' . $e->getMessage(), [
                'quotation_data' => $quotationData,
                'operator_id' => $operatorId
            ]);
            return false;
        }
    }
    
    /**
     * 发送合同审批通知
     * 
     * @param array $contractData 合同数据
     * @param array $approverIds 审批人ID列表
     * @param int $operatorId 操作人ID
     * @return bool
     */
    public function sendContractApprovalNotification(array $contractData, array $approverIds, int $operatorId): bool
    {
        try {
            // 构建符合模板字段映射的数据结构
            $variables = [
                'contract' => [
                    'contract_no' => $contractData['contract_no'] ?? '',
                    'contract_amount' => number_format((float)($contractData['contract_amount'] ?? 0), 2)
                ],
                'customer' => [
                    'customer_name' => $contractData['customer_name'] ?? ''
                ],
                'submit_time' => date('Y-m-d H:i:s'),
                'submitter_name' => $this->getOperatorName($operatorId)
            ];
            
            return $this->sendNotification(
                'crm_contract_approval',
                $approverIds,
                $variables,
                'crm',
                (string)$contractData['id']
            );
            
        } catch (\Exception $e) {
            Log::error('发送合同审批通知失败: ' . $e->getMessage(), [
                'contract_data' => $contractData,
                'approver_ids' => $approverIds,
                'operator_id' => $operatorId
            ]);
            return false;
        }
    }
    
    /**
     * 发送通知的通用方法
     *
     * @param string $templateCode 模板编码
     * @param array $recipientIds 接收人ID列表
     * @param array $variables 变量数据
     * @param string $moduleCode 模块编码
     * @param string $businessId 业务ID
     * @return bool
     */
    private function sendNotification(string $templateCode, array $recipientIds, array $variables, string $moduleCode, string $businessId): bool
    {
        try {
            // 解析模板编码为模块和动作
            $parts = explode('_', $templateCode, 2);
            if (count($parts) !== 2) {
                Log::error('模板编码格式错误: ' . $templateCode);
                return false;
            }

            $module = $parts[0];
            $action = $parts[1];

            echo "  [DEBUG] 模板解析: module=$module, action=$action\n";
            echo "  [DEBUG] 调用参数: " . json_encode([
                'module' => $module,
                'action' => $action,
                'variables' => $variables,
                'recipients' => $recipientIds,
                'options' => ['module_code' => $moduleCode, 'business_id' => $businessId]
            ], JSON_UNESCAPED_UNICODE) . "\n";

            // 测试 getNestedValue 方法
            echo "  [DEBUG] 测试变量提取:\n";
            $testPaths = ['lead.lead_name', 'customer.customer_name'];
            foreach ($testPaths as $path) {
                $value = $this->testGetNestedValue($variables, $path);
                echo "    路径 '$path' => " . json_encode($value) . "\n";
            }

            // 使用NoticeDispatcherService的send方法
            $result = $this->noticeDispatcher->send(
                $module,
                $action,
                $variables,
                $recipientIds,
                [
                    'module_code' => $moduleCode,
                    'business_id' => $businessId
                ]
            );

            echo "  [DEBUG] NoticeDispatcher返回: " . json_encode($result) . "\n";

            return $result !== false;
        } catch (\Exception $e) {
            Log::error('发送通知失败: ' . $e->getMessage(), [
                'template_code' => $templateCode,
                'recipient_ids' => $recipientIds,
                'variables' => $variables,
                'module_code' => $moduleCode,
                'business_id' => $businessId
            ]);
            return false;
        }
    }
    
    /**
     * 获取操作人姓名
     * 
     * @param int $operatorId 操作人ID
     * @return string
     */
    private function getOperatorName(int $operatorId): string
    {
        try {
            // 这里应该从用户表获取用户姓名，暂时返回默认值
            $user = \think\facade\Db::name('system_user')
                ->where('id', $operatorId)
                ->field('real_name,username')
                ->find();

            return $user['real_name'] ?? $user['username'] ?? '系统用户';
        } catch (\Exception $e) {
            Log::warning('获取操作人姓名失败: ' . $e->getMessage(), ['operator_id' => $operatorId]);
            return '系统用户';
        }
    }

    /**
     * 测试 getNestedValue 方法（调试用）
     */
    private function testGetNestedValue($data, string $path)
    {
        if (empty($path)) {
            return null;
        }

        // 首先检查是否有直接匹配的键（适用于扁平结构）
        $keys = explode('.', $path);
        $lastKey = end($keys);

        // 如果数据中直接包含了与最后一个键匹配的值，直接返回
        if (is_array($data) && array_key_exists($lastKey, $data)) {
            return $data[$lastKey];
        }

        // 尝试按照路径逐层查找（适用于嵌套结构）
        $value = $data;

        foreach ($keys as $key) {
            if (is_array($value) && array_key_exists($key, $value)) {
                $value = $value[$key];
            } elseif (is_object($value) && property_exists($value, $key)) {
                $value = $value->$key;
            } else {
                return null;
            }
        }

        return $value;
    }

    /**
     * 发送报价单审批提交通知
     *
     * @param int $quotationId 报价单ID
     * @param int $submitterId 提交人ID
     * @param int $instanceId 工作流实例ID
     * @return bool
     */
    public function sendQuotationApprovalSubmitNotification(int $quotationId, int $submitterId, int $instanceId): bool
    {
        try {
            // 获取报价单信息
            $quotation = \think\facade\Db::table('crm_quotation')->where('id', $quotationId)->find();
            if (!$quotation) {
                Log::error("报价单不存在: ID={$quotationId}");
                return false;
            }

            // 获取客户信息
            $customer = \think\facade\Db::table('crm_customer')->where('id', $quotation['customer_id'])->find();

            // 构建通知变量
            $variables = [
                'quotation' => [
                    'quotation_no' => $quotation['quotation_no'],
                    'final_amount' => number_format($quotation['final_amount'], 2)
                ],
                'customer' => [
                    'customer_name' => $customer['customer_name'] ?? '未知客户'
                ],
                'submit_time' => date('Y-m-d H:i:s'),
                'submitter_name' => $this->getOperatorName($submitterId),
                'instance_id' => $instanceId
            ];

            // 获取审批人列表
            $approverIds = $this->getApprovalRecipients($instanceId);

            return $this->sendNotification(
                'crm_quotation_approval_submit',
                $approverIds,
                $variables,
                'crm',
                (string)$quotationId
            );

        } catch (\Exception $e) {
            Log::error('发送报价单审批提交通知失败: ' . $e->getMessage(), [
                'quotation_id' => $quotationId,
                'submitter_id' => $submitterId,
                'instance_id' => $instanceId
            ]);
            return false;
        }
    }

    /**
     * 发送合同审批提交通知
     *
     * @param int $contractId 合同ID
     * @param int $submitterId 提交人ID
     * @param int $instanceId 工作流实例ID
     * @return bool
     */
    public function sendContractApprovalSubmitNotification(int $contractId, int $submitterId, int $instanceId): bool
    {
        try {
            // 获取合同信息
            $contract = \think\facade\Db::table('crm_contract')->where('id', $contractId)->find();
            if (!$contract) {
                Log::error("合同不存在: ID={$contractId}");
                return false;
            }

            // 获取客户信息
            $customer = \think\facade\Db::table('crm_customer')->where('id', $contract['customer_id'])->find();

            // 构建通知变量
            $variables = [
                'contract' => [
                    'contract_no' => $contract['contract_no'],
                    'contract_amount' => number_format($contract['contract_amount'], 2)
                ],
                'customer' => [
                    'customer_name' => $customer['customer_name'] ?? '未知客户'
                ],
                'submit_time' => date('Y-m-d H:i:s'),
                'submitter_name' => $this->getOperatorName($submitterId),
                'instance_id' => $instanceId
            ];

            // 获取审批人列表
            $approverIds = $this->getApprovalRecipients($instanceId);

            return $this->sendNotification(
                'crm_contract_approval_submit',
                $approverIds,
                $variables,
                'crm',
                (string)$contractId
            );

        } catch (\Exception $e) {
            Log::error('发送合同审批提交通知失败: ' . $e->getMessage(), [
                'contract_id' => $contractId,
                'submitter_id' => $submitterId,
                'instance_id' => $instanceId
            ]);
            return false;
        }
    }

    /**
     * 发送报价单审批通过通知
     *
     * @param int $quotationId 报价单ID
     * @param string $remark 审批备注
     * @return bool
     */
    public function sendQuotationApprovalPassNotification(int $quotationId, string $remark = ''): bool
    {
        try {
            // 获取报价单信息
            $quotation = \think\facade\Db::table('crm_quotation')->where('id', $quotationId)->find();
            if (!$quotation) {
                Log::error("报价单不存在: ID={$quotationId}");
                return false;
            }

            // 获取客户信息
            $customer = \think\facade\Db::table('crm_customer')->where('id', $quotation['customer_id'])->find();

            // 构建通知变量
            $variables = [
                'quotation' => [
                    'quotation_no' => $quotation['quotation_no'],
                    'final_amount' => number_format($quotation['final_amount'], 2)
                ],
                'customer' => [
                    'customer_name' => $customer['customer_name'] ?? '未知客户'
                ],
                'approval_time' => date('Y-m-d H:i:s'),
                'remark' => $remark
            ];

            // 发送通知给提交人
            $recipients = [$quotation['created_id']];

            return $this->sendNotification(
                'crm_quotation_approval_pass',
                $recipients,
                $variables,
                'crm',
                (string)$quotationId
            );

        } catch (\Exception $e) {
            Log::error('发送报价单审批通过通知失败: ' . $e->getMessage(), [
                'quotation_id' => $quotationId,
                'remark' => $remark
            ]);
            return false;
        }
    }

    /**
     * 发送报价单审批驳回通知
     *
     * @param int $quotationId 报价单ID
     * @param string $remark 驳回原因
     * @return bool
     */
    public function sendQuotationApprovalRejectNotification(int $quotationId, string $remark = ''): bool
    {
        try {
            // 获取报价单信息
            $quotation = \think\facade\Db::table('crm_quotation')->where('id', $quotationId)->find();
            if (!$quotation) {
                Log::error("报价单不存在: ID={$quotationId}");
                return false;
            }

            // 获取客户信息
            $customer = \think\facade\Db::table('crm_customer')->where('id', $quotation['customer_id'])->find();

            // 构建通知变量
            $variables = [
                'quotation' => [
                    'quotation_no' => $quotation['quotation_no'],
                    'final_amount' => number_format($quotation['final_amount'], 2)
                ],
                'customer' => [
                    'customer_name' => $customer['customer_name'] ?? '未知客户'
                ],
                'reject_time' => date('Y-m-d H:i:s'),
                'reject_reason' => $remark
            ];

            // 发送通知给提交人
            $recipients = [$quotation['created_id']];

            return $this->sendNotification(
                'crm_quotation_approval_reject',
                $recipients,
                $variables,
                'crm',
                (string)$quotationId
            );

        } catch (\Exception $e) {
            Log::error('发送报价单审批驳回通知失败: ' . $e->getMessage(), [
                'quotation_id' => $quotationId,
                'remark' => $remark
            ]);
            return false;
        }
    }

    /**
     * 发送合同审批通过通知
     *
     * @param int $contractId 合同ID
     * @param string $remark 审批备注
     * @return bool
     */
    public function sendContractApprovalPassNotification(int $contractId, string $remark = ''): bool
    {
        try {
            // 获取合同信息
            $contract = \think\facade\Db::table('crm_contract')->where('id', $contractId)->find();
            if (!$contract) {
                Log::error("合同不存在: ID={$contractId}");
                return false;
            }

            // 获取客户信息
            $customer = \think\facade\Db::table('crm_customer')->where('id', $contract['customer_id'])->find();

            // 构建通知变量
            $variables = [
                'contract' => [
                    'contract_no' => $contract['contract_no'],
                    'contract_amount' => number_format($contract['contract_amount'], 2)
                ],
                'customer' => [
                    'customer_name' => $customer['customer_name'] ?? '未知客户'
                ],
                'approval_time' => date('Y-m-d H:i:s'),
                'remark' => $remark
            ];

            // 发送通知给提交人
            $recipients = [$contract['created_id']];

            return $this->sendNotification(
                'crm_contract_approval_pass',
                $recipients,
                $variables,
                'crm',
                (string)$contractId
            );

        } catch (\Exception $e) {
            Log::error('发送合同审批通过通知失败: ' . $e->getMessage(), [
                'contract_id' => $contractId,
                'remark' => $remark
            ]);
            return false;
        }
    }

    /**
     * 发送合同审批驳回通知
     *
     * @param int $contractId 合同ID
     * @param string $remark 驳回原因
     * @return bool
     */
    public function sendContractApprovalRejectNotification(int $contractId, string $remark = ''): bool
    {
        try {
            // 获取合同信息
            $contract = \think\facade\Db::table('crm_contract')->where('id', $contractId)->find();
            if (!$contract) {
                Log::error("合同不存在: ID={$contractId}");
                return false;
            }

            // 获取客户信息
            $customer = \think\facade\Db::table('crm_customer')->where('id', $contract['customer_id'])->find();

            // 构建通知变量
            $variables = [
                'contract' => [
                    'contract_no' => $contract['contract_no'],
                    'contract_amount' => number_format($contract['contract_amount'], 2)
                ],
                'customer' => [
                    'customer_name' => $customer['customer_name'] ?? '未知客户'
                ],
                'reject_time' => date('Y-m-d H:i:s'),
                'reject_reason' => $remark
            ];

            // 发送通知给提交人
            $recipients = [$contract['created_id']];

            return $this->sendNotification(
                'crm_contract_approval_reject',
                $recipients,
                $variables,
                'crm',
                (string)$contractId
            );

        } catch (\Exception $e) {
            Log::error('发送合同审批驳回通知失败: ' . $e->getMessage(), [
                'contract_id' => $contractId,
                'remark' => $remark
            ]);
            return false;
        }
    }

    /**
     * 获取审批接收人列表
     *
     * @param int $instanceId 工作流实例ID
     * @return array
     */
    private function getApprovalRecipients(int $instanceId): array
    {
        // TODO: 从工作流任务表获取当前审批人
        return [1, 2]; // 临时返回固定值
    }
}
