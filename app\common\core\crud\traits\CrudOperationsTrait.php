<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use app\common\exception\BusinessException;

/**
 * CRUD操作相关功能
 */
trait CrudOperationsTrait
{
	/**
	 * 获取列表
	 */
	public function getList(
		array $where = [], $order = [], array $with = [], bool $applyDataPermission = null, string $scene = 'list'
	)
	{
		$query = $this->buildBaseQuery($where, $with, $applyDataPermission);
		$query = $this->applyOrder($query, $order);
		
		$result = $query->hidden($this->hiddenFields, true)
		                ->select();
		
		// 应用字段场景
		return method_exists($this, 'applyFieldScene')
			? $this->applyFieldScene($result, $scene)
			: $result;
	}
	
	/**
	 * 获取分页列表
	 */
	public function getPageList(
		array $where = [], $order = [], int $page = 1, int $limit = 10, array $with = [],
		bool $applyDataPermission = null, string $scene = 'list'
	)
	{
		// 限制最大页码和每页数量，防止大量数据查询
		$page  = max(1, min($page, 1000));
		$limit = max(1, min($limit, 100));
		
		$query = $this->buildBaseQuery($where, $with, $applyDataPermission);
		$query = $this->applyOrder($query, $order);
		
		$result = $query->hidden($this->hiddenFields, true)
		                ->page($page, $limit)
		                ->select();
		
		// 应用字段场景
		return method_exists($this, 'applyFieldScene')
			? $this->applyFieldScene($result, $scene)
			: $result;
	}
	
	/**
	 * 获取记录数
	 */
	public function getCount(array $where = [], bool $applyDataPermission = null): int
	{
		$query = $this->buildBaseQuery($where, [], $applyDataPermission);
		
		return $query->count();
	}
	
	/**
	 * 获取单条记录
	 */
	public function getOne(array $where = [], array $with = [], bool $applyDataPermission = null, string $scene = null)
	{
		$query = $this->buildBaseQuery($where, $with, $applyDataPermission);
		
		$result = $query->hidden($this->hiddenFields, true)
		                ->findOrEmpty();
		
		// 如果指定了场景并且不是空结果，应用字段场景
		if ($scene && !$result->isEmpty() && method_exists($this, 'applyFieldScene')) {
			return $this->applyFieldScene($result, $scene);
		}
		
		return $result;
	}
	
	/**
	 * 创建新的模型实例
	 * 避免状态污染，每次操作都使用全新的模型实例
	 *
	 * @return \think\Model
	 */
	protected function createFreshModel(): \think\Model
	{
		$modelClass = get_class($this->model);

		// 记录模型实例创建（用于调试）
		if (function_exists('app') && app()->isDebug()) {
			trace('CrudService创建新模型实例: ' . $modelClass, 'debug');
		}

		return new $modelClass();
	}

	/**
	 * 添加记录 - 优化版本，避免状态污染
	 *
	 * @param array $data 数据
	 * @return int 返回新增记录的ID
	 *
	 */
	public function add(array $data): int
	{
		$this->checkModel();

		// 验证数据
		if (method_exists($this, 'validateData')) {
			$data = $this->validateData($data, 'add');
		}

		// 过滤数据
		$safeData = $this->filterData($data);

		// 创建新的模型实例，避免状态污染
		$freshModel = $this->createFreshModel();

		// 记录操作日志
		if (function_exists('app') && app()->isDebug()) {
			trace('CrudService::add 使用新模型实例创建记录', 'debug');
		}

		return $freshModel->saveByCreate($safeData);
	}
	
	/**
	 * 编辑记录
	 *
	 * @param array $data  数据
	 * @param array $where 条件
	 * @return bool
	 * @throws \Exception
	 */
	public function edit(array $data, array $where): bool
	{
		$this->checkModel();
		
		// 验证数据
		if (method_exists($this, 'validateData')) {
			$data = $this->validateData($data, 'edit');
		}
		
		// 过滤数据
		$safeData = $this->filterData($data);
		
		$info = $this->getOne($where);
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在或无权限操作');
		}
		
		// 检查字段是否禁止更新
		$data = [];
		foreach ($safeData as $key => $value) {
			if (in_array($key, $this->forbidUpdateFields)) {
				continue;
			}
			
			$data[$key] = $value;
		}
		
		return $info->saveByUpdate($data);
	}
	
	/**
	 * 删除记录
	 */
	public function delete(array $ids)
	{
		$info = $this->getList([
			[
				'id',
				'in',
				$ids
			]
		]);
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		
		return $info->delete();
	}

	/**
	 * 批量添加记录 - 新增方法，专门处理批量操作
	 * 每条记录都使用新的模型实例，避免状态污染
	 *
	 * @param array $dataList 数据列表
	 * @return array 返回新增记录的ID列表
	 */
	public function batchAdd(array $dataList): array
	{
		$this->checkModel();

		$results = [];

		foreach ($dataList as $index => $data) {
			try {
				// 验证数据
				if (method_exists($this, 'validateData')) {
					$data = $this->validateData($data, 'add');
				}

				// 过滤数据
				$safeData = $this->filterData($data);

				// 为每条记录创建新的模型实例
				$freshModel = $this->createFreshModel();
				$id = $freshModel->saveByCreate($safeData);

				$results[] = $id;

				// 记录成功日志
				if (function_exists('app') && app()->isDebug()) {
					trace("CrudService::batchAdd 第{$index}条记录创建成功，ID: {$id}", 'debug');
				}

			} catch (\Exception $e) {
				// 记录失败日志
				if (function_exists('app') && app()->isDebug()) {
					trace("CrudService::batchAdd 第{$index}条记录创建失败: " . $e->getMessage(), 'error');
				}

				// 可以选择继续处理其他记录，或者抛出异常
				throw new BusinessException("第{$index}条记录创建失败: " . $e->getMessage());
			}
		}

		return $results;
	}

	/**
	 * 搜索记录
	 */
	public function search(
		array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null,
		string $scene = 'list'
	)
	{
		$this->checkModel();
		
		// 使用默认搜索字段配置
		if (empty($searchFields) && !empty($this->defaultSearchFields)) {
			$searchFields = $this->defaultSearchFields;
		}
		
		// 限制最大页码和每页数量，防止大量数据查询
		$page  = (int)($params['page'] ?? 1);
		$page  = max(1, min($page, 1000));
		$limit = (int)($params['limit'] ?? 10);
		$limit = max(1, min($limit, 100));
		
		// 处理搜索条件
		$where = $this->buildSearchConditions($params, $searchFields);
		
		// 处理排序
		$order = $this->buildSearchOrder($params);
		
		// 获取总数
		$total = $this->getCount($where, $applyDataPermission);
		
		// 获取分页数据
		$list = $this->getPageList($where, $order, $page, $limit, $with, $applyDataPermission, $scene);
		
		return [
			'list'      => $list,
			'total'     => $total,
			'page'      => $page,
			'limit'     => $limit,
			'last_page' => ceil($total / $limit)
		];
	}
	
	/**
	 * 获取下拉选项
	 */
	public function getSelectOptions(
		array $where = [], string $labelField = 'name', string $valueField = 'id', $order = [], string $scene = 'select'
	)
	{
		$this->checkModel();
		
		// 检查字段是否有效
		if (!$this->isValidField($labelField) || !$this->isValidField($valueField)) {
			throw new BusinessException('无效的字段名');
		}
		
		$query = $this->buildBaseQuery($where);
		$query = $this->applyOrder($query, $order);
		
		$list = $query->field([
			$labelField,
			$valueField
		])
		              ->select();
		
		// 应用字段场景可能会过滤掉字段，所以要先转成数组
		if (method_exists($this, 'applyFieldScene')) {
			$list = $this->applyFieldScene($list, $scene);
		}
		
		$result = [];
		foreach ($list as $item) {
			$result[] = [
				'label' => is_array($item)
					? $item[$labelField]
					: $item[$labelField],
				'value' => is_array($item)
					? $item[$valueField]
					: $item[$valueField]
			];
		}
		
		return $result;
	}
	
	/**
	 * 获取详情
	 */
	public function getDetail($id, $with = [], string $scene = 'detail')
	{
		$info = $this->getOne(['id' => $id], $with);
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		
		// 应用字段场景
		return method_exists($this, 'applyFieldScene')
			? $this->applyFieldScene($info, $scene)
			: $info;
	}
	
	/**
	 * 更新单个字段
	 */
	public function updateField($id, string $field, $value): bool
	{
		$this->checkModel();
		
		// 检查字段是否有效且存在
		if (!$this->isValidField($field) || !$this->model->hasField($field)) {
			throw new BusinessException('无效的字段名或字段不存在');
		}
		
		// 检查字段是否允许更新
		if (!empty($this->allowUpdateFields) && !in_array($field, $this->allowUpdateFields)) {
			throw new BusinessException('该字段不允许单独更新');
		}
		
		// 检查字段是否禁止更新
		if (in_array($field, $this->forbidUpdateFields)) {
			throw new BusinessException('该字段不允许更新');
		}
		
		/*// 验证字段数据
		if (method_exists($this, 'validateData')) {
			$this->validateData([$field => $value], 'updateField_' . $field);
		}*/
		
		$info = $this->getOne(['id' => $id]);
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在或无权限操作');
		}
		
		// 过滤值
		$value = is_string($value)
			? $this->filterString($value)
			: $value;
		
		return $info->saveByUpdate([$field => $value]);
	}
} 