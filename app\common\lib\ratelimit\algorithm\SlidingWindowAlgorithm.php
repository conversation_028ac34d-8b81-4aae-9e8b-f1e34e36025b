<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\algorithm;

use app\common\lib\ratelimit\storage\StorageInterface;

/**
 * 滑动窗口限流算法
 * 
 * 滑动窗口算法通过将时间窗口分成多个小窗口，并考虑过去一段时间内的请求情况来判断是否允许通过
 * 优点：比固定窗口更平滑，避免了边界突刺问题
 * 缺点：实现复杂，需要更多存储空间
 */
class SlidingWindowAlgorithm implements AlgorithmInterface
{
    /**
     * 存储适配器
     */
    protected StorageInterface $storage;
    
    /**
     * 构造函数
     *
     * @param StorageInterface $storage 存储适配器
     */
    public function __construct(StorageInterface $storage)
    {
        $this->storage = $storage;
    }
    
    /**
     * 生成滑动窗口的键名
     *
     * @param string $key 基础键名
     * @param string $identifier 标识符
     * @param int $timestamp 时间戳
     * @return string 完整键名
     */
    protected function generateKey(string $key, string $identifier, int $timestamp): string
    {
        return sprintf('sliding:%s:%s:%d', $key, $identifier, $timestamp);
    }
    
    /**
     * 获取当前滑动窗口的时间分片
     * 
     * @param int $timeWindow 时间窗口(秒)
     * @param int $slices 窗口分片数量
     * @return array [当前分片时间戳, 分片大小(秒)]
     */
    protected function getCurrentTimeSlice(int $timeWindow, int $slices = 10): array
    {
        $now = time();
        $sliceSize = max(1, (int)($timeWindow / $slices)); // 每个分片的秒数，至少为1秒
        $currentSlice = floor($now / $sliceSize) * $sliceSize; // 当前分片的起始时间戳
        
        return [$currentSlice, $sliceSize];
    }
    
    /**
     * 检查是否允许请求通过
     *
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @return bool 是否允许通过
     */
    public function allow(string $key, string $identifier, int $limitCount, int $timeWindow): bool
    {
        // 获取当前时间分片
        [$currentSlice, $sliceSize] = $this->getCurrentTimeSlice($timeWindow);
        
        // 计算窗口内的所有分片键
        $windowStart = $currentSlice - $timeWindow + $sliceSize;
        $sliceKeys = [];
        
        // 生成所有需要检查的分片键
        for ($timestamp = $windowStart; $timestamp <= $currentSlice; $timestamp += $sliceSize) {
            $sliceKeys[] = $this->generateKey($key, $identifier, $timestamp);
        }
        
        // 当前分片的键
        $currentKey = $this->generateKey($key, $identifier, $currentSlice);
        
        // 递增当前分片计数
        $this->storage->increment($currentKey, $timeWindow);
        
        // 获取所有分片的计数
        $counts = $this->storage->batchGet($sliceKeys);
        
        // 计算总请求数
        $totalCount = array_sum($counts);
        
        // 检查是否超过限制
        return $totalCount <= $limitCount;
    }
    
    /**
     * 获取剩余可用请求次数
     *
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @return int 剩余可用请求次数
     */
    public function getRemainingLimit(string $key, string $identifier, int $limitCount, int $timeWindow): int
    {
        // 获取当前时间分片
        [$currentSlice, $sliceSize] = $this->getCurrentTimeSlice($timeWindow);
        
        // 计算窗口内的所有分片键
        $windowStart = $currentSlice - $timeWindow + $sliceSize;
        $sliceKeys = [];
        
        // 生成所有需要检查的分片键
        for ($timestamp = $windowStart; $timestamp <= $currentSlice; $timestamp += $sliceSize) {
            $sliceKeys[] = $this->generateKey($key, $identifier, $timestamp);
        }
        
        // 获取所有分片的计数
        $counts = $this->storage->batchGet($sliceKeys);
        
        // 计算总请求数
        $totalCount = array_sum($counts);
        
        // 计算剩余可用次数
        $remaining = $limitCount - $totalCount;
        return max(0, $remaining);
    }
    
    /**
     * 获取重置时间（秒）
     * 
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $timeWindow 时间窗口(秒)
     * @return int 重置时间
     */
    public function getResetTime(string $key, string $identifier, int $timeWindow): int
    {
        // 获取当前时间分片
        [$currentSlice, $sliceSize] = $this->getCurrentTimeSlice($timeWindow);
        
        // 计算窗口中最早的分片
        $earliestSlice = $currentSlice - $timeWindow + $sliceSize;
        $earliestKey = $this->generateKey($key, $identifier, $earliestSlice);
        
        // 获取该分片的TTL
        $ttl = $this->storage->ttl($earliestKey);
        
        // 如果最早的分片已经不存在，则返回一个分片的时间作为重置时间
        return $ttl > 0 ? $ttl : $sliceSize;
    }
} 