<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\algorithm;

use app\common\lib\ratelimit\storage\StorageInterface;

/**
 * 固定窗口限流算法
 * 
 * 固定窗口算法在每个时间窗口开始时重置计数器
 * 优点：实现简单，资源占用少
 * 缺点：可能出现边界突刺问题，例如在一个窗口结束和下一个窗口开始的临界点
 */
class FixedWindowAlgorithm implements AlgorithmInterface
{
    /**
     * 存储适配器
     */
    protected StorageInterface $storage;
    
    /**
     * 构造函数
     *
     * @param StorageInterface $storage 存储适配器
     */
    public function __construct(StorageInterface $storage)
    {
        $this->storage = $storage;
    }
    
    /**
     * 生成固定窗口的键名
     *
     * @param string $key 基础键名
     * @param string $identifier 标识符
     * @param int $timeWindow 时间窗口(秒)
     * @return string 完整键名
     */
    protected function generateKey(string $key, string $identifier, int $timeWindow): string
    {
        // 计算当前时间窗口的起始时间戳
        $windowStart = floor(time() / $timeWindow) * $timeWindow;
        return sprintf('fixed:%s:%s:%d', $key, $identifier, $windowStart);
    }
    
    /**
     * 检查是否允许请求通过
     *
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @return bool 是否允许通过
     */
    public function allow(string $key, string $identifier, int $limitCount, int $timeWindow): bool
    {
        $storageKey = $this->generateKey($key, $identifier, $timeWindow);
        
        // 递增计数并设置过期时间
        $currentCount = $this->storage->increment($storageKey, $timeWindow);
        
        // 检查是否超过限制
        return $currentCount <= $limitCount;
    }
    
    /**
     * 获取剩余可用请求次数
     *
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     * @return int 剩余可用请求次数
     */
    public function getRemainingLimit(string $key, string $identifier, int $limitCount, int $timeWindow): int
    {
        $storageKey = $this->generateKey($key, $identifier, $timeWindow);
        $currentCount = $this->storage->get($storageKey);
        
        // 计算剩余可用次数
        $remaining = $limitCount - $currentCount;
        return max(0, $remaining);
    }
    
    /**
     * 获取重置时间（秒）
     * 
     * @param string $key 限流键名
     * @param string $identifier 限流标识符
     * @param int $timeWindow 时间窗口(秒)
     * @return int 重置时间
     */
    public function getResetTime(string $key, string $identifier, int $timeWindow): int
    {
        $storageKey = $this->generateKey($key, $identifier, $timeWindow);
        
        // 当前窗口过期时间即为重置时间
        return $this->storage->ttl($storageKey);
    }
} 