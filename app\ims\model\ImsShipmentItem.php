<?php
declare(strict_types=1);

namespace app\ims\model;

use app\common\core\base\BaseModel;
use app\crm\model\CrmProduct;

/**
 * 出货明细表模型
 */
class ImsShipmentItem extends BaseModel
{
	// 设置表名
	protected $name = 'ims_shipment_item';
	
	// 字段类型转换
	protected $type = [
		'shipment_id'  => 'integer',
		'product_id'   => 'integer',
		'quantity'     => 'float',
		'unit_price'   => 'float',
		'total_amount' => 'float',
	];
	
	protected $append = [
		'product',
		'supplier'
	];
	
	/**
	 * 获取默认搜索字段
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'shipment_id'  => ['type' => 'eq'],
			'product_id'   => ['type' => 'eq'],
			'quantity'     => ['type' => 'between'],
			'unit_price'   => ['type' => 'between'],
			'total_amount' => ['type' => 'between'],
			'created_at'   => ['type' => 'date'],
		];
	}
	
	/**
	 * 关联出货申请
	 */
	public function shipment()
	{
		return $this->belongsTo(ImsShipmentApproval::class, 'shipment_id', 'id');
	}
	
	/**
	 * 关联供应商
	 */
	public function supplier()
	{
		return $this->belongsTo(ImsSupplier::class, 'supplier_id', 'id')
		            ->bind([
			            'supplier_name' => 'name',
		            ]);
	}
	
	/**
	 * 关联产品
	 */
	public function product()
	{
		return $this->belongsTo(CrmProduct::class, 'product_id', 'id')
		            ->bind([
			            'product_name' => 'name',
		            ]);
	}
}
