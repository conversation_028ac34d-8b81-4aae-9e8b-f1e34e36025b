<?php
declare(strict_types=1);

namespace app\crm\controller\traits;

use app\crm\model\CrmContact;
use app\crm\model\CrmContract;
use app\crm\model\CrmContractReceivable;
use app\crm\model\CrmCustomer;
use app\crm\service\CustomerPermissionService;
use think\facade\Db;
use think\response\Json;

/**
 * 客户联系人操作Trait
 */
trait CustomerContactTrait
{
	/**
	 * 新增联系人
	 */
	public function addContact(): Json
	{
		$data       = $this->request->post();
		$customerId = $data['customer_id'] ?? 0;
		
		if (!$customerId) {
			return $this->error('客户ID不能为空');
		}
		
		// 验证客户访问权限 (暂时禁用，先开发功能)
		// // $permissionService = app(CustomerPermissionService::class);
		// // if (!$permissionService->validateCustomerAccess((int)$customerId, (int)get_user_id())) {
		//     // return $this->error('无权限操作此客户');
		// }
		
		try {
			// 验证客户是否存在
			$customerInfo = (new CrmCustomer())->findOrEmpty($customerId);
			
			if ($customerInfo->isEmpty()) {
				return $this->error('客户不存在');
			}
			
			$res = (new CrmContact())->saveByCreate($data);
			return $res
				? $this->success('添加成功')
				: $this->error('添加失败');
			
		}
		catch (\Exception $e) {
			return $this->error('联系人添加失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 编辑联系人
	 */
	public function editContact(): Json
	{
		$data      = $this->request->post();
		$contactId = $data['id'] ?? 0;
		
		if (!$contactId) {
			return $this->error('联系人ID不能为空');
		}
		
		// 验证联系人访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContactAccess((int)$contactId, (int)get_user_id())) {
		// return $this->error('无权限操作此联系人');
		//        }
		
		try {
			$contact = CrmContact::find($contactId);
			if (!$contact) {
				return $this->error('联系人不存在');
			}
			
			// 更新数据
			unset($data['id']);
			$data['updated_at'] = date('Y-m-d H:i:s');
			
			$contact->save($data);
			
			return $this->success('联系人更新成功', $contact);
			
		}
		catch (\Exception $e) {
			return $this->error('联系人更新失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 删除联系人
	 */
	public function deleteContact(): Json
	{
		$data      = $this->request->post();
		$contactId = $data['id'] ?? 0;
		
		if (!$contactId) {
			return $this->error('联系人ID不能为空');
		}
		
		// 验证联系人访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContactAccess((int)$contactId, (int)get_user_id())) {
		// return $this->error('无权限操作此联系人');
		//	}
		
		try {
			$contact = CrmContact::find($contactId);
			if (!$contact) {
				return $this->error('联系人不存在');
			}
			
			// 软删除
			$contact->delete();
			
			return $this->success('联系人删除成功');
			
		}
		catch (\Exception $e) {
			return $this->error('联系人删除失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 联系人列表
	 */
	public function contactList(): Json
	{
		$customerId = $this->request->param('customer_id', 0);
		$page       = $this->request->param('page', 1, 'int');
		$limit      = $this->request->param('limit', 15, 'int');
		
		if (!$customerId) {
			return $this->error('客户ID不能为空');
		}
		
		// 验证客户访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateCustomerAccess((int)$customerId, (int)get_user_id())) {
		//		return $this->error('无权限访问此客户');
		//	}
		
		try {
			$query = CrmContact::where('customer_id', $customerId)
			                   ->order('created_at desc');
			
			$total = $query->count();
			$list  = $query->page($page, $limit)
			               ->select();
			
			return $this->success('获取成功', [
				'list'  => $list,
				'total' => $total,
				'page'  => $page,
				'limit' => $limit
			]);
			
		}
		
		catch (\Exception $e) {
			return $this->error('获取联系人列表失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 联系人详情
	 */
	public function contactDetail(): Json
	{
		$contactId = $this->request->param('id', 0);
		
		if (!$contactId) {
			return $this->error('联系人ID不能为空');
		}
		
		// 验证联系人访问权限 (暂时禁用)
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContactAccess((int)$contactId, (int)get_user_id())) {
		//     return $this->error('无权限访问此联系人');
		// }
		
		try {
			$contact = CrmContact::where('id', $contactId)
			                     ->find();
			
			if (!$contact) {
				return $this->error('联系人不存在');
			}
			
			return $this->success('获取成功', $contact);
			
		}
		catch (\Exception $e) {
			return $this->error('获取联系人详情失败：' . $e->getMessage());
		}
	}
}
