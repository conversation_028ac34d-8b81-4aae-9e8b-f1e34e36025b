<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\system\service\OperationLogService;
use app\system\service\PermissionService;
use app\system\model\MenuModel;
use app\common\utils\CacheUtil;
use think\facade\App;
use think\facade\Config;
use think\Response;

/**
 * 操作日志中间件
 */
class OperationLogMiddleware
{
	/**
	 * 操作日志服务
	 *
	 * @var \app\system\service\OperationLogService
	 */
	protected $logService;
	
	/**
	 * 不需要记录日志的请求方法
	 *
	 * @var array
	 */
	protected $exceptMethods = [
		'OPTIONS',
		'HEAD'
	];
	
	/**
	 * 不需要记录日志的路由
	 *
	 * @var array
	 */
	protected $exceptRoutes = [];
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->logService    = OperationLogService::getInstance();
		$this->exceptRoutes  = Config::get('log.operation.except_routes', []);
		$this->exceptMethods = Config::get('log.operation.except_methods', [
			'OPTIONS',
			'HEAD'
		]);
	}
	
	/**
	 * 处理请求
	 *
	 * @param \think\Request $request
	 * @param \Closure       $next
	 * @return Response
	 */
	public function handle($request, \Closure $next)
	{
		// 获取请求信息
		$method = $request->method();
		
		// 如果是不需要记录的请求方法，直接跳过
		if (in_array($method, $this->exceptMethods)) {
			return $next($request);
		}
		
		// 获取当前路由
		$route = $request->pathinfo();
		
		// 如果是不需要记录的路由，直接跳过
		foreach ($this->exceptRoutes as $exceptRoute) {
			if (str_starts_with($route, $exceptRoute)) {
				return $next($request);
			}
		}
		
		// 记录开始时间
		$startTime = microtime(true);
		
		// 执行下一个中间件或控制器，获取响应
		$response = $next($request);

		// 计算执行时间
		$executionTime = intval((microtime(true) - $startTime) * 1000);

		// 获取路由信息
		$ruleName = $request->rule()->getName();
		[
			$controller,
			$action
		] = explode('@', $ruleName);

		// 新增: 权限标识解析
		$permissionService = app(PermissionService::class);
		[$module, $controllerName, $actionName] = $permissionService->parsePermissionInfo($ruleName);

		// 新增: 路由方法与权限标识映射
		$actionMap = [
			'detail' => 'show',
			'add' => 'create',
			'edit' => 'update',
			'delete' => 'delete',
			'batchDelete' => 'batchDelete',
			'updateField' => 'updateField',
			'status' => 'status',
			'import' => 'import',
			'importTemplate' => 'importTemplate',
			'downloadTemplate' => 'downloadTemplate',
			'export' => 'export',
			'index' => 'index'
		];

		$mappedAction = $actionMap[$actionName] ?? $actionName;
		$permission = strtolower("{$module}:{$controllerName}:{$mappedAction}");

		// 新增: 菜单匹配
		$menuInfo = $this->findMenuByPermissionWithCache($permission);

		// 处理请求参数，过滤敏感信息
		$params = $request->param();
		$params = $this->filterSensitiveData($params);

		// 获取用户信息
		$admin = $request->adminInfo ?? null;

		$adminData = $admin['data'] ?? [];
		$adminId   = $adminData['id'] ?? 0;
		$tenantId  = $adminData['tenant_id'] ?? 0;

		// 增强日志数据 (保持原有字段)
		$logData = [
			'admin_id'       => $adminId,
			'controller'     => $controller,        // 保持原有格式
			'action'         => $action,            // 保持原有路由方法
			'module'         => $module,            // 新增
			'permission'     => $permission,        // 新增
			'menu_id'        => $menuInfo['id'] ?? 0,     // 新增
			'menu_title'     => $menuInfo['title'] ?? '', // 新增
			'url'            => $route,
			'method'         => $method,
			'params'         => $params,
			'code'           => $response->getCode(),
			//			'result'         => $result,
			'ip'             => $request->ip(),
			'user_agent'     => $request->header('user-agent'),
			'execution_time' => $executionTime,
			'creator_id'     => $adminId,
			'tenant_id'      => $tenantId,
		];
		
		// 异步记录日志，不影响主业务流程
		try {
			$this->logService->create($logData, $tenantId);
		}
		catch (\Exception $e) {
			// 记录日志失败不影响正常业务
			App::log()
			   ->error('记录操作日志失败：' . $e->getMessage());
		}
		
		return $response;
	}
	
	/**
	 * 过滤敏感数据
	 *
	 * @param array $data
	 * @return array
	 */
	protected function filterSensitiveData(array $data): array
	{
		$sensitiveFields = Config::get('log.operation.sensitive_fields', [
			'password',
			'old_password',
			'new_password',
			'confirm_password',
			'token',
			'secret',
			'credit_card'
		]);
		
		foreach ($data as $key => $value) {
			if (in_array($key, $sensitiveFields)) {
				$data[$key] = '******';
			}
			elseif (is_array($value)) {
				$data[$key] = $this->filterSensitiveData($value);
			}
		}
		
		return $data;
	}

	/**
	 * 根据权限标识查找菜单信息
	 *
	 * @param string $permission 权限标识
	 * @return array 菜单信息数组
	 */
	private function findMenuByPermission(string $permission): array
	{
		try {
			$menuModel = app(MenuModel::class);
			$menu = $menuModel->where('name', $permission)
			                 ->where('status', 1)
			                 ->findOrEmpty();

			return $menu->isEmpty() ? [] : $menu->toArray();
		} catch (\Exception $e) {
			// 查询失败不影响主业务
			\think\facade\Log::error('菜单匹配失败: ' . $e->getMessage());
			return [];
		}
	}

	/**
	 * 带缓存的菜单匹配方法 - 使用系统封装的CacheUtil
	 *
	 * @param string $permission 权限标识
	 * @return array 菜单信息数组
	 */
	private function findMenuByPermissionWithCache(string $permission): array
	{
		$cacheKey = "menu:permission:{$permission}";

		// 使用系统封装的CacheUtil工具类，支持租户隔离
		$cached = CacheUtil::get($cacheKey, null, true);

		if ($cached !== null) {
			return $cached;
		}

		$menuInfo = $this->findMenuByPermission($permission);

		// 缓存5分钟，使用租户隔离缓存
		CacheUtil::set($cacheKey, $menuInfo, 300, true);

		return $menuInfo;
	}

	/**
	 * 清理菜单权限缓存
	 *
	 * @return bool 清理是否成功
	 */
	private function clearMenuPermissionCache(): bool
	{
		try {
			// 使用CacheUtil的标签功能清理相关缓存
			return CacheUtil::tag('menu_permission', true)->clear();
		} catch (\Exception $e) {
			\think\facade\Log::error('清理菜单权限缓存失败: ' . $e->getMessage());
			return false;
		}
	}
} 