<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmCustomerService;
use app\crm\service\CustomerSeaManagementService;
use app\system\model\AdminModel;
use think\facade\Log;
use think\response\Json;

/**
 * 公海客户控制器
 */
class CrmCustomerSeaController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CustomerSeaManagementService
	 */
	protected CustomerSeaManagementService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		// 使用单例模式获取Service实例
		$this->service = CustomerSeaManagementService::getInstance();
	}
	
	/**
	 * 获取公海客户列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->get();
		
		$params['in_sea'] = 1;
		$params['status'] = 1;
		
		// 获取列表数据
		$result = $this->service->search($params);
		
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取公海客户详情
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		try {
			$result = CrmCustomerService::getInstance()
			                            ->getCrudService()
			                            ->getOne(['id' => $id]);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			Log::error('获取客户详情失败: ' . $e->getMessage());
			return $this->error('获取失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 编辑公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function edit(int $id): Json
	{
		$result = CrmCustomerService::getInstance()
		                            ->getCrudService()
		                            ->edit($this->request->post(), [
			                            'id' => $id
		                            ]);
		return $result
			? $this->success('更新成功')
			: $this->error('更新失败');
	}
	
	/**
	 * 删除公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function delete(int $id): Json
	{
		try {
			$result = CrmCustomerService::getInstance()
			                            ->getCrudService()
			                            ->delete([$id]);
			return $this->success('删除成功', $result);
		}
		catch (\Exception $e) {
			Log::error('删除客户失败: ' . $e->getMessage());
			return $this->error('删除失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 导出公海客户数据
	 *
	 * @return Json
	 */
	public function export(): Json
	{
		$params           = $this->request->get();
		$params['in_sea'] = 1;
		
		try {
			$result = CrmCustomerService::getInstance()
			                            ->getCrudService()
			                            ->exportData($params);
			return $this->success('导出成功', $result);
		}
		catch (\Exception $e) {
			Log::error('导出客户数据失败: ' . $e->getMessage());
			return $this->error('导出失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 认领公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function claim(int $id): Json
	{
		try {
			$result = $this->service->claimCustomer($id);
			return $result
				? $this->success('认领成功')
				: $this->error('认领失败');
		}
		catch (\Exception $e) {
			Log::error('认领客户失败: ' . $e->getMessage());
			return $this->error('认领失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * todo 暂时不需要 批量认领公海客户
	 *
	 * @return Json
	 */
	public function batchClaim(): Json
	{
		$customerIds = $this->request->post('customer_ids', []);
		
		if (empty($customerIds) || !is_array($customerIds)) {
			return $this->error('请选择要认领的客户');
		}
		
		try {
			$successCount = 0;
			$failedCount  = 0;
			$errors       = [];
			
			foreach ($customerIds as $customerId) {
				try {
					$result = $this->service->claimCustomer((int)$customerId, get_user_id());
					if ($result) {
						$successCount++;
					}
					else {
						$failedCount++;
						$errors[] = "客户ID {$customerId}: 认领失败";
					}
				}
				catch (\Exception $e) {
					$failedCount++;
					$errors[] = "客户ID {$customerId}: " . $e->getMessage();
				}
			}
			
			$message = "批量认领完成，成功 {$successCount} 个";
			if ($failedCount > 0) {
				$message .= "，失败 {$failedCount} 个";
			}
			
			return $this->success($message, [
				'success_count' => $successCount,
				'failed_count'  => $failedCount,
				'errors'        => $errors
			]);
		}
		catch (\Exception $e) {
			Log::error('批量认领客户失败: ' . $e->getMessage());
			return $this->error('批量认领失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 分配给后台用户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function assign(int $id): Json
	{
		$result = $this->service->assignCustomer($id, $this->request->post('owner_user_id', 0, 'int'));
		return $result
			? $this->success('分配成功')
			: $this->error('分配失败');
	}
	
	/**
	 * 锁定公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function lockSea(int $id): Json
	{
		
		try {
			$lock_expire_time = $this->request->post('lock_expire_time', 30);
			
			$result = $this->service->edit([
				'lock_status'      => 1,
				'lock_expire_time' => $lock_expire_time
			], [
				'id' => $id
			]);
			
			return $result
				? $this->success('锁定成功')
				: $this->error('锁定失败');
		}
		catch (\Exception $e) {
			Log::error('锁定客户失败: ' . $e->getMessage());
			return $this->error('锁定失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 解锁公海客户
	 *
	 * @param int $id 客户ID
	 * @return Json
	 */
	public function unlockSea(int $id): Json
	{
		try {
			$result = $this->service->edit([
				'lock_status'      => 0,
				'lock_expire_time' => null
			], ['id' => $id]);
			return $result
				? $this->success('解锁成功')
				: $this->error('解锁失败');
		}
		catch (\Exception $e) {
			Log::error('解锁客户失败: ' . $e->getMessage());
			return $this->error('解锁失败: ' . $e->getMessage());
		}
	}
	
}