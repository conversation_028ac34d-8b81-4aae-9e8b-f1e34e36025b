<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 用户模型
 */
class AdminModel extends BaseModel
{
	
	/**
	 * 表名
	 *
	 * @var string
	 */
	protected $name = 'system_admin';
	
	// 设置追加属性
	protected $append = [
		'status_text',
		'gender_text',
	];
	
	public function getPostIdsAttr($value): array
	{
		$arr = !empty($value)
			? explode(',', $value)
			: [];
		foreach ($arr as $key => $value) {
			$arr[$key] = (int)$value;
		}
		return $arr;
	}
	
	public function setPostIdsAttr($value): string
	{
		return !empty($value)
			? implode(',', $value)
			: '';
	}
	
	/**
	 * 部门关联
	 *
	 * @return BelongsTo
	 */
	public function dept(): BelongsTo
	{
		return $this->belongsTo(DeptModel::class, 'dept_id', 'id')
		            ->bind([
			            'dept_name' => 'name',
		            ]);
	}
	
	/**
	 * 角色关联
	 *
	 * @return HasMany
	 */
	public function roles(): HasMany
	{
		return $this->hasMany(AdminRoleModel::class, 'admin_id', 'id');
	}
	
	// 关联角色
	/*public function roles()
	{
		return $this->belongsToMany(RoleModel::class, 'system_admin_role', 'admin_id', 'role_id');
	}*/
	
	// 获取状态文本
	public function getStatusTextAttr($value, $data)
	{
		$status    = $data['status'] ?? 0;
		$statusMap = [
			0 => '禁用',
			1 => '正常'
		];
		return $statusMap[$status] ?? '';
	}
	
	// 获取性别文本
	public function getGenderTextAttr($value, $data)
	{
		$gender    = $data['gender'] ?? 0;
		$genderMap = [
			0 => '保密',
			1 => '男',
			2 => '女'
		];
		return $genderMap[$gender] ?? '';
	}
	
	
	/**
	 * 根据用户名获取用户(无数据权限)
	 *
	 * @param string $username 用户名
	 * @param int    $tenantId 租户ID
	 * @return AdminModel
	 */
	public function getAdminByUsername(string $username): AdminModel
	{
		return $this->where('username', $username)
		            ->findOrEmpty();
	}
	
	
} 