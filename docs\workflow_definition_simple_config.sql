-- 工作流定义简化配置SQL
-- 直接复制id=32的flow_config到新增的9个业务类型
-- 创建时间：2025-07-28

-- 方案一：直接复制id=32的flow_config
INSERT INTO workflow_definition (name, type_id, flow_config, status, is_template, remark, creator_id, created_at, updated_at, tenant_id)
SELECT 
    CONCAT(wt.name, '标准流程') as name,
    wt.id as type_id,
    (SELECT flow_config FROM workflow_definition WHERE id = 32) as flow_config,
    1 as status,
    0 as is_template,
    CONCAT(wt.name, '标准审批流程，复用id=32的流程配置') as remark,
    1 as creator_id,
    NOW() as created_at,
    NOW() as updated_at,
    0 as tenant_id
FROM workflow_type wt
WHERE wt.business_code IN (
    'ims_outbound_approval',
    'ims_inbound_approval', 
    'ims_shipment_approval',
    'ims_purchase_approval',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'hr_business_trip',
    'hr_outing',
    'office_sample_mail'
)
AND NOT EXISTS (
    SELECT 1 FROM workflow_definition wd WHERE wd.type_id = wt.id
);

-- 验证插入结果
SELECT 
    wd.id,
    wd.name,
    wt.business_code,
    wt.module_code,
    wd.status,
    wd.created_at,
    CASE 
        WHEN wd.flow_config IS NOT NULL AND wd.flow_config != '' THEN '已配置'
        ELSE '未配置'
    END as flow_config_status
FROM workflow_definition wd
LEFT JOIN workflow_type wt ON wd.type_id = wt.id
WHERE wt.business_code IN (
    'ims_outbound_approval',
    'ims_inbound_approval', 
    'ims_shipment_approval',
    'ims_purchase_approval',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'hr_business_trip',
    'hr_outing',
    'office_sample_mail'
)
ORDER BY wt.module_code, wt.business_code;

-- 检查id=32的flow_config是否存在
SELECT 
    id,
    name,
    CASE 
        WHEN flow_config IS NOT NULL AND flow_config != '' THEN '配置存在'
        ELSE '配置为空'
    END as config_status,
    CHAR_LENGTH(flow_config) as config_length
FROM workflow_definition 
WHERE id = 32;

-- 如果需要查看id=32的完整flow_config内容，执行：
-- SELECT id, name, flow_config FROM workflow_definition WHERE id = 32;

-- 使用说明：
-- 1. 确保已执行 docs/workflow_type_config.sql
-- 2. 确保id=32的workflow_definition记录存在且flow_config有效
-- 3. 执行此SQL将自动为9个业务类型创建对应的workflow_definition记录
-- 4. 所有新记录将使用与id=32相同的flow_config配置
