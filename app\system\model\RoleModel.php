<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\HasMany;

/**
 * 角色模型
 */
class RoleModel extends BaseModel
{
	
	
	// 数据范围：全部
	const DATA_SCOPE_ALL = 1;
	
	// 数据范围：本部门
	const DATA_SCOPE_DEPT = 2;
	
	// 数据范围：本部门及以下
	const DATA_SCOPE_DEPT_AND_CHILD = 3;
	
	// 数据范围：仅本人
	const DATA_SCOPE_SELF = 4;
	
	// 数据范围：自定义
	const DATA_SCOPE_CUSTOM = 5;
	
	/**
	 * 表名
	 *
	 * @var string
	 */
	protected $name = 'system_role';
	
	protected $json = ['data_scope_dept_ids'];
	
	protected $jsonAssoc = true;
	
	protected $append = [
		'status_text',
		'data_scope_text'
	];
	
	/**
	 * 角色菜单关联
	 *
	 * @return HasMany
	 */
	public function menu(): HasMany
	{
		return $this->hasMany(RoleMenuModel::class, 'role_id', 'id');
	}
	
	/**
	 * 角色用户关联
	 *
	 * @return HasMany
	 */
	public function role()
	{
		return $this->hasMany(AdminRoleModel::class, 'role_id', 'id');
	}
	
	public function admins()
	{
		return $this->belongsToMany(AdminModel::class, 'system_admin_role', 'role_id', 'admin_id');
	}
	
	public function getStatusTextAttr($value, $data)
	{
		if (isset($data['status'])) {
			return $data['status'] == 1
				? '正常'
				: '禁用';
		}
		return '';
	}
	
	public function getDataScopeTextAttr($value, $data)
	{
		if (isset($data['data_scope'])) {
			$scope    = $data['data_scope'] ?? 1;
			$scopeMap = [
				1 => '全部数据',
				2 => '本部门数据',
				3 => '本部门及以下数据',
				4 => '仅本人数据',
				5 => '自定义数据'
			];
			return $scopeMap[$scope] ?? '';
		}
		
		return '';
	}
	
	
} 