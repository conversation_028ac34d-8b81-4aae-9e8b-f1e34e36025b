-- =====================================================
-- CRM商机阶段管理表初始化脚本
-- 创建时间: 2024-01-01
-- 说明: 用于创建商机阶段管理相关的数据表和初始化数据
-- =====================================================

-- 1. 商机阶段表
-- =====================================================
DROP TABLE IF EXISTS `crm_business_stage`;
CREATE TABLE `crm_business_stage` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '阶段ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID(0=系统默认)',
    `stage_name` varchar(50) NOT NULL DEFAULT '' COMMENT '阶段名称 @required @max:50 @search:like @exp @imp',
    `stage_code` varchar(50) NOT NULL DEFAULT '' COMMENT '阶段编码 @required @max:50 @search:eq @exp @imp',
    `order_num` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序号 @required @number @search:between @exp @imp',
    `description` text COMMENT '阶段描述 @form:textarea @exp @imp',
    `color` varchar(20) NOT NULL DEFAULT '#409EFF' COMMENT '显示颜色 @max:20 @component:color @exp @imp',
    `icon` varchar(50) NOT NULL DEFAULT '' COMMENT '图标 @max:50 @component:icon @exp @imp',
    `is_initial` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否初始阶段 @component:switch @exp @imp',
    `is_final` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否最终阶段 @component:switch @exp @imp',
    `is_success` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否成功阶段 @component:switch @exp @imp',
    `avg_duration_days` int(11) UNSIGNED DEFAULT NULL COMMENT '平均停留天数 @number @search:between @exp',
    `success_rate` decimal(5,2) UNSIGNED DEFAULT 0.00 COMMENT '历史成功率(%) @number @min:0 @max:100 @search:between @exp',
    `required_fields` json DEFAULT NULL COMMENT '必填字段配置 @form:textarea @component:json',
    `auto_rules` json DEFAULT NULL COMMENT '自动化规则配置 @form:textarea @component:json',
    `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用 @search:eq @component:switch @exp @imp',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人 @search:eq @exp',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 @search:date @exp',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_id`, `stage_code`),
    KEY `idx_tenant_order` (`tenant_id`, `order_num`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='商机阶段表 @module:crm @exp:true @imp:true';

-- 2. 商机阶段流转规则表
-- =====================================================
DROP TABLE IF EXISTS `crm_business_stage_flow`;
CREATE TABLE `crm_business_stage_flow` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规则ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID @search:eq @exp @imp',
    `from_stage_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '源阶段ID @required @search:eq @exp @imp',
    `to_stage_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标阶段ID @required @search:eq @exp @imp',
    `is_allowed` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否允许流转 @component:switch @exp @imp',
    `conditions` json DEFAULT NULL COMMENT '流转条件 @form:textarea @component:json',
    `auto_actions` json DEFAULT NULL COMMENT '自动执行动作 @form:textarea @component:json',
    `remark` varchar(255) DEFAULT '' COMMENT '备注 @max:255 @form:textarea @exp @imp',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人 @search:eq @exp',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @search:date @exp',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_flow` (`tenant_id`, `from_stage_id`, `to_stage_id`),
    KEY `idx_from_stage` (`from_stage_id`),
    KEY `idx_to_stage` (`to_stage_id`),
    KEY `idx_deleted_at` (`deleted_at`),
    FOREIGN KEY (`from_stage_id`) REFERENCES `crm_business_stage` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`to_stage_id`) REFERENCES `crm_business_stage` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='商机阶段流转规则表 @module:crm @exp:true @imp:true';

-- 3. 商机阶段变更记录表
-- =====================================================
DROP TABLE IF EXISTS `crm_business_stage_record`;
CREATE TABLE `crm_business_stage_record` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID @search:eq @exp',
    `business_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商机ID @required @search:eq @exp @imp',
    `from_stage_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '原阶段ID @search:eq @exp',
    `to_stage_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '新阶段ID @required @search:eq @exp',
    `from_stage_name` varchar(50) DEFAULT '' COMMENT '原阶段名称 @max:50 @exp',
    `to_stage_name` varchar(50) NOT NULL DEFAULT '' COMMENT '新阶段名称 @max:50 @exp',
    `duration_days` int(11) UNSIGNED DEFAULT NULL COMMENT '在原阶段停留天数 @number @search:between @exp',
    `change_reason` varchar(255) DEFAULT '' COMMENT '变更原因 @max:255 @form:textarea @exp @imp',
    `remark` text COMMENT '变更备注 @form:textarea @exp @imp',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作人ID @search:eq @exp',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间 @search:date @exp',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_business` (`tenant_id`, `business_id`),
    KEY `idx_from_stage` (`from_stage_id`),
    KEY `idx_to_stage` (`to_stage_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_creator` (`creator_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='商机阶段变更记录表 @module:crm @exp:true @imp:true';

-- 4. 修改商机表，添加阶段ID字段
-- =====================================================
-- 注意：这里假设商机表已存在，只是添加新字段
ALTER TABLE `crm_business` 
ADD COLUMN `stage_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '当前阶段ID @search:eq @exp' AFTER `stage`,
ADD COLUMN `stage_entered_at` datetime DEFAULT NULL COMMENT '进入当前阶段时间 @search:date @exp' AFTER `stage_id`,
ADD INDEX `idx_tenant_stage_id` (`tenant_id`, `stage_id`),
ADD INDEX `idx_stage_entered_at` (`stage_entered_at`);

-- 如果需要添加外键约束（可选）
-- ALTER TABLE `crm_business` 
-- ADD FOREIGN KEY (`stage_id`) REFERENCES `crm_business_stage` (`id`) ON DELETE SET NULL;

-- 5. 插入系统默认阶段数据
-- =====================================================
INSERT INTO `crm_business_stage` (
    `tenant_id`, `stage_name`, `stage_code`, `order_num`, `description`, 
    `color`, `icon`, `is_initial`, `is_final`, `is_success`, 
    `avg_duration_days`, `success_rate`, `status`, `creator_id`
) VALUES
-- 初始阶段
(0, '初步接洽', 'initial', 1, '首次接触客户，了解基本需求和意向', '#909399', 'el-icon-phone', 1, 0, 0, 5, 15.00, 1, 1),

-- 中间阶段
(0, '需求确认', 'demand', 2, '明确客户具体需求和预算范围', '#E6A23C', 'el-icon-edit', 0, 0, 0, 7, 35.00, 1, 1),
(0, '方案报价', 'proposal', 3, '提供解决方案和详细报价', '#409EFF', 'el-icon-document', 0, 0, 0, 10, 55.00, 1, 1),
(0, '商务谈判', 'negotiation', 4, '价格和条款谈判阶段', '#F56C6C', 'el-icon-chat-dot-round', 0, 0, 0, 8, 75.00, 1, 1),
(0, '合同签署', 'contract', 5, '准备和签署合同文件', '#67C23A', 'el-icon-document-checked', 0, 0, 0, 5, 90.00, 1, 1),

-- 最终阶段
(0, '成功签约', 'closed_won', 6, '合同签署完成，商机成功', '#67C23A', 'el-icon-success', 0, 1, 1, 0, 100.00, 1, 1),
(0, '失败关闭', 'closed_lost', 7, '商机失败或主动放弃', '#F56C6C', 'el-icon-error', 0, 1, 0, 0, 0.00, 1, 1);

-- 6. 插入默认阶段流转规则
-- =====================================================
INSERT INTO `crm_business_stage_flow` (`tenant_id`, `from_stage_id`, `to_stage_id`, `is_allowed`, `remark`) VALUES
-- 从初步接洽可以流转到的阶段
(0, 1, 2, 1, '客户有明确需求时推进'),
(0, 1, 7, 1, '客户无兴趣或无响应时关闭'),

-- 从需求确认可以流转到的阶段
(0, 2, 1, 1, '需求不明确时回退'),
(0, 2, 3, 1, '需求明确后进入方案阶段'),
(0, 2, 7, 1, '客户放弃需求时关闭'),

-- 从方案报价可以流转到的阶段
(0, 3, 2, 1, '方案需要重新调整时回退'),
(0, 3, 4, 1, '方案被认可后进入谈判'),
(0, 3, 7, 1, '方案被拒绝时关闭'),

-- 从商务谈判可以流转到的阶段
(0, 4, 3, 1, '需要重新报价时回退'),
(0, 4, 5, 1, '谈判成功后签署合同'),
(0, 4, 7, 1, '谈判失败时关闭'),

-- 从合同签署可以流转到的阶段
(0, 5, 4, 1, '合同条款需要重新谈判时回退'),
(0, 5, 6, 1, '合同签署成功'),
(0, 5, 7, 1, '合同签署失败时关闭');

-- 7. 为现有商机数据设置默认阶段
-- =====================================================
-- 为所有没有设置stage_id的商机设置默认阶段
UPDATE `crm_business` 
SET `stage_id` = (
    SELECT `id` FROM `crm_business_stage` 
    WHERE `stage_code` = 'initial' AND `tenant_id` = 0 
    LIMIT 1
),
`stage_entered_at` = `created_at`
WHERE `stage_id` IS NULL;

-- 8. 根据现有stage字段映射到新的stage_id（可选）
-- =====================================================
-- 如果现有数据中的stage字段有值，可以尝试映射到新的stage_id
UPDATE `crm_business` b
SET `stage_id` = (
    SELECT s.`id` 
    FROM `crm_business_stage` s 
    WHERE s.`stage_code` = CASE 
        WHEN b.`stage` LIKE '%接洽%' OR b.`stage` LIKE '%初步%' THEN 'initial'
        WHEN b.`stage` LIKE '%需求%' OR b.`stage` LIKE '%确认%' THEN 'demand'
        WHEN b.`stage` LIKE '%方案%' OR b.`stage` LIKE '%报价%' THEN 'proposal'
        WHEN b.`stage` LIKE '%谈判%' OR b.`stage` LIKE '%商务%' THEN 'negotiation'
        WHEN b.`stage` LIKE '%合同%' OR b.`stage` LIKE '%签署%' THEN 'contract'
        WHEN b.`stage` LIKE '%成功%' OR b.`stage` LIKE '%签约%' OR b.`stage` LIKE '%赢%' THEN 'closed_won'
        WHEN b.`stage` LIKE '%失败%' OR b.`stage` LIKE '%关闭%' OR b.`stage` LIKE '%输%' THEN 'closed_lost'
        ELSE 'initial'
    END
    AND s.`tenant_id` = 0
    LIMIT 1
)
WHERE b.`stage` IS NOT NULL AND b.`stage` != '';

-- 9. 创建视图用于查询（可选）
-- =====================================================
CREATE OR REPLACE VIEW `v_crm_business_with_stage` AS
SELECT 
    b.*,
    s.`stage_name`,
    s.`stage_code`,
    s.`color` as `stage_color`,
    s.`icon` as `stage_icon`,
    s.`order_num` as `stage_order`,
    s.`is_final` as `stage_is_final`,
    s.`is_success` as `stage_is_success`,
    s.`success_rate` as `stage_success_rate`
FROM `crm_business` b
LEFT JOIN `crm_business_stage` s ON b.`stage_id` = s.`id`
WHERE b.`deleted_at` IS NULL;

-- 10. 创建统计视图（可选）
-- =====================================================
CREATE OR REPLACE VIEW `v_crm_business_stage_stats` AS
SELECT 
    s.`id` as `stage_id`,
    s.`stage_name`,
    s.`stage_code`,
    s.`order_num`,
    COUNT(b.`id`) as `business_count`,
    SUM(CASE WHEN b.`status` = 'active' THEN 1 ELSE 0 END) as `active_count`,
    SUM(CASE WHEN b.`status` = 'won' THEN 1 ELSE 0 END) as `won_count`,
    SUM(CASE WHEN b.`status` = 'lost' THEN 1 ELSE 0 END) as `lost_count`,
    SUM(b.`amount`) as `total_amount`,
    AVG(b.`amount`) as `avg_amount`,
    AVG(CASE 
        WHEN b.`stage_entered_at` IS NOT NULL 
        THEN DATEDIFF(COALESCE(b.`updated_at`, NOW()), b.`stage_entered_at`)
        ELSE NULL 
    END) as `avg_duration_days`
FROM `crm_business_stage` s
LEFT JOIN `crm_business` b ON s.`id` = b.`stage_id` AND b.`deleted_at` IS NULL
WHERE s.`deleted_at` IS NULL
GROUP BY s.`id`, s.`stage_name`, s.`stage_code`, s.`order_num`
ORDER BY s.`order_num`;

-- =====================================================
-- 初始化脚本执行完成
-- =====================================================
