<?php
declare(strict_types=1);

namespace app\hr\model;

use app\common\core\base\BaseModel;
use app\system\model\AdminModel;
use app\system\model\DeptModel;
use app\workflow\model\WorkflowInstance;
use think\model\relation\BelongsTo;
use think\model\relation\HasOneThrough;

/**
 * 请假申请表模型（新工作流集成版本）
 *
 * 数据表字段说明：
 *
 * @property int              $id                   主键ID
 * @property int              $tenant_id            租户ID
 * @property int              $workflow_instance_id 工作流实例ID，关联workflow_instance表
 * @property int              $approval_status      审批状态：0=草稿,1=审批中,2=已通过,3=已驳回,4=已终止,5=已撤回,6=已作废
 * @property string           $submit_time          提交时间
 * @property string           $approval_time        审批时间
 * @property string           $approval_opinion     审批意见
 * @property int              $submitter_id         提交人ID
 * @property int              $leave_type           请假类型：1=年假,2=事假,3=病假,4=婚假,5=产假,6=丧假,7=其他
 * @property string           $start_time           开始时间
 * @property string           $end_time             结束时间
 * @property float            $duration             请假时长(天)
 * @property string           $reason               请假原因
 * @property string           $attachment           附件
 * @property string           $emergency_contact    紧急联系人
 * @property string           $emergency_phone      紧急联系电话
 * @property string           $remark               备注
 * @property int              $creator_id           创建人ID
 * @property string           $created_at           创建时间
 * @property string           $updated_at           更新时间
 * @property string           $deleted_at           删除时间
 *
 * 关联关系说明：
 * @property AdminModel            $submitter            提交人信息
 * @property WorkflowInstance $workflowInstance     工作流实例信息
 * @property AdminModel            $creator              创建人信息
 *
 * 虚拟字段说明：
 * @property string           $leave_type_text      请假类型文本
 * @property string           $approval_status_text 审批状态文本
 * @property string           $submitter_name       提交人姓名
 * @property string           $submitter_dept_name  提交人部门名称
 * @property string           $creator_name         创建人姓名
 * @property bool             $can_edit             是否可编辑
 * @property bool             $can_submit           是否可提交
 * @property bool             $can_withdraw         是否可撤回
 * @property bool             $can_delete           是否可删除
 */
class HrLeave extends BaseModel
{
	// 设置表名
	protected $name = 'hr_leave';
	
	// 设置字段类型
	protected $type = [
		'id'                   => 'integer',
		'tenant_id'            => 'integer',
		'workflow_instance_id' => 'integer',
		'approval_status'      => 'integer',
		'submit_time'          => 'datetime',
		'approval_time'        => 'datetime',
		'submitter_id'         => 'integer',
		'leave_type'           => 'integer',
		'start_time'           => 'datetime',
		'end_time'             => 'datetime',
		'duration'             => 'float',
		'creator_id'           => 'integer',
		'created_at'           => 'datetime',
		'updated_at'           => 'datetime',
		'deleted_at'           => 'datetime',
	];
	
	// 设置JSON字段
	protected $json = ['attachment'];
	
	// 设置允许写入的字段
	protected $field = [
		'workflow_instance_id',
		'approval_status',
		'submit_time',
		'approval_time',
		'approval_opinion',
		'submitter_id',
		'leave_type',
		'start_time',
		'end_time',
		'duration',
		'reason',
		'attachment',
		'emergency_contact',
		'emergency_phone',
		'remark',
	];
	
	// ==================== 常量定义 ====================
	
	/**
	 * 审批状态常量
	 */
	const APPROVAL_STATUS_DRAFT      = 0; // 草稿
	const APPROVAL_STATUS_APPROVING  = 1; // 审批中
	const APPROVAL_STATUS_APPROVED   = 2; // 已通过
	const APPROVAL_STATUS_REJECTED   = 3; // 已驳回
	const APPROVAL_STATUS_TERMINATED = 4; // 已终止
	const APPROVAL_STATUS_WITHDRAWN  = 5; // 已撤回
	const APPROVAL_STATUS_VOIDED     = 6; // 已作废
	
	/**
	 * 请假类型常量
	 */
	const LEAVE_TYPE_ANNUAL    = 1; // 年假
	const LEAVE_TYPE_PERSONAL  = 2; // 事假
	const LEAVE_TYPE_SICK      = 3; // 病假
	const LEAVE_TYPE_MARRIAGE  = 4; // 婚假
	const LEAVE_TYPE_MATERNITY = 5; // 产假
	const LEAVE_TYPE_FUNERAL   = 6; // 丧假
	const LEAVE_TYPE_OTHER     = 7; // 其他
	
	// ==================== 关联关系 ====================
	
	/**
	 * 关联提交人信息
	 *
	 * @return BelongsTo 返回提交人关联关系
	 * 关联数据结构：
	 * {
	 *   "id": 1,
	 *   "real_name": "张三",
	 *   "mobile": "13800138001",
	 *   "dept_id": 1,
	 *   "status": 1
	 * }
	 */
	public function submitter(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'submitter_id', 'id')
		            ->bind([
			            'submitter_name'    => 'real_name',
			            'submitter_mobile'  => 'mobile',
			            'submitter_dept_id' => 'dept_id'
		            ]);
	}



	/**
	 * 关联工作流实例信息
	 *
	 * @return BelongsTo 返回工作流实例关联关系
	 * 关联数据结构：
	 * {
	 *   "id": 1,
	 *   "title": "张三的年假申请",
	 *   "status": 1,
	 *   "start_time": "2025-01-19 10:00:00",
	 *   "end_time": null,
	 *   "current_node": "approval_node_1"
	 * }
	 */
	public function workflowInstance(): BelongsTo
	{
		return $this->belongsTo(WorkflowInstance::class, 'workflow_instance_id', 'id')
		            ->bind([
			            'workflow_title'        => 'title',
			            'workflow_status'       => 'status',
			            'workflow_start_time'   => 'start_time',
			            'workflow_end_time'     => 'end_time',
			            'workflow_current_node' => 'current_node'
		            ]);
	}
	
	/**
	 * 关联创建人信息
	 *
	 * @return BelongsTo 返回创建人关联关系
	 * 关联数据结构：
	 * {
	 *   "id": 1,
	 *   "real_name": "管理员",
	 *   "mobile": "13800138000"
	 * }
	 */
	public function creator(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'creator_id', 'id')
		            ->bind(['creator_name' => 'real_name']);
	}
	
	// ==================== 获取器（虚拟字段） ====================
	
	/**
	 * 获取请假类型文本
	 *
	 * @param mixed $value 原始值
	 * @param array $data  完整数据数组
	 * @return string 请假类型文本
	 *                     数据结构：
	 *                     $data = [
	 *                     "leave_type" => 1,
	 *                     "start_time" => "2025-01-19 09:00:00",
	 *                     ...
	 *                     ]
	 */
	public function getLeaveTypeTextAttr($value, array $data): string
	{
		$typeMap = [
			self::LEAVE_TYPE_ANNUAL    => '年假',
			self::LEAVE_TYPE_PERSONAL  => '事假',
			self::LEAVE_TYPE_SICK      => '病假',
			self::LEAVE_TYPE_MARRIAGE  => '婚假',
			self::LEAVE_TYPE_MATERNITY => '产假',
			self::LEAVE_TYPE_FUNERAL   => '丧假',
			self::LEAVE_TYPE_OTHER     => '其他',
		];
		
		return $typeMap[$data['leave_type']] ?? '未知';
	}
	
	/**
	 * 获取审批状态文本
	 *
	 * @param mixed $value 原始值
	 * @param array $data  完整数据数组
	 * @return string 审批状态文本
	 *                     数据结构：
	 *                     $data = [
	 *                     "approval_status" => 1,
	 *                     "workflow_instance_id" => 123,
	 *                     ...
	 *                     ]
	 */
	public function getApprovalStatusTextAttr($value, array $data): string
	{
		$statusMap = [
			self::APPROVAL_STATUS_DRAFT      => '草稿',
			self::APPROVAL_STATUS_APPROVING  => '审批中',
			self::APPROVAL_STATUS_APPROVED   => '已通过',
			self::APPROVAL_STATUS_REJECTED   => '已驳回',
			self::APPROVAL_STATUS_TERMINATED => '已终止',
			self::APPROVAL_STATUS_WITHDRAWN  => '已撤回',
			self::APPROVAL_STATUS_VOIDED     => '已作废',
		];
		
		return $statusMap[$data['approval_status']] ?? '未知';
	}
	
	/**
	 * 获取审批状态样式类
	 *
	 * @param mixed $value 原始值
	 * @param array $data  完整数据数组
	 * @return string CSS样式类名
	 */
	public function getApprovalStatusClassAttr($value, array $data): string
	{
		$classMap = [
			self::APPROVAL_STATUS_DRAFT      => 'info',
			self::APPROVAL_STATUS_APPROVING  => 'warning',
			self::APPROVAL_STATUS_APPROVED   => 'success',
			self::APPROVAL_STATUS_REJECTED   => 'danger',
			self::APPROVAL_STATUS_TERMINATED => 'danger',
			self::APPROVAL_STATUS_WITHDRAWN  => 'info',
			self::APPROVAL_STATUS_VOIDED     => 'danger',
		];
		
		return $classMap[$data['approval_status']] ?? 'default';
	}
	
	// ==================== 权限判断方法 ====================
	
	/**
	 * 判断是否可以编辑
	 *
	 * @param mixed $value 原始值
	 * @param array $data  完整数据数组
	 * @return bool 是否可编辑
	 *                     业务规则：只有草稿和已驳回状态可以编辑
	 *                     数据结构：
	 *                     $data = [
	 *                     "approval_status" => 0,
	 *                     "submitter_id" => 1,
	 *                     "workflow_instance_id" => 0,
	 *                     ...
	 *                     ]
	 */
	public function getCanEditAttr($value, array $data): bool
	{
		$editableStatuses = [
			self::APPROVAL_STATUS_DRAFT,
			self::APPROVAL_STATUS_REJECTED
		];
		
		return in_array($data['approval_status'], $editableStatuses);
	}
	
	/**
	 * 判断是否可以提交审批
	 *
	 * @param mixed $value 原始值
	 * @param array $data  完整数据数组
	 * @return bool 是否可提交
	 *                     业务规则：只有草稿和已驳回状态可以提交
	 */
	public function getCanSubmitAttr($value, array $data): bool
	{
		$submittableStatuses = [
			self::APPROVAL_STATUS_DRAFT,
			self::APPROVAL_STATUS_REJECTED
		];
		
		return in_array($data['approval_status'], $submittableStatuses);
	}
	
	/**
	 * 判断是否可以撤回
	 *
	 * @param mixed $value 原始值
	 * @param array $data  完整数据数组
	 * @return bool 是否可撤回
	 *                     业务规则：只有审批中状态可以撤回
	 */
	public function getCanWithdrawAttr($value, array $data): bool
	{
		return $data['approval_status'] === self::APPROVAL_STATUS_APPROVING;
	}
	
	/**
	 * 判断是否可以删除
	 *
	 * @param mixed $value 原始值
	 * @param array $data  完整数据数组
	 * @return bool 是否可删除
	 *                     业务规则：只有草稿、已驳回、已撤回状态可以删除
	 */
	public function getCanDeleteAttr($value, array $data): bool
	{
		$deletableStatuses = [
			self::APPROVAL_STATUS_DRAFT,
			self::APPROVAL_STATUS_REJECTED,
			self::APPROVAL_STATUS_WITHDRAWN
		];
		
		return in_array($data['approval_status'], $deletableStatuses);
	}
	
	// ==================== 业务方法 ====================
	
	/**
	 * 获取业务代码（用于工作流集成）
	 *
	 * @return string 业务代码
	 */
	public function getBusinessCode(): string
	{
		return 'hr_leave';
	}
	
	/**
	 * 获取审批标题（用于工作流实例）
	 *
	 * @return string 审批标题
	 * 标题格式：{提交人姓名}的{请假类型}申请({请假天数}天)
	 */
	public function getApprovalTitle(): string
	{
		$submitterName = $this->submitter_name ?? '';
		$leaveTypeText = $this->leave_type_text;
		$duration      = $this->duration;
		
		return "{$submitterName}的{$leaveTypeText}申请({$duration}天)";
	}
	
	/**
	 * 验证请假时间是否合理
	 *
	 * @return array 验证结果
	 * 返回数据结构：
	 * [
	 *   "valid" => true,
	 *   "message" => "",
	 *   "errors" => []
	 * ]
	 */
	public function validateLeaveTime(): array
	{
		$errors = [];
		
		// 检查开始时间不能早于当前时间
		if ($this->start_time && strtotime($this->start_time) < time()) {
			$errors[] = '请假开始时间不能早于当前时间';
		}
		
		// 检查结束时间不能早于开始时间
		if ($this->start_time && $this->end_time && $this->start_time >= $this->end_time) {
			$errors[] = '请假结束时间必须晚于开始时间';
		}
		
		// 检查请假天数是否合理
		if ($this->duration <= 0) {
			$errors[] = '请假天数必须大于0';
		}
		
		// 检查请假天数是否与时间段匹配（简单验证）
		if ($this->start_time && $this->end_time) {
			$daysDiff = (strtotime($this->end_time) - strtotime($this->start_time)) / (24 * 3600);
			if (abs($daysDiff - $this->duration) > 1) {
				$errors[] = '请假天数与时间段不匹配';
			}
		}
		
		return [
			'valid'   => empty($errors),
			'message' => empty($errors)
				? '验证通过'
				: implode('；', $errors),
			'errors'  => $errors
		];
	}
	
	/**
	 * 获取请假申请的摘要信息
	 *
	 * @return array 摘要信息
	 * 返回数据结构：
	 * [
	 *   "title" => "张三的年假申请(3天)",
	 *   "time_range" => "2025-01-19 09:00:00 至 2025-01-22 18:00:00",
	 *   "duration" => 3.0,
	 *   "reason" => "年假休息",
	 *   "status" => "审批中",
	 *   "emergency_contact" => "李四(13800138001)"
	 * ]
	 */
	public function getSummary(): array
	{
		return [
			'title'             => $this->getApprovalTitle(),
			'time_range'        => $this->start_time . ' 至 ' . $this->end_time,
			'duration'          => $this->duration,
			'reason'            => $this->reason,
			'status'            => $this->approval_status_text,
			'emergency_contact' => $this->emergency_contact . ($this->emergency_phone
					? "({$this->emergency_phone})"
					: '')
		];
	}

	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'submitter_id' => ['type' => 'eq'],
			'leave_type' => ['type' => 'eq'],
			'approval_status' => ['type' => 'eq'],
			'start_time' => ['type' => 'date'],
			'end_time' => ['type' => 'date'],
			'submit_time' => ['type' => 'date'],
			'approval_time' => ['type' => 'date'],
			'reason' => ['type' => 'like'],
			'created_at' => ['type' => 'date'],
			'updated_at' => ['type' => 'date'],
		];
	}
}