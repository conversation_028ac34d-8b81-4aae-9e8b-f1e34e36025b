# CRM商机-合同流程图

## 1. 整体业务流程图

```mermaid
flowchart TD
    A[潜在客户] --> B[客户管理]
    B --> C{业务类型判断}
    
    C -->|大客户/复杂项目| D[创建商机]
    C -->|小客户/标准产品| E[直接创建合同]
    C -->|老客户续约| F[基于历史合同创建]
    
    D --> G[商机阶段推进]
    G --> H[初步接洽]
    H --> I[需求确认]
    I --> J[方案报价]
    J --> K[商务谈判]
    K --> L[合同签署阶段]
    
    L --> M{商机结果}
    M -->|成功| N[转为合同]
    M -->|失败| O[关闭商机]
    
    N --> P[合同执行]
    E --> P
    F --> P
    
    P --> Q[项目交付]
    Q --> R[收款管理]
    Q --> S[客户服务]
    
    O --> T[流程结束]
    R --> T
    S --> U[客户维护]
    U --> B
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style D fill:#fff3e0
    style E fill:#e8f5e8
    style F fill:#fff8e1
    style P fill:#f1f8e9
    style T fill:#ffebee
```

## 2. 商机阶段详细流程图

```mermaid
flowchart TD
    A[商机创建] --> B[初步接洽]
    
    B --> B1{客户响应}
    B1 -->|有兴趣| C[需求确认]
    B1 -->|无响应/无兴趣| Z1[失败关闭]
    
    C --> C1{需求明确}
    C1 -->|需求明确| D[方案报价]
    C1 -->|需求不明确| B
    C1 -->|客户放弃| Z1
    
    D --> D1{方案反馈}
    D1 -->|方案认可| E[商务谈判]
    D1 -->|需要调整| C
    D1 -->|方案被拒| Z1
    
    E --> E1{谈判结果}
    E1 -->|谈判成功| F[合同签署]
    E1 -->|需要重新报价| D
    E1 -->|谈判失败| Z1
    
    F --> F1{合同状态}
    F1 -->|合同签署| Z2[成功签约]
    F1 -->|需要修改| E
    F1 -->|合同终止| Z1
    
    Z1 --> END1[商机结束]
    Z2 --> G[转为合同]
    G --> END2[进入合同流程]
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fff8e1
    style F fill:#f1f8e9
    style Z1 fill:#ffebee
    style Z2 fill:#e8f5e8
```

## 3. 合同状态流转图

```mermaid
stateDiagram-v2
    [*] --> 草稿 : 创建合同
    
    草稿 --> 待审批 : 提交审批
    草稿 --> 已取消 : 取消合同
    
    待审批 --> 已审批 : 审批通过
    待审批 --> 已拒绝 : 审批拒绝
    待审批 --> 已取消 : 取消审批
    
    已拒绝 --> 草稿 : 修改重提
    已拒绝 --> 已取消 : 放弃合同
    
    已审批 --> 执行中 : 开始执行
    已审批 --> 已取消 : 执行前取消
    
    执行中 --> 已完成 : 执行完成
    执行中 --> 已终止 : 提前终止
    
    已完成 --> [*] : 流程结束
    已终止 --> [*] : 流程结束
    已取消 --> [*] : 流程结束
```

## 4. 商机阶段转换矩阵

```mermaid
flowchart LR
    subgraph "阶段转换规则"
        A[初步接洽] --> B[需求确认]
        A --> Z[失败关闭]
        
        B --> A
        B --> C[方案报价]
        B --> Z
        
        C --> B
        C --> D[商务谈判]
        C --> Z
        
        D --> C
        D --> E[合同签署]
        D --> Z
        
        E --> D
        E --> F[成功签约]
        E --> Z
        
        F --> G[转为合同]
        Z --> H[商机结束]
    end
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fff8e1
    style F fill:#e8f5e8
    style Z fill:#ffebee
    style G fill:#f1f8e9
    style H fill:#ffebee
```

## 5. 数据流转关系图

```mermaid
erDiagram
    CUSTOMER ||--o{ BUSINESS : "拥有"
    CUSTOMER ||--o{ CONTRACT : "签署"
    BUSINESS ||--o| CONTRACT : "转化为"
    BUSINESS }o--|| BUSINESS_STAGE : "处于"
    BUSINESS ||--o{ STAGE_RECORD : "记录"
    BUSINESS_STAGE ||--o{ STAGE_FLOW : "流转规则"
    CONTRACT ||--o{ CONTRACT_PAYMENT : "付款计划"
    
    CUSTOMER {
        bigint id PK
        string customer_name
        string contact_info
        int status
    }
    
    BUSINESS {
        bigint id PK
        bigint customer_id FK
        bigint stage_id FK
        string business_name
        decimal amount
        date expected_date
        enum status
    }
    
    BUSINESS_STAGE {
        bigint id PK
        bigint tenant_id
        string stage_name
        string stage_code
        int order_num
        decimal success_rate
    }
    
    CONTRACT {
        bigint id PK
        bigint customer_id FK
        bigint business_id FK
        string contract_no
        decimal contract_amount
        enum status
        date sign_date
    }
    
    STAGE_RECORD {
        bigint id PK
        bigint business_id FK
        bigint from_stage_id FK
        bigint to_stage_id FK
        datetime created_at
    }
```

## 6. 时间轴流程图

```mermaid
gantt
    title 商机到合同典型时间轴
    dateFormat  YYYY-MM-DD
    section 商机阶段
    初步接洽     :active, initial, 2024-01-01, 7d
    需求确认     :demand, after initial, 10d
    方案报价     :proposal, after demand, 14d
    商务谈判     :negotiation, after proposal, 15d
    合同签署     :contract, after negotiation, 7d
    
    section 合同阶段
    合同草稿     :draft, after contract, 3d
    合同审批     :approval, after draft, 5d
    合同执行     :execution, after approval, 30d
    项目交付     :delivery, after execution, 60d
```

## 7. 决策流程图

```mermaid
flowchart TD
    A[客户需求] --> B{客户类型}
    
    B -->|新客户| C{项目复杂度}
    B -->|老客户| D{业务类型}
    
    C -->|复杂项目| E[标准商机流程]
    C -->|简单项目| F[简化商机流程]
    
    D -->|新业务| G{项目规模}
    D -->|续约业务| H[直接合同流程]
    
    G -->|大项目| E
    G -->|小项目| F
    
    E --> I[完整7阶段流程]
    F --> J[3-4阶段流程]
    H --> K[合同续签流程]
    
    I --> L[详细跟进记录]
    J --> M[关键节点记录]
    K --> N[续约条件确认]
    
    L --> O[转为合同]
    M --> O
    N --> P[续约合同]
    
    O --> Q[合同执行]
    P --> Q
    
    style A fill:#e1f5fe
    style E fill:#fff3e0
    style F fill:#e8f5e8
    style H fill:#f1f8e9
    style Q fill:#e8f5e8
```

## 8. 权限控制流程图

```mermaid
flowchart TD
    A[用户操作请求] --> B{用户角色}
    
    B -->|销售代表| C[基础权限检查]
    B -->|销售经理| D[管理权限检查]
    B -->|系统管理员| E[完全权限]
    
    C --> C1{操作类型}
    C1 -->|查看| C2[检查数据权限]
    C1 -->|编辑| C3[检查编辑权限]
    C1 -->|删除| C4[拒绝操作]
    
    D --> D1{操作范围}
    D1 -->|本部门| D2[部门数据权限]
    D1 -->|跨部门| D3[检查特殊权限]
    
    C2 --> F{权限验证}
    C3 --> F
    D2 --> F
    D3 --> F
    E --> G[允许操作]
    
    F -->|通过| G
    F -->|拒绝| H[拒绝操作]
    
    G --> I[执行操作]
    H --> J[返回错误信息]
    
    I --> K[记录操作日志]
    J --> L[记录拒绝日志]
    
    style A fill:#e3f2fd
    style G fill:#e8f5e8
    style H fill:#ffebee
    style I fill:#f1f8e9
```

## 9. 异常处理流程图

```mermaid
flowchart TD
    A[系统操作] --> B{操作结果}
    
    B -->|成功| C[正常流程]
    B -->|失败| D{错误类型}
    
    D -->|数据验证错误| E[返回验证信息]
    D -->|权限错误| F[返回权限错误]
    D -->|业务规则错误| G[返回业务错误]
    D -->|系统错误| H[记录错误日志]
    
    E --> I[用户修正数据]
    F --> J[联系管理员]
    G --> K[检查业务规则]
    H --> L[系统管理员处理]
    
    I --> A
    K --> M{规则是否合理}
    M -->|合理| N[用户调整操作]
    M -->|不合理| O[修改业务规则]
    
    N --> A
    O --> P[系统更新]
    P --> A
    
    C --> Q[操作完成]
    J --> R[等待权限分配]
    L --> S[系统修复]
    
    R --> T{权限已分配}
    T -->|是| A
    T -->|否| U[继续等待]
    
    S --> V{系统已修复}
    V -->|是| A
    V -->|否| W[继续修复]
    
    style A fill:#e3f2fd
    style C fill:#e8f5e8
    style Q fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#ffebee
    style G fill:#fff8e1
    style H fill:#ffebee
```

## 10. 数据同步流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant S as 服务层
    participant D as 数据库
    participant C as 缓存
    participant N as 通知服务
    
    U->>F: 商机阶段变更
    F->>A: POST /api/business/change-stage
    A->>S: 调用业务服务
    
    S->>D: 验证当前状态
    D-->>S: 返回当前数据
    
    S->>S: 业务规则验证
    
    alt 验证通过
        S->>D: 更新商机阶段
        S->>D: 插入变更记录
        D-->>S: 更新成功
        
        S->>C: 更新缓存
        S->>N: 发送通知
        
        S-->>A: 返回成功
        A-->>F: 返回结果
        F-->>U: 显示成功信息
        
        N->>N: 异步发送通知
    else 验证失败
        S-->>A: 返回错误
        A-->>F: 返回错误
        F-->>U: 显示错误信息
    end
```
