<!--
  ⚠️ 临时屏蔽 - 入库申请表单暂时不可用
  TODO: 需要进一步完善后再启用
  屏蔽时间: 2025-07-28
-->
<template>
  <ElDialog
    v-model="dialogVisible"
    title="入库申请功能暂时不可用"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div style="text-align: center; padding: 40px;">
      <el-icon size="64" color="#E6A23C">
        <WarningFilled />
      </el-icon>
      <h3 style="margin: 20px 0; color: #E6A23C;">功能暂时不可用</h3>
      <p style="color: #909399; margin-bottom: 30px;">
        入库申请功能正在完善中，请稍后再试。<br>
        如有紧急需求，请联系系统管理员。
      </p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElDialog, ElButton, ElIcon } from 'element-plus'
  import { WarningFilled } from '@element-plus/icons-vue'

  // 保持原有接口兼容性
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 保持接口兼容性的空方法
  const showForm = async (id?: number | string) => {
    console.log('入库申请表单已被暂时屏蔽')
  }

  const setFormData = (data: any) => {
    console.log('入库申请表单已被暂时屏蔽')
  }

  defineExpose({
    showForm,
    setFormData
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }
</style>

<!--
  原始表单代码已被注释，需要时可以恢复

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="入库单号" prop="inbound_no">
              <ElInput
                v-model="formData.inbound_no"
                placeholder="请输入入库单号"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="入库类型" prop="inbound_type">
              <ElSelect
                v-model="formData.inbound_type"
                placeholder="请选择入库类型"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption label="采购入库" :value="1" />
                <ElOption label="调拨入库" :value="2" />
                <ElOption label="退货入库" :value="3" />
                <ElOption label="其他入库" :value="4" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="入库日期" prop="inbound_date">
              <ElDatePicker
                v-model="formData.inbound_date"
                type="date"
                placeholder="请选择入库日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="入库仓库" prop="warehouse_id">
              <ElSelect
                v-model="formData.warehouse_id"
                placeholder="请选择入库仓库"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.value"
                  :label="warehouse.label"
                  :value="warehouse.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="供应商" prop="supplier_id">
              <ElSelect
                v-model="formData.supplier_id"
                placeholder="请选择供应商"
                style="width: 100%"
                filterable
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="supplier in supplierOptions"
                  :key="supplier.value"
                  :label="supplier.label"
                  :value="supplier.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="来源仓库" prop="source_warehouse_id">
              <ElSelect
                v-model="formData.source_warehouse_id"
                placeholder="请选择来源仓库"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.value"
                  :label="warehouse.label"
                  :value="warehouse.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

      </ElForm>

      <!-- 入库明细 - 独立的卡片区域 -->
<ElCard class="items-card" shadow="never">
<template #header>
  <div class="card-header">
    <span class="card-title">入库明细</span>
    <ElButton
      v-if="isEditable"
      type="primary"
      size="small"
      @click="addItem"
    >
      添加明细
    </ElButton>
  </div>
</template>

<div class="table-wrapper">
  <ElTable :data="formData.items" border size="default" style="width: 100%">
    <ElTableColumn label="产品名称" min-width="150">
      <template #default="{ row, $index }">
        <ElSelect
          v-if="isEditable"
          v-model="row.product_id"
          placeholder="请选择产品"
          filterable
          style="width: 100%"
          @change="onProductChange(row)"
        >
          <ElOption
            v-for="product in productOptions"
            :key="product.value"
            :label="product.label"
            :value="product.value"
          />
        </ElSelect>
        <span v-else>{{ row.product_name || '-' }}</span>
      </template>
    </ElTableColumn>
    <ElTableColumn label="规格" min-width="120">
      <template #default="{ row, $index }">
        <ElSelect
          v-if="isEditable"
          v-model="row.product_spec_id"
          placeholder="请选择规格"
          style="width: 100%"
          @change="onSpecChange(row)"
        >
          <ElOption
            v-for="spec in getProductSpecs(row.product_id)"
            :key="spec.value"
            :label="spec.label"
            :value="spec.value"
          />
        </ElSelect>
        <span v-else>{{ row.spec_name || '-' }}</span>
      </template>
    </ElTableColumn>
    <ElTableColumn label="数量" width="120">
      <template #default="{ row, $index }">
        <ElInputNumber
          v-if="isEditable"
          v-model="row.quantity"
          :min="0"
          :precision="2"
          :controls="false"
          style="width: 100%"
          @change="calculateItemTotal(row)"
        />
        <span v-else>{{ row.quantity || 0 }}</span>
      </template>
    </ElTableColumn>
    <ElTableColumn label="单价" width="120">
      <template #default="{ row, $index }">
        <ElInputNumber
          v-if="isEditable"
          v-model="row.unit_price"
          :min="0"
          :precision="2"
          :controls="false"
          style="width: 100%"
          @change="calculateItemTotal(row)"
        />
        <span v-else>{{ row.unit_price || 0 }}</span>
      </template>
    </ElTableColumn>
    <ElTableColumn label="小计" width="120">
      <template #default="{ row }">
        <span>{{ row.total_amount || 0 }}</span>
      </template>
    </ElTableColumn>
    <ElTableColumn label="操作" width="100" v-if="isEditable" fixed="right">
      <template #default="{ row, $index }">
        <ElButton type="danger" size="default" @click="removeItem($index)">删除</ElButton>
      </template>
    </ElTableColumn>
  </ElTable>

  <div class="total-summary" style="margin-top: 10px; text-align: right;">
    <span>总数量: {{ totalQuantity }}</span>
    <span style="margin-left: 20px;">总金额: ¥{{ totalAmount }}</span>
  </div>
</div>
</ElCard>
</div>

<template #footer>
  <div class="dialog-footer">
    <ElButton @click="handleCancel">取消</ElButton>
    <ElButton
      v-if="isEditable"
      type="primary"
      :loading="saving"
      @click="handleSave"
    >
      保存
    </ElButton>
    <ElButton
      v-if="isEditable"
      type="success"
      :loading="submitting"
      @click="handleSubmit"
    >
      提交审批
    </ElButton>
  </div>
</template>
</ElDialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface ImsInboundFormData {
    id?: number
    inbound_no: string
    inbound_type: number | null
    inbound_date: string
    warehouse_id: number | null
    supplier_id: number | null
    source_warehouse_id: number | null
    remark: string
    items: any[]
    total_quantity: number
    total_amount: number
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `入库申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起入库申请'
  })

  /** 表单数据 */
  const formData = reactive<ImsInboundFormData & any>({
    inbound_no: '',
    inbound_type: null,
    inbound_date: '',
    warehouse_id: null,
    supplier_id: null,
    source_warehouse_id: null,
    remark: '',
    items: [],
    total_quantity: 0,
    total_amount: 0,
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
  })

  // 选项数据
  const warehouseOptions = ref([])
  const supplierOptions = ref([])
  const productOptions = ref([])
  const productSpecOptions = ref([])

  // 计算总数量和总金额
  const totalQuantity = computed(() => {
    return formData.items.reduce((sum, item) => sum + (item.quantity || 0), 0)
  })

  const totalAmount = computed(() => {
    return formData.items.reduce((sum, item) => sum + (item.total_amount || 0), 0)
  })

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    inbound_no: [
      { required: true, message: '请输入入库单号', trigger: 'blur' }
    ],
    inbound_type: [
      { required: true, message: '请选择入库类型', trigger: 'change' }
    ],
    inbound_date: [
      { required: true, message: '请选择入库日期', trigger: 'change' }
    ],
    warehouse_id: [
      { required: true, message: '请选择入库仓库', trigger: 'change' }
    ]
  }

  // ==================== 方法定义 ====================

  /**
   * 添加明细项
   */
  const addItem = () => {
    formData.items.push({
      product_id: null,
      product_spec_id: null,
      product_name: '',
      spec_name: '',
      quantity: 0,
      unit_price: 0,
      total_amount: 0
    })
  }

  /**
   * 删除明细项
   */
  const removeItem = (index: number) => {
    formData.items.splice(index, 1)
  }

  /**
   * 计算明细小计
   */
  const calculateItemTotal = (item: any) => {
    item.total_amount = (item.quantity || 0) * (item.unit_price || 0)
  }

  /**
   * 产品选择变化
   */
  const onProductChange = (item: any) => {
    const product = productOptions.value.find(p => p.value === item.product_id)
    if (product) {
      item.product_name = product.label
      item.unit_price = product.price || 0
      item.product_spec_id = null
      item.spec_name = ''
      calculateItemTotal(item)
    }
  }

  /**
   * 规格选择变化
   */
  const onSpecChange = (item: any) => {
    const spec = getProductSpecs(item.product_id).find(s => s.value === item.product_spec_id)
    if (spec) {
      item.spec_name = spec.label
    }
  }

  /**
   * 获取产品规格选项
   */
  const getProductSpecs = (productId: number) => {
    return productSpecOptions.value.filter(spec => spec.product_id === productId) || []
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('ims_inbound_approval-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      inbound_no: '',
      inbound_type: null,
      inbound_date: '',
      warehouse_id: null,
      supplier_id: null,
      source_warehouse_id: null,
      remark: '',
      items: [],
      total_quantity: 0,
      total_amount: 0,
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        // 确保items是数组
        if (!Array.isArray(formData.items)) {
          formData.items = []
        }

        console.log('入库申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('ims_inbound_approval-form setFormData called with:', data)
    Object.assign(formData, data)
    if (!Array.isArray(formData.items)) {
      formData.items = []
    }
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('ims_inbound_approval-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: ImsInboundFormData = {
        inbound_no: formData.inbound_no,
        inbound_type: formData.inbound_type,
        inbound_date: formData.inbound_date,
        warehouse_id: formData.warehouse_id,
        supplier_id: formData.supplier_id,
        source_warehouse_id: formData.source_warehouse_id,
        remark: formData.remark,
        items: formData.items,
        total_quantity: totalQuantity.value,
        total_amount: totalAmount.value
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('入库申请保存数据:', submitData)
      emit('save', submitData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      if (formData.items.length === 0) {
        ElMessage.warning('请至少添加一条入库明细')
        return
      }

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: ImsInboundFormData = {
        inbound_no: formData.inbound_no,
        inbound_type: formData.inbound_type,
        inbound_date: formData.inbound_date,
        warehouse_id: formData.warehouse_id,
        supplier_id: formData.supplier_id,
        source_warehouse_id: formData.source_warehouse_id,
        remark: formData.remark,
        items: formData.items,
        total_quantity: totalQuantity.value,
        total_amount: totalAmount.value
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('入库申请提交数据:', submitData)
      emit('submit', submitData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 监听总数量和总金额变化
  watch([totalQuantity, totalAmount], () => {
    formData.total_quantity = totalQuantity.value
    formData.total_amount = totalAmount.value
  })

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }

  // 卡片样式
  .items-card {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .table-wrapper {
    overflow-x: auto;
    max-width: 100%;

    :deep(.el-table) {
      width: 100%;
    }

    :deep(.el-table__body-wrapper) {
      overflow-x: auto;
    }
  }

  .total-summary {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }
</style>
