<?php
declare(strict_types=1);

namespace app\common\utils;

/**
 * 数字转换工具类
 * 提供数字转中文大写、格式化等功能
 * 主要用于财务相关的业务场景
 */
class NumberConverter
{
    /**
     * 中文数字映射
     */
    private static array $digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    
    /**
     * 中文单位映射
     */
    private static array $units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];
    
    /**
     * 小数单位映射
     */
    private static array $decimalUnits = ['角', '分'];
    
    /**
     * 数字转中文大写金额
     * 主要用于财务相关业务场景，如出库申请、出货申请、付款申请等
     * 
     * @param float|int $num 要转换的数字，支持整数和小数
     * @return string 中文大写金额字符串
     * 
     * @example
     * // 基本用法
     * NumberConverter::convertToChineseNumber(0); // 返回 "零元整"
     * NumberConverter::convertToChineseNumber(123.45); // 返回 "壹佰贰拾叁元肆角伍分"
     * NumberConverter::convertToChineseNumber(1000); // 返回 "壹仟元整"
     * NumberConverter::convertToChineseNumber(10000.50); // 返回 "壹万元伍角"
     * NumberConverter::convertToChineseNumber(100000000); // 返回 "壹亿元整"
     * 
     * // 特殊情况
     * NumberConverter::convertToChineseNumber(-100); // 返回 "负壹佰元整"
     * NumberConverter::convertToChineseNumber(1234567890.12); // 返回 "拾贰亿叁仟肆佰伍拾陆万柒仟捌佰玖拾元壹角贰分"
     */
    public static function convertToChineseNumber($num): string
    {
        // 处理特殊情况
        if (!is_numeric($num)) {
            return '零元整';
        }
        
        $num = floatval($num);
        
        if ($num == 0) {
            return '零元整';
        }
        
        // 处理负数
        $isNegative = $num < 0;
        $absNum = abs($num);
        
        // 格式化为2位小数
        $numStr = number_format($absNum, 2, '.', '');
        [$integerPart, $decimalPart] = explode('.', $numStr);
        
        $result = '';
        
        // 处理整数部分
        if (intval($integerPart) === 0) {
            $result = '零';
        } else {
            $result = self::convertIntegerPart($integerPart);
        }
        
        $result .= '元';
        
        // 处理小数部分
        if (intval($decimalPart) > 0) {
            $jiao = intval($decimalPart[0]);
            $fen = intval($decimalPart[1]);
            
            if ($jiao > 0) {
                $result .= self::$digits[$jiao] . self::$decimalUnits[0];
            }
            if ($fen > 0) {
                $result .= self::$digits[$fen] . self::$decimalUnits[1];
            }
        } else {
            $result .= '整';
        }
        
        // 处理负数前缀
        if ($isNegative) {
            $result = '负' . $result;
        }
        
        return $result;
    }
    
    /**
     * 转换整数部分为中文
     * 
     * @param string $integerPart 整数部分字符串
     * @return string 中文表示
     */
    private static function convertIntegerPart(string $integerPart): string
    {
        $integerArray = array_reverse(str_split($integerPart));
        $result = '';
        $hasZero = false; // 标记是否需要添加零
        
        for ($i = 0; $i < count($integerArray); $i++) {
            $digit = intval($integerArray[$i]);
            $unitIndex = $i % 9; // 处理亿级别的循环
            
            if ($digit !== 0) {
                // 如果前面有零且当前不是万位或亿位，添加零
                if ($hasZero && $unitIndex !== 4 && $unitIndex !== 8) {
                    $result = '零' . $result;
                }
                $result = self::$digits[$digit] . self::$units[$unitIndex] . $result;
                $hasZero = false;
            } else {
                // 当前位是0
                if ($unitIndex === 4 || $unitIndex === 8) {
                    // 万位或亿位，需要添加单位
                    if ($result && !str_starts_with($result, self::$units[$unitIndex])) {
                        $result = self::$units[$unitIndex] . $result;
                    }
                }
                $hasZero = true;
            }
        }
        
        return $result;
    }
    
    /**
     * 格式化数字为千分位显示
     * 
     * @param float|int $num 要格式化的数字
     * @param int $decimals 小数位数，默认2位
     * @return string 格式化后的字符串
     * 
     * @example
     * NumberConverter::formatNumberWithCommas(1234567.89); // 返回 "1,234,567.89"
     * NumberConverter::formatNumberWithCommas(1234567.89, 0); // 返回 "1,234,568"
     * NumberConverter::formatNumberWithCommas(1234567.89, 3); // 返回 "1,234,567.890"
     */
    public static function formatNumberWithCommas($num, int $decimals = 2): string
    {
        if (!is_numeric($num)) {
            return '0';
        }
        
        return number_format(floatval($num), $decimals, '.', ',');
    }
    
    /**
     * 格式化货币显示
     * 
     * @param float|int $num 要格式化的数字
     * @param string $currency 货币符号，默认'¥'
     * @param int $decimals 小数位数，默认2位
     * @return string 格式化后的货币字符串
     * 
     * @example
     * NumberConverter::formatCurrency(1234.56); // 返回 "¥1,234.56"
     * NumberConverter::formatCurrency(1234.56, '$'); // 返回 "$1,234.56"
     * NumberConverter::formatCurrency(1234.56, '¥', 0); // 返回 "¥1,235"
     */
    public static function formatCurrency($num, string $currency = '¥', int $decimals = 2): string
    {
        if (!is_numeric($num)) {
            return $currency . '0';
        }
        
        return $currency . self::formatNumberWithCommas($num, $decimals);
    }
    
    /**
     * 安全的数字加法运算（避免浮点数精度问题）
     * 
     * @param float|int $a 第一个数字
     * @param float|int $b 第二个数字
     * @return float 相加结果
     * 
     * @example
     * NumberConverter::safeAdd(0.1, 0.2); // 返回 0.3（而不是 0.30000000000000004）
     * NumberConverter::safeAdd(1.23, 4.56); // 返回 5.79
     */
    public static function safeAdd($a, $b): float
    {
        if (!is_numeric($a) || !is_numeric($b)) {
            return 0.0;
        }
        
        $a = floatval($a);
        $b = floatval($b);
        
        $aDecimals = strlen(substr(strrchr(rtrim(sprintf('%.10f', $a), '0'), '.'), 1));
        $bDecimals = strlen(substr(strrchr(rtrim(sprintf('%.10f', $b), '0'), '.'), 1));
        $maxDecimals = max($aDecimals, $bDecimals);
        $multiplier = pow(10, $maxDecimals);
        
        return round(($a * $multiplier + $b * $multiplier)) / $multiplier;
    }
    
    /**
     * 安全的数字乘法运算（避免浮点数精度问题）
     * 
     * @param float|int $a 第一个数字
     * @param float|int $b 第二个数字
     * @return float 相乘结果
     * 
     * @example
     * NumberConverter::safeMultiply(0.1, 3); // 返回 0.3（而不是 0.30000000000000004）
     * NumberConverter::safeMultiply(1.23, 4.56); // 返回 5.61
     */
    public static function safeMultiply($a, $b): float
    {
        if (!is_numeric($a) || !is_numeric($b)) {
            return 0.0;
        }
        
        $a = floatval($a);
        $b = floatval($b);
        
        $aDecimals = strlen(substr(strrchr(rtrim(sprintf('%.10f', $a), '0'), '.'), 1));
        $bDecimals = strlen(substr(strrchr(rtrim(sprintf('%.10f', $b), '0'), '.'), 1));
        $multiplier = pow(10, $aDecimals + $bDecimals);
        
        return round((round($a * pow(10, $aDecimals)) * round($b * pow(10, $bDecimals)))) / $multiplier;
    }
    
    /**
     * 保留指定小数位数（四舍五入）
     * 
     * @param float|int $num 要处理的数字
     * @param int $decimals 小数位数，默认2位
     * @return float 处理后的数字
     * 
     * @example
     * NumberConverter::roundToDecimals(1.2345, 2); // 返回 1.23
     * NumberConverter::roundToDecimals(1.2356, 2); // 返回 1.24
     * NumberConverter::roundToDecimals(1.2345); // 返回 1.23（默认2位小数）
     */
    public static function roundToDecimals($num, int $decimals = 2): float
    {
        if (!is_numeric($num)) {
            return 0.0;
        }
        
        return round(floatval($num), $decimals);
    }
    
    /**
     * 判断是否为有效的数字
     * 
     * @param mixed $value 要检查的值
     * @return bool 是否为有效数字
     * 
     * @example
     * NumberConverter::isValidNumber(123); // 返回 true
     * NumberConverter::isValidNumber('123'); // 返回 true
     * NumberConverter::isValidNumber('abc'); // 返回 false
     * NumberConverter::isValidNumber(null); // 返回 false
     */
    public static function isValidNumber($value): bool
    {
        return is_numeric($value) && is_finite(floatval($value));
    }
    
    /**
     * 转换为安全的数字（如果无效则返回默认值）
     * 
     * @param mixed $value 要转换的值
     * @param float $defaultValue 默认值，默认为0
     * @return float 转换后的数字
     * 
     * @example
     * NumberConverter::toSafeNumber('123'); // 返回 123.0
     * NumberConverter::toSafeNumber('abc'); // 返回 0.0
     * NumberConverter::toSafeNumber('abc', 100); // 返回 100.0
     */
    public static function toSafeNumber($value, float $defaultValue = 0.0): float
    {
        return self::isValidNumber($value) ? floatval($value) : $defaultValue;
    }
}
