-- 项目任务管理模块菜单配置
-- 基于飞书风格的项目管理界面设计
-- 支持多租户权限控制

-- 获取当前最大菜单ID
SET @max_id = (SELECT IFNULL(MAX(id), 0) FROM system_menu);

-- 项目管理主菜单 (目录)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 1, 0, '项目管理', 'project', '/project', '', 0, 'el-icon-folder-opened', 300, 0, 0, 1, 1, '项目任务管理模块主菜单', NOW(), NOW());

-- 项目概览 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 2, @max_id + 1, '项目概览', 'project:dashboard', '/project/dashboard', '/project/dashboard/index', 1, 'el-icon-data-analysis', 1, 0, 1, 1, 1, '项目数据概览和统计', NOW(), NOW());

-- 我的项目 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 3, @max_id + 1, '我的项目', 'project:my-projects', '/project/my-projects', '/project/project/my-list', 1, 'el-icon-user', 2, 0, 1, 1, 1, '我参与的项目列表', NOW(), NOW());

-- 全部项目 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 4, @max_id + 1, '全部项目', 'project:all-projects', '/project/all-projects', '/project/project/list', 1, 'el-icon-folder', 3, 0, 1, 1, 1, '全部项目管理', NOW(), NOW());

-- 项目详情 (隐藏菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 5, @max_id + 4, '项目详情', 'project:detail', '/project/detail/:id', '/project/project/detail', 1, '', 1, 0, 1, 0, 1, '项目详情页面', NOW(), NOW());

-- 任务管理 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 6, @max_id + 1, '任务管理', 'project:tasks', '/project/tasks', '/project/task/list', 1, 'el-icon-tickets', 4, 0, 1, 1, 1, '任务列表管理', NOW(), NOW());

-- 我的任务 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 7, @max_id + 1, '我的任务', 'project:my-tasks', '/project/my-tasks', '/project/task/my-list', 1, 'el-icon-user', 5, 0, 1, 1, 1, '我的任务列表', NOW(), NOW());

-- 工时记录 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 8, @max_id + 1, '工时记录', 'project:time-logs', '/project/time-logs', '/project/time-log/list', 1, 'el-icon-timer', 6, 0, 1, 1, 1, '工时记录管理', NOW(), NOW());

-- 项目文档 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 9, @max_id + 1, '项目文档', 'project:documents', '/project/documents', '/project/document/list', 1, 'el-icon-document', 7, 0, 1, 1, 1, '项目文档管理', NOW(), NOW());

-- 统计报表 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 10, @max_id + 1, '统计报表', 'project:statistics', '/project/statistics', '/project/statistics/index', 1, 'el-icon-pie-chart', 8, 0, 1, 1, 1, '项目统计报表', NOW(), NOW());

-- 系统设置 (目录)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 11, @max_id + 1, '系统设置', 'project:settings', '/project/settings', '', 0, 'el-icon-setting', 9, 0, 0, 1, 1, '项目系统设置', NOW(), NOW());

-- 项目模板 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 12, @max_id + 11, '项目模板', 'project:templates', '/project/templates', '/project/template/list', 1, 'el-icon-files', 1, 0, 1, 1, 1, '项目模板管理', NOW(), NOW());

-- 任务状态 (菜单)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 13, @max_id + 11, '任务状态', 'project:task-status', '/project/task-status', '/project/task-status/list', 1, 'el-icon-finished', 2, 0, 1, 1, 1, '任务状态配置', NOW(), NOW());

-- ================================
-- 按钮权限配置
-- ================================

-- 项目管理按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 20, @max_id + 4, '新增项目', 'project:project:add', '', '', 2, '', 1, 0, 0, 1, 1, '新增项目按钮权限', NOW(), NOW()),
(@max_id + 21, @max_id + 4, '编辑项目', 'project:project:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑项目按钮权限', NOW(), NOW()),
(@max_id + 22, @max_id + 4, '删除项目', 'project:project:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除项目按钮权限', NOW(), NOW()),
(@max_id + 23, @max_id + 4, '归档项目', 'project:project:archive', '', '', 2, '', 4, 0, 0, 1, 1, '归档项目按钮权限', NOW(), NOW()),
(@max_id + 24, @max_id + 4, '项目成员', 'project:project:members', '', '', 2, '', 5, 0, 0, 1, 1, '项目成员管理权限', NOW(), NOW()),
(@max_id + 25, @max_id + 4, '项目设置', 'project:project:settings', '', '', 2, '', 6, 0, 0, 1, 1, '项目设置权限', NOW(), NOW());

-- 任务管理按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 30, @max_id + 6, '新增任务', 'project:task:add', '', '', 2, '', 1, 0, 0, 1, 1, '新增任务按钮权限', NOW(), NOW()),
(@max_id + 31, @max_id + 6, '编辑任务', 'project:task:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑任务按钮权限', NOW(), NOW()),
(@max_id + 32, @max_id + 6, '删除任务', 'project:task:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除任务按钮权限', NOW(), NOW()),
(@max_id + 33, @max_id + 6, '分配任务', 'project:task:assign', '', '', 2, '', 4, 0, 0, 1, 1, '分配任务按钮权限', NOW(), NOW()),
(@max_id + 34, @max_id + 6, '任务状态', 'project:task:status', '', '', 2, '', 5, 0, 0, 1, 1, '修改任务状态权限', NOW(), NOW()),
(@max_id + 35, @max_id + 6, '任务评论', 'project:task:comment', '', '', 2, '', 6, 0, 0, 1, 1, '任务评论权限', NOW(), NOW()),
(@max_id + 36, @max_id + 6, '任务附件', 'project:task:attachment', '', '', 2, '', 7, 0, 0, 1, 1, '任务附件权限', NOW(), NOW());

-- 工时记录按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 40, @max_id + 8, '记录工时', 'project:time:add', '', '', 2, '', 1, 0, 0, 1, 1, '记录工时按钮权限', NOW(), NOW()),
(@max_id + 41, @max_id + 8, '编辑工时', 'project:time:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑工时按钮权限', NOW(), NOW()),
(@max_id + 42, @max_id + 8, '删除工时', 'project:time:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除工时按钮权限', NOW(), NOW()),
(@max_id + 43, @max_id + 8, '审核工时', 'project:time:approve', '', '', 2, '', 4, 0, 0, 1, 1, '审核工时按钮权限', NOW(), NOW());

-- 文档管理按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 50, @max_id + 9, '新增文档', 'project:document:add', '', '', 2, '', 1, 0, 0, 1, 1, '新增文档按钮权限', NOW(), NOW()),
(@max_id + 51, @max_id + 9, '编辑文档', 'project:document:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑文档按钮权限', NOW(), NOW()),
(@max_id + 52, @max_id + 9, '删除文档', 'project:document:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除文档按钮权限', NOW(), NOW()),
(@max_id + 53, @max_id + 9, '发布文档', 'project:document:publish', '', '', 2, '', 4, 0, 0, 1, 1, '发布文档按钮权限', NOW(), NOW());

-- 系统设置按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 60, @max_id + 12, '新增模板', 'project:template:add', '', '', 2, '', 1, 0, 0, 1, 1, '新增模板按钮权限', NOW(), NOW()),
(@max_id + 61, @max_id + 12, '编辑模板', 'project:template:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑模板按钮权限', NOW(), NOW()),
(@max_id + 62, @max_id + 12, '删除模板', 'project:template:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除模板按钮权限', NOW(), NOW()),
(@max_id + 63, @max_id + 13, '新增状态', 'project:status:add', '', '', 2, '', 1, 0, 0, 1, 1, '新增状态按钮权限', NOW(), NOW()),
(@max_id + 64, @max_id + 13, '编辑状态', 'project:status:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑状态按钮权限', NOW(), NOW()),
(@max_id + 65, @max_id + 13, '删除状态', 'project:status:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除状态按钮权限', NOW(), NOW());
