<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\system\model\AdminModel;
use app\workflow\model\WorkflowInstance;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 合同表模型 - 基于crm_data.sql表结构
 */
class CrmContract extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_contract';
	
	// 字段类型转换
	protected $type = [
		'customer_id'      => 'integer',
		'business_id'      => 'integer',
		'contract_amount'  => 'float',
		'paid_amount'      => 'float',
		'status'           => 'integer',
		'payment_status'   => 'integer',
		'owner_user_id'    => 'integer',
		'payment_deadline' => 'date',
		'start_date'       => 'date',
		'end_date'         => 'date',
		'sign_date'        => 'date',
	];
	
	protected $append = [
		'creator',
		'customer',
		'owner',
		'workflow'
	];
	
	// 关联负责人
	public function owner(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'owner_user_id', 'id')
		            ->bind([
			            'owner_name' => 'real_name'
		            ]);
	}
	
	// 关联客户信息
	public function customer(): BelongsTo
	{
		return $this->belongsTo(CrmCustomer::class, 'customer_id', 'id')
		            ->bind([
			            'customer_number',
			            'customer_name',
		            ]);
	}
	
	// 关联商机
	public function business(): BelongsTo
	{
		return $this->belongsTo(CrmBusiness::class, 'business_id', 'id');
	}
	
	// 关联合同产品
	public function products(): HasMany
	{
		return $this->hasMany(CrmContractProduct::class, 'contract_id', 'id');
	}
	
	// 关联回款记录
	public function receivables(): HasMany
	{
		return $this->hasMany(CrmContractReceivable::class, 'contract_id', 'id');
	}
	
	// 关联工作流实例
	public function workflow(): BelongsTo
	{
		return $this->belongsTo(WorkflowInstance::class, 'workflow_instance_id', 'id')
		            ->bind([
			            'workflow_status' => 'status',
			            'workflow_title'  => 'title'
		            ]);
	}
	
	// 获取合同状态文本（基于workflow_instance状态）
	public function getStatusTextAttr($value, $data)
	{
		// 如果有工作流实例，使用工作流状态
		if (isset($data['workflow_status'])) {
			$statusMap = [
				0 => '已保存(草稿)',
				1 => '审批中',
				2 => '已通过',
				3 => '已驳回',
				4 => '已终止',
				5 => '已撤回'
			];
			return $statusMap[$data['workflow_status']] ?? '已保存(草稿)';
		}
		
		// 兼容旧的状态字段
		$statusMap = [
			0 => '草稿',
			1 => '待审核',
			2 => '执行中',
			3 => '已完成',
			4 => '已终止'
		];
		return $statusMap[$data['status']] ?? '草稿';
	}
	
	// 获取付款状态文本
	public function getPaymentStatusTextAttr($value, $data)
	{
		$statusMap = [
			0 => '未付款',
			1 => '部分付款',
			2 => '已付清',
			3 => '逾期'
		];
		return $statusMap[$data['payment_status']] ?? '未付款';
	}
	
	public function getImpSceneFields(): array
	{
		return [
			'customer_id'      => [
				'label' => '客户ID',
				'type'  => 'number',
			],
			'business_id'      => [
				'label' => '商机ID',
				'type'  => 'number',
			],
			'contract_number'  => [
				'label' => '合同编号',
				'type'  => 'text',
			],
			'contract_name'    => [
				'label' => '合同名称',
				'type'  => 'text',
			],
			'contract_amount'  => [
				'label' => '合同金额',
				'type'  => 'number',
			],
			'paid_amount'      => [
				'label' => '已支付金额',
				'type'  => 'number',
			],
			'payment_terms'    => [
				'label' => '付款条件',
				'type'  => 'textarea',
			],
			'payment_deadline' => [
				'label' => '最终付款期限',
				'type'  => 'date',
			],
			'start_date'       => [
				'label' => '合同开始日期',
				'type'  => 'date',
			],
			'end_date'         => [
				'label' => '合同结束日期',
				'type'  => 'date',
			],
			'sign_date'        => [
				'label' => '签署日期',
				'type'  => 'date',
			],
			'status'           => [
				'label'   => '合同状态',
				'type'    => 'select',
				'options' => [
					0 => '草稿',
					1 => '待审核',
					2 => '执行中',
					3 => '已完成',
					4 => '已终止'
				]
			],
			'payment_status'   => [
				'label'   => '付款状态',
				'type'    => 'select',
				'options' => [
					0 => '未付款',
					1 => '部分付款',
					2 => '已付清',
					3 => '逾期'
				]
			],
			'type'             => [
				'label' => '合同类型',
				'type'  => 'text',
			],
			'payment_method'   => [
				'label' => '付款方式',
				'type'  => 'text',
			],
			'description'      => [
				'label' => '合同描述',
				'type'  => 'textarea',
			]
		];
	}

	/**
	 * 获取业务代码（WorkflowableModel抽象方法实现）
	 */
	public function getBusinessCode(): string
	{
		return 'crm_contract';
	}

	/**
	 * 获取审批标题（WorkflowableModel抽象方法实现）
	 */
	public function getApprovalTitle(): string
	{
		return '合同审批-' . $this->contract_name;
	}
	
	public function getDefaultSearchFields(): array
	{
		return [
			'approval_status' => ['type' => 'eq'],
			'contract_name'         => ['type' => 'like'],
			'contract_number'   => ['type' => 'like'],
		];
	}
}