---
type: "manual"
---

# 多租户前后端分离框架系统 - Augment 规则

## 项目概述
基于 ThinkPHP 8 + Vue 3 + TypeScript 的多租户前后端分离框架系统，支持 RBAC 权限管理、CRUD 生成器、导入导出等功能。

## 技术栈规范

### 后端技术栈
- **框架**: ThinkPHP 8
- **PHP版本**: 8.2+
- **数据库**: MySQL
- **缓存**: Redis
- **架构模式**: 控制器 + 服务层 + 模型

### 前端技术栈
- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **构建工具**: Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **CSS**: SCSS

## 目录结构规范

### 后端目录结构
```
app/
├── {module}/                    # 模块目录
│   ├── controller/             # 控制器
│   ├── model/                  # 模型
│   ├── service/                # 服务层
│   ├── validate/               # 验证器
│   └── middleware/             # 中间件
├── common/                     # 公共模块
│   ├── core/                   # 核心功能
│   ├── generator/              # 代码生成器
│   └── traits/                 # 公共特性
```

### 前端目录结构
```
frontend/src/
├── api/                        # API接口
├── components/                 # 公共组件
├── views/                      # 页面视图
├── stores/                     # Pinia状态管理
├── utils/                      # 工具函数
├── types/                      # TypeScript类型定义
└── styles/                     # 样式文件
```

## 编码规范

### 后端编码规范

#### 控制器规范
- 继承 `BaseController`
- 使用 `CrudService` 单例处理基础CRUD
- 复杂业务逻辑放在服务层或模型方法中
- 避免在新增/编辑操作中使用静态方法，确保租户隔离

```php
class TestController extends BaseController
{
    protected $service;
    
    public function __construct()
    {
        $this->service = CrudService::getInstance();
    }
}
```

#### 模型规范
- 继承 `BaseModel`
- 使用软删除: `use SoftDelete`
- 定义字段映射: `protected $field = []`
- 租户隔离字段: `tenant_id`

#### 服务层规范
- 继承 `BaseService`
- 使用 Trait 扩展功能: `ExportableTrait`, `ImportableTrait`
- 单例模式: `CrudService::getInstance()`

### 前端编码规范

#### Vue 组件规范
- 使用 Composition API
- TypeScript 严格模式
- 组件自动导入 (无需手动 import)
- 单文件组件结构:

```vue
<template>
  <!-- 模板 -->
</template>

<script setup lang="ts">
// 逻辑
</script>

<style lang="scss" scoped>
/* 样式 */
</style>
```

#### API 接口规范
- 统一使用 `request` 工具
- 返回类型定义: `BaseResult<T>`
- 静态方法命名: 动词 + 名词

```typescript
export class TestApi {
  static getList(params: any) {
    return request.get<BaseResult<any>>({
      url: 'test/list',
      params
    })
  }
}
```

## CRUD 生成器规范

### 数据表注释规范
- 字段注释格式: `描述@验证规则@组件类型@其他配置`
- 验证规则: `@required`, `@max:100`, `@min:1`
- 组件类型: `@switch`, `@select`, `@textarea`, `@number`
- 搜索配置: `@search:like`, `@search:eq`

### 生成器命令
```bash
# 生成完整CRUD (包含前端)
php think generator:crud table_name --module=module_name --frontend --overwrite

# 仅生成后端
php think generator:crud table_name --module=module_name --overwrite
```

### 生成文件结构
- 后端: Controller + Model + Service
- 前端: list.vue + form-dialog.vue + import-export-dialog.vue

## 权限控制规范

### 多租户隔离
- 所有数据表必须包含 `tenant_id` 字段
- 查询时自动添加租户条件
- 新增时自动设置当前租户ID

### RBAC权限
- 角色-权限-用户三级关联
- 权限标识格式: `module:controller:action`
- 数据权限: 基于 `creator_id` 字段

## 前端组件规范

### 表格组件
- 使用 `el-table` + 自定义列组件
- 支持排序、筛选、分页
- 操作列统一样式

### 表单组件
- 使用 `el-form` + 验证规则
- 支持动态表单项
- 统一错误处理

### 对话框组件
- 使用 `el-dialog`
- 支持拖拽、全屏
- 统一确认/取消逻辑

## 开发工作流

### 新功能开发流程
1. 设计数据表结构 (包含完整字段注释)
2. 使用生成器生成基础代码
3. 根据业务需求调整生成的代码
4. 添加自定义业务逻辑
5. 测试功能完整性

### 代码提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构
- test: 测试相关

## 性能优化规范

### 后端优化
- 使用缓存减少数据库查询
- 分页查询避免大数据量
- 索引优化查询性能

### 前端优化
- 组件懒加载
- 图片懒加载
- 合理使用 keep-alive
- 避免不必要的响应式数据

## 安全规范

### 数据安全
- 输入验证和过滤
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证

### 权限安全
- 接口权限验证
- 数据权限隔离
- 敏感操作日志记录

## 测试规范

### 单元测试
- 服务层方法测试
- 工具函数测试
- 组件功能测试

### 集成测试
- API接口测试
- 业务流程测试
- 权限验证测试

## 部署规范

### 环境配置
- 开发环境: `.env.development`
- 测试环境: `.env.testing`
- 生产环境: `.env.production`

### 构建部署
- 后端: Composer 依赖管理
- 前端: Vite 构建优化
- 静态资源 CDN 部署