<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\core\constants\CacheConstant;
use app\common\exception\ValidateFailedException;
use app\common\utils\CacheUtil;
use app\system\model\TenantConfigModel;

/**
 * 商户配置服务类
 */
class TenantConfigService extends BaseService
{
	
	protected string $cache_tag = CacheConstant::TENANT_CONFIG_PREFIX;
	
	protected function __construct()
	{
		$this->model = new TenantConfigModel();
		parent::__construct();
	}
	
	/**
	 * @notes 设置配置值
	 * @param string $group
	 * @param array  $params
	 * @return bool
	 * @date  2021/12/27 15:00
	 */
	public function create(string $group, array $params): bool
	{
		if (empty($group) || empty($params)) {
			throw new ValidateFailedException('参数错误');
		}
		
		foreach ($params as $itemKey => $itemValue) {
			
			$info               = $this->crudService->getOne([
				[
					'group',
					'=',
					$group
				],
				[
					'item_key',
					'=',
					$itemKey
				]
			]);
			$data               = [];
			$data['item_value'] = $itemValue;
			if ($info->isEmpty()) {
				$data['group']    = $group;
				$data['item_key'] = $itemKey;
				$res              = $info->saveByCreate($data);
			}
			else {
				$res = $info->saveByUpdate($data);
			}
			if (!$res) {
				throw new ValidateFailedException('配置保存失败');
			}
		}
		
		// 缓存处理
		CacheUtil::delete($this->cache_tag . $group);
		// 返回原始值
		return true;
	}
	
	/**
	 * @notes 获取配置值
	 * @param string $group
	 * @param string $itemKey
	 * @return mixed
	 * @date  2021/7/15 15:16
	 */
	public function getInfo(string $group, string $itemKey = ''): mixed
	{
		$data = [];
		if (!empty($group)) {

			$cacheTag = $this->cache_tag . $group;

			$data = CacheUtil::get($cacheTag, []);

			if (empty($data)) {

				$where = [
					[
						'group',
						'=',
						$group
					],
				];

				if (!empty($itemKey)) {
					$where[] = [
						'item_key',
						'=',
						$itemKey
					];
				}

				$result = $this->model->where($where)
				                      ->field('group,item_key,item_value')
				                      ->select()
				                      ->toArray();

				// 转换为键值对格式
				$data = [];
				foreach ($result as $item) {
					$data[$item['item_key']] = $item['item_value'];
				}

				if (!empty($data)) {
					CacheUtil::set($cacheTag, $data, 3600);
				}
			}

		}

		return $data;
	}
	
}