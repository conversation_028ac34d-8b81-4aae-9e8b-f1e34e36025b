<?php
declare(strict_types=1);

namespace app\notice\service\interfaces;

/**
 * 消息队列服务接口
 */
interface QueueServiceInterface
{
	/**
	 * 添加消息到队列
	 *
	 * @param int|string $messageId    消息ID
	 * @param string     $channel      通道编码
	 * @param array      $recipientIds 接收人ID数组
	 * @param array      $options      选项参数
	 * @return int 返回队列ID,失败为0
	 */
	public function addToQueue(int|string $messageId, string $channel, array $recipientIds, array $options = []): int;
	
	/**
	 * 批量添加消息到队列
	 *
	 * @param array $queueItems 队列项数组，每项包含 messageId, channel, recipientIds
	 * @return bool 是否成功
	 */
	public function batchAddToQueue(array $queueItems): bool;
	
	/**
	 * 处理队列中的消息
	 *
	 * @param int $limit 处理数量限制
	 * @return array 处理结果 ['success' => 成功数, 'failed' => 失败数]
	 */
	public function processQueue(int $limit = 10): array;
	
	/**
	 * 重试失败的队列项
	 *
	 * @param int $queueId 队列ID
	 * @return bool 是否成功
	 */
	public function retryQueueItem(int $queueId): bool;
	
	/**
	 * 批量重试失败的队列项
	 *
	 * @param array $queueIds 队列ID数组
	 * @return array 处理结果 ['success' => 成功数, 'failed' => 失败数]
	 */
	public function batchRetryQueueItems(array $queueIds): array;
	
	/**
	 * 获取队列状态统计
	 *
	 * @return array 状态统计 ['pending' => 待处理数, 'processing' => 处理中数, 'success' => 成功数, 'failed' => 失败数]
	 */
	public function getQueueStats(): array;
	
	/**
	 * 清理已处理的队列项
	 *
	 * @param int $days 保留天数，默认7天
	 * @return int 清理数量
	 */
	public function cleanProcessedItems(int $days = 7): int;
} 