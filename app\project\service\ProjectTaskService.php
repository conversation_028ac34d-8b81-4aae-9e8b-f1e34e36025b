<?php
declare(strict_types=1);

namespace app\project\service;

use app\common\core\base\BaseService;
use app\project\model\ProjectTask;

/**
 * 任务表服务类
 */
class ProjectTaskService extends BaseService
{
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new ProjectTask();
		parent::__construct();
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'project_id' => ['type' => 'eq'],
			
			'title' => ['type' => 'like'],
			
			'status' => ['type' => 'eq'],
			
			'priority' => ['type' => 'eq'],
			
			'assignee_id' => ['type' => 'eq'],
			
			'start_date' => ['type' => 'between'],
			
			'due_date' => ['type' => 'between'],
			
			'completed_at' => ['type' => 'between'],
		
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			// 在这里定义验证规则
			// 例如：'username' => 'require|unique:project_task',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 获取我的任务列表
	 */
	public function getMyTasks(int $userId, array $params = [])
	{
		$where = [
			[
				'assignee_id',
				'=',
				$userId
			]
		];
		
		// 搜索条件
		if (!empty($params['title'])) {
			$where[] = [
				'title',
				'like',
				'%' . $params['title'] . '%'
			];
		}
		
		if (!empty($params['status'])) {
			$where[] = [
				'status',
				'=',
				$params['status']
			];
		}
		
		if (!empty($params['project_id'])) {
			$where[] = [
				'project_id',
				'=',
				$params['project_id']
			];
		}
		
		return $this->crudService->getPageList($where, ['id' => 'desc'], $params['page'] ?? 1, $params['size'] ?? 20, ['assignee']);
	}
	
	/**
	 * 更新任务状态
	 */
	public function updateTaskStatus(int $id, int $status, int $userId): bool
	{
		$task = $this->crudService->getDetail($id);
		if ($task->isEmpty()) {
			throw new \Exception('任务不存在');
		}
		
		return $task->updateStatus($status, $userId);
	}
	
	/**
	 * 分配任务
	 */
	public function assignTask(int $id, int $assigneeId, int $operatorId): bool
	{
		$task = $this->crudService->getDetail($id);
		if ($task->isEmpty()) {
			throw new \Exception('任务不存在');
		}
		
		return $task->assignTo($assigneeId, $operatorId);
	}
}