<?php

namespace app\system\controller;

use app\common\core\base\BaseAdminController;
use app\common\lib\upload\UploadService;
use app\system\service\AttachmentService;
use think\response\Json;

class Attachment extends BaseAdminController
{
	/**
	 * @var AttachmentService
	 */
	private AttachmentService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = AttachmentService::getInstance();
		$this->service->getCrudService()
		              ->setEnableDataPermission(false);
	}
	
	/**
	 * 获取文件列表
	 */
	public function index(): Json
	{
		return $this->success('获取成功', $this->service->getList(input()));
	}
	
	
	/**
	 * 移动文件
	 */
	public function move($id): Json
	{
		return $this->service->getCrudService()
		                     ->edit([
			                     'cate_id' => input('cate_id/d'),
		                     ], [
			                     'id' => $id,
		                     ])
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	public function delete(): Json
	{
		$id = input('id');
		if (empty($id)) {
			return $this->error('参数错误');
		}
		
		if (!is_array($id)) {
			$id = [$id];
		}
		
		try {
			$uploadService = new UploadService();
			$res           = false;
			foreach ($id as $v) {
				$res = $uploadService->deleteFile($v, $this->tenantId);
			}
			return $res
				? $this->success('删除成功')
				: $this->error('删除失败');
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
}