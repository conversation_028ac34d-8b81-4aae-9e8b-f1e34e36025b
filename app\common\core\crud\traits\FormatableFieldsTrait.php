<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use app\common\core\crud\format\FormatProcessor;

/**
 * 字段格式处理基础特性
 * 
 * 提供字段格式解析和格式推断等基础功能，供导入导出功能使用
 */
trait FormatableFieldsTrait
{
    /**
     * 格式化处理器实例
     */
    protected $formatProcessor;
    
    /**
     * 初始化格式处理器
     */
    public function initFormatProcessor()
    {
        if (!$this->formatProcessor) {
            $this->formatProcessor = new FormatProcessor();
        }
    }

    /**
     * 解析字段格式配置
     *
     * @param array $fields 字段配置
     * @return array 解析后的字段格式配置
     */
    public function parseFieldFormats(array $fields): array
    {
        $this->initFormatProcessor();
        
        // 解析字段格式配置
        $formattedFields = [];
        
        foreach ($fields as $field) {
            $format = $this->formatProcessor->inferFormat($field);
            $field['format'] = $format;
            $formattedFields[] = $field;
        }
        
        return $formattedFields;
    }
    
    /**
     * 获取模型中的场景字段
     *
     * @param string $sceneName 场景名称
     * @param string $type 类型(exp|imp)
     * @return array 字段列表
     */
    public function getSceneFields(string $sceneName, string $type): array
    {
        if (!$this->model) {
            return [];
        }

        // 尝试从模型中获取指定类型的场景字段
        $methodName = 'get' . ucfirst($type) . 'SceneFields';
        if (method_exists($this->model, $methodName)) {
            return $this->model->{$methodName}($sceneName);
        }

        // 尝试从模型注释中解析场景字段
        $configKey = $type . '_scene';
        if ($sceneName && method_exists($this->model, 'getTableConfig')) {
            $config = $this->model->getTableConfig($configKey);
            if ($config && is_string($config)) {
                return explode(',', $config);
            }
        }

        // 获取默认场景字段
        if (method_exists($this->model, 'getFieldScene')) {
            return $this->model->getFieldScene($sceneName ?: 'list');
        }

        // 从字段注释中解析导入/导出字段
        return $this->parseFieldsFromComments($type);
    }

    /**
     * 从字段注释中解析导入/导出字段
     *
     * @param string $type 类型(exp|imp)
     * @return array 字段列表
     */
    public function parseFieldsFromComments(string $type): array
    {
        if (!$this->model || !method_exists($this->model, 'getFieldsInfo')) {
            return [];
        }

        $fieldsInfo = $this->model->getFieldsInfo();
        $result = [];

        foreach ($fieldsInfo as $fieldName => $info) {
            $comment = $info['comment'] ?? '';

            // 检查字段注释中是否包含导入/导出标记
            if ($type === 'imp' && (strpos($comment, '@imp') !== false || strpos($comment, '@import') !== false)) {
                $result[] = $fieldName;
            } elseif ($type === 'exp' && (strpos($comment, '@exp') !== false || strpos($comment, '@export') !== false)) {
                $result[] = $fieldName;
            }
        }

        return $result;
    }
    
    /**
     * 获取指定场景的字段配置
     *
     * @param array $fields 字段列表
     * @param string $type 类型(exp|imp)
     * @return array 字段配置
     */
    public function getFieldsConfig(array $fields, string $type): array
    {
        if (empty($fields)) {
            return [];
        }
        
        $result = [];
        $fieldInfo = $this->model ? $this->model->getFieldsInfo() : [];
        
        foreach ($fields as $field) {
            // 检查是否是关联字段
            if (is_array($field)) {
                // TODO: 处理关联字段
                continue;
            }
            
            $info = $fieldInfo[$field] ?? [];
            $comment = $info['comment'] ?? '';
            
            // 解析字段注释中的导入导出配置
            $config = [
                'name' => $field,
                'title' => $this->getFieldTitle($field, $comment),
                'type' => $info['type'] ?? 'string',
                'required' => $this->isFieldRequired($field, $comment, $type),
                'comment' => $comment,
            ];
            
            // 获取字段验证规则
            if ($type === 'imp' && !empty($info['validate'])) {
                $config['validate'] = $info['validate'];
            }
            
            // 获取字段格式化方式
            if (!empty($info['formatter'])) {
                $config['formatter'] = $info['formatter'];
            }
            
            // 获取字段选项
            if (!empty($info['options'])) {
                $config['options'] = $info['options'];
            }
            
            $result[] = $config;
        }
        
        return $result;
    }
    
    /**
     * 获取字段标题
     *
     * @param string $field 字段名
     * @param string $comment 字段注释
     * @return string 字段标题
     */
    public function getFieldTitle(string $field, string $comment): string
    {
        if (empty($comment)) {
            return $field;
        }
        
        // 从注释中提取标题（第一个|前的内容）
        $parts = explode('|', $comment);
        $title = trim($parts[0]);
        
        // 如果注释中包含选项定义，提取真实标题
        if (strpos($title, ':') !== false) {
            $title = trim(explode(':', $title)[0]);
        }
        
        return $title ?: $field;
    }
    
    /**
     * 检查字段是否必填
     *
     * @param string $field 字段名
     * @param string $comment 字段注释
     * @param string $type 类型(exp|imp)
     * @return bool 是否必填
     */
    public function isFieldRequired(string $field, string $comment, string $type): bool
    {
        // 主键字段在导入时通常不是必填
        if ($field === 'id' && $type === 'imp') {
            return false;
        }
        
        // 检查字段注释中是否包含必填标记
        if (strpos($comment, '@req') !== false || strpos($comment, '@required') !== false) {
            return true;
        }
        
        // 检查验证规则中是否包含必填规则
        if (strpos($comment, '@v=required') !== false || strpos($comment, '@validate=required') !== false) {
            return true;
        }
        
        return false;
    }
} 