# 导入字段配置指南 - getImpSceneFields方法详解

## 1. 概述

`getImpSceneFields`方法用于自定义模型的导入字段配置，覆盖从数据表注释中自动解析的配置。通过此方法，可以精确控制导入模板中的字段展示、验证规则和数据转换逻辑，提供更友好的用户体验。

## 2. 方法定义

```php
/**
 * 获取导入场景字段配置
 * 
 * @param string $scene 场景名称，默认为空
 * @return array 导入字段配置
 */
public function getImpSceneFields(string $scene = ''): array
{
    return [
        // 字段配置...
    ];
}
```

## 3. 字段配置项

每个字段的配置支持以下属性：

| 属性名 | 类型 | 说明 | 示例 |
|-------|------|------|------|
| label | string | 字段在导入模板中的显示名称 | '客户名称' |
| type | string | 字段类型 | 'text', 'number', 'date', 'select', 'textarea' |
| required | boolean | 是否必填 | true, false |
| options | array | 枚举选项（适用于select类型） | [1 => '普通', 2 => '重要'] |
| default | mixed | 默认值 | 0, '', '默认值' |
| min | number | 最小值（适用于number类型） | 0 |
| max | number | 最大值（适用于number类型） | 100 |
| precision | number | 小数精度（适用于number类型） | 2 |
| format | string | 日期格式（适用于date类型） | 'Y-m-d', 'Y-m-d H:i:s' |
| validator | string/callable | 自定义验证规则 | 'email', 'url', function($value) {...} |
| formatter | callable | 自定义格式化函数 | function($value) {...} |
| description | string | 字段说明，会显示在导入模板中 | '请输入有效的邮箱地址' |

## 4. 常用字段类型配置示例

### 4.1 文本字段 (text)

```php
'customer_name' => [
    'label' => '客户名称',
    'type' => 'text',
    'required' => true,
    'description' => '请输入客户公司全称',
]
```

### 4.2 数字字段 (number)

```php
'annual_revenue' => [
    'label' => '年营业额',
    'type' => 'number',
    'required' => false,
    'min' => 0,
    'precision' => 2, // 保留2位小数
    'description' => '单位：万元',
]
```

### 4.3 日期字段 (date)

```php
'sign_date' => [
    'label' => '签约日期',
    'type' => 'date',
    'format' => 'Y-m-d',
    'description' => '格式：2023-01-01',
]
```

### 4.4 选择字段 (select)

```php
'level' => [
    'label' => '客户级别',
    'type' => 'select',
    'options' => [
        1 => '普通',
        2 => '重要',
        3 => '战略',
    ],
    'description' => '请从下拉列表中选择',
]
```

### 4.5 多行文本 (textarea)

```php
'address' => [
    'label' => '详细地址',
    'type' => 'textarea',
    'description' => '请输入完整的地址信息',
]
```

### 4.6 金额字段 (currency)

```php
'contract_amount' => [
    'label' => '合同金额',
    'type' => 'currency',
    'precision' => 2,
    'description' => '单位：元',
]
```

### 4.7 布尔值字段 (boolean/switch)

```php
'status' => [
    'label' => '状态',
    'type' => 'switch',
    'options' => [
        0 => '停用',
        1 => '启用',
    ],
    'default' => 1,
]
```

## 5. 完整示例 - 客户表导入字段配置

以下是基于`crm_customer`表的完整导入字段配置示例：

```php
/**
 * 获取导入场景字段配置
 * 
 * @return array 导入字段配置
 */
public function getImpSceneFields(): array
{
    return [
        'customer_name' => [
            'label' => '客户名称',
            'type' => 'text',
            'required' => true,
            'description' => '客户公司全称，必填',
        ],
        'industry' => [
            'label' => '所属行业',
            'type' => 'text',
            'description' => '如：互联网、制造业、金融业等',
        ],
        'level' => [
            'label' => '客户级别',
            'type' => 'select',
            'options' => [
                1 => '普通',
                2 => '重要',
                3 => '战略',
            ],
            'default' => 1,
        ],
        'source' => [
            'label' => '客户来源',
            'type' => 'text',
            'description' => '如：搜索引擎、展会、客户推荐等',
        ],
        'phone' => [
            'label' => '电话',
            'type' => 'text',
            'description' => '公司总机或前台电话',
        ],
        'website' => [
            'label' => '网站',
            'type' => 'text',
            'validator' => 'url',
            'description' => '公司官网地址，需包含http://或https://',
        ],
        'region_province' => [
            'label' => '省份',
            'type' => 'text',
        ],
        'region_city' => [
            'label' => '城市',
            'type' => 'text',
        ],
        'region_district' => [
            'label' => '区/县',
            'type' => 'text',
        ],
        'address' => [
            'label' => '详细地址',
            'type' => 'textarea',
        ],
        'zip_code' => [
            'label' => '邮政编码',
            'type' => 'text',
            'description' => '6位数字邮政编码',
        ],
        'credit_code' => [
            'label' => '统一社会信用代码',
            'type' => 'text',
            'description' => '18位统一社会信用代码',
        ],
        'annual_revenue' => [
            'label' => '年营业额',
            'type' => 'number',
            'precision' => 2,
            'min' => 0,
            'description' => '单位：万元',
        ],
        'employee_count' => [
            'label' => '员工人数',
            'type' => 'number',
            'precision' => 0, // 整数
            'min' => 0,
        ],
        'registered_capital' => [
            'label' => '注册资本',
            'type' => 'number',
            'precision' => 2,
            'min' => 0,
            'description' => '单位：万元',
        ],
        'status' => [
            'label' => '客户状态',
            'type' => 'select',
            'options' => [
                0 => '停用',
                1 => '正常',
            ],
            'default' => 1,
        ],
        'remark' => [
            'label' => '备注',
            'type' => 'textarea',
        ],
    ];
}
```

## 6. 高级用法

### 6.1 自定义验证器

```php
'email' => [
    'label' => '邮箱',
    'type' => 'text',
    'validator' => function($value) {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('邮箱格式不正确');
        }
        return $value;
    },
]
```

### 6.2 自定义格式化器

```php
'mobile' => [
    'label' => '手机号',
    'type' => 'text',
    'formatter' => function($value) {
        // 移除所有非数字字符
        return preg_replace('/[^0-9]/', '', $value);
    },
]
```

### 6.3 关联字段导入

```php
'tags' => [
    'label' => '标签',
    'type' => 'tags',
    'relation' => [
        'model' => 'Tag',
        'foreign_key' => 'tag_id',
        'local_key' => 'customer_id',
        'pivot_table' => 'customer_tag',
    ],
    'description' => '多个标签用逗号分隔',
]
```

## 7. 注意事项

1. **字段类型与数据库类型匹配**：确保配置的字段类型与数据库中的字段类型兼容。
2. **必填字段验证**：标记为`required`的字段在导入时会进行非空验证。
3. **选项值映射**：对于`select`类型字段，确保`options`中的键值与数据库中的实际值对应。
4. **数值精度处理**：对于`number`类型，使用`precision`属性控制小数位数。
5. **默认值设置**：可以通过`default`属性为字段设置默认值。
6. **自定义验证与格式化**：对于复杂的验证和格式化需求，可以使用自定义函数。

## 8. 与自动解析的关系

当模型中定义了`getImpSceneFields`方法时，系统会优先使用该方法的返回值作为导入字段配置，而不再从字段注释中自动解析。这提供了更精确的控制，同时避免了将技术性注释暴露给最终用户。

## 9. 多场景支持

可以根据传入的`$scene`参数返回不同的字段配置，支持多种导入场景：

```php
public function getImpSceneFields(string $scene = ''): array
{
    // 基础字段配置
    $baseFields = [
        'customer_name' => [
            'label' => '客户名称',
            'type' => 'text',
            'required' => true,
        ],
        // 其他基础字段...
    ];
    
    // 根据场景返回不同配置
    switch ($scene) {
        case 'simple':
            // 简化版导入，只包含必要字段
            return array_intersect_key($baseFields, array_flip([
                'customer_name', 'phone', 'level', 'status'
            ]));
            
        case 'full':
            // 完整版导入，包含所有字段
            return $baseFields;
            
        default:
            // 默认导入配置
            return $baseFields;
    }
}
```

## 10. 总结

通过`getImpSceneFields`方法，可以完全控制导入字段的配置，提供更友好的用户体验。该方法支持各种字段类型、验证规则和格式化逻辑，能够满足复杂的业务需求。

在实际开发中，建议根据业务需求和用户习惯，合理配置导入字段，简化用户操作，提高数据导入的准确性和效率。 