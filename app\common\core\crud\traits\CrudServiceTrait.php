<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use app\common\core\crud\CrudService;
use app\common\core\crud\factory\CrudFactory;
use app\common\utils\DataPermissionCacheUtil;
use think\Exception;
use think\facade\Log;
use think\Model;
use think\db\BaseQuery;
use think\db\exception\DbException;
use think\facade\Db;
use Throwable;

/**
 * CRUD服务特性
 * 提供标准的CRUD服务方法实现
 */
trait CrudServiceTrait
{
	
	/**
	 * CRUD服务实例
	 *
	 * @var CrudService
	 */
	protected CrudService $crudService;
	
	/**
	 * 初始化CRUD服务
	 *
	 * @param Model|null $model 模型实例
	 * @return void
	 */
	protected function initCrudService(?Model $model = null): void
	{
		if ($model) {
			$this->model = $model;
		}
		
		if (isset($this->model)) {
			$this->crudService = CrudFactory::create($this->model);
		}
	}
	
	/**
	 * 获取列表
	 *
	 * @param array        $where               查询条件
	 * @param array|string $order               排序
	 * @param array        $with                关联数据
	 * @param bool|null    $applyDataPermission 是否应用数据权限过滤
	 * @return mixed
	 */
	public function getList(array $where = [], $order = [], array $with = [], bool $applyDataPermission = null)
	{
		return $this->crudService->getList($where, $order, $with, $applyDataPermission);
	}
	
	/**
	 * 获取分页列表
	 *
	 * @param array        $where               查询条件
	 * @param array|string $order               排序
	 * @param int          $page                页码
	 * @param int          $limit               每页数量
	 * @param array        $with                关联数据
	 * @param bool|null    $applyDataPermission 是否应用数据权限过滤
	 * @return mixed
	 */
	public function getPageList(
		array $where = [], $order = [], int $page = 1, int $limit = 10, array $with = [],
		bool $applyDataPermission = null
	)
	{
		return $this->crudService->getPageList($where, $order, $page, $limit, $with, $applyDataPermission);
	}
	
	/**
	 * 获取总数
	 *
	 * @param array     $where               查询条件
	 * @param bool|null $applyDataPermission 是否应用数据权限过滤
	 * @return int
	 */
	public function getCount(array $where = [], bool $applyDataPermission = null): int
	{
		return $this->crudService->getCount($where, $applyDataPermission);
	}
	
	/**
	 * 获取单条记录
	 *
	 * @param array $where       查询条件
	 * @param array $with        关联查询
	 * @param bool  $useDataAuth 是否使用数据权限过滤
	 * @return Model
	 * @throws \Exception
	 */
	public function getOne(array $where, array $with = [], bool $useDataAuth = true)
	{
		try {
			// 基础查询构建
			$query = $this->model->where($where);
			
			if ($useDataAuth) {
				$query = $this->applyDataAuth($query);
			}
			// 关联查询
			if (!empty($with)) {
				$query = $query->with($with);
			}
			
			// 执行查询
			return $query->findOrEmpty();
		}
		catch (Throwable $e) {
			throw new \Exception($e->getMessage());
		}
	}
	
	/**
	 * 新增数据
	 *
	 * @param array $data 数据
	 * @return int 返回新增ID，不成功则为0
	 */
	public function add(array $data): int
	{
		return $this->crudService->add($data);
	}
	
	public function detail(int $id, $with = [])
	{
		return $this->crudService->getDetail($id, $with);
	}
	
	/**
	 * 编辑数据
	 *
	 * @param array $data  数据
	 * @param array $where 条件
	 * @return bool
	 */
	public function edit(array $data, array $where): bool
	{
		return $this->crudService->edit($data, $where);
	}
	
	/**
	 * 删除数据
	 *
	 * @param array $ids id数组
	 * @return mixed
	 */
	public function delete(array $ids): mixed
	{
		return $this->crudService->delete($ids);
	}
	
	/**
	 * 通用搜索和排序
	 *
	 * @param array     $params              请求参数
	 * @param array     $searchFields        可搜索字段配置
	 * @param array     $with                关联数据
	 * @param bool|null $applyDataPermission 是否应用数据权限过滤
	 * @return mixed
	 */
	public function search(array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null)
	{
		return $this->crudService->search($params, $searchFields, $with, $applyDataPermission);
	}
	
	/**
	 * 通用搜索和排序
	 *
	 * @param array     $params              请求参数
	 * @param array     $searchFields        可搜索字段配置
	 * @param array     $with                关联数据
	 * @param bool|null $applyDataPermission 是否应用数据权限过滤
	 * @return mixed
	 */
	public function searchTree(
		array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null
	)
	{
		return $this->crudService->searchTree($params, $searchFields, $with, $applyDataPermission);
	}
	
	/**
	 * 获取下拉列表数据
	 *
	 * @param array        $where      查询条件
	 * @param string       $labelField 标签字段
	 * @param string       $valueField 值字段
	 * @param array|string $order      排序
	 * @return mixed
	 */
	public function getSelectOptions(
		array $where = [], string $labelField = 'name', string $valueField = 'id', $order = []
	)
	{
		return $this->crudService->getSelectOptions($where, $labelField, $valueField, $order);
	}
	
	/**
	 * 更新单个字段值
	 *
	 * @param int|string $id    记录ID
	 * @param string     $field 字段名
	 * @param mixed      $value 字段值
	 * @return bool
	 */
	public function updateField($id, string $field, $value): bool
	{
		return $this->crudService->updateField($id, $field, $value);
	}
	
	/**
	 * 设置是否启用数据权限过滤
	 *
	 * @param bool $enable 是否启用
	 * @return $this
	 */
	public function setEnableDataPermission(bool $enable): self
	{
		$this->crudService->setEnableDataPermission($enable);
		return $this;
	}
	
	/**
	 * 获取是否启用数据权限过滤
	 *
	 * @return bool
	 */
	public function getEnableDataPermission(): bool
	{
		return $this->crudService->getEnableDataPermission();
	}
	
	/**
	 * 设置CRUD服务实例
	 *
	 * @param CrudService $crudService
	 * @return $this
	 */
	public function setCrudService(CrudService $crudService): self
	{
		$this->crudService = $crudService;
		return $this;
	}
	
	/**
	 * 应用数据权限过滤
	 * 注意：租户隔离由BaseModel全局范围处理，这里主要处理数据权限
	 *
	 * @param BaseQuery $query 查询对象
	 * @return BaseQuery
	 */
	protected function applyDataAuth(BaseQuery $query): BaseQuery
	{
		$userId = request()->adminId ?? 0;
		$isSuperAdmin = is_super_admin();
		$isTenantSuperAdmin = is_tenant_super_admin();

		// 断言：TokenAuthMiddleware已经确保了adminId > 0
		assert($userId > 0, 'Admin ID should be > 0 after TokenAuthMiddleware validation');

		// 1. 系统超级管理员：根据工作模式决定权限范围
		if ($isSuperAdmin && !should_apply_tenant_isolation()) {
			// 系统管理模式：跳过所有权限检查
			return $query;
		}

		// 2. 租户超级管理员：在租户范围内有全部权限
		if ($isTenantSuperAdmin) {
			// 租户隔离由BaseModel全局范围处理，这里不需要额外处理
			return $query;
		}

		// 3. 普通用户：应用数据权限
		if ($this->model->hasField('creator_id')) {
			// 获取有效租户ID
			$effectiveTenantId = get_effective_tenant_id();

			try {
				// 获取数据权限范围
				$adminIds = DataPermissionCacheUtil::getUserDataPermission($userId, $effectiveTenantId);

				if (empty($adminIds)) {
					// 没有权限范围，只能访问自己的数据
					$query->where('creator_id', '=', $userId);
				} else {
					// 有权限范围，使用IN查询
					$query->whereIn('creator_id', $adminIds);
				}
			} catch (\Exception $e) {
				// 权限检查失败，限制为只能访问自己的数据
				Log::error('CrudService数据权限检查失败', [
					'admin_id' => $userId,
					'effective_tenant_id' => $effectiveTenantId,
					'error' => $e->getMessage()
				]);
				$query->where('creator_id', '=', $userId);
			}
		}

		return $query;
	}
	
	/**
	 * 记录错误信息
	 *
	 * @param Throwable $e 异常
	 * @return void
	 */
	protected function recordError(Throwable $e): void
	{
		// 实际使用中应该记录到日志系统
		trace($e->getMessage(), 'error');
	}
} 