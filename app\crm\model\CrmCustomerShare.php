<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 客户共享表模型 - 基于crm_data.sql表结构
 */
class CrmCustomerShare extends BaseModel
{
    use CreatorTrait;

    // 设置表名
    protected $name = 'crm_customer_share';

    // 字段类型转换
    protected $type = [
        'customer_id' => 'integer',
        'shared_user_id' => 'integer',
        'status' => 'integer',
    ];

    // 关联客户
    public function customer()
    {
        return $this->belongsTo(CrmCustomer::class, 'customer_id', 'id');
    }

    // 获取状态文本
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            0 => '停用',
            1 => '启用'
        ];
        return $statusMap[$data['status']] ?? '启用';
    }

    public function getImpSceneFields(): array
    {
        return [
            'customer_id' => [
                'label' => '客户ID',
                'type'  => 'number',
            ],
            'shared_user_id' => [
                'label' => '共享员工ID',
                'type'  => 'number',
            ],
            'status' => [
                'label' => '状态',
                'type'  => 'select',
                'options' => [
                    0 => '停用',
                    1 => '启用'
                ]
            ],
            'remark' => [
                'label' => '备注说明',
                'type'  => 'textarea',
            ]
        ];
    }
}