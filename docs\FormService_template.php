<?php
declare(strict_types=1);

namespace app\{MODULE}\service;

use app\common\core\base\BaseService;
use app\workflow\interfaces\FormServiceInterface;
use app\{MODULE}\model\{MODEL_CLASS};
use app\common\exception\BusinessException;
use think\facade\Log;

/**
 * {BUSINESS_NAME}服务类
 */
class {SERVICE_CLASS} extends BaseService implements FormServiceInterface
{
    protected string $modelClass = {MODEL_CLASS}::class;
    
    public function __construct()
    {
        $this->model = new {MODEL_CLASS}();
        parent::__construct();
    }
    
    /**
     * 获取表单数据
     */
    public function getFormData(int $id): array
    {
        $model = $this->model->with([{WITH_RELATIONS}])
                            ->find($id);
        
        if (!$model) {
            throw new BusinessException('{BUSINESS_NAME}记录不存在');
        }
        
        return $model->toArray();
    }
    
    /**
     * 创建表单数据
     */
    public function saveForm(array $data): array
    {
        try {
            // 验证数据
            $validatedData = $this->validateFormData($data, 'create');
            
            // 创建主记录
            $id = $this->model->saveByCreate($validatedData);
            
            {SAVE_ITEMS_LOGIC}
            
            // 返回完整数据
            $formData = $this->getFormData($id);
            
            return [$id, $formData];
            
        } catch (\Exception $e) {
            Log::error('{BUSINESS_NAME}创建失败: ' . $e->getMessage(), [
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
            throw new BusinessException('{BUSINESS_NAME}创建失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新表单数据
     */
    public function updateForm(int $id, array $data): bool
    {
        try {
            $model = $this->model->find($id);
            if (!$model) {
                throw new BusinessException('{BUSINESS_NAME}记录不存在');
            }
            
            // 验证数据
            $validatedData = $this->validateFormData($data, 'update');
            
            // 更新主记录
            $result = $model->saveByUpdate($validatedData);
            
            {UPDATE_ITEMS_LOGIC}
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('{BUSINESS_NAME}更新失败: ' . $e->getMessage(), [
                'id' => $id,
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
            throw new BusinessException('{BUSINESS_NAME}更新失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新表单状态
     */
    public function updateFormStatus(int $id, int $status, array $extra = []): bool
    {
        try {
            $updateData = ['approval_status' => $status];
            
            // 根据状态添加时间字段
            if ($status === 1) { // 审批中
                $updateData['submit_time'] = date('Y-m-d H:i:s');
            } elseif (in_array($status, [2, 3, 4, 5, 6])) { // 已完成状态
                $updateData['approval_time'] = date('Y-m-d H:i:s');
            }
            
            // 添加额外数据
            if (isset($extra['workflow_instance_id'])) {
                $updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
            }
            
            return $this->model->where('id', $id)->update($updateData) > 0;
            
        } catch (\Exception $e) {
            Log::error('{BUSINESS_NAME}状态更新失败: ' . $e->getMessage(), [
                'id' => $id,
                'status' => $status,
                'extra' => $extra,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 删除表单
     */
    public function deleteForm(int $id): bool
    {
        try {
            $model = $this->model->find($id);
            if (!$model) {
                return false;
            }
            
            // 软删除主记录
            $model->delete();
            
            {DELETE_ITEMS_LOGIC}
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('{BUSINESS_NAME}删除失败: ' . $e->getMessage(), [
                'id' => $id,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 获取流程实例标题
     */
    public function getInstanceTitle($formData): string
    {
        if (is_array($formData)) {
            {TITLE_LOGIC}
        }
        
        return '{BUSINESS_NAME}';
    }
    
    /**
     * 验证表单数据
     */
    public function validateFormData(array $data, string $scene = 'create'): array
    {
        {VALIDATION_LOGIC}
        
        return $data;
    }
    
    /**
     * 工作流状态变更后的业务处理
     */
    public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
    {
        try {
            // 根据不同状态执行相应的业务逻辑
            switch ($status) {
                case 2: // 已通过
                    Log::info("{BUSINESS_NAME}已通过，业务ID: {$businessId}");
                    break;
                    
                case 3: // 已拒绝
                    Log::info("{BUSINESS_NAME}已拒绝，业务ID: {$businessId}");
                    break;
                    
                default:
                    break;
            }
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('{BUSINESS_NAME}工作流状态变更后处理失败: ' . $e->getMessage(), [
                'business_id' => $businessId,
                'status' => $status,
                'extra' => $extra,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    {ADDITIONAL_METHODS}
}
