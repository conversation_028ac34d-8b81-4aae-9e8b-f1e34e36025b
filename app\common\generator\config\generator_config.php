<?php
/**
 * CRUD生成器全局配置
 */
return [
    // 代码生成路径
    'paths' => [
        // 后端路径
        'backend' => [
            'model' => app()->getRootPath() . 'app/{module}/model/',
            'service' => app()->getRootPath() . 'app/{module}/service/',
            'controller' => app()->getRootPath() . 'app/{module}/controller/',
            'route' => app()->getRootPath() . 'route/',
        ],
        // 前端路径
        'frontend' => [
            'api' => app()->getRootPath() . 'frontend/art-design-pro/src/api/{module}/',
            'views' => app()->getRootPath() . 'frontend/art-design-pro/src/views/{module}/{table}/',
        ],
    ],
    
    // 模板配置
    'templates' => [
        // 后端模板
        'backend' => [
            'model' => 'model.template',
            'service' => 'service.template',
            'controller' => 'controller.template',
            'route' => 'route.template',
        ],
        // 前端模板
        'frontend' => [
            'api' => 'api.template',
            'list_vue' => 'list_vue.template',
            'form_drawer' => 'form_drawer.template',
            'form_dialog' => 'form_dialog.template',
            'form_helper' => 'form_helper.template',
        ],
    ],
    
    // 文件命名规则
    'file_naming' => [
        // 后端文件命名
        'backend' => [
            'model' => '{entity_name}.php',
            'service' => '{entity_name}Service.php',
            'controller' => '{entity_name}Controller.php',
            'route' => '{module}.php',
        ],
        // 前端文件命名
        'frontend' => [
            'api' => '{api_name}.ts',
            'list_vue' => 'list.vue',
            'form_drawer' => 'form-drawer.vue',
            'form_dialog' => 'form-dialog.vue',
            'form_helper' => 'form-helper.ts',
        ],
    ],
    
    // 字段类型映射
    'type_mapping' => [
        // 数据库类型到表单类型
        'db_to_form' => [
            'varchar' => 'input',
            'char' => 'input',
            'text' => 'textarea',
            'mediumtext' => 'textarea',
            'longtext' => 'textarea',
            'int' => 'number',
            'tinyint' => 'switch',
            'smallint' => 'number',
            'mediumint' => 'number',
            'bigint' => 'number',
            'decimal' => 'number',
            'float' => 'number',
            'double' => 'number',
            'date' => 'date',
            'datetime' => 'datetime',
            'timestamp' => 'datetime',
            'time' => 'time',
            'year' => 'year',
            'enum' => 'select',
            'set' => 'select',
        ],
    ],
    
    // 特殊字段处理
    'special_fields' => [
        // 不在表单中显示的字段
        'exclude_from_form' => [
            'id', 'created_at', 'updated_at', 'deleted_at'
        ],
        // 不在表格中显示的字段
        'exclude_from_table' => [
            'password', 'salt', 'deleted_at'
        ],
        // 创建时间字段
        'created_at' => [
            'created_at', 'create_time', 'create_at'
        ],
        // 更新时间字段
        'updated_at' => [
            'updated_at', 'update_time', 'update_at'
        ],
        // 软删除字段
        'deleted_at' => [
            'deleted_at', 'delete_time', 'delete_at'
        ],
    ],
]; 