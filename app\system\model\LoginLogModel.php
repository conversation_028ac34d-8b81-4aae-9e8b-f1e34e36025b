<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 登录日志模型
 */
class LoginLogModel extends BaseModel
{
    
    
    /**
     * 表名
     * @var string
     */
    protected $name = 'system_login_log';
    
    /**
     * 是否需要自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * 用户关联
     * @return BelongsTo
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(AdminModel::class, 'admin_id', 'id')->bind(['username']);
    }
	
} 