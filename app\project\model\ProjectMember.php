<?php
declare(strict_types=1);

namespace app\project\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\system\model\AdminModel;

/**
 * 项目成员表模型
 */
class ProjectMember extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'project_member';
	
	protected $append = [
		'user'
	];
	
	/**
	 * 所属项目关联
	 */
	public function project()
	{
		return $this->belongsTo(ProjectProject::class, 'project_id');
	}
	
	/**
	 * 用户关联
	 */
	public function user()
	{
		return $this->belongsTo(AdminModel::class, 'user_id')
		            ->with(['dept'])
		            ->bind([
			            'username',
			            'real_name',
			            'avatar',
			            'gender',
			            'mobile',
			            'dept_id',
			            'dept_name'  // 通过 AdminModel 的 dept 关联获取部门名称
		            ]);
	}
	
	/**
	 * 获取角色文本
	 */
	public function getRoleText(): string
	{
		$roleMap = [
			'owner'  => '负责人',
			'member' => '成员'
		];
		
		return $roleMap[$this->role] ?? '成员';
	}
	
	/**
	 * 检查是否为项目负责人
	 */
	public function isOwner(): bool
	{
		return $this->role === 'owner';
	}
}