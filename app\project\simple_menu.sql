-- 简化版项目任务管理菜单配置
-- 根据CRUD生成器生成的控制器权限标识来配置
-- 权限标识格式：模块:控制器:方法 (例如: project:projectproject:index)

-- 获取当前最大菜单ID
SET @max_id = (SELECT IFNULL(MAX(id), 0) FROM system_menu);

-- 项目管理主菜单 (目录)
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 1, 0, '项目管理', 'project', '/project', '', 0, 'el-icon-folder-opened', 300, 0, 0, 1, 1, '项目任务管理模块', NOW(), NOW());

-- 项目列表 (菜单) - 对应 project:projectproject:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 2, @max_id + 1, '项目列表', 'project:projectproject:index', '/project/project-list', '/project/project_project/list', 1, 'el-icon-folder', 1, 0, 1, 1, 1, '项目列表管理', NOW(), NOW());

-- 任务管理 (菜单) - 对应 project:projecttask:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 3, @max_id + 1, '任务管理', 'project:projecttask:index', '/project/task-list', '/project/project_task/list', 1, 'el-icon-tickets', 2, 0, 1, 1, 1, '任务列表管理', NOW(), NOW());

-- 项目成员 (菜单) - 对应 project:projectmember:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 4, @max_id + 1, '项目成员', 'project:projectmember:index', '/project/member-list', '/project/project_member/list', 1, 'el-icon-user', 3, 0, 1, 1, 1, '项目成员管理', NOW(), NOW());

-- 任务评论 (菜单) - 对应 project:projecttaskcomment:index
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 5, @max_id + 1, '任务评论', 'project:projecttaskcomment:index', '/project/comment-list', '/project/project_task_comment/list', 1, 'el-icon-chat-dot-round', 4, 0, 1, 1, 1, '任务评论管理', NOW(), NOW());

-- ================================
-- 按钮权限配置 (基于实际生成的控制器方法)
-- ================================

-- 项目管理按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 10, @max_id + 2, '查看项目', 'project:projectproject:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看项目详情', NOW(), NOW()),
(@max_id + 11, @max_id + 2, '新增项目', 'project:projectproject:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增项目', NOW(), NOW()),
(@max_id + 12, @max_id + 2, '编辑项目', 'project:projectproject:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑项目', NOW(), NOW()),
(@max_id + 13, @max_id + 2, '删除项目', 'project:projectproject:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除项目', NOW(), NOW()),
(@max_id + 14, @max_id + 2, '批量删除项目', 'project:projectproject:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除项目', NOW(), NOW()),
(@max_id + 15, @max_id + 2, '更新项目字段', 'project:projectproject:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新项目字段', NOW(), NOW()),
(@max_id + 16, @max_id + 2, '项目状态', 'project:projectproject:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改项目状态', NOW(), NOW());

-- 任务管理按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 20, @max_id + 3, '查看任务', 'project:projecttask:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看任务详情', NOW(), NOW()),
(@max_id + 21, @max_id + 3, '新增任务', 'project:projecttask:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增任务', NOW(), NOW()),
(@max_id + 22, @max_id + 3, '编辑任务', 'project:projecttask:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑任务', NOW(), NOW()),
(@max_id + 23, @max_id + 3, '删除任务', 'project:projecttask:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除任务', NOW(), NOW()),
(@max_id + 24, @max_id + 3, '批量删除任务', 'project:projecttask:batchdelete', '', '', 2, '', 5, 0, 0, 1, 1, '批量删除任务', NOW(), NOW()),
(@max_id + 25, @max_id + 3, '更新任务字段', 'project:projecttask:updatefield', '', '', 2, '', 6, 0, 0, 1, 1, '更新任务字段', NOW(), NOW()),
(@max_id + 26, @max_id + 3, '任务状态', 'project:projecttask:status', '', '', 2, '', 7, 0, 0, 1, 1, '修改任务状态', NOW(), NOW());

-- 项目成员按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 30, @max_id + 4, '查看成员', 'project:projectmember:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看成员详情', NOW(), NOW()),
(@max_id + 31, @max_id + 4, '新增成员', 'project:projectmember:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增项目成员', NOW(), NOW()),
(@max_id + 32, @max_id + 4, '编辑成员', 'project:projectmember:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑项目成员', NOW(), NOW()),
(@max_id + 33, @max_id + 4, '删除成员', 'project:projectmember:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除项目成员', NOW(), NOW()),

-- 任务评论按钮权限
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(@max_id + 40, @max_id + 5, '查看评论', 'project:projecttaskcomment:detail', '', '', 2, '', 1, 0, 0, 1, 1, '查看评论详情', NOW(), NOW()),
(@max_id + 41, @max_id + 5, '新增评论', 'project:projecttaskcomment:add', '', '', 2, '', 2, 0, 0, 1, 1, '新增任务评论', NOW(), NOW()),
(@max_id + 42, @max_id + 5, '编辑评论', 'project:projecttaskcomment:edit', '', '', 2, '', 3, 0, 0, 1, 1, '编辑任务评论', NOW(), NOW()),
(@max_id + 43, @max_id + 5, '删除评论', 'project:projecttaskcomment:delete', '', '', 2, '', 4, 0, 0, 1, 1, '删除任务评论', NOW(), NOW());
