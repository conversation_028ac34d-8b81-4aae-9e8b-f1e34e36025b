<?php

namespace app\common\utils;

class BitFormat
{
	// 定义单位字节映射表
	private const UNIT_MAP = [
		'B'  => 1,
		'KB' => 1024,
		'MB' => 1048576,     // 1024^2
		'GB' => 1073741824,  // 1024^3
		'TB' => 1099511627776 // 1024^4
	];
	
	/**
	 * 根据指定单位格式化文件大小
	 *
	 * @param int $bytes 文件字节大小
	 * @param string $unit 目标单位（B/KB/MB/GB/TB）
	 * @param int $precision 小数位数（默认2位）
	 * @return string
	 * @throws \InvalidArgumentException
	 */
	public static function formatSize(int $bytes, string $unit, int $precision = 2): string
	{
		// 参数验证
		$unit = strtoupper($unit);
		if (!isset(self::UNIT_MAP[$unit])) {
			throw new \InvalidArgumentException("无效的单位: {$unit}");
		}
		
		if ($bytes < 0) {
			throw new \InvalidArgumentException("文件大小不能为负数");
		}
		
		// 单位转换计算
		$factor = self::UNIT_MAP[$unit];
		if ($bytes < $factor && $unit !== 'B') {
			return "0{$unit}";
		}
		
		$size = $bytes / $factor;
		
		// 格式化输出
		return number_format($size, $precision, '.', '') . $unit;
	}
	
	/**
	 * 自动识别单位格式化文件大小
	 *
	 * @param int $bytes 文件字节大小
	 * @param int $precision 小数位数（默认2位）
	 * @return string 格式化后的大小字符串
	 * @throws \InvalidArgumentException
	 */
	public static function formatSizeAuto(int $bytes, int $precision = 2): string
	{
		// 负值校验
		if ($bytes < 0) {
			throw new \InvalidArgumentException("文件大小不能为负数");
		}
		
		// 零值特殊处理
		if ($bytes === 0) {
			return '0B';
		}
		
		// 单位匹配计算
		foreach (self::UNIT_MAP as $unit => $threshold) {
			if ($bytes >= $threshold) {
				$size = $bytes / $threshold;
				return number_format($size, $precision, '.', '') . $unit;
			}
		}
	}
}
