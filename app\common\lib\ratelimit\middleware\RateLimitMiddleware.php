<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Config;
use app\common\lib\ratelimit\facade\RateLimit;
use app\common\lib\ratelimit\exception\RateLimitException;

/**
 * 限流中间件
 */
class RateLimitMiddleware
{
    /**
     * 处理请求
     *
     * @param Request $request 请求对象
     * @param Closure $next 闭包
     * @param string|null $key 自定义限流键名
     * @return Response
     */
    public function handle(Request $request, Closure $next, ?string $key = null): Response
    {
        // 获取限流键名
        $key = $key ?? $this->getDefaultLimitKey($request);
        
        // 尝试获取当前请求的租户ID
        $tenantId = $request->middleware('tenant_id');
        if (empty($tenantId)) {
            try {
                $tenantId = app()->jwt->getUser()->tenant_id ?? null;
            } catch (\Throwable $e) {
                $tenantId = null;
            }
        }
        
        try {
            // 检查是否允许请求通过
            RateLimit::check($key, $tenantId);
            
            // 请求通过，继续处理
            $response = $next($request);
            
        } catch (RateLimitException $e) {
            // 请求被限流，返回错误响应
            $response = Response::create($e->getExceptionData(), 'json', $e->getCode());
            
            // 添加限流响应头
            $response->header([
                'X-RateLimit-Limit' => $e->getRule()->limit_count ?? 0,
                'X-RateLimit-Remaining' => $e->getRemaining(),
                'X-RateLimit-Reset' => $e->getResetTime(),
                'Retry-After' => $e->getResetTime(),
            ]);
        }
        
        return $response;
    }
    
    /**
     * 获取默认的限流键名
     *
     * @param Request $request 请求对象
     * @return string 限流键名
     */
    protected function getDefaultLimitKey(Request $request): string
    {
        $controller = $request->controller();
        $action = $request->action();
        
        // 格式：控制器/方法
        return strtolower("{$controller}/{$action}");
    }
}