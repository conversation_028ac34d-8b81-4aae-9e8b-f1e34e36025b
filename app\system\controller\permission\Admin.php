<?php

namespace app\system\controller\permission;

use app\common\core\base\BaseAdminController;
use app\system\service\AdminService;
use app\system\service\MenuService;
use app\system\service\PostService;
use think\facade\Cache;
use think\response\Json;

/**
 * 管理员权限控制器
 */
class Admin extends BaseAdminController
{
	/**
	 * @var AdminService
	 */
	private AdminService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取AdminService实例
		$this->service = AdminService::getInstance();
	}
	
	/**
	 * 获取当前管理员信息以及权限信息
	 *
	 * @return Json
	 */
	public function info(): Json
	{
		// 使用AdminService获取管理员信息
		$adminInfo = $this->service->getModel()
		                           ->where('id', $this->adminId)
		                           ->findOrEmpty();
		
		$data = [];
		
		if (!$adminInfo->isEmpty()) {
			// 筛选需要的字段
			$data = $adminInfo->visible([
				'id',
				'username',
				'real_name',
				'avatar',
				'mobile',
				'email',
				'status',
				'gender',
				'dept_name',
			])
			                  ->toArray();
			
			// 获取岗位信息
			$jobs = PostService::getInstance()
			                   ->getCrudService()
			                   ->getList([
				                   [
					                   'id',
					                   'in',
					                   $adminInfo['post_ids']
				                   ]
			                   ])
			                   ->column('name');
			
			$data['jobs'] = implode(',', $jobs);
		}
		
		return $this->success('获取成功', $data);
	}
	
	/**
	 * 获取用户权限
	 *
	 * @return Json
	 */
	public function permissions(): Json
	{
		// 从缓存获取或重新生成
		$cacheKey = "admin:permission_menus:{$this->adminId}";
		//		$permissionsData = Cache::get($cacheKey);
		$permissionsData = [];
		// 如果缓存不存在，则重新生成
		if (empty($permissionsData)) {
			// 获取用户菜单权限
			$permissionsData = MenuService::getInstance()
			                              ->getUserMenus($this->adminId);
			
			// 缓存权限数据，设置1小时过期
			Cache::tag('menu')
			     ->set($cacheKey, $permissionsData, 3600);
		}
		
		return $this->success('获取成功', $permissionsData);
	}
	
	/**
	 * 获取管理员列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		// 使用AdminService的getList方法获取列表数据
		$adminList = $this->service->getList(input());
		
		return $this->success('获取成功', $adminList);
	}
	
	/**
	 * 获取管理员详情
	 *
	 * @param int $id 管理员ID
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		// 使用AdminService的getDetail方法获取详情数据
		$detail = $this->service->getDetail($id);
		
		return $this->success('获取成功', $detail);
	}
	
	/**
	 * 创建管理员
	 *
	 * @return Json
	 * @throws \Throwable
	 */
	public function add(): Json
	{
		// 使用AdminService的create方法创建数据
		$result = $this->service->create(input(), $this->adminId, $this->tenantId);
		
		return $result
			? $this->success('创建成功')
			: $this->error('创建失败');
	}
	
	/**
	 * 更新管理员
	 *
	 * @param int $id 管理员ID
	 * @return Json
	 */
	public function edit(int $id): Json
	{
		// 使用AdminService的update方法更新数据
		$result = $this->service->update((int)$id, $this->adminId, $this->tenantId, input());
		// todo 此处是否需要更新用户相关缓存？比如角色变更，状态变更
		return $result
			? $this->success('更新成功')
			: $this->error('更新失败');
	}
	
	/**
	 * 删除管理员
	 *
	 * @param int $id 管理员ID
	 * @return Json
	 */
	public function delete(): Json
	{
		// 使用AdminService的delete方法删除数据
		$result = $this->service->delete(input('id/d'), $this->tenantId);
		
		return $result
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * 重置table管理员密码
	 *
	 * @return Json
	 */
	public function resetPassword($id): Json
	{
		// 使用AdminService的resetPassword方法重置密码
		$result = $this->service->resetPassword($id, input());
		
		return $result
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	// 修改密码
	public function changePassword(): Json
	{
		// 使用AdminService的changePassword方法修改密码
		$result = $this->service->changePassword($this->adminId, input());
		return $result
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	/**
	 * 更新个人信息头像
	 *
	 * @return Json
	 */
	public function avatar(): Json
	{
		// 获取头像数据
		$avatar = input('avatar/s');
		
		// 使用AdminService的updateAvatar方法更新头像
		$result = $this->service->updateAvatar($this->adminId, $avatar);
		
		return $result
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	public function options(): Json
	{
		// 使用AdminService的getOptions方法获取下拉选项
		$where  = [];
		$deptId = input('dept_id/d');
		if ($deptId) {
			$where[] = [
				'dept_id',
				'=',
				$deptId
			];
		}
		/*$ids = input('ids/a');
		if ($ids) {
			$where[] = [
				'id',
				'in',
				$ids
			];
		}*/
		$options = $this->service->getOptions($where);
		
		return $this->success('获取成功', $options);
	}
}
