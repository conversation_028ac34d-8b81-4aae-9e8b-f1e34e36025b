<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\crm\model\CrmContract;
use app\crm\model\CrmContractProduct;
use app\crm\model\CrmContractReceivable;
use app\common\exception\BusinessException;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\traits\DefaultWorkflowCallbackTrait;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\service\WorkflowEngineService;
use think\facade\Db;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;
use think\facade\Log;


/**
 * 合同表服务类
 */
class CrmContractService extends BaseService implements FormServiceInterface
{
	use CrudServiceTrait, ExportableTrait, ImportableTrait, DefaultWorkflowCallbackTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmContract();
		parent::__construct();
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'contract_no' => ['type' => 'like'],
			
			'contract_name' => ['type' => 'like'],
			
			'customer_id' => ['type' => 'eq'],
			
			'contact_id' => ['type' => 'eq'],
			
			'business_id' => ['type' => 'eq'],
			
			'quotation_id' => ['type' => 'eq'],
			
			'contract_amount' => ['type' => 'between'],
			
			'received_amount' => ['type' => 'between'],
			
			'unreceived_amount' => ['type' => 'between'],
			
			'owner_user_id' => ['type' => 'eq'],
			
			'status' => ['type' => 'eq'],
			
			'contract_type' => ['type' => 'eq'],
			
			'start_date' => ['type' => 'date'],
			
			'end_date' => ['type' => 'date'],
			
			'sign_date' => ['type' => 'date'],
		
		];
	}
	
	/**
	 * 获取验证规则 - 基于crm_data.sql字段约束
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'customer_id'      => 'require|integer|gt:0',
			'contract_number'  => 'require|max:50',
			'contract_name'    => 'require|max:200',
			'contract_amount'  => 'require|float|egt:0',
			'paid_amount'      => 'float|egt:0',
			'payment_deadline' => 'date',
			'type'             => 'max:50',
			'payment_method'   => 'max:50',
			'status'           => 'integer|in:0,1,2,3',
			'payment_status'   => 'integer|in:0,1,2',
			'description'      => 'max:1000',
			'owner_user_id'    => 'require|integer|gt:0',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	// ==================== FormServiceInterface 接口实现 ====================
	
	/**
	 * 获取表单数据
	 *
	 * @param int $id 表单ID
	 * @return array 表单详情数据
	 */
	public function getFormData(int $id): array
	{
		$contract = $this->model->find($id);
		if (!$contract) {
			throw new BusinessException('合同不存在');
		}
		return $contract->toArray();
	}
	
	/**
	 * 重写添加方法，添加数据预处理和验证
	 *
	 * @param array $data 表单数据
	 * @return int 返回新增ID
	 * @throws BusinessException
	 */
	public function add(array $data): int
	{
		// 1. 预处理数据
		$data = $this->preprocessContractData($data, 'create');

		// 2. 验证数据
		$data = $this->validateContractData($data, 'create');

		// 3. 执行添加
		$result = $this->crudService->add($data);
		if (!$result) {
			throw new BusinessException('合同创建失败');
		}

		return $result;
	}

	/**
	 * 重写编辑方法，添加数据预处理和验证
	 *
	 * @param array $data 表单数据
	 * @param array $where 条件
	 * @return bool 是否成功
	 * @throws BusinessException
	 */
	public function edit(array $data, array $where): bool
	{
		// 1. 获取原合同数据
		$contract = $this->model->where($where)->find();
		if (!$contract) {
			throw new BusinessException('合同不存在');
		}

		// 2. 预处理数据
		$data = $this->preprocessContractData($data, 'update', $contract);

		// 3. 验证数据
		$data = $this->validateContractData($data, 'update');

		// 4. 执行更新
		$result = $this->crudService->edit($data, $where);

		// 5. 如果修改了合同金额或付款期限，重新计算付款状态
		if (isset($data['contract_amount']) || isset($data['payment_deadline'])) {
			$this->recalculatePaymentStatus($contract->id);
		}

		return $result;
	}

	/**
	 * 创建表单数据
	 *
	 * @param array $data 表单数据
	 * @return array [id,formData]
	 */
	public function saveForm(array $data): array
	{
		$result = $this->add($data);
		return [
			'id'       => $result,
			'formData' => $this->getFormData($result)
		];
	}
	
	/**
	 * 更新表单数据
	 *
	 * @param int   $id   表单ID
	 * @param array $data 表单数据
	 * @return bool 是否成功
	 */
	public function updateForm(int $id, array $data): bool
	{
		return $this->edit($data, ['id' => $id]);
	}
	
	/**
	 * 更新表单状态
	 *
	 * @param int   $id     表单ID
	 * @param int   $status 状态值(0=已保存(草稿),1=已提交(待审批),2=审批中,3=已通过,4=已驳回,5=已终止,6=已撤回)
	 * @param array $extra  额外数据
	 * @return bool 是否成功
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		return $this->crudService->edit(['status' => $status], ['id' => $id]);
	}
	
	/**
	 * 删除表单
	 *
	 * @param int $id 表单ID
	 * @return bool 是否成功
	 */
	public function deleteForm(int $id): bool
	{
		return $this->crudService->delete(['id' => $id]);
	}
	

	
	/**
	 * 获取流程实例标题
	 *
	 * @param $formData
	 * @return string
	 */
	public function getInstanceTitle($formData): string
	{
		$contractNo   = $formData['contract_no'] ?? '';
		$customerName = $formData['customer_name'] ?? '';
		return "合同审批 - {$contractNo} ({$customerName})";
	}
	
	/**
	 * 验证表单数据 - 基于crm_data.sql字段约束
	 *
	 * @param array  $data  表单数据
	 * @param string $scene 验证场景
	 * @return array 验证后的数据
	 * @throws BusinessException 验证失败时抛出异常
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		// 基础验证规则
		$rules = [
			'customer_id'      => 'require|integer|gt:0',
			'contract_number'  => 'require|max:50',
			'contract_name'    => 'require|max:200',
			'contract_amount'  => 'require|float|egt:0',
			'paid_amount'      => 'float|egt:0',
			'payment_deadline' => 'date',
			'type'             => 'max:50',
			'payment_method'   => 'max:50',
			'status'           => 'integer|in:0,1,2,3',
			'payment_status'   => 'integer|in:0,1,2',
			'description'      => 'max:1000',
			'owner_user_id'    => 'require|integer|gt:0',
		];
		
		// 根据场景调整规则
		if ($scene === 'update') {
			unset($rules['contract_number']); // 更新时不验证编号
		}
		
		// 执行验证
		$validate = validate($rules);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		return $data;
	}
	
	// ==================== 合同产品管理 ====================
	
	/**
	 * 创建合同并关联产品
	 *
	 * @param array $contractData 合同数据
	 * @param array $products     产品列表
	 * @return array 创建结果
	 * @throws BusinessException
	 */
	public function createWithProducts(array $contractData, array $products): array
	{
		Db::startTrans();
		try {
			// 1. 验证合同数据
			$contractData = $this->validateFormData($contractData, 'create');
			
			// 2. 创建合同
			$contractId = $this->crudService->add($contractData);
			if (!$contractId) {
				throw new BusinessException('合同创建失败');
			}
			
			// 3. 创建合同产品关联
			$totalAmount = 0;
			foreach ($products as $product) {
				$productData = [
					'contract_id'     => $contractId,
					'product_id'      => $product['product_id'],
					'product_info'    => $product['product_info'] ?? '',
					'quantity'        => $product['quantity'],
					'unit_price'      => $product['unit_price'],
					'discount_rate'   => $product['discount_rate'] ?? 0,
					'discount_amount' => $product['discount_amount'] ?? 0,
					'subtotal'        => $product['subtotal']
				];
				
				CrmContractProduct::create($productData);
				$totalAmount += $product['subtotal'];
			}
			
			// 4. 更新合同总金额
			$this->crudService->edit(['contract_amount' => $totalAmount], ['id' => $contractId]);
			
			Db::commit();
			
			return [
				'id'              => $contractId,
				'contract_amount' => $totalAmount,
				'products_count'  => count($products)
			];
			
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('合同创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新合同产品
	 *
	 * @param int   $contractId 合同ID
	 * @param array $products   产品列表
	 * @return bool
	 * @throws BusinessException
	 */
	public function updateProducts(int $contractId, array $products): bool
	{
		Db::startTrans();
		try {
			// 1. 删除原有产品关联
			CrmContractProduct::where('contract_id', $contractId)
			                  ->delete();
			
			// 2. 创建新的产品关联
			$totalAmount = 0;
			foreach ($products as $product) {
				$productData = [
					'contract_id'     => $contractId,
					'product_id'      => $product['product_id'],
					'product_info'    => $product['product_info'] ?? '',
					'quantity'        => $product['quantity'],
					'unit_price'      => $product['unit_price'],
					'discount_rate'   => $product['discount_rate'] ?? 0,
					'discount_amount' => $product['discount_amount'] ?? 0,
					'subtotal'        => $product['subtotal']
				];
				
				CrmContractProduct::create($productData);
				$totalAmount += $product['subtotal'];
			}
			
			// 3. 更新合同总金额
			$this->crudService->edit(['contract_amount' => $totalAmount], ['id' => $contractId]);
			
			Db::commit();
			return true;
			
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('产品更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 获取合同产品列表
	 *
	 * @param int $contractId 合同ID
	 * @return array
	 */
	public function getContractProducts(int $contractId): array
	{
		return CrmContractProduct::where('contract_id', $contractId)
		                         ->with(['product'])
		                         ->select()
		                         ->toArray();
	}
	
	/**
	 * 计算产品小计
	 *
	 * @param float $quantity     数量
	 * @param float $unitPrice    单价
	 * @param float $discountRate 折扣率
	 * @return array [subtotal, discount_amount]
	 */
	public function calculateSubtotal(float $quantity, float $unitPrice, float $discountRate = 0): array
	{
		$originalAmount = $quantity * $unitPrice;
		$discountAmount = $originalAmount * ($discountRate / 100);
		$subtotal       = $originalAmount - $discountAmount;
		
		return [
			'subtotal'        => round($subtotal, 2),
			'discount_amount' => round($discountAmount, 2)
		];
	}
	
	/**
	 * 作废合同（集成工作流）
	 *
	 * @param int $contractId 合同ID
	 * @param string $reason 作废原因
	 * @return bool
	 * @throws BusinessException
	 */
	public function voidContract(int $contractId, string $reason = ''): bool
	{
		Db::startTrans();
		try {
			// 1. 获取合同信息
			$crmContractModel = new CrmContract();
			$contract = $crmContractModel->with(['receivables', 'workflow'])
			                            ->findOrEmpty($contractId);
			if ($contract->isEmpty()) {
				throw new BusinessException('合同不存在');
			}

			// 2. 检查合同审批状态
			if ($contract['approval_status'] == 6) {
				throw new BusinessException('合同已经是作废状态');
			}

			// 3. 验证作废原因
			if (empty(trim($reason))) {
				throw new BusinessException('作废原因不能为空');
			}

			// 4. 如果有工作流实例且已通过，需要作废工作流实例
			if ($contract['workflow_instance_id'] && $contract['approval_status'] == 2) {
				$workflowEngineService = WorkflowEngineService::getInstance();
				$voidResult = $workflowEngineService->voidApprovedInstance(
					$contract['workflow_instance_id'],
					$reason, // 使用用户输入的作废原因
					request()->adminId ?? 0
				);

				if (!$voidResult) {
					throw new BusinessException('工作流实例作废失败');
				}
			}

			// 5. 更新合同状态为作废
			$result = $contract->saveByUpdate([
				'approval_status' => 6, // 6=已作废
				'approval_time' => date('Y-m-d H:i:s'),
				'approval_opinion' => $reason // 保存作废原因
			]);

			if (!$result) {
				throw new BusinessException('合同作废失败');
			}

			// 6. 同时作废该合同下所有待审批的回款记录
			CrmContractReceivable::where('contract_id', $contractId)
			                     ->where('approval_status', '<=', 1) // 草稿和审批中
			                     ->update([
				                     'approval_status' => 6, // 6=已作废
				                     'approval_opinion' => '因合同作废而自动作废', // 添加作废原因
				                     'updated_id' => get_user_id()
			                     ]);

			Db::commit();
			return true;

		} catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('作废失败：' . $e->getMessage());
		}
	}

	// ==================== 数据预处理和验证方法 ====================

	/**
	 * 预处理合同数据
	 *
	 * @param array $data 原始数据
	 * @param string $scene 场景：create|update
	 * @param object|null $originalContract 原合同数据（更新时）
	 * @return array 预处理后的数据
	 */
	private function preprocessContractData(array $data, string $scene, $originalContract = null): array
	{
		// 1. 新建合同时强制设置初始值
		if ($scene === 'create') {
			$data['paid_amount'] = 0.00;  // 新建合同已付金额固定为0
			$data['payment_status'] = 0;  // 新建合同付款状态固定为未付款
		}

		// 2. 更新时忽略前端传入的paid_amount（只能通过回款记录更新）
		if ($scene === 'update' && isset($data['paid_amount'])) {
			unset($data['paid_amount']); // 移除前端传入的已付金额
		}

		// 3. 更新时忽略前端传入的payment_status（自动计算）
		if ($scene === 'update' && isset($data['payment_status'])) {
			unset($data['payment_status']); // 移除前端传入的付款状态
		}

		// 4. 处理空值字段
		$dateFields = ['payment_deadline', 'start_date', 'end_date', 'sign_date'];
		foreach ($dateFields as $field) {
			if (isset($data[$field]) && empty($data[$field])) {
				$data[$field] = null;
			}
		}

		return $data;
	}

	/**
	 * 验证合同数据
	 *
	 * @param array $data 数据
	 * @param string $scene 场景：create|update
	 * @return array 验证后的数据
	 * @throws BusinessException
	 */
	private function validateContractData(array $data, string $scene): array
	{
		// 1. 基础验证规则
		$rules = [
			'customer_id'      => 'require|integer|gt:0',
			'contract_number'  => 'require|max:50',
			'contract_name'    => 'require|max:200',
			'contract_amount'  => 'require|float|egt:0',
			'payment_deadline' => 'date',
			'type'             => 'max:50',
			'payment_method'   => 'max:50',
			'description'      => 'max:1000',
			'owner_user_id'    => 'require|integer|gt:0',
		];

		// 2. 根据场景调整规则
		if ($scene === 'update') {
			unset($rules['contract_number']); // 更新时不验证编号
		}

		// 3. 执行基础验证
		$validate = validate($rules);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}

		// 4. 业务逻辑验证
		$this->validateBusinessLogic($data, $scene);

		return $data;
	}

	/**
	 * 业务逻辑验证
	 *
	 * @param array $data 数据
	 * @param string $scene 场景
	 * @throws BusinessException
	 */
	private function validateBusinessLogic(array $data, string $scene): void
	{
		// 1. 验证合同金额必须大于0
		if (isset($data['contract_amount']) && $data['contract_amount'] <= 0) {
			throw new BusinessException('合同金额必须大于0');
		}

		// 2. 验证日期逻辑
		if (isset($data['start_date']) && isset($data['end_date'])) {
			if ($data['start_date'] && $data['end_date'] && $data['start_date'] > $data['end_date']) {
				throw new BusinessException('合同开始日期不能晚于结束日期');
			}
		}

		// 3. 验证付款期限不能早于签署日期
		if (isset($data['sign_date']) && isset($data['payment_deadline'])) {
			if ($data['sign_date'] && $data['payment_deadline'] && $data['sign_date'] > $data['payment_deadline']) {
				throw new BusinessException('付款期限不能早于签署日期');
			}
		}
	}

	// ==================== 付款状态计算方法 ====================

	/**
	 * 重新计算合同的付款状态
	 *
	 * @param int $contractId 合同ID
	 * @return bool 是否成功
	 */
	public function recalculatePaymentStatus(int $contractId): bool
	{
		try {
			// 1. 获取合同信息
			$contract = $this->model->find($contractId);
			if (!$contract) {
				return false;
			}

			// 2. 计算该合同下所有已审批通过的回款总额
			$totalPaidAmount = CrmContractReceivable::where('contract_id', $contractId)
			                                        ->where('approval_status', 2) // 已通过
			                                        ->sum('amount');

			// 3. 计算付款状态
			$paymentStatus = $this->calculatePaymentStatus(
				$contract->contract_amount,
				$totalPaidAmount,
				$contract->payment_deadline
			);

			// 4. 更新合同的已付金额和付款状态
			$updateData = [
				'paid_amount' => $totalPaidAmount,
				'payment_status' => $paymentStatus
			];

			return $contract->saveByUpdate($updateData);

		} catch (\Exception $e) {
			Log::error('重新计算付款状态失败：' . $e->getMessage(), [
				'contract_id' => $contractId,
				'error' => $e->getMessage()
			]);
			return false;
		}
	}

	/**
	 * 计算付款状态
	 *
	 * @param float $contractAmount 合同金额
	 * @param float $paidAmount 已付金额
	 * @param string|null $paymentDeadline 付款期限
	 * @return int 付款状态：0=未付款,1=部分付款,2=已付清,3=逾期
	 */
	public function calculatePaymentStatus(float $contractAmount, float $paidAmount, ?string $paymentDeadline): int
	{
		// 1. 未付款
		if ($paidAmount <= 0) {
			return 0;
		}

		// 2. 已付清
		if ($paidAmount >= $contractAmount) {
			return 2;
		}

		// 3. 部分付款 - 检查是否逾期
		if ($paymentDeadline && strtotime($paymentDeadline) < time()) {
			return 3; // 逾期
		}

		// 4. 部分付款（未逾期）
		return 1;
	}

	/**
	 * 批量重新计算付款状态
	 *
	 * @param array $contractIds 合同ID数组
	 * @return array 处理结果
	 */
	public function batchRecalculatePaymentStatus(array $contractIds): array
	{
		$results = [
			'success' => 0,
			'failed' => 0,
			'errors' => []
		];

		foreach ($contractIds as $contractId) {
			if ($this->recalculatePaymentStatus($contractId)) {
				$results['success']++;
			} else {
				$results['failed']++;
				$results['errors'][] = "合同ID {$contractId} 计算失败";
			}
		}

		return $results;
	}

	/**
	 * 修复所有合同的付款状态（管理命令使用）
	 *
	 * @param int $limit 每次处理的数量限制
	 * @return array 处理结果
	 */
	public function fixAllContractPaymentStatus(int $limit = 100): array
	{
		$results = [
			'total' => 0,
			'success' => 0,
			'failed' => 0,
			'errors' => []
		];

		try {
			// 获取所有需要修复的合同（非作废状态）
			$contracts = $this->model->where('approval_status', '<>', 6)
				->limit($limit)
				->column('id');

			$results['total'] = count($contracts);

			if (empty($contracts)) {
				return $results;
			}

			// 批量处理
			$batchResults = $this->batchRecalculatePaymentStatus($contracts);
			$results['success'] = $batchResults['success'];
			$results['failed'] = $batchResults['failed'];
			$results['errors'] = $batchResults['errors'];

		} catch (\Exception $e) {
			$results['errors'][] = '批量修复失败：' . $e->getMessage();
		}

		return $results;
	}

	// ==================== 工作流回调处理 ====================

	/**
	 * 工作流状态变更后的业务处理（覆盖默认实现）
	 *
	 * @param int $businessId 合同ID
	 * @param int $status 新的工作流状态
	 * @param array $extra 额外数据
	 * @return bool 处理结果
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		Log::info('CRM合同工作流状态变更处理', [
			'contract_id' => $businessId,
			'status' => $status,
			'status_text' => $this->getWorkflowStatusText($status),
			'extra' => $extra
		]);

		try {
			switch ($status) {
				case WorkflowStatusConstant::STATUS_COMPLETED:
					// 审批通过后的处理
					return $this->handleContractApproved($businessId, $extra);

				case WorkflowStatusConstant::STATUS_REJECTED:
					// 审批拒绝后的处理
					return $this->handleContractRejected($businessId, $extra);

				case WorkflowStatusConstant::STATUS_RECALLED:
					// 撤回后的处理
					return $this->handleContractRecalled($businessId, $extra);

				case WorkflowStatusConstant::STATUS_VOID:
					// 作废后的处理
					return $this->handleContractVoided($businessId, $extra);

				default:
					// 其他状态使用默认处理
					return parent::afterWorkflowStatusChange($businessId, $status, $extra);
			}

		} catch (\Exception $e) {
			Log::error('CRM合同工作流状态变更处理失败', [
				'contract_id' => $businessId,
				'status' => $status,
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);

			return false;
		}
	}

	/**
	 * 处理合同审批通过
	 *
	 * @param int $contractId 合同ID
	 * @param array $extra 额外数据
	 * @return bool 处理结果
	 */
	private function handleContractApproved(int $contractId, array $extra): bool
	{
		Log::info('CRM合同审批通过处理', [
			'contract_id' => $contractId,
			'extra' => $extra
		]);

		// 审批通过的具体业务逻辑：
		// 1. 发送通知给相关人员
		// 2. 更新合同状态为生效
		// 3. 创建回款计划
		// 4. 同步到财务系统
		// 5. 触发后续业务流程

		return true;
	}

	/**
	 * 处理合同审批拒绝
	 *
	 * @param int $contractId 合同ID
	 * @param array $extra 额外数据
	 * @return bool 处理结果
	 */
	private function handleContractRejected(int $contractId, array $extra): bool
	{
		Log::info('CRM合同审批拒绝处理', [
			'contract_id' => $contractId,
			'extra' => $extra
		]);

		// 审批拒绝的具体业务逻辑：
		// 1. 发送拒绝通知
		// 2. 记录拒绝原因
		// 3. 提醒修改后重新提交

		return true;
	}

	/**
	 * 处理合同撤回
	 *
	 * @param int $contractId 合同ID
	 * @param array $extra 额外数据
	 * @return bool 处理结果
	 */
	private function handleContractRecalled(int $contractId, array $extra): bool
	{
		Log::info('CRM合同撤回处理', [
			'contract_id' => $contractId,
			'extra' => $extra
		]);

		// 撤回的具体业务逻辑：
		// 1. 清理相关的临时数据
		// 2. 发送撤回通知
		// 3. 恢复到可编辑状态

		return true;
	}

	/**
	 * 处理合同作废
	 *
	 * @param int $contractId 合同ID
	 * @param array $extra 额外数据
	 * @return bool 处理结果
	 */
	private function handleContractVoided(int $contractId, array $extra): bool
	{
		Log::info('CRM合同作废处理', [
			'contract_id' => $contractId,
			'extra' => $extra
		]);

		// 作废的具体业务逻辑：
		// 1. 标记为作废状态
		// 2. 发送作废通知
		// 3. 清理相关数据
		// 4. 处理关联的回款记录

		return true;
	}

}