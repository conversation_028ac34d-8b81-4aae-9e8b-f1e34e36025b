<template>
  <div class="daily-price-form-view">
    <!-- 基本信息 -->
    <el-descriptions :column="2" border>
      <el-descriptions-item label="报价日期">
        <span class="date-text">
          {{ formatDate(formData.price_date) }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="产品总数">
        <span class="count-text"> {{ formData.total_items || 0 }} 个 </span>
      </el-descriptions-item>

      <el-descriptions-item label="创建时间">
        {{ formatDateTime(formData.created_at) }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 报价明细表格 -->
    <div v-if="priceItems.length > 0" class="price-details">
      <el-divider content-position="left">
        <span class="section-title">报价明细</span>
      </el-divider>

      <div class="price-table-container">
        <el-table :data="priceItems" border stripe size="small">
          <el-table-column type="index" label="序号" width="60" align="center" />

          <el-table-column label="供应商" min-width="120" align="center">
            <template #default="{ row }">
              {{ row.supplier?.name || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="产品规格" min-width="150" align="center">
            <template #default="{ row }">
              {{ row.product?.spec || row.product?.name || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="单价(元/吨)" width="120" align="center">
            <template #default="{ row }">
              <span class="price-text">
                {{ formatPrice(row.unit_price) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="涨跌幅" width="150" align="center">
            <template #default="{ row }">
              <span :class="getPriceChangeClass(row.price_change || row.change_rate)">
                {{ formatPriceChangeWithAmount(row) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="库存价格" width="120" align="center">
            <template #default="{ row }">
              {{ formatPrice(row.stock_price) }}
            </template>
          </el-table-column>

          <el-table-column label="库存数量" width="100" align="center">
            <template #default="{ row }">
              {{ row.stock_qty || 0 }}
            </template>
          </el-table-column>

          <el-table-column label="优惠政策/备注" min-width="150" align="center">
            <template #default="{ row }">
              {{ row.policy_remark || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  // 组件属性
  interface Props {
    formData: any
  }

  const props = withDefaults(defineProps<Props>(), {
    formData: () => ({})
  })

  // 计算属性
  const priceItems = computed(() => {
    return props.formData.items || []
  })

  // 格式化日期
  const formatDate = (date: string) => {
    if (!date) return '-'
    return new Date(date).toLocaleDateString('zh-CN')
  }

  // 格式化日期时间
  const formatDateTime = (dateTime: string) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 格式化价格
  const formatPrice = (price: number | string) => {
    if (!price) return '0.00'
    const num = typeof price === 'string' ? parseFloat(price) : price
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  // 格式化涨跌幅（包含数值和百分比）
  const formatPriceChangeWithAmount = (row: any) => {
    const priceChange = row.price_change
    const changeRate = row.change_rate

    // 如果没有价格变动数据，显示无变动
    if (!priceChange && !changeRate) {
      return '-'
    }

    // 如果价格变动为0，显示无变动
    if (priceChange === 0) {
      return '-'
    }

    // 格式化价格变动和比例
    const sign = priceChange > 0 ? '+' : ''
    const priceText = `${sign}${(priceChange || 0).toFixed(2)}`
    // const rateText = `${sign}${(changeRate || 0).toFixed(2)}%`

    // return `${priceText}(${rateText})`
    return `${priceText}`
  }

  // 获取涨跌幅样式类
  const getPriceChangeClass = (value: number | string | null | undefined) => {
    if (!value) return 'price-stable'
    const num = typeof value === 'string' ? parseFloat(value) : value
    if (num > 0) return 'price-rise'
    if (num < 0) return 'price-fall'
    return 'price-stable'
  }
</script>

<style scoped>
  .daily-price-form-view {
    padding: 20px;
  }

  .date-text {
    font-weight: bold;
    color: #409eff;
    font-size: 16px;
  }

  .count-text {
    font-weight: bold;
    color: #67c23a;
  }

  .price-text {
    font-weight: bold;
    color: #e6a23c;
  }

  .price-rise {
    color: #f56c6c;
    font-weight: bold;
  }

  .price-fall {
    color: #67c23a;
    font-weight: bold;
  }

  .price-stable {
    color: #909399;
  }

  .remark-text,
  .void-reason {
    line-height: 1.6;
    color: #606266;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .price-details,
  .approval-details {
    margin-top: 20px;
  }

  .section-title {
    font-weight: bold;
    color: #409eff;
  }

  .price-table-container {
    margin-top: 10px;
  }

  .action-buttons {
    margin-top: 20px;
    text-align: center;
  }

  .el-descriptions {
    margin-bottom: 20px;
  }

  :deep(.el-descriptions__label) {
    font-weight: 500;
    color: #303133;
  }

  :deep(.el-descriptions__content) {
    color: #606266;
  }

  :deep(.el-table) {
    font-size: 13px;
  }

  :deep(.el-table th) {
    background-color: #f5f7fa;
  }
</style>
