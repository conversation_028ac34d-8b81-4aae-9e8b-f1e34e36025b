<?php
declare(strict_types=1);

namespace app\system\controller;

use app\common\core\base\BaseController;
use app\system\service\SystemTestCrudAllFieldsService;

/**
 * 综合测试表控制器
 */
class SystemTestCrudAllFieldsController extends BaseController
{
    /**
     * @var SystemTestCrudAllFieldsService
     */
    protected $service;
    
    /**
     * 构造函数
     */
    public function __construct(SystemTestCrudAllFieldsService $service)
    {
        parent::__construct();
        $this->service = $service;
    }
    
    /**
     * 列表
     */
    public function index()
    {
        $params = $this->request->get();
        $result = $this->service->getList($params);
        return $this->success($result);
    }
    
    /**
     * 详情
     */
    public function detail($id)
    {
        $result = $this->service->getInfo($id);
        return $this->success($result);
    }
    
    /**
     * 添加
     */
    public function add()
    {
        $params = $this->request->post();
        $result = $this->service->add($params);
        return $this->success($result);
    }
    
    /**
     * 编辑
     */
    public function edit($id)
    {
        $params = $this->request->post();
        $result = $this->service->update($id, $params);
        return $this->success($result);
    }
    
    /**
     * 删除
     */
    public function delete()
    {
        $ids = $this->request->post('ids');
        if (!$ids) {
            return $this->error('参数错误');
        }
        $result = $this->service->batchDelete($ids);
        return $this->success($result);
    }
    
    /**
     * 状态切换
     */
    public function status($id)
    {
        $status = $this->request->post('status');
        $result = $this->service->updateStatus($id, $status);
        return $this->success($result);
    }
} 