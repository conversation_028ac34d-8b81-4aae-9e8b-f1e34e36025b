# CRUD生成器使用说明

## 概述

这是一个基于ThinkPHP框架的CRUD代码生成器，可以根据数据库表结构自动生成前后端代码，帮助开发者快速构建管理系统。生成器支持与前端ArtTable表格组件的无缝集成，使用声明式渲染方式，提供了多种高级表格列组件的自动映射。

## 特性

- 一键生成完整的CRUD代码（前端+后端）
- 通过字段注释驱动代码生成
- 智能映射表格列组件，支持16种不同的列组件
- 使用声明式渲染方式，直接渲染专用列组件
- 支持导入导出功能
- 支持多种搜索条件类型
- 支持表单校验规则自动生成（基于数据库注释标记）
- 支持多种注释格式（旧格式@s=like和新格式@search:like）
- 增强的中文编码处理，避免乱码问题
- 智能日期时间格式处理，统一使用Element Plus兼容格式
- 数字字段类型自动识别和默认值处理
- 表单验证规则按需生成，避免过度验证

## 安装

1. 项目目录中已经包含了生成器代码，不需要额外安装
2. 确保数据库连接配置正确

## 使用方法

### 命令行方式

```bash
php think generator:crud 表名 [--module=模块名] [--frontend] [--overwrite]
```

参数说明：
- `表名`：数据库表名，不需要加前缀
- `--module`：模块名称，默认为system
- `--frontend`：是否生成前端代码，默认不生成
- `--overwrite`：是否覆盖已存在的文件，默认不覆盖

示例：
```bash
# 生成系统用户表的后端代码
php think generator:crud test_user --module=system

# 生成系统用户表的前后端代码，并覆盖已有文件
php think generator:crud test_user --module=system --frontend --overwrite
```

### 字段注释格式

CRUD生成器通过解析字段注释来确定字段的各种属性和行为。支持两种注释格式：

#### 1. 传统格式（使用 | 和 @tag=value）

```
字段说明[:选项列表] | @tag1=value1 @tag2=value2 @tag3
```

示例：
```sql
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用 | @s=eq @e @exp @imp @fmt=status',
```

#### 2. 新格式（直接使用 @tag:value）

```
字段说明[:选项列表] @tag1:value1 @tag2:value2 @tag3
```

示例：
```sql
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1=启用,0=禁用 @search:eq @form:switch @component:switch',
`title` varchar(100) NOT NULL COMMENT '标题 @required @max:100',
`email` varchar(100) DEFAULT NULL COMMENT '邮箱 @email',
`sort` int(11) DEFAULT 0 COMMENT '排序 @number',
```

### 支持的标记

**搜索相关：**
- `@search:xxx` 或 `@s=xxx`：搜索类型（eq, like, gt, lt, between, date等）

**表单相关：**
- `@form:xxx` 或 `@e`：在编辑表单中显示，可以指定控件类型
- `@required`：必填验证
- `@max:数字`：最大长度验证（如 @max:100）
- `@number`：数字验证
- `@email`：邮箱格式验证
- `@url`：URL格式验证
- `@rule:xxx` 或 `@val=xxx`：其他验证规则

**表格相关：**
- `@component:xxx` 或 `@col=xxx`：列组件类型（switch, image, currency等）
- `@format:xxx` 或 `@fmt=xxx`：格式化方式（datetime, date, status, enum等）
- `@h`：在表格中隐藏

**导入导出相关：**
- `@exp`：包含在导出中
- `@imp`：包含在导入中

### 表注释格式

表注释用于配置表级别的生成选项：

```
表说明 @标记1:值1 @标记2:值2
```

支持的标记：
- `@module:xxx`：指定模块名称
- `@exp:true/false`：启用/禁用导出功能
- `@imp:true/false`：启用/禁用导入功能

示例：
```sql
CREATE TABLE `test_user` (
  -- 字段定义
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表 @module:system @exp:true @imp:true';
```

## 表格列组件

生成器支持多种专用列组件，并可以根据字段名称、字段类型或注释标记自动映射：

- `SwitchColumn` - 开关组件，用于状态字段（自动识别 `status` 和所有 `is_` 开头的字段）
- `TagColumn` - 标签组件，用于枚举/状态显示
- `ImageColumn` - 图片组件，支持预览
- `DocumentColumn` - 文档/文件组件
- `LinkColumn` - 链接组件
- `CurrencyColumn` - 货币/金额组件
- `LongTextColumn` - 长文本组件，支持截断和tooltip
- `CopyableColumn` - 可复制文本组件
- `ProgressColumn` - 进度条组件
- `QrcodeColumn` - 二维码组件
- `MediaColumn` - 媒体播放组件
- `EditableColumn` - 可编辑组件

可以通过 `@component:组件名称` 或 `@col=组件名称` 显式指定要使用的组件。

## 表单验证规则

生成器支持基于数据库注释的智能验证规则生成。**重要原则：只有在数据表注释中明确标注验证规则时，才会生成相应的验证代码。**

### 验证规则标记

**必填验证**：
```sql
`title` varchar(100) NOT NULL COMMENT '标题 @required'
```
生成的验证规则：
```javascript
title: [
  { required: true, message: '标题不能为空', trigger: 'blur' }
]
```

**长度限制验证**：
```sql
`title` varchar(100) NOT NULL COMMENT '标题 @required @max:100'
```
生成的验证规则：
```javascript
title: [
  { required: true, message: '标题不能为空', trigger: 'blur' },
  { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
]
```

**数字验证**：
```sql
`sort` int(11) DEFAULT 0 COMMENT '排序 @number'
```
生成的验证规则：
```javascript
sort: [
  {
    validator: (rule: any, value: any, callback: any) => {
      if (value === '' || value === null || value === undefined) {
        callback(new Error('排序不能为空'))
      } else if (isNaN(Number(value))) {
        callback(new Error('排序必须是数字'))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }
]
```

**邮箱验证**：
```sql
`email` varchar(100) DEFAULT NULL COMMENT '邮箱 @email'
```

**URL验证**：
```sql
`website` varchar(200) DEFAULT NULL COMMENT '网站 @url'
```

### 验证触发方式

生成器会根据字段类型自动选择合适的验证触发方式：
- **文本输入框**：使用 `trigger: 'blur'`
- **下拉选择、单选、复选框、开关**：使用 `trigger: 'change'`

### 无验证规则的字段

如果字段注释中没有验证标记，生成器会生成空的验证规则对象：
```javascript
const rules = {}
```
这样可以避免不必要的验证，确保表单能够正常提交。

## 日期时间格式处理

生成器会根据数据库字段类型自动设置正确的 `value-format` 属性，确保与 Element Plus 组件兼容：

### 自动格式映射

**date 类型字段**：
```sql
`date` date DEFAULT NULL COMMENT '日期'
```
生成的组件：
```vue
<ElDatePicker
  v-model="formData.date"
  type="date"
  value-format="YYYY-MM-DD"
  style="width: 100%"
/>
```

**time 类型字段**：
```sql
`time` time DEFAULT NULL COMMENT '时间'
```
生成的组件：
```vue
<ElDatePicker
  v-model="formData.time"
  type="datetime"
  value-format="YYYY-MM-DD HH:mm:ss"
  style="width: 100%"
/>
```

**datetime/timestamp 类型字段**：
```sql
`created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
```
生成的组件：
```vue
<ElDatePicker
  v-model="formData.created_at"
  type="datetime"
  value-format="YYYY-MM-DD HH:mm:ss"
  style="width: 100%"
/>
```

### 格式说明

- **date 字段**：使用 `YYYY-MM-DD` 格式
- **time 字段**：使用 `YYYY-MM-DD HH:mm:ss` 格式（注意：time字段也使用datetime组件）
- **datetime/timestamp 字段**：使用 `YYYY-MM-DD HH:mm:ss` 格式

这样可以避免生成 ISO 格式（如 `2025-07-07T16:00:00.000Z`），确保与后端 API 的兼容性。

## 数字字段处理

生成器会智能识别数字类型字段，并自动配置合适的组件和默认值：

### 自动组件映射

**整数字段**（int, tinyint, smallint, mediumint, bigint）：
```sql
`quantity` int(11) DEFAULT 0 COMMENT '数量'
`sort` int(11) DEFAULT 0 COMMENT '排序'
```
生成的组件：
```vue
<ElInputNumber
  v-model="formData.quantity"
  placeholder="请输入数量"
  style="width: 100%"
/>
```

**小数字段**（decimal, float, double）：
```sql
`price` decimal(10,2) DEFAULT 0.00 COMMENT '价格'
`amount` decimal(15,2) DEFAULT 0.00 COMMENT '金额'
```
生成的组件：
```vue
<ElInputNumber
  v-model="formData.price"
  :precision="2"
  placeholder="请输入价格"
  style="width: 100%"
/>
```

### 默认值处理

生成器会根据字段类型自动设置正确的默认值类型：

**整数字段默认值**：
```javascript
const defaultFormData = {
  quantity: 0,    // 数字类型，不是字符串
  sort: 0,
  level: 1
}
```

**小数字段默认值**：
```javascript
const defaultFormData = {
  price: 0.00,    // 数字类型，不是字符串
  amount: 0.00,
  discount: 0.00
}
```

**特殊字段处理**：
- `tinyint(1)` 类型的字段（如 status, is_xxx）：默认值为数字 0 或 1
- 带有枚举选项的字段：使用第一个选项的值作为默认值

这样可以避免 Vue.js 的类型验证警告，确保表单数据类型的一致性。

### 开关组件配置详解

对于布尔类型字段，生成器会自动配置为开关组件：

**自动识别规则**：
- `status` 字段：显示"启用/禁用"
- 所有 `is_` 开头的字段（如 `is_hot`、`is_recommend`、`is_published` 等）：显示"是/否"

**生成的配置示例**：
```vue
{
  prop: 'is_hot',
  label: '是否热门',
  component: SwitchColumn,
  componentProps: {
    activeValue: 1,
    inactiveValue: 0,
    activeText: '是',
    inactiveText: '否',
    updateApi: EntityNameApi.updateField
  },
  isSpecialColumn: 1
}
```

**重要特性**：
- 使用统一的 `updateField` API 接口，支持任意字段的单独更新
- 自动处理加载状态和错误恢复
- 支持成功/失败消息提示
- 比单独的状态接口更灵活

## 测试表

在`app/common/generator/tests/test_table.sql`中提供了三个测试表：

1. `test_user`：系统用户表
2. `test_article`：文章表
3. `test_product`：商品表

可以导入这些测试表进行生成器测试。

## 配置文件

生成器的配置文件位于`app/common/generator/config/`目录下：

- `generator_config.php`：全局配置
- `column_mapping.php`：表格列组件映射配置
- `form_mapping.php`：表单组件映射配置

通过修改这些配置文件，可以自定义生成器的行为。

## 自定义模板

可以通过修改`app/common/generator/templates/`目录下的模板文件来自定义生成的代码。模板使用简单的变量替换语法：

- `{{变量名}}`：替换为变量值
- `{{#条件变量}}...{{/条件变量}}`：条件块，当条件变量为真时才会包含其中的内容

## 最佳实践

### 数据库设计建议

1. **字段注释**：为表和字段添加清晰的中文注释
2. **枚举值**：利用枚举值提供选项（如：`状态:1=启用,0=禁用`）
3. **字段类型**：选择合适的字段类型
   - 金额字段使用 `decimal(15,2)`
   - 百分比字段使用 `decimal(5,2)`
   - 状态字段使用 `tinyint(1)`
   - 排序字段使用 `int(11)`

### 注释标记使用

4. **验证规则**：只在需要验证的字段上添加验证标记
   ```sql
   `title` varchar(100) NOT NULL COMMENT '标题 @required @max:100'
   `email` varchar(100) DEFAULT NULL COMMENT '邮箱 @email'
   ```

5. **组件指定**：为需要精确控制的列使用`@component:组件名称`指定专用组件
   ```sql
   `status` tinyint(1) DEFAULT 1 COMMENT '状态:1=启用,0=禁用 @component:switch'
   ```

6. **搜索配置**：使用`@search:条件类型`标记需要用于搜索的字段
   ```sql
   `title` varchar(100) NOT NULL COMMENT '标题 @search:like'
   `status` tinyint(1) DEFAULT 1 COMMENT '状态 @search:eq'
   ```

7. **表单控件**：利用`@form:组件类型`定制表单控件类型
   ```sql
   `content` text COMMENT '内容 @form:editor'
   `tags` varchar(500) COMMENT '标签 @form:select'
   ```

### 表级配置

8. **模块配置**：使用`@module:模块名`在表注释中指定模块名称
   ```sql
   COMMENT='系统用户表 @module:system @exp:true @imp:true'
   ```

### 代码生成建议

9. **覆盖生成**：使用 `--overwrite` 参数重新生成代码时要小心，确保备份自定义修改
10. **测试验证**：生成代码后及时测试表单提交、搜索、导入导出等功能
11. **渐进式开发**：先生成基础代码，再根据需要添加自定义功能

## 问题排查

### 常见问题及解决方案

**1. 中文乱码问题**
- 确保数据库字符集为 `utf8mb4`
- 注释使用 UTF-8 编码
- 检查数据库连接配置中的字符集设置

**2. 表单验证过度问题**
- 检查字段注释是否包含不必要的验证标记
- 移除不需要验证的字段上的 `@required`、`@max` 等标记
- 确保只在真正需要验证的字段上添加验证标记

**3. 日期时间格式问题**
- 检查生成的 ElDatePicker 组件是否包含 `value-format` 属性
- 确认格式是否正确：date 使用 `YYYY-MM-DD`，datetime 使用 `YYYY-MM-DD HH:mm:ss`
- 如果仍有问题，重新生成代码：`php think generator:crud 表名 --frontend --overwrite`

**4. 数字字段类型警告**
- 检查表单默认数据中数字字段是否为数字类型而非字符串
- 确认 ElInputNumber 组件的 v-model 绑定正确
- 查看浏览器控制台是否有 Vue.js 类型验证警告

**5. 组件不匹配问题**
- 使用 `@component:组件名称` 显式指定组件
- 检查组件名称是否正确（如：switch, image, currency 等）
- 确认组件是否已正确导入

**6. 搜索功能问题**
- 使用 `@search:条件类型` 指定搜索类型
- 支持的搜索类型：eq, like, gt, lt, between, date 等
- 检查搜索表单是否正确生成

**7. 表单控件问题**
- 使用 `@form:控件类型` 指定表单控件
- 支持的控件类型：input, select, radio, checkbox, switch, editor 等
- 检查控件属性是否正确传递

**8. 导入导出功能问题**
- 检查表注释是否包含 `@exp:true` 和 `@imp:true`
- 确认字段注释是否包含 `@exp` 和 `@imp` 标记
- 检查后端路由和控制器方法是否正确生成

## 开发计划

未来版本将添加以下功能：

1. 增加对多对多关系的支持
2. 支持复杂表单的生成
3. 提供Web界面进行代码生成
4. 添加更多自定义选项

## 更新日志

### 2025-07-03 重大更新
1. **表单验证规则优化**：
   - 重构验证规则生成逻辑，只在字段注释明确标记时才生成验证规则
   - 支持 `@required`、`@max:数字`、`@number`、`@email`、`@url` 等验证标记
   - 避免过度验证导致的表单提交失败问题

2. **日期时间格式标准化**：
   - 自动根据数据库字段类型设置正确的 `value-format` 属性
   - date 字段使用 `YYYY-MM-DD` 格式
   - time 字段使用 `YYYY-MM-DD HH:mm:ss` 格式（使用 datetime 组件）
   - datetime/timestamp 字段使用 `YYYY-MM-DD HH:mm:ss` 格式
   - 避免生成 ISO 格式，确保与 Element Plus 兼容

3. **数字字段类型处理**：
   - 智能识别整数和小数字段，自动使用 ElInputNumber 组件
   - 修复默认值类型问题，确保数字字段默认值为数字类型而非字符串
   - 避免 Vue.js 类型验证警告

4. **组件映射改进**：
   - 优化 time 字段处理，统一使用 ElDatePicker 组件
   - 改进 tinyint 字段的默认值处理
   - 完善组件属性传递机制

5. **代码生成稳定性**：
   - 修复变量污染问题，确保循环间变量正确重置
   - 改进模板渲染逻辑，避免组件导入错误
   - 增强错误处理和调试信息

### 2023-06-30更新
1. **修复了表格列组件封装问题**：改进了前端列组件渲染方式，不再使用嵌套ElTableColumn，直接渲染专用组件
2. **增强了字段注释解析**：现在同时支持两种标记方式（`@s=like`和`@search:like`）
3. **修复了中文乱码问题**：增强了编码检测和转换功能，支持多种中文编码格式
4. **改进了组件属性传递**：确保updateApi等重要属性正确传递给专用列组件
5. **优化了组件映射**：更好地处理特殊组件的选项和属性映射
6. **完善了文档**：更新了使用说明和最佳实践建议

## 问题反馈

如果使用过程中遇到问题或有改进建议，请联系开发团队。 