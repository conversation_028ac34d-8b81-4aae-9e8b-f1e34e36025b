<?php
declare(strict_types=1);

namespace app\finance\service;

use app\common\core\base\BaseService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use app\finance\model\FinancePaymentApproval;
use app\common\exception\BusinessException;
use app\common\utils\PaymentMethodHelper;
use think\facade\Log;

/**
 * 付款申请服务类
 */
class FinancePaymentApprovalService extends BaseService implements FormServiceInterface
{
	protected string $modelClass = FinancePaymentApproval::class;
	
	public function __construct()
	{
		$this->model = new FinancePaymentApproval();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = $this->model->with([
			'submitter',
			'creator'
		])
		                     ->find($id);
		
		if (!$model) {
			throw new BusinessException('付款申请记录不存在');
		}
		
		return $model->toArray();
	}
	
	/**
	 * 创建表单数据
	 */
	public function saveForm(array $data): array
	{
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			// 验证数据
			$validatedData = $this->validateFormData($formData, 'create');
			
			// 创建主记录
			$id = $this->model->saveByCreate($validatedData);
			
			// 返回完整数据
			$formData = $this->getFormData($id);
			
			return [
				$id,
				$formData
			];
			
		}
		catch (\Exception $e) {
			Log::error('付款申请创建失败: ' . $e->getMessage(), [
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('付款申请创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				throw new BusinessException('付款申请记录不存在');
			}
			
			// 验证数据
			$validatedData = $this->validateFormData($data, 'update');
			
			// 更新主记录
			return $model->saveByUpdate($validatedData);
			
		}
		catch (\Exception $e) {
			Log::error('付款申请更新失败: ' . $e->getMessage(), [
				'id'    => $id,
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('付款申请更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		try {
			$updateData = ['approval_status' => $status];
			
			// 根据状态添加时间字段
			if ($status === 1) { // 审批中
				$updateData['submit_time'] = date('Y-m-d H:i:s');
			}
			elseif (in_array($status, [
				2,
				3,
				4,
				5,
				6
			])) { // 已完成状态
				$updateData['approval_time'] = date('Y-m-d H:i:s');
			}
			
			// 添加额外数据
			if (isset($extra['workflow_instance_id'])) {
				$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
			}
			
			return $this->model->where('id', $id)
			                   ->update($updateData) > 0;
			
		}
		catch (\Exception $e) {
			Log::error('付款申请状态更新失败: ' . $e->getMessage(), [
				'id'     => $id,
				'status' => $status,
				'extra'  => $extra,
				'trace'  => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 删除表单
	 */
	public function deleteForm(int $id): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			// 软删除主记录
			$model->delete();
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('付款申请删除失败: ' . $e->getMessage(), [
				'id'    => $id,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 获取流程实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		if (is_array($formData)) {
			$submitterName = $formData['submitter_name'] ?? '';
			$amount        = $formData['payment_amount']
				? $formData['payment_amount'] . '元'
				: '';
			return "{$submitterName}付款申请-{$amount}";
		}
		
		return '付款申请';
	}
	
	/**
	 * 验证表单数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		// 基础验证
		/*if (empty($data['payment_no'])) {
			throw new BusinessException('付款申请单号不能为空');
		}*/
		
		// 使用PaymentMethodHelper验证付款方式
		if (isset($data['payment_method']) && !PaymentMethodHelper::isValid($data['payment_method'])) {
			throw new BusinessException('付款方式无效，请选择正确的付款方式');
		}
		
		// 获取有效的付款方式值用于验证规则
		$validPaymentMethods = implode(',', PaymentMethodHelper::getValidValues());
		
		$rules = [
			'payee_name'     => 'require',
			'payment_amount' => 'require|float|gt:0',
			'payment_date'   => 'require|date',
			'payment_method' => "require|in:{$validPaymentMethods}",
			'reason'         => 'require',
		];
		
		$messages = [
			'payee_name.require'     => '支付对象不能为空',
			'payment_amount.float'   => '付款金额格式错误',
			'payment_amount.gt'      => '付款金额不能小于0',
			'payment_date.require'   => '请选择支付日期',
			'payment_date.date'      => '请选择正确的支付日期',
			'payment_method.require' => '请选择付款方式',
			'payment_method.in'      => '付款方式无效，请选择正确的付款方式',
			'reason.require'         => '付款事由不能为空',
		];
		
		$validate = validate($rules, $messages);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		return $data;
	}
	
	/**
	 * 工作流状态变更后的业务处理
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		try {
			// 根据不同状态执行相应的业务逻辑
			switch ($status) {
				case 2: // 已通过
					// 可以在这里添加财务记账逻辑
					Log::info("付款申请已通过，业务ID: {$businessId}");
					break;
				
				case 3: // 已拒绝
					Log::info("付款申请已拒绝，业务ID: {$businessId}");
					break;
				
				default:
					break;
			}
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('付款申请工作流状态变更后处理失败: ' . $e->getMessage(), [
				'business_id' => $businessId,
				'status'      => $status,
				'extra'       => $extra,
				'trace'       => $e->getTraceAsString()
			]);
			return false;
		}
	}
}
