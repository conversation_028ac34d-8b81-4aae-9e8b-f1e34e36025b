-- 工作流定义配置SQL
-- 为9个业务审批表配置workflow_definition记录
-- 使用id=32的flow_config作为标准模板
-- 创建时间：2025-07-28

-- 标准的一级审批流程配置（基于id=32的flow_config）
SET @standard_flow_config = '{
  "nodeConfig": {
    "type": 0,
    "nodeName": "发起人",
    "nodeId": "start_node",
    "selfSelect": false,
    "childNode": {
      "type": 1,
      "nodeName": "部门主管审批",
      "nodeId": "manager_approve",
      "setType": 4,
      "directorLevel": 1,
      "approvalMode": 1,
      "nodeUserList": [],
      "timeLimit": 24,
      "autoApprove": false,
      "noHandlerAction": 1,
      "childNode": null
    }
  },
  "flowPermission": []
}';

-- 获取各业务类型的workflow_type ID
SET @ims_outbound_type_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_outbound_approval' LIMIT 1);
SET @ims_inbound_type_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_inbound_approval' LIMIT 1);
SET @ims_shipment_type_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_shipment_approval' LIMIT 1);
SET @ims_purchase_type_id = (SELECT id FROM workflow_type WHERE business_code = 'ims_purchase_approval' LIMIT 1);
SET @finance_payment_type_id = (SELECT id FROM workflow_type WHERE business_code = 'finance_payment_approval' LIMIT 1);
SET @finance_expense_type_id = (SELECT id FROM workflow_type WHERE business_code = 'finance_expense_reimbursement' LIMIT 1);
SET @hr_business_trip_type_id = (SELECT id FROM workflow_type WHERE business_code = 'hr_business_trip' LIMIT 1);
SET @hr_outing_type_id = (SELECT id FROM workflow_type WHERE business_code = 'hr_outing' LIMIT 1);
SET @office_sample_mail_type_id = (SELECT id FROM workflow_type WHERE business_code = 'office_sample_mail' LIMIT 1);

-- 清理可能存在的重复记录
DELETE FROM workflow_definition WHERE type_id IN (
    @ims_outbound_type_id,
    @ims_inbound_type_id,
    @ims_shipment_type_id,
    @ims_purchase_type_id,
    @finance_payment_type_id,
    @finance_expense_type_id,
    @hr_business_trip_type_id,
    @hr_outing_type_id,
    @office_sample_mail_type_id
);

-- 插入9个业务类型的工作流定义
INSERT INTO workflow_definition (name, type_id, flow_config, status, is_template, remark, creator_id, created_at, updated_at, tenant_id) VALUES

-- IMS模块（库存管理）
('出库申请标准审批流程', @ims_outbound_type_id, @standard_flow_config, 1, 0, '出库申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),
('入库申请标准审批流程', @ims_inbound_type_id, @standard_flow_config, 1, 0, '入库申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),
('出货申请标准审批流程', @ims_shipment_type_id, @standard_flow_config, 1, 0, '出货申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),
('采购申请标准审批流程', @ims_purchase_type_id, @standard_flow_config, 1, 0, '采购申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),

-- Finance模块（财务管理）
('付款申请标准审批流程', @finance_payment_type_id, @standard_flow_config, 1, 0, '付款申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),
('报销申请标准审批流程', @finance_expense_type_id, @standard_flow_config, 1, 0, '报销申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),

-- HR模块（人力资源）
('出差申请标准审批流程', @hr_business_trip_type_id, @standard_flow_config, 1, 0, '出差申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),
('外出申请标准审批流程', @hr_outing_type_id, @standard_flow_config, 1, 0, '外出申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0),

-- Office模块（办公管理）
('样品邮寄申请标准审批流程', @office_sample_mail_type_id, @standard_flow_config, 1, 0, '样品邮寄申请标准审批流程，部门主管一级审批', 1, NOW(), NOW(), 0);

-- 验证插入结果
SELECT 
    wd.id,
    wd.name,
    wt.business_code,
    wd.status,
    wd.is_template,
    wd.created_at
FROM workflow_definition wd
LEFT JOIN workflow_type wt ON wd.type_id = wt.id
WHERE wt.business_code IN (
    'ims_outbound_approval',
    'ims_inbound_approval', 
    'ims_shipment_approval',
    'ims_purchase_approval',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'hr_business_trip',
    'hr_outing',
    'office_sample_mail'
)
ORDER BY wt.module_code, wt.business_code;

-- 流程配置说明
-- nodeConfig.type: 0=发起人, 1=审批人, 2=抄送人, 3=条件, 4=条件分支
-- setType: 1=指定成员, 2=发起人自选, 4=部门主管, 8=角色, 16=表单人员
-- approvalMode: 1=任意一人通过, 2=所有人通过, 3=按顺序审批
-- directorLevel: 部门主管层级，1=直属主管
-- timeLimit: 审批时限（小时）
-- autoApprove: 超时是否自动通过
-- noHandlerAction: 审批人为空时处理方式，1=自动通过，2=转交管理员

-- 如果需要复制id=32的具体flow_config，请执行以下查询获取：
-- SELECT flow_config FROM workflow_definition WHERE id = 32;
-- 然后替换上面的@standard_flow_config变量值

-- 使用说明：
-- 1. 先执行 docs/workflow_type_config.sql 创建workflow_type记录
-- 2. 再执行此文件创建workflow_definition记录
-- 3. 确保id=32的workflow_definition记录存在且flow_config有效
-- 4. 如需自定义flow_config，可修改@standard_flow_config变量
