<?php
declare (strict_types=1);

namespace app\system\validate;

use think\Validate;


/*
 * 管理员验证类
 * */

class AdminValidate extends Validate
{
	/**
	 * 定义验证规则
	 * 格式：'字段名' =>  ['规则1','规则2'...]
	 *
	 * @var array
	 */
	protected $rule = [
		'username'       => 'require|alphaDash|length:3,50|unique:system_admin',
		'password'       => 'require|length:6,100',
		'real_name'      => 'max:50',
		//		'avatar'         => 'url',
		'gender'         => 'in:0,1,2',
		'email'          => 'email|max:100',
		'mobile'         => 'mobile|max:20',
		'is_super_admin' => 'in:0,1',
		'dept_id'        => 'integer|>=:0',
		'post_ids'       => 'array',
		'status'         => 'in:0,1',
		'role_ids'       => 'array',
		'remark'         => 'max:255',
	];
	
	/**
	 * 定义错误信息
	 * 格式：'字段名.规则名' =>  '错误信息'
	 *
	 * @var array
	 */
	protected $message = [
		'username.require'         => '请输入用户名',
		'username.alphaDash'       => '用户名只能包含字母、数字、下划线和破折号',
		'username.length'          => '用户名长度必须在3-50个字符之间',
		'username.unique'          => '用户名已存在',
		'password.require'         => '请输入密码',
		'password.alphaDash'       => '密码只能包含字母、数字、下划线和破折号',
		'password.length'          => '密码长度必须在6-100个字符之间',
		'real_name.max'            => '真实姓名最多不能超过50个字符',
		//		'avatar.url'         => '头像必须是有效的URL地址',
		'avatar.require'           => '请上传头像',
		'gender.in'                => '性别值不正确',
		'email.email'              => '请输入正确的邮箱地址',
		'email.max'                => '邮箱长度不能超过100个字符',
		'mobile.mobile'            => '请输入正确的手机号码',
		'mobile.max'               => '手机号码长度不能超过20个字符',
		'is_super_admin.in'        => '超级管理员标志值不正确',
		'dept_id.integer'          => '部门ID必须是整数',
		'dept_id.>='               => '部门ID必须大于等于0',
		'post_ids.array'           => '岗位ID必须是数组格式',
		'status.in'                => '状态值不正确',
		'role_ids.array'           => '角色ID必须是数组格式',
		'remark.max'               => '备注最多不能超过255个字符',
		'new_password.require'     => '请输入新密码',
		'new_password.alphaDash'   => '新密码只能包含字母、数字、下划线和破折号',
		'new_password.length'      => '新密码长度必须在6-100个字符之间',
		'confirm_password.require' => '请输入确认密码',
		'confirm_password.confirm' => '确认密码与新密码不一致',
	];
	
	/**
	 * 添加场景
	 */
	public function sceneAdd()
	{
		return $this;
	}
	
	public function sceneEdit()
	{
		return $this->remove('password', 'require|length')
		            ->remove('username', 'require|alphaDash|length|unique');
	}
	
	public function sceneResetPassword()
	{
		return $this->only(['password'])
		            ->append('confirm_password', 'require|confirm:new_password');
	}
	
	public function sceneAvatar()
	{
		return $this->only(['avatar'])
		            ->append('avatar', 'require');
	}
	
	public function sceneChangePassword()
	{
		return $this->only(['password'])
		            ->append('new_password', 'require|alphaDash|length')
		            ->append('confirm_password', 'require|confirm:new_password');
	}
}
