<?php
declare(strict_types=1);

namespace app\project\controller;

use app\common\core\base\BaseController;
use app\project\service\ProjectTaskRecordService;
use think\response\Json;

/**
 * 任务记录表（评论+跟进）控制器 - 仅保留必要的API接口
 * 主要功能已迁移到 ProjectTaskController
 */
class ProjectTaskRecordController extends BaseController
{
    /**
     * @var ProjectTaskRecordService
     */
    protected $service;

    /**
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();
        $this->service = ProjectTaskRecordService::getInstance();
    }

    /**
     * 编辑记录（评论或跟进）
     */
    public function edit($id): Json
    {
        try {
            // 检查操作权限
            if (!$this->service->checkRecordPermission($id, 'edit')) {
                return $this->error('无权限编辑此记录');
            }

            $data = $this->request->post();
            unset($data['id'], $data['task_id'], $data['record_type']); // 不允许修改这些字段

            $result = $this->service->update($id, $data);
            return $this->success('记录更新成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除记录
     */
    public function delete($id): Json
    {
        try {
            // 检查操作权限
            if (!$this->service->checkRecordPermission($id, 'delete')) {
                return $this->error('无权限删除此记录');
            }

            $result = $this->service->delete($id);
            return $this->success('记录删除成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}