<?php
// 测试工作流表单功能
require_once 'vendor/autoload.php';

use think\facade\Db;
use app\workflow\factory\DynamicWorkflowFactory;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

echo "🚀 开始测试工作流表单功能...\n\n";

// 测试的业务类型
$testCases = [
    [
        'name' => '出差申请',
        'business_code' => 'hr_business_trip',
        'test_data' => [
            'business_trip' => [
                'start_time' => '2025-07-29 09:00:00',
                'end_time' => '2025-07-31 18:00:00',
                'destination' => '北京',
                'reason' => '客户拜访',
                'remark' => '重要客户拜访',
                'creator_id' => 1
            ],
            'itineraries' => [
                ['date' => '2025-07-29', 'destination' => '北京', 'reason' => '客户会议'],
                ['date' => '2025-07-30', 'destination' => '北京', 'reason' => '项目洽谈']
            ]
        ]
    ],
    [
        'name' => '外出申请',
        'business_code' => 'hr_outing',
        'test_data' => [
            'start_time' => '2025-07-28 14:00:00',
            'end_time' => '2025-07-28 17:00:00',
            'reason' => '银行办事',
            'attachments' => [],
            'creator_id' => 1
        ]
    ],
    [
        'name' => '样品邮寄申请',
        'business_code' => 'office_sample_mail',
        'test_data' => [
            'sample_name' => '产品样品A',
            'sample_description' => '新款产品样品，用于客户测试',
            'sender_phone' => '***********',
            'recipient_info' => '张三，北京市朝阳区xxx路xxx号，***********',
            'remark' => '加急处理',
            'creator_id' => 1
        ]
    ],
    [
        'name' => '付款申请',
        'business_code' => 'finance_payment_approval',
        'test_data' => [
            'payment_reason' => '供应商货款支付',
            'payment_amount' => 50000,
            'payment_method' => '银行转账',
            'payment_date' => '2025-07-30',
            'payee_name' => 'ABC供应商',
            'payee_bank' => '中国银行北京分行',
            'payee_account' => '1234567890123456789',
            'remark' => '月度货款结算',
            'creator_id' => 1
        ]
    ],
    [
        'name' => '报销申请',
        'business_code' => 'finance_expense_reimbursement',
        'test_data' => [
            'amount' => 1500,
            'expense_type' => '差旅费',
            'items' => [
                ['description' => '高铁票费用'],
                ['description' => '住宿费用'],
                ['description' => '餐费']
            ],
            'attachments' => [],
            'remark' => '出差报销',
            'creator_id' => 1
        ]
    ],
    [
        'name' => '出库申请',
        'business_code' => 'ims_outbound_approval',
        'test_data' => [
            'dept_id' => 1,
            'outbound_date' => '2025-07-29',
            'customer_id' => 1,
            'items' => [
                ['product_id' => 1, 'product_name' => '产品A', 'quantity' => 10, 'unit_price' => 100, 'total_amount' => 1000],
                ['product_id' => 2, 'product_name' => '产品B', 'quantity' => 5, 'unit_price' => 200, 'total_amount' => 1000]
            ],
            'total_amount' => 2000,
            'remark' => '客户订单出库',
            'attachments' => [],
            'creator_id' => 1
        ]
    ],
    [
        'name' => '出货申请',
        'business_code' => 'ims_shipment_approval',
        'test_data' => [
            'dept_id' => 1,
            'shipment_date' => '2025-07-30',
            'customer_id' => 1,
            'items' => [
                ['product_id' => 3, 'product_name' => '产品C', 'quantity' => 8, 'unit_price' => 150, 'total_amount' => 1200],
                ['product_id' => 4, 'product_name' => '产品D', 'quantity' => 12, 'unit_price' => 80, 'total_amount' => 960]
            ],
            'total_amount' => 2160,
            'remark' => '月度出货',
            'attachments' => [],
            'creator_id' => 1
        ]
    ]
];

$results = [];

foreach ($testCases as $testCase) {
    echo "📝 测试 {$testCase['name']} ({$testCase['business_code']})...\n";
    
    $result = [
        'name' => $testCase['name'],
        'business_code' => $testCase['business_code'],
        'service_creation' => false,
        'save_form' => false,
        'update_form' => false,
        'delete_form' => false,
        'errors' => []
    ];
    
    try {
        // 1. 测试Service创建
        echo "  🔧 测试Service创建...\n";
        $service = DynamicWorkflowFactory::createFormServiceByBusinessCode($testCase['business_code']);
        
        if ($service && $service instanceof \app\workflow\interfaces\FormServiceInterface) {
            $result['service_creation'] = true;
            echo "  ✅ Service创建成功: " . get_class($service) . "\n";
            
            // 2. 测试保存表单
            echo "  💾 测试保存表单...\n";
            try {
                list($id, $formData) = $service->saveForm($testCase['test_data']);
                $result['save_form'] = true;
                echo "  ✅ 表单保存成功，ID: {$id}\n";
                
                // 3. 测试更新表单
                echo "  ✏️ 测试更新表单...\n";
                $updateData = array_merge($testCase['test_data'], ['remark' => '测试更新']);
                $updateResult = $service->updateForm($id, $updateData);
                
                if ($updateResult) {
                    $result['update_form'] = true;
                    echo "  ✅ 表单更新成功\n";
                } else {
                    $result['errors'][] = '表单更新失败';
                    echo "  ❌ 表单更新失败\n";
                }
                
                // 4. 测试删除表单
                echo "  🗑️ 测试删除表单...\n";
                $deleteResult = $service->deleteForm($id);
                
                if ($deleteResult) {
                    $result['delete_form'] = true;
                    echo "  ✅ 表单删除成功\n";
                } else {
                    $result['errors'][] = '表单删除失败';
                    echo "  ❌ 表单删除失败\n";
                }
                
            } catch (Exception $e) {
                $result['errors'][] = '表单操作错误: ' . $e->getMessage();
                echo "  ❌ 表单操作错误: " . $e->getMessage() . "\n";
            }
            
        } else {
            $result['errors'][] = 'Service创建失败或未实现FormServiceInterface';
            echo "  ❌ Service创建失败或未实现FormServiceInterface\n";
        }
        
    } catch (Exception $e) {
        $result['errors'][] = 'Service创建错误: ' . $e->getMessage();
        echo "  ❌ Service创建错误: " . $e->getMessage() . "\n";
    }
    
    $results[] = $result;
    echo "\n";
}

// 输出测试总结
echo "📊 测试总结:\n";
echo "=" . str_repeat("=", 80) . "\n";

$totalTests = count($results);
$passedTests = 0;

foreach ($results as $result) {
    $status = ($result['service_creation'] && $result['save_form'] && $result['update_form'] && $result['delete_form']) ? '✅ 通过' : '❌ 失败';
    
    if ($status === '✅ 通过') {
        $passedTests++;
    }
    
    echo sprintf("%-20s %-25s %s\n", $result['name'], $result['business_code'], $status);
    
    if (!empty($result['errors'])) {
        foreach ($result['errors'] as $error) {
            echo "  ⚠️  {$error}\n";
        }
    }
}

echo "\n";
echo "总计: {$totalTests} 个表单，{$passedTests} 个通过，" . ($totalTests - $passedTests) . " 个失败\n";

if ($passedTests === $totalTests) {
    echo "🎉 所有表单测试通过！\n";
} else {
    echo "⚠️  部分表单测试失败，请检查错误信息\n";
}

echo "\n测试完成！\n";
