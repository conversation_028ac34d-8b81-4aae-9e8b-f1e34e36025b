---
description: 多租户前后端分离框架系统
globs: 
alwaysApply: false
---
{
  "name": "多租户前后端分离框架系统",
  "description": "基于ThinkPHP 8和Vue 3的多租户前后端分离框架系统，支持RBAC权限管理、动态表单、工作流等功能",
  "backend": {
    "projectType": "thinkphp",
    "phpVersion": "8.2",
    "framework": "thinkphp8",
    "fileStructure": {
      "app": {
        "controller": "控制器目录",
        "model": "模型目录",
        "service": "服务层目录",
        "validate": "验证器目录",
        "middleware": "中间件目录",
        "common": "公共函数目录"
      },
      "config": "配置文件目录",
      "public": "公共资源目录",
      "runtime": "运行时目录",
      "vendor": "第三方依赖目录"
    },
    "namingConventions": {
      "controller": "PascalCase",
      "model": "PascalCase",
      "service": "PascalCase",
      "table": "snake_case",
      "class": "PascalCase",
      "method": "camelCase",
      "variable": "camelCase"
    },
    "coding": {
      "indentation": 4,
      "docBlockStyle": "phpdoc",
      "lineLength": 120
    },
    "suggestions": {
      "database": {
        "tablePrefix": "system_",
        "fieldCasing": "snake_case",
        "commonFields": ["id", "created_at", "updated_at", "deleted_at", "tenant_id"]
      },
      "modules": [
        "admin", "role", "menu", "dept", "post", "tenant", "log", "attachment", "notice", "message", "dict"
      ]
    }
  },
  "frontend": {
    "projectName": "Art Design Pro",
    "projectPath": "frontend/art-design-pro",
    "framework": "Vue 3",
    "language": "TypeScript",
    "buildTool": "Vite",
    "uiLibrary": "Element Plus",
    "stateManagement": "Pinia",
    "router": "Vue Router",
    "cssPreprocessor": "SCSS"
  }
}
