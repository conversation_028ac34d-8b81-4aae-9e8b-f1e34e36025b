<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 综合测试表-包含所有字段类型模型
 */
class SystemTestCrudComprehensive extends BaseModel
{
    // 设置表名
    protected $name = 'system_test_crud_comprehensive';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段转换
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // 软删除
    protected string $deleteTime = 'deleted_at';

    public function __construct(array $data = [])
    {
        parent::__construct($data);
    }
}