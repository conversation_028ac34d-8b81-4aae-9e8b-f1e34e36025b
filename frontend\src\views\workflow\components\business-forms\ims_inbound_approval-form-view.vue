<!--
  ⚠️ 临时屏蔽 - 入库申请详情预览暂时不可用
  TODO: 需要进一步完善后再启用
  屏蔽时间: 2025-07-28
-->
<template>
  <div class="inbound-form-view">
    <div style="text-align: center; padding: 40px;">
      <el-icon size="48" color="#E6A23C">
        <WarningFilled />
      </el-icon>
      <h4 style="margin: 15px 0; color: #E6A23C;">详情预览暂时不可用</h4>
      <p style="color: #909399;">
        入库申请详情预览功能正在完善中，请稍后再试。
      </p>
    </div>

    <!-- 原始详情预览代码已被注释
    <el-descriptions :column="2" border>
      <el-descriptions-item label="入库单号">
        {{ formData.inbound_no || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="入库类型">
        {{ getInboundTypeName(formData.inbound_type) }}
      </el-descriptions-item>

      <el-descriptions-item label="入库日期">
        {{ formData.inbound_date || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="入库仓库">
        {{ formData.warehouse_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="供应商">
        {{ formData.supplier_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="来源仓库">
        {{ formData.source_warehouse_name || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="总数量">
        {{ formData.total_quantity || 0 }}
      </el-descriptions-item>

      <el-descriptions-item label="总金额">
        ¥{{ formData.total_amount || 0 }}
      </el-descriptions-item>

      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        {{ formData.remark }}
      </el-descriptions-item>

      <!-- 入库明细 -->
      <el-descriptions-item label="入库明细" :span="2" v-if="formData.items && formData.items.length > 0">
        <el-table :data="formData.items" border size="small">
          <el-table-column prop="product_name" label="产品名称" />
          <el-table-column prop="spec_name" label="规格" />
          <el-table-column prop="quantity" label="数量" />
          <el-table-column prop="unit_price" label="单价" />
          <el-table-column prop="total_amount" label="小计" />
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
import { ElIcon } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'

// 保持接口兼容性
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  businessCode: {
    type: String,
    default: 'ims_inbound_approval'
  }
})

// 原始功能代码已被注释
/*
import { ElDescriptions, ElDescriptionsItem, ElTable, ElTableColumn } from 'element-plus'

const getInboundTypeName = (type: number): string => {
  const types = {
    1: '采购入库',
    2: '调拨入库',
    3: '退货入库',
    4: '其他入库'
  }
  return types[type] || '未知类型'
}
*/
</script>

<style scoped lang="scss">

</style>
