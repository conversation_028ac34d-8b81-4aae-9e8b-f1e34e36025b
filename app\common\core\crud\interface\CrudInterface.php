<?php
declare(strict_types=1);

namespace app\common\core\crud\interface;

/**
 * CRUD操作接口
 * 定义标准的增删改查操作
 */
interface CrudInterface
{
    /**
     * 获取列表数据
     *
     * @param array $where 查询条件
     * @param mixed $order 排序条件
     * @param array $with 关联查询
     * @param bool|null $applyDataPermission 是否应用数据权限
     * @return mixed
     */
    public function getList(array $where = [], $order = [], array $with = [], bool $applyDataPermission = null);
    
    /**
     * 获取分页列表数据
     *
     * @param array $where 查询条件
     * @param mixed $order 排序条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $with 关联查询
     * @param bool|null $applyDataPermission 是否应用数据权限
     * @return mixed
     */
    public function getPageList(
        array $where = [], $order = [], int $page = 1, int $limit = 10, array $with = [],
        bool $applyDataPermission = null
    );
    
    /**
     * 获取数据总数
     *
     * @param array $where 查询条件
     * @param bool|null $applyDataPermission 是否应用数据权限
     * @return int
     */
    public function getCount(array $where = [], bool $applyDataPermission = null): int;
    
    /**
     * 获取单条数据
     *
     * @param array $where 查询条件
     * @param array $with 关联查询
     * @param bool|null $applyDataPermission 是否应用数据权限
     * @return mixed
     */
    public function getOne(array $where = [], array $with = [], bool $applyDataPermission = null);
    
    /**
     * 添加数据
     *
     * @param array $data 数据
     * @return mixed
     */
    public function add(array $data);
    
    /**
     * 编辑数据
     *
     * @param array $data 数据
     * @param array $where 查询条件
     * @return mixed
     */
    public function edit(array $data, array $where);
    
    /**
     * 删除数据
     *
     * @param array $where 查询条件
     * @return mixed
     */
    public function delete(array $where);
    
    /**
     * 搜索数据
     *
     * @param array $params 搜索参数
     * @param array $searchFields 搜索字段配置
     * @param array $with 关联查询
     * @param bool|null $applyDataPermission 是否应用数据权限
     * @return array
     */
    public function search(array $params, array $searchFields = [], array $with = [], bool $applyDataPermission = null);
    
    /**
     * 获取下拉选项数据
     *
     * @param array $where 查询条件
     * @param string $labelField 标签字段
     * @param string $valueField 值字段
     * @param mixed $order 排序条件
     * @return array
     */
    public function getSelectOptions(
        array $where = [], string $labelField = 'name', string $valueField = 'id', $order = []
    );
    
    /**
     * 更新单个字段
     *
     * @param mixed $id 主键ID
     * @param string $field 字段名
     * @param mixed $value 字段值
     * @return bool
     */
    public function updateField($id, string $field, $value): bool;
} 