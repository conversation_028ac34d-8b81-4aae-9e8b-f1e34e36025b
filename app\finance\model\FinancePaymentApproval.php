<?php
declare(strict_types=1);

namespace app\finance\model;

use app\common\core\base\BaseModel;
use app\workflow\constants\WorkflowStatusConstant;
use app\common\utils\PaymentMethodHelper;

/**
 * 付款申请表模型
 */
class FinancePaymentApproval extends BaseModel
{
    // 设置表名
    protected $name = 'finance_payment_approval';
    
    // 字段类型转换
    protected $type = [
        'workflow_instance_id' => 'integer',
        'approval_status' => 'integer',
        'submitter_id' => 'integer',
        'payment_amount' => 'float',
        'payment_method' => 'integer',
        'creator_id' => 'integer',
    ];

    // 追加属性
    protected $append = [
        'payment_method_text',
        'approval_status_text'
    ];
    
    // 审批状态常量
    const STATUS_DRAFT = WorkflowStatusConstant::STATUS_DRAFT;
    const STATUS_PROCESSING = WorkflowStatusConstant::STATUS_PROCESSING;
    const STATUS_COMPLETED = WorkflowStatusConstant::STATUS_COMPLETED;
    const STATUS_REJECTED = WorkflowStatusConstant::STATUS_REJECTED;
    const STATUS_TERMINATED = WorkflowStatusConstant::STATUS_TERMINATED;
    const STATUS_RECALLED = WorkflowStatusConstant::STATUS_RECALLED;
    const STATUS_VOID = WorkflowStatusConstant::STATUS_VOID;
    
    /**
     * 获取默认搜索字段
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'payment_no' => ['type' => 'like'],
            'approval_status' => ['type' => 'eq'],
            'payee_name' => ['type' => 'like'],
            'payee_account' => ['type' => 'like'],
            'payee_bank' => ['type' => 'like'],
            'payment_amount' => ['type' => 'between'],
            'payment_date' => ['type' => 'date'],
            'payment_method' => ['type' => 'eq'],
            'submit_time' => ['type' => 'datetime'],
            'approval_time' => ['type' => 'datetime'],
            'created_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 关联提交人
     */
    public function submitter()
    {
        return $this->belongsTo(\app\system\model\SystemAdmin::class, 'submitter_id', 'id')->bind([
            'submitter_name' => 'username'
        ]);
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(\app\system\model\SystemAdmin::class, 'creator_id', 'id')->bind([
            'creator_name' => 'username'
        ]);
    }
    
    /**
     * 获取付款方式文本
     */
    public function getPaymentMethodTextAttr($value, $data)
    {
        return PaymentMethodHelper::getLabel($data['payment_method'] ?? null);
    }

    /**
     * 获取审批状态文本
     */
    public function getApprovalStatusTextAttr($value, $data)
    {
        return WorkflowStatusConstant::getBusinessStatusText($data['approval_status'] ?? 0);
    }
}
