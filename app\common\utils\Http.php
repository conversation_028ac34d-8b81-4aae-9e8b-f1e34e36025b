<?php
declare(strict_types=1);

namespace app\common\utils;

/**
 * HTTP请求工具类
 */
class Http
{
    /**
     * 请求超时时间（秒）
     * @var int
     */
    protected $timeout = 30;
    
    /**
     * 最大重定向次数
     * @var int
     */
    protected $maxRedirects = 3;
    
    /**
     * UserAgent
     * @var string
     */
    protected $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    
    /**
     * 请求头
     * @var array
     */
    protected $headers = [];
    
    /**
     * Cookie
     * @var string
     */
    protected $cookie = '';
    
    /**
     * SSL验证
     * @var bool
     */
    protected $sslVerify = false;
    
    /**
     * 代理
     * @var string
     */
    protected $proxy = '';
    
    /**
     * 设置超时时间
     * @param int $timeout 超时时间（秒）
     * @return $this
     */
    public function setTimeout(int $timeout)
    {
        $this->timeout = $timeout;
        return $this;
    }
    
    /**
     * 设置最大重定向次数
     * @param int $maxRedirects 最大重定向次数
     * @return $this
     */
    public function setMaxRedirects(int $maxRedirects)
    {
        $this->maxRedirects = $maxRedirects;
        return $this;
    }
    
    /**
     * 设置UserAgent
     * @param string $userAgent UserAgent
     * @return $this
     */
    public function setUserAgent(string $userAgent)
    {
        $this->userAgent = $userAgent;
        return $this;
    }
    
    /**
     * 设置请求头
     * @param array $headers 请求头
     * @return $this
     */
    public function setHeaders(array $headers)
    {
        $this->headers = $headers;
        return $this;
    }
    
    /**
     * 添加请求头
     * @param string $name 请求头名称
     * @param string $value 请求头值
     * @return $this
     */
    public function addHeader(string $name, string $value)
    {
        $this->headers[$name] = $value;
        return $this;
    }
    
    /**
     * 设置Cookie
     * @param string $cookie Cookie
     * @return $this
     */
    public function setCookie(string $cookie)
    {
        $this->cookie = $cookie;
        return $this;
    }
    
    /**
     * 设置SSL验证
     * @param bool $verify 是否验证
     * @return $this
     */
    public function setSslVerify(bool $verify)
    {
        $this->sslVerify = $verify;
        return $this;
    }
    
    /**
     * 设置代理
     * @param string $proxy 代理地址
     * @return $this
     */
    public function setProxy(string $proxy)
    {
        $this->proxy = $proxy;
        return $this;
    }
    
    /**
     * 发送GET请求
     * @param string $url URL地址
     * @param array $params 请求参数
     * @param array $options 请求选项
     * @return string|false
     */
    public function get(string $url, array $params = [], array $options = [])
    {
        // 构建URL参数
        if (!empty($params)) {
            $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
        }
        
        return $this->request('GET', $url, [], $options);
    }
    
    /**
     * 发送POST请求
     * @param string $url URL地址
     * @param array|string $data 请求数据
     * @param array $options 请求选项
     * @return string|false
     */
    public function post(string $url, $data = [], array $options = [])
    {
        return $this->request('POST', $url, $data, $options);
    }
    
    /**
     * 发送JSON POST请求
     * @param string $url URL地址
     * @param array $data 请求数据
     * @param array $options 请求选项
     * @return string|false
     */
    public function postJson(string $url, array $data = [], array $options = [])
    {
        $this->addHeader('Content-Type', 'application/json');
        
        if (is_array($data)) {
            $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        
        return $this->request('POST', $url, $data, $options);
    }
    
    /**
     * 上传文件
     * @param string $url URL地址
     * @param array $files 文件数组，格式：['file' => '/path/to/file']
     * @param array $data 附加数据
     * @param array $options 请求选项
     * @return string|false
     */
    public function upload(string $url, array $files, array $data = [], array $options = [])
    {
        $boundary = uniqid('----WebKitFormBoundary');
        $this->addHeader('Content-Type', 'multipart/form-data; boundary=' . $boundary);
        
        $body = '';
        
        // 处理附加数据
        foreach ($data as $name => $value) {
            $body .= "--{$boundary}\r\n";
            $body .= "Content-Disposition: form-data; name=\"{$name}\"\r\n\r\n";
            $body .= "{$value}\r\n";
        }
        
        // 处理文件
        foreach ($files as $name => $path) {
            $filename = basename($path);
            $mime = mime_content_type($path) ?: 'application/octet-stream';
            $content = file_get_contents($path);
            
            $body .= "--{$boundary}\r\n";
            $body .= "Content-Disposition: form-data; name=\"{$name}\"; filename=\"{$filename}\"\r\n";
            $body .= "Content-Type: {$mime}\r\n\r\n";
            $body .= "{$content}\r\n";
        }
        
        $body .= "--{$boundary}--\r\n";
        
        return $this->request('POST', $url, $body, $options);
    }
    
    /**
     * 发送PUT请求
     * @param string $url URL地址
     * @param array|string $data 请求数据
     * @param array $options 请求选项
     * @return string|false
     */
    public function put(string $url, $data = [], array $options = [])
    {
        return $this->request('PUT', $url, $data, $options);
    }
    
    /**
     * 发送DELETE请求
     * @param string $url URL地址
     * @param array $params 请求参数
     * @param array $options 请求选项
     * @return string|false
     */
    public function delete(string $url, array $params = [], array $options = [])
    {
        // 构建URL参数
        if (!empty($params)) {
            $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
        }
        
        return $this->request('DELETE', $url, [], $options);
    }
    
    /**
     * 发送请求
     * @param string $method 请求方法
     * @param string $url URL地址
     * @param array|string $data 请求数据
     * @param array $options 请求选项
     * @return string|false
     */
    public function request(string $method, string $url, $data = [], array $options = [])
    {
        // 初始化CURL
        $ch = curl_init();
        
        // 请求方法
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));
        
        // URL
        curl_setopt($ch, CURLOPT_URL, $url);
        
        // 请求数据
        if (!empty($data)) {
            if (is_array($data)) {
                $data = http_build_query($data);
            }
            
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        
        // 请求头
        $headers = [];
        
        foreach ($this->headers as $name => $value) {
            $headers[] = "{$name}: {$value}";
        }
        
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        
        // 基本设置
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_USERAGENT, $this->userAgent);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, $this->maxRedirects);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // Cookie
        if (!empty($this->cookie)) {
            curl_setopt($ch, CURLOPT_COOKIE, $this->cookie);
        }
        
        // SSL验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->sslVerify);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->sslVerify ? 2 : 0);
        
        // 代理
        if (!empty($this->proxy)) {
            curl_setopt($ch, CURLOPT_PROXY, $this->proxy);
        }
        
        // 合并选项
        if (!empty($options)) {
            foreach ($options as $key => $value) {
                curl_setopt($ch, $key, $value);
            }
        }
        
        // 发送请求
        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return false;
        }
        
        return $response;
    }
    
    /**
     * 创建实例
     * @return static
     */
    public static function instance()
    {
        return new static();
    }
} 