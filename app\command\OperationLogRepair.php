<?php
declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\system\model\OperationLogModel;
use app\system\model\MenuModel;
use app\system\service\PermissionService;
use think\facade\Db;

/**
 * 操作日志历史数据修复命令
 */
class OperationLogRepair extends Command
{
    protected function configure()
    {
        $this->setName('operation-log:repair')
             ->setDescription('修复历史操作日志数据，补充菜单信息');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始修复历史操作日志数据...');
        
        $count = $this->repairHistoryData($output);
        
        $output->writeln("<info>✅ 成功修复 {$count} 条历史数据</info>");
        
        return 0;
    }
    
    /**
     * 修复历史数据
     *
     * @param Output $output 输出对象
     * @return int 修复的记录数
     */
    private function repairHistoryData(Output $output): int
    {
        $repairedCount = 0;
        
        try {
            // 查询需要修复的历史数据（缺少module、permission、menu_id、menu_title字段的记录）
            $logs = OperationLogModel::where(function($query) {
                $query->whereNull('module')
                      ->whereOr('module', '')
                      ->whereOr('permission', '')
                      ->whereOr('menu_id', 0);
            })->select();
            
            $output->writeln("找到 {$logs->count()} 条需要修复的记录");
            
            $permissionService = app(PermissionService::class);
            
            foreach ($logs as $log) {
                $output->write("修复记录 ID: {$log->id} ... ");
                
                try {
                    // 基于现有的 controller 和 action 字段推导权限标识
                    $controller = $log->controller;
                    $action = $log->action;
                    
                    if (empty($controller) || empty($action)) {
                        $output->writeln('<comment>跳过（缺少控制器或方法信息）</comment>');
                        continue;
                    }
                    
                    // 构造路由名称
                    $ruleName = $controller . '@' . $action;
                    
                    // 解析权限信息
                    [$module, $controllerName, $actionName] = $permissionService->parsePermissionInfo($ruleName);
                    
                    // 路由方法与权限标识映射
                    $actionMap = [
                        'detail' => 'show',
                        'add' => 'create',
                        'edit' => 'update',
                        'delete' => 'delete',
                        'batchDelete' => 'batchDelete',
                        'updateField' => 'updateField',
                        'status' => 'status',
                        'import' => 'import',
                        'importTemplate' => 'importTemplate',
                        'downloadTemplate' => 'downloadTemplate',
                        'export' => 'export',
                        'index' => 'index'
                    ];
                    
                    $mappedAction = $actionMap[$actionName] ?? $actionName;
                    $permission = strtolower("{$module}:{$controllerName}:{$mappedAction}");
                    
                    // 查找对应的菜单
                    $menu = MenuModel::where('name', $permission)
                                    ->where('status', 1)
                                    ->findOrEmpty();
                    
                    $menuId = $menu->isEmpty() ? 0 : $menu->id;
                    $menuTitle = $menu->isEmpty() ? '' : $menu->title;
                    
                    // 更新记录
                    $updateData = [
                        'module' => $module,
                        'permission' => $permission,
                        'menu_id' => $menuId,
                        'menu_title' => $menuTitle,
                    ];
                    
                    $result = $log->save($updateData);
                    
                    if ($result) {
                        $repairedCount++;
                        $output->writeln('<info>成功</info>');
                    } else {
                        $output->writeln('<error>失败</error>');
                    }
                    
                } catch (\Exception $e) {
                    $output->writeln("<error>错误: {$e->getMessage()}</error>");
                }
            }
            
        } catch (\Exception $e) {
            $output->writeln("<error>修复过程中发生错误: {$e->getMessage()}</error>");
        }
        
        return $repairedCount;
    }
}
