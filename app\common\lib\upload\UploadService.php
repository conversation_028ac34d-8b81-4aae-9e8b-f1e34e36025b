<?php

namespace app\common\lib\upload;

use app\system\service\AttachmentService;
use app\system\service\ConfigService;
use think\facade\Event;

class UploadService
{
	
	/**
	 * 获取上传Token
	 *
	 * @param string $storage  存储方式
	 * @param array  $params   参数
	 * @param int    $tenantId 租户ID
	 * @return array
	 * @throws \Exception
	 */
	public function getUploadToken(string $storage, int $tenantId,array $params = []): array
	{
		$driver = UploadStorageFactory::create($storage);
		$config = UploadStorageFactory::getConfig($storage, $tenantId);
		
		$result = $driver->getUploadToken($config, array_merge($params, ['tenant_id' => $tenantId]));
		
		return array_merge($result, [
			'storage'  => $storage,
			'deadline' => $config['deadline']
		]);
	}
	
	/**
	 * 上传文件
	 *
	 * @param array  $file     文件信息
	 * @param string $storage  存储方式
	 * @param int    $cateId   分类ID
	 * @param int    $tenantId 租户ID
	 * @return array
	 * @throws \Exception
	 */
	public function uploadFile(array $file, string $storage = 'local', int $cateId = 0, int $tenantId = 0): array
	{
		// 验证文件
		//		$this->validateFile($file);
		
		// 获取驱动和配置
		$driver = UploadStorageFactory::create($storage);
		//		$config = UploadStorageFactory::getConfig($storage, $tenantId);
		$config = [];
		// 上传文件
		$result = $driver->upload($file, $config);
		
		// 添加租户和分类信息
		$result['tenant_id'] = $tenantId;
		$result['cate_id']   = $cateId;
		
		// 保存到数据库
		$this->saveAttachment($result);
		
		// 触发上传完成事件
		Event::trigger('upload.after', $result);
		
		return $result;
	}
	
	/**
	 * 处理上传回调
	 *
	 * @param string $storage  存储方式
	 * @param array  $params   回调参数
	 * @param int    $tenantId 租户ID
	 * @return array
	 * @throws \Exception
	 */
	public function handleCallback(string $storage, array $params, int $tenantId = 0): array
	{
		$driver = UploadStorageFactory::create($storage);
		$config = UploadStorageFactory::getConfig($storage, $tenantId);
		
		$result = $driver->callback($params, $config);
		
		// 保存到数据库
		if (!empty($result)) {
			$this->saveAttachment($result);
			
			// 触发回调完成事件
			Event::trigger('upload.callback', $result);
		}
		
		return $result;
	}
	
	/**
	 * 删除文件
	 *
	 * @param int $id       附件ID
	 * @param int $tenantId 租户ID
	 * @return bool
	 * @throws \Exception
	 */
	public function deleteFile(int $id, int $tenantId = 0): bool
	{
		$attachmentService = AttachmentService::getInstance();
		
		$info = $attachmentService->getDetail($id, $tenantId);
		
		// 获取驱动和配置
		$driver = UploadStorageFactory::create($info['storage']);
		//		$config = UploadStorageFactory::getConfig($info['storage'], $tenantId);
		$config = [];
		// 删除文件
		$result = $driver->delete($info->getData('path'), $config);
		
		if ($result) {
			
			$result = $info->delete($id);
			
			// 触发删除完成事件
			Event::trigger('upload.delete', $info);
			
		}
		
		return $result;
	}
	
	/**
	 * 验证文件
	 *
	 * @param array $file 文件信息
	 * @throws \Exception
	 */
	private function validateFile(array $file): void
	{
		if (empty($file) || !isset($file['tmp_name']) || !file_exists($file['tmp_name'])) {
			throw new \Exception('文件不存在或已损坏');
		}
		
		// 验证文件大小
		$maxSize = Config::get('upload.max_size', 50 * 1024 * 1024); // 默认50MB
		if ($file['size'] > $maxSize) {
			throw new \Exception('文件大小超出限制');
		}
		
		// 验证文件类型
		$allowExt  = Config::get('upload.allow_ext', [
			'jpg',
			'jpeg',
			'png',
			'gif',
			'mp4',
			'zip',
			'pdf',
			'doc',
			'docx',
			'xls',
			'xlsx'
		]);
		$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
		if (!in_array(strtolower($extension), $allowExt)) {
			throw new \Exception('不支持的文件类型');
		}
	}
	
	/**
	 * 保存附件信息到数据库
	 *
	 * @param array $data 附件信息
	 * @return int
	 */
	private function saveAttachment(array $data): int
	{
		// 组装数据
		$insertData = [
			'cate_id'    => $data['cate_id'] ?? 0,
			'name'       => $data['name'],
			'real_name'  => $data['real_name'] ?? $data['name'],
			'path'       => $data['path'],
			'extension'  => $data['extension'],
			'size'       => $data['size'],
			'mime_type'  => $data['mime_type'],
			'storage'    => $data['storage'],
			'storage_id' => $data['storage_id'] ?? '',
			'creator_id' => request()->user['id'] ?? 0,
			'tenant_id'  => $data['tenant_id'] ?? 0,
		];
		
		return AttachmentService::getInstance()
		                        ->getModel()
		                        ->save($insertData);
	}
}