-- 工作流类型配置SQL
-- 为9个业务审批表配置workflow_type记录
-- 支持模式二通用页面集成对接
-- 创建时间：2025-07-28

-- 清理可能存在的重复记录
DELETE FROM workflow_type WHERE business_code IN (
    'ims_outbound_approval',
    'ims_inbound_approval', 
    'ims_shipment_approval',
    'ims_purchase_approval',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'hr_business_trip',
    'hr_outing',
    'office_sample_mail'
);

-- 插入9个业务类型的工作流配置
INSERT INTO workflow_type (name, module_code, business_code, status, creator_id, created_at, updated_at) VALUES

-- IMS模块（库存管理）
('出库申请审批', 'ims', 'ims_outbound_approval', 1, 1, NOW(), NOW()),
('入库申请审批', 'ims', 'ims_inbound_approval', 1, 1, NOW(), NOW()),
('出货申请审批', 'ims', 'ims_shipment_approval', 1, 1, NOW(), NOW()),
('采购申请审批', 'ims', 'ims_purchase_approval', 1, 1, NOW(), NOW()),

-- Finance模块（财务管理）
('付款申请审批', 'finance', 'finance_payment_approval', 1, 1, NOW(), NOW()),
('报销申请审批', 'finance', 'finance_expense_reimbursement', 1, 1, NOW(), NOW()),

-- HR模块（人力资源）
('出差申请审批', 'hr', 'hr_business_trip', 1, 1, NOW(), NOW()),
('外出申请审批', 'hr', 'hr_outing', 1, 1, NOW(), NOW()),

-- Office模块（办公管理）
('样品邮寄申请审批', 'office', 'office_sample_mail', 1, 1, NOW(), NOW());

-- 验证插入结果
SELECT 
    id,
    name,
    module_code,
    business_code,
    status,
    created_at
FROM workflow_type 
WHERE business_code IN (
    'ims_outbound_approval',
    'ims_inbound_approval', 
    'ims_shipment_approval',
    'ims_purchase_approval',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'hr_business_trip',
    'hr_outing',
    'office_sample_mail'
)
ORDER BY module_code, business_code;

-- DynamicWorkflowFactory映射规则说明：
-- business_code: ims_outbound_approval -> ImsOutboundApprovalService
-- business_code: finance_payment_approval -> FinancePaymentApprovalService  
-- business_code: hr_business_trip -> HrBusinessTripService
-- business_code: office_sample_mail -> OfficeSampleMailService

-- 映射算法：
-- 1. 从workflow_type表获取module_code和business_code
-- 2. 将business_code转换为PascalCase：ims_outbound_approval -> ImsOutboundApproval
-- 3. 构建Service类名：app\{module_code}\service\{BusinessName}Service
-- 4. 示例：app\ims\service\ImsOutboundApprovalService
