<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\system\model\SystemAdmin;

/**
 * 系统用户表服务类
 */
class SystemAdminService extends BaseService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new SystemAdmin();
    }
    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [
            // 在这里定义搜索字段配置
            // 例如：'username' => ['type' => 'like'],
        ];
    }
    
    /**
     * 获取验证规则
     * 
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            // 在这里定义验证规则
            // 例如：'username' => 'require|unique:system_admin',
        ];
        
        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('id', $ids)->delete();
    }
} 