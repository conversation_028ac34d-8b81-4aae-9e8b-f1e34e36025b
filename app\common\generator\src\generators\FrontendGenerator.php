<?php
declare(strict_types=1);

namespace app\common\generator\src\generators;

use app\common\generator\src\interfaces\GeneratorInterface;
use app\common\generator\src\engines\TemplateEngine;
use app\common\generator\src\mappers\ColumnComponentMapper;
use app\common\generator\src\mappers\FormComponentMapper;

/**
 * 前端代码生成器
 */
class FrontendGenerator implements GeneratorInterface
{
    /**
     * 模板引擎
     * @var TemplateEngine
     */
    protected TemplateEngine $templateEngine;
    
    /**
     * 配置信息
     * @var array
     */
    protected array $config;
    
    /**
     * 列组件映射器
     * @var ColumnComponentMapper
     */
    protected ColumnComponentMapper $columnMapper;
    
    /**
     * 表单组件映射器
     * @var FormComponentMapper
     */
    protected FormComponentMapper $formMapper;
    
    /**
     * 构造函数
     *
     * @param TemplateEngine $templateEngine 模板引擎
     * @param array $config 配置信息
     */
    public function __construct(TemplateEngine $templateEngine, array $config)
    {
        $this->templateEngine = clone $templateEngine;
        $this->config = $config;
        
        // 设置模板路径
        $this->templateEngine->setTemplatePath(
            app()->getRootPath() . 'app/common/generator/templates/frontend/'
        );
        
        // 初始化列组件映射器
        $this->columnMapper = new ColumnComponentMapper($config['column_mapping'] ?? []);
        
        // 初始化表单组件映射器
        $this->formMapper = new FormComponentMapper($config['form_mapping'] ?? []);
    }
    
    /**
     * 生成前端代码
     *
     * @param array $tableInfo 表结构信息
     * @param array $options 生成选项
     * @return array 生成结果
     */
    public function generate(mixed $tableInfo, array $options = []): array
    {
        // 检查参数类型
        if (!is_array($tableInfo)) {
            throw new \InvalidArgumentException('表结构信息必须是数组类型');
        }
        
        // 生成的文件列表
        $files = [];
        
        // 获取模块名称
        $module = $options['module'] ?? $this->getModuleFromTableComment($tableInfo);
        
        // 准备基础数据
        $entityName = $tableInfo['entity_name'];
        $tableName = $tableInfo['table_name_without_prefix'];
        
        // 生成API文件
        $apiFile = $this->generateApiFile($tableInfo, $module, $options);
        if ($apiFile) {
            $files[] = $apiFile;
        }
        
        // 生成列表页面
        $listFile = $this->generateListFile($tableInfo, $module, $options);
        if ($listFile) {
            $files[] = $listFile;
        }
        
        // 检查是否使用抽屉组件 - 默认使用对话框
        $useDrawer = false;
        if (isset($tableInfo['parsed_comment']['tags']['form'])) {
            $useDrawer = $tableInfo['parsed_comment']['tags']['form'] === 'drawer';
        } elseif (isset($tableInfo['parsed_comment']['tags']['drawer'])) {
            $useDrawer = true;
        }

        // 根据配置只生成一个表单组件
        if ($useDrawer) {
            // 生成抽屉表单组件
            $formFile = $this->generateFormDrawer($tableInfo, $module, $options);
            if ($formFile) {
                $files[] = $formFile;
            }
        } else {
            // 生成对话框表单组件
            $formDialog = $this->generateFormDialog($tableInfo, $module, $options);
            if ($formDialog) {
                $files[] = $formDialog;
            }
        }

        // 生成导入导出对话框（如果需要）
        if ($this->hasImportSupport($tableInfo) || $this->hasExportSupport($tableInfo)) {
            $importExportDialog = $this->generateImportExportDialog($tableInfo, $module, $options);
            if ($importExportDialog) {
                $files[] = $importExportDialog;
            }
        }

        return $files;
    }
    
    /**
     * 从表注释中获取模块名称
     *
     * @param array $tableInfo 表结构信息
     * @return string 模块名称
     */
    protected function getModuleFromTableComment(array $tableInfo): string
    {
        if (isset($tableInfo['parsed_comment']['tags']['module'])) {
            return $tableInfo['parsed_comment']['tags']['module'];
        }
        
        // 默认模块名称
        return 'system';
    }
    
    /**
     * 生成API文件
     *
     * @param array $tableInfo 表结构信息
     * @param string $module 模块名称
     * @param array $options 生成选项
     * @return array|null 生成的文件信息
     */
    protected function generateApiFile(array $tableInfo, string $module, array $options = []): ?array
    {
        // 获取API文件名 - 使用驼峰命名
        $apiName = $this->convertToCamelCase($tableInfo['table_name_without_prefix']);

        // 生成文件路径
        $frontendRoot = $this->getFrontendRoot();
        $apiDir = $frontendRoot . '/src/api/' . strtolower($module);
        $filePath = $apiDir . '/' . $apiName . '.ts';
        
        // 创建目录
        $this->makeDirIfNotExists($apiDir);
        
        // 检查文件是否存在
        if (file_exists($filePath) && empty($options['overwrite'])) {
            // 文件存在且不允许覆盖
            return [
                'type' => 'api',
                'path' => $filePath,
                'content' => '文件已存在，跳过生成',
                'skipped' => true
            ];
        }
        
        // 准备模板数据
        $data = [
            'moduleName' => strtolower($module),
            'EntityName' => $tableInfo['entity_name'],
            'entityName' => $this->convertToCamelCase($tableInfo['table_name_without_prefix']),
            'tableName' => $tableInfo['table_name_without_prefix'],
            'apiPath' => strtolower(preg_replace('/(?<!^)[A-Z]/', '-$0', $tableInfo['table_name_without_prefix'])),
            'comment' => $tableInfo['parsed_comment']['title'],
            'hasStatusField' => $this->hasStatusField($tableInfo['columns']),
            'hasExport' => $this->hasExportSupport($tableInfo),
            'hasImport' => $this->hasImportSupport($tableInfo)
        ];
        
        // 渲染模板
        $content = $this->templateEngine->render('api', $data);
        
        // 写入文件
        if (file_put_contents($filePath, $content)) {
            return [
                'type' => 'api',
                'path' => $filePath,
                'content' => $content
            ];
        }
        
        return null;
    }
    
    /**
     * 生成列表页面
     *
     * @param array $tableInfo 表结构信息
     * @param string $module 模块名称
     * @param array $options 生成选项
     * @return array|null 生成的文件信息
     */
    protected function generateListFile(array $tableInfo, string $module, array $options = []): ?array
    {
        // 生成表格列配置数组
        $tableColumnsArray = $this->generateTableColumnsArray($tableInfo['columns'], $tableInfo);

        // 生成自定义列模板
        $customColumnsData = $this->generateCustomColumnsTemplate($tableInfo['columns']);

        // 处理自定义列模板
        $specialColumnsItems = [];
        if (!empty($customColumnsData['columns'])) {
            foreach ($customColumnsData['columns'] as $item) {
                $specialColumnsItems[] = $item;
            }
        }

        // 生成搜索表单项
        $searchFormItems = $this->generateSearchFormItems($tableInfo['columns']);

        // 如果没有生成表格列，至少添加一个ID列
        if (empty($tableColumnsArray)) {
            $tableColumnsArray = [['prop' => 'id', 'label' => 'ID', 'width' => 80]];
        }

        // 检查是否使用抽屉组件 - 默认使用对话框
        $useDrawer = false;
        if (isset($tableInfo['parsed_comment']['tags']['form'])) {
            $useDrawer = $tableInfo['parsed_comment']['tags']['form'] === 'drawer';
        } elseif (isset($tableInfo['parsed_comment']['tags']['drawer'])) {
            $useDrawer = true;
        }

        // 生成详情项
        $detailItems = $this->generateDetailItems($tableInfo['columns']);

        // 准备模板数据
        $data = [
            'moduleName' => strtolower($module),
            'EntityName' => $tableInfo['entity_name'],
            'entityName' => $this->convertToCamelCase($tableInfo['table_name_without_prefix']),
            'entityTitle' => $tableInfo['parsed_comment']['title'],
            'comment' => $tableInfo['parsed_comment']['title'],
            'tableColumns' => $tableColumnsArray,
            'specialColumns' => $specialColumnsItems,
            'searchItems' => $searchFormItems,
            'detailItems' => $detailItems,
            'hasStatusField' => $this->hasStatusField($tableInfo['columns']),
            'hasExport' => $this->hasExportSupport($tableInfo),
            'hasImport' => $this->hasImportSupport($tableInfo),
            'hasBatchDelete' => $this->hasBatchDeleteSupport($tableInfo),
            // 添加更多模板变量
            'importColumns' => true,
            'columns' => 'LongTextColumn, TagColumn, SwitchColumn, ImageColumn, DocumentColumn, LinkColumn, CurrencyColumn',
            'searchFields' => [],
            'hasStatusSwitch' => $this->hasStatusField($tableInfo['columns']),
            'useDrawer' => $useDrawer
        ];
        
        // 渲染模板
        $content = $this->templateEngine->render('list_vue', $data);
        
        // 生成文件路径
        $frontendRoot = $this->getFrontendRoot();
        $viewDir = $frontendRoot . '/src/views/' . strtolower($module) . '/' . strtolower($tableInfo['table_name_without_prefix']);
        $filePath = $viewDir . '/list.vue';
        
        // 创建目录
        $this->makeDirIfNotExists(dirname($filePath));
        
        // 检查文件是否存在
        if (file_exists($filePath) && empty($options['overwrite'])) {
            // 文件存在且不允许覆盖
            return [
                'type' => 'list_vue',
                'path' => $filePath,
                'content' => '文件已存在，跳过生成',
                'skipped' => true
            ];
        }
        
        // 写入文件
        if (file_put_contents($filePath, $content)) {
            return [
                'type' => 'list_vue',
                'path' => $filePath,
                'content' => $content
            ];
        }
        
        return null;
    }
    
    /**
     * 生成表单抽屉
     *
     * @param array $tableInfo 表结构信息
     * @param string $module 模块名称
     * @param array $options 生成选项
     * @return array|null 生成的文件信息
     */
    protected function generateFormDrawer(array $tableInfo, string $module, array $options = []): ?array
    {
        // 获取表单项相关数据
        $formItemsResult = $this->generateFormItems($tableInfo['columns']);
        $formItems = $formItemsResult['items'];
        
        // 生成表单默认数据
        $defaultFormData = $this->generateDefaultFormData($tableInfo['columns']);
        
        // 生成表单验证规则
        $formRules = $this->generateFormRules($tableInfo['columns']);
        
        // 生成表单HTML
        $formItemsHtml = '';
        foreach ($formItems as $item) {
            $formItemsHtml .= $this->generateFormItemHtml($item) . "\n";
        }
        
        // 准备模板数据
        $data = [
            'moduleName' => strtolower($module),
            'EntityName' => $tableInfo['entity_name'],
            'entityName' => $this->convertToCamelCase($tableInfo['table_name_without_prefix']),
            'entityTitle' => $tableInfo['parsed_comment']['title'],
            'customImports' => $formItemsResult['customImports'] ?? '',
            'defaultFormData' => $defaultFormData,
            'formRules' => $formRules,
            'formItems' => $formItemsHtml
        ];
        
        // 渲染模板
        $content = $this->templateEngine->render('form_drawer', $data);
        
        // 生成文件路径
        $frontendRoot = $this->getFrontendRoot();
        $viewDir = $frontendRoot . '/src/views/' . strtolower($module) . '/' . strtolower($tableInfo['table_name_without_prefix']);
        $filePath = $viewDir . '/form-drawer.vue';
        
        // 创建目录
        $this->makeDirIfNotExists(dirname($filePath));
        
        // 检查文件是否存在
        if (file_exists($filePath) && empty($options['overwrite'])) {
            // 文件存在且不允许覆盖
            return [
                'type' => 'form_drawer',
                'path' => $filePath,
                'content' => '文件已存在，跳过生成',
                'skipped' => true
            ];
        }
        
        // 修复formData格式
        $content = preg_replace('/formData = reactive\({\n{/', 'formData = reactive({', $content);
        $content = preg_replace('/Object\.assign\(formData, {\n{/', 'Object.assign(formData, {', $content);
        $content = preg_replace('/}\n}\)/', '})', $content);
        
        // 写入文件
        if (file_put_contents($filePath, $content)) {
            return [
                'type' => 'form_drawer',
                'path' => $filePath,
                'content' => $content
            ];
        }
        
        return null;
    }
    
    /**
     * 生成表单助手文件
     *
     * @param array $tableInfo 表结构信息
     * @param string $module 模块名称
     * @param array $options 生成选项
     * @return array|null 生成的文件信息
     */
    protected function generateFormHelperFile(array $tableInfo, string $module, array $options = []): ?array
    {
        // 生成表单校验规则
        $formRules = $this->generateFormRules($tableInfo['columns']);
        
        // 生成默认表单数据
        $defaultFormData = $this->generateDefaultFormData($tableInfo['columns']);
        
        // 检查是否使用抽屉组件 - 默认使用对话框
        $useDrawer = false;
        if (isset($tableInfo['parsed_comment']['tags']['form'])) {
            $useDrawer = $tableInfo['parsed_comment']['tags']['form'] === 'drawer';
        } elseif (isset($tableInfo['parsed_comment']['tags']['drawer'])) {
            $useDrawer = true;
        }
        
        // 准备模板数据
        $data = [
            'moduleName' => strtolower($module),
            'EntityName' => $tableInfo['entity_name'],
            'entityName' => $this->convertToCamelCase($tableInfo['table_name_without_prefix']),
            'entityTitle' => $tableInfo['parsed_comment']['title'],
            'comment' => $tableInfo['parsed_comment']['title'],
            'formRules' => $formRules,
            'defaultFormData' => $defaultFormData,
            'useDrawer' => $useDrawer ? 'true' : 'false' // 使用字符串形式以便在模板中正确渲染
        ];
        
        // 渲染模板
        $content = $this->templateEngine->render('form_helper', $data);
        
        // 生成文件路径
        $frontendRoot = $this->getFrontendRoot();
        $viewDir = $frontendRoot . '/src/views/' . strtolower($module) . '/' . strtolower($tableInfo['table_name_without_prefix']);
        $filePath = $viewDir . '/form-helper.vue';
        
        // 创建目录
        $this->makeDirIfNotExists(dirname($filePath));
        
        // 检查文件是否存在
        if (file_exists($filePath) && empty($options['overwrite'])) {
            // 文件存在且不允许覆盖
            return [
                'type' => 'form_helper',
                'path' => $filePath,
                'content' => '文件已存在，跳过生成',
                'skipped' => true
            ];
        }
        
        // 写入文件
        if (file_put_contents($filePath, $content)) {
            return [
                'type' => 'form_helper',
                'path' => $filePath,
                'content' => $content
            ];
        }
        
        return null;
    }
    
    /**
     * 生成表单对话框
     *
     * @param array $tableInfo 表结构信息
     * @param string $module 模块名称
     * @param array $options 生成选项
     * @return array|null 生成的文件信息
     */
    protected function generateFormDialog(array $tableInfo, string $module, array $options = []): ?array
    {
        // 获取表单项相关数据
        $formItemsResult = $this->generateFormItems($tableInfo['columns']);
        $formItems = $formItemsResult['items'];
        
        // 生成表单默认数据
        $defaultFormData = $this->generateDefaultFormData($tableInfo['columns']);
        
        // 生成表单验证规则
        $formRules = $this->generateFormRules($tableInfo['columns']);
        
        // 生成表单HTML
        $formItemsHtml = '';
        foreach ($formItems as $item) {
            $formItemsHtml .= $this->generateFormItemHtml($item) . "\n";
        }

        // 生成数据转换逻辑
        $dataConversions = $this->generateDataConversions($tableInfo['columns']);
        $submitConversions = $this->generateSubmitConversions($tableInfo['columns']);

        // 准备模板数据
        $data = [
            'moduleName' => strtolower($module),
            'EntityName' => $tableInfo['entity_name'],
            'entityName' => $this->convertToCamelCase($tableInfo['table_name_without_prefix']),
            'entityTitle' => $tableInfo['parsed_comment']['title'],
            'customImports' => $formItemsResult['customImports'] ?? '',
            'defaultFormData' => $defaultFormData,
            'formRules' => $formRules,
            'formItems' => $formItemsHtml,
            'dataConversions' => $dataConversions,
            'submitConversions' => $submitConversions
        ];

        // 调试输出
        $logFile = app()->getRootPath() . 'runtime/log/template_debug.log';
        file_put_contents($logFile, date('Y-m-d H:i:s') . " customImports: " . json_encode($data['customImports']) . "\n", FILE_APPEND);
        
        // 渲染模板
        $content = $this->templateEngine->render('form_dialog', $data);
        
        // 生成文件路径
        $frontendRoot = $this->getFrontendRoot();
        $viewDir = $frontendRoot . '/src/views/' . strtolower($module) . '/' . strtolower($tableInfo['table_name_without_prefix']);
        $filePath = $viewDir . '/form-dialog.vue';
        
        // 创建目录
        $this->makeDirIfNotExists(dirname($filePath));
        
        // 检查文件是否存在
        if (file_exists($filePath) && empty($options['overwrite'])) {
            // 文件存在且不允许覆盖
            return [
                'type' => 'form_dialog',
                'path' => $filePath,
                'content' => '文件已存在，跳过生成',
                'skipped' => true
            ];
        }
        
        // 修复formData格式
        $pattern = '/\/\/ 表单数据\nconst formData = reactive\({(\s*)\}\)/s';
        $replacement = "// 表单数据\nconst formData = reactive({\n$defaultFormData\n})";
        $content = preg_replace($pattern, $replacement, $content);
        
        // 修复重置表单数据格式
        $pattern = '/\/\/ 重置表单数据\n\s+Object\.assign\(formData, {(\s*)\}\)/s';
        $replacement = "// 重置表单数据\n  Object.assign(formData, {\n$defaultFormData\n  })";
        $content = preg_replace($pattern, $replacement, $content);
        
        // 修复rules格式
        $pattern = '/\/\/ 表单验证规则\nconst rules = {(.+?)}/s';
        $replacement = "// 表单验证规则\nconst rules = $formRules";
        $content = preg_replace($pattern, $replacement, $content);
        
        // 修复formItems格式
        $pattern = '/<ElForm(.+?)>(.+?)<\/ElForm>/s';
        $replacement = "<ElForm$1>\n      $formItemsHtml\n    </ElForm>";
        $content = preg_replace($pattern, $replacement, $content);
        
        // 写入文件
        if (file_put_contents($filePath, $content)) {
            return [
                'type' => 'form_dialog',
                'path' => $filePath,
                'content' => $content
            ];
        }
        
        return null;
    }
    
    /**
     * 生成表格列配置数组
     *
     * @param array $columns 字段信息
     * @param array $tableInfo 表信息
     * @return array 表格列配置数组
     */
    protected function generateTableColumnsArray(array $columns, array $tableInfo): array
    {
        $columnItems = [];

        // 调试输出
        file_put_contents(app()->getRootPath() . 'runtime/columns_debug.txt', json_encode($columns, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        foreach ($columns as $column) {
            // 跳过隐藏字段
            if ($this->isHiddenField($column)) {
                continue;
            }

            $field = $column['Field'];
            $label = $column['parsed_comment']['description'] ?: $field;

            // 基本列配置
            $columnConfig = [
                'prop' => $field,
                'label' => $label
            ];

            // 根据字段类型设置宽度
            $typeInfo = $this->parseFieldType($column['Type']);
            if (in_array($field, ['id'])) {
                $columnConfig['width'] = 80;
            } elseif (in_array($typeInfo['type'], ['datetime', 'timestamp'])) {
                $columnConfig['width'] = 180;
            } elseif (in_array($typeInfo['type'], ['date'])) {
                $columnConfig['width'] = 120;
            } elseif (in_array($field, ['status', 'type', 'level', 'priority'])) {
                $columnConfig['width'] = 100;
            }

            // 添加表格列组件配置
            $this->addTableColumnComponent($columnConfig, $column, $field, $typeInfo, $tableInfo);

            $columnItems[] = $columnConfig;
        }

        return $columnItems;
    }

    /**
     * 生成表格列配置
     *
     * @param array $columns 字段信息
     * @return string 表格列配置代码
     */
    protected function generateTableColumns(array $columns): string
    {
        $columnItems = [];

        // 调试输出
        file_put_contents(app()->getRootPath() . 'runtime/columns_debug.txt', json_encode($columns, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        foreach ($columns as $column) {
            // 跳过隐藏字段
            if ($this->isHiddenField($column)) {
                continue;
            }

            $field = $column['Field'];
            $label = $column['parsed_comment']['description'] ?: $field;

            // 获取字段类型
            $typeInfo = $this->parseFieldType($column['Type']);

            // 使用列组件映射器获取合适的组件
            $componentInfo = $this->columnMapper->getColumnComponent($column);

            if ($componentInfo) {
                $columnItems[] = $this->generateColumnComponentCode($field, $label, $componentInfo);
            } else {
                // 默认列配置
                $columnItems[] = "{ prop: '{$field}', label: '{$label}' }";
            }
        }

        return implode(",\n  ", $columnItems);
    }
    
    /**
     * 生成列组件代码
     *
     * @param string $field 字段名
     * @param string $label 标签
     * @param array $componentInfo 组件信息
     * @return string 组件代码
     */
    protected function generateColumnComponentCode(string $field, string $label, array $componentInfo): string
    {
        $component = $componentInfo['component'];
        $props = $componentInfo['props'] ?? [];
        
        // 创建组件对象，而不是XML标签
        $propsArray = [];
        
        // 基本属性
        $propsArray[] = "prop: '{$field}'";
        $propsArray[] = "label: '{$label}'";
        
        // 添加其他属性
        foreach ($props as $key => $value) {
            if (is_bool($value)) {
                $propsArray[] = "{$key}: " . ($value ? 'true' : 'false');
            } elseif (is_numeric($value)) {
                $propsArray[] = "{$key}: {$value}";
            } elseif (is_string($value)) {
                $propsArray[] = "{$key}: '{$value}'";
            } elseif (is_array($value)) {
                $jsonValue = json_encode($value, JSON_UNESCAPED_UNICODE);
                $propsArray[] = "{$key}: {$jsonValue}";
            }
        }
        
        // 返回组件对象形式，修复type属性
        // Element Plus表格列类型只支持 'index' | 'selection' | 'expand' 或 undefined
        // 自定义组件不应该设置type属性，而是使用其他方式传递组件类型
        if ($component !== 'index' && $component !== 'selection' && $component !== 'expand') {
            return "{ " . implode(', ', $propsArray) . ", component: '{$component}' }";
        } else {
            return "{ type: '{$component}', " . implode(', ', $propsArray) . " }";
        }
    }
    
    /**
     * 生成自定义列组件模板
     *
     * @param array $columns 字段信息
     * @return array 组件模板数据
     */
    protected function generateCustomColumnsTemplate(array $columns): array
    {
        $templateCode = [];
        $columnImports = [];
        
        // 组件映射
        $components = [
            'switch' => 'SwitchColumn',
            'status' => 'TagColumn',
            'tag' => 'TagColumn',
            'image' => 'ImageColumn',
            'document' => 'DocumentColumn',
            'currency' => 'CurrencyColumn',
            'link' => 'LinkColumn',
            'copyable' => 'CopyableColumn',
            'long_text' => 'LongTextColumn',
            'progress' => 'ProgressColumn',
            'qrcode' => 'QrcodeColumn',
            'editable' => 'EditableColumn'
        ];
        
        foreach ($columns as $column) {
            // 跳过隐藏字段
            if ($this->isHiddenField($column)) {
                continue;
            }
            
            $field = $column['Field'];
            $label = $column['parsed_comment']['description'] ?: $field;
            
            // 获取组件类型
            $componentInfo = $this->columnMapper->getColumnComponent($column);
            
            if ($componentInfo && !empty($componentInfo['isDirectComponent'])) {
                // 直接使用组件的情况
                $component = $componentInfo['component'];
                $props = $componentInfo['props'] ?? [];
                
                // 添加到导入列表
                $componentName = $component;
                if (!in_array($componentName, $columnImports)) {
                    $columnImports[] = $componentName;
                }
                
                // 构建组件项
                $item = [
                    'component' => $component,
                    'prop' => $field,
                    'label' => $label
                ];
                
                // 特殊处理某些组件
                if ($component === 'SwitchColumn') {
                    // 添加开关组件的特殊属性
                    $item['activeValue'] = "1";
                    $item['inactiveValue'] = "0";
                    $item['updateApi'] = true;
                } 
                else if ($component === 'TagColumn') {
                    // 添加标签组件的特殊属性
                    $componentProps = [];
                    if (!empty($column['parsed_comment']['options'])) {
                        $options = [];
                        $typeMap = ['1' => 'danger', '2' => 'success', '3' => 'warning', '4' => 'primary'];
                        foreach ($column['parsed_comment']['options'] as $value => $label) {
                            $options[] = [
                                'value' => (string)$value,
                                'label' => $label,
                                'type' => $typeMap[$value] ?? 'primary'
                            ];
                        }
                        $componentProps['options'] = $options;
                    }
                    if (!empty($componentProps)) {
                        $item['componentProps'] = $componentProps;
                    }
                }
                else if ($component === 'LongTextColumn') {
                    $item['maxLines'] = "3";
                    $item['tooltip'] = "true";
                } 
                else if ($component === 'ImageColumn') {
                    $item['width'] = "60";
                    $item['height'] = "60";
                    $item['previewable'] = "true";
                } 
                else if ($component === 'LinkColumn') {
                    $item['type'] = "primary";
                }
                else if ($component === 'CurrencyColumn') {
                    $item['currency'] = "CNY";
                    $item['digits'] = "2";
                }
                else if ($component === 'EditableColumn') {
                    $item['type'] = "text";
                    $item['updateApi'] = true;
                }
                
                $templateCode[] = $item;
            }
        }
        
        // 调试输出特殊列
        file_put_contents(app()->getRootPath() . 'runtime/special_columns.json', json_encode($templateCode, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        file_put_contents(app()->getRootPath() . 'runtime/column_imports.json', json_encode($columnImports, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        return [
            'imports' => $columnImports,
            'columns' => $templateCode
        ];
    }
    
    /**
     * 生成搜索表单项
     *
     * @param array $columns 字段信息
     * @return string 搜索表单项代码
     */
    protected function generateSearchFormItems(array $columns): string
    {
        $formItems = [];
        
        foreach ($columns as $column) {
            // 检查是否是搜索字段
            if (!$this->isSearchField($column)) {
                continue;
            }
            
            $field = $column['Field'];
            $label = $column['parsed_comment']['description'] ?: $field;
            
            // 获取搜索类型
            $searchType = $this->getSearchType($column);
            
            // 日志一下检测到的搜索字段
            file_put_contents(
                app()->getRootPath() . "runtime/search_field_debug_{$field}.json",
                json_encode([
                    'field' => $field,
                    'label' => $label,
                    'searchType' => $searchType,
                    'comment' => $column['Comment'],
                    'parsed_comment' => $column['parsed_comment'] ?? []
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            );
            
            // 生成表单项
            $formItem = $this->generateSearchFormItem($field, $label, $searchType, $column);
            if ($formItem) {
                $formItems[] = $formItem;
            }
        }
        
        // 如果没有搜索字段，返回空字符串
        if (empty($formItems)) {
            return '';
        }
        
        // 返回处理后的搜索表单项
        return implode(",\n", $formItems);
    }
    
    /**
     * 生成搜索表单项
     *
     * @param string $field 字段名
     * @param string $label 标签
     * @param string $searchType 搜索类型
     * @param array $column 字段信息
     * @return string 表单项代码
     */
    protected function generateSearchFormItem(string $field, string $label, string $searchType, array $column): string
    {
        $typeInfo = $this->parseFieldType($column['Type']);
        $formType = 'input';
        $placeholder = "请输入{$label}";
        $options = [];
        
        // 根据搜索类型和字段类型确定表单项类型
        switch ($searchType) {
            case 'eq':
                if (!empty($column['parsed_comment']['options'])) {
                    $formType = 'select';
                    $placeholder = "请选择{$label}";
                    foreach ($column['parsed_comment']['options'] as $value => $text) {
                        $options[] = "{ label: '{$text}', value: '{$value}' }";
                    }
                } elseif ($typeInfo['type'] === 'tinyint' && ($typeInfo['length'] == 1 || $field === 'status')) {
                    $formType = 'select';
                    $placeholder = "请选择{$label}";
                    // 添加常见状态选项
                    $options[] = "{ label: '启用', value: '1' }";
                    $options[] = "{ label: '禁用', value: '0' }";
                }
                break;
            case 'between':
                if (str_contains($typeInfo['type'], 'int') || $typeInfo['type'] === 'decimal') {
                    $formType = 'numberrange';
                    $placeholder = "请输入{$label}范围";
                } elseif ($typeInfo['type'] === 'date' || $typeInfo['type'] === 'datetime') {
                    $formType = 'daterange';
                    $placeholder = "请选择{$label}范围";
                }
                break;
            case 'date':
                $formType = 'date';
                $placeholder = "请选择{$label}";
                break;
            case 'datetime':
                $formType = 'daterange';
                $placeholder = "请选择{$label}范围";
                break;
            case 'like':
                $formType = 'input';
                $placeholder = "请输入{$label}关键词";
                break;
            default:
                $formType = 'input';
        }
        
        // 构建表单项
        $item = "  {\n";
        $item .= "    prop: '{$field}',\n";
        $item .= "    label: '{$label}',\n";
        $item .= "    type: '{$formType}',\n";
        
        // 添加配置
        $item .= "    config: {\n";
        $item .= "      clearable: true,\n";
        $item .= "      placeholder: '{$placeholder}'";
        
        // 添加日期选择器特有配置
        if ($formType === 'date' || $formType === 'datetime' || $formType === 'daterange') {
            $dateType = $formType === 'daterange' ? 'daterange' : ($formType === 'datetime' ? 'datetime' : 'date');
            $item .= ",\n      type: '{$dateType}'";
        }

        // 添加数字范围特有配置
        if ($formType === 'numberrange') {
            // 从规则中提取min和max值
            $minValue = null;
            $maxValue = null;
            if (isset($column['parsed_comment']['tags']['rule'])) {
                $rule = $column['parsed_comment']['tags']['rule'];
                if (preg_match('/min:(\d+(?:\.\d+)?)/', $rule, $matches)) {
                    $minValue = $matches[1];
                }
                if (preg_match('/max:(\d+(?:\.\d+)?)/', $rule, $matches)) {
                    $maxValue = $matches[1];
                }
            }

            if ($minValue !== null) {
                $item .= ",\n      min: {$minValue}";
            }
            if ($maxValue !== null) {
                $item .= ",\n      max: {$maxValue}";
            }

            // 添加占位符配置
            $item .= ",\n      minPlaceholder: '最小{$label}'";
            $item .= ",\n      maxPlaceholder: '最大{$label}'";
        }
        
        $item .= "\n    }";
        
        // 添加选项
        if (!empty($options)) {
            $item .= ",\n    options: () => [\n      ";
            $item .= implode(",\n      ", $options);
            $item .= "\n    ]";
        }
        
        // 添加onChange处理
        $item .= ",\n    onChange: handleFormChange";
        
        $item .= "\n  }";
        
        return $item;
    }
    
    /**
     * 生成表单项
     *
     * @param array $columns 表字段信息
     * @return array 表单项数据
     */
    protected function generateFormItems(array $columns): array
    {
        $formItems = [];
        $imports = [];
        
        foreach ($columns as $column) {
            // 调试输出 - 检查所有字段的可编辑性
            if ($column['Field'] === 'quantity' || $column['Field'] === 'sort' || $column['Field'] === 'order_num') {
                $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                $isEdit = $this->isEditField($column);
                file_put_contents($logFile, date('Y-m-d H:i:s') . " isEditField检查 - 字段: {$column['Field']}, 结果: " . ($isEdit ? 'true' : 'false') . "\n", FILE_APPEND);
            }

            // 跳过不需要在表单中显示的字段
            if (!$this->isEditField($column)) {
                continue;
            }

            $field = $column['Field'];
            $label = $column['parsed_comment']['description'] ?: $field;

            // 重置变量，避免循环间的变量污染
            $control = null;

            // 获取表单组件类型
            $formType = 'input';
            $componentProps = [];

            // 首先尝试使用FormComponentMapper获取组件类型
            $componentInfo = $this->formMapper->getFormComponent($column);

            // 调试输出
            if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count' || $column['Field'] === 'quantity' || $column['Field'] === 'sort' || $column['Field'] === 'order_num') {
                $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                file_put_contents($logFile, date('Y-m-d H:i:s') . " FrontendGenerator - 字段: {$column['Field']}, ComponentInfo: " . json_encode($componentInfo) . "\n", FILE_APPEND);
            }

            if ($componentInfo && isset($componentInfo['component'])) {
                // 将组件映射转换为表单类型
                $component = $componentInfo['component'];

                // 调试输出
                if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count' || $column['Field'] === 'quantity' || $column['Field'] === 'sort' || $column['Field'] === 'order_num') {
                    $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                    file_put_contents($logFile, date('Y-m-d H:i:s') . " Switch处理 - 字段: {$column['Field']}, 组件: {$component}\n", FILE_APPEND);
                }

                switch ($component) {
                    case 'el-input-number':
                        if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count' || $column['Field'] === 'quantity' || $column['Field'] === 'sort' || $column['Field'] === 'order_num') {
                            $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                            file_put_contents($logFile, date('Y-m-d H:i:s') . " 匹配到el-input-number - 字段: {$column['Field']}\n", FILE_APPEND);
                        }
                        $formType = 'number';
                        $imports[] = 'InputNumber';
                        break;
                    case 'el-switch':
                        $formType = 'switch';
                        $imports[] = 'Switch';
                        break;
                    case 'el-select':
                        $formType = 'select';
                        $imports[] = 'Select';
                        break;
                    case 'el-radio-group':
                        $formType = 'radio';
                        $imports[] = 'Radio';
                        break;
                    case 'el-checkbox-group':
                        $formType = 'checkbox';
                        $imports[] = 'Checkbox';
                        break;
                    case 'el-date-picker':
                        $formType = 'date-picker';
                        $componentProps = $componentInfo['props'] ?? [];
                        $imports[] = 'DatePicker';
                        break;
                    case 'el-time-picker':
                        $formType = 'time-picker';
                        $imports[] = 'TimePicker';
                        break;
                    case 'el-color-picker':
                        $formType = 'color-picker';
                        $imports[] = 'ColorPicker';
                        break;
                    case 'el-input':
                        if (isset($componentInfo['props']['type']) && $componentInfo['props']['type'] === 'textarea') {
                            $formType = 'textarea';
                        } else {
                            $formType = 'input';
                        }
                        $imports[] = 'Input';
                        break;
                    case 'rich-editor':
                        $formType = 'editor';
                        $imports[] = 'ArtWangEditor';
                        break;
                    default:
                        $formType = 'input';
                        $imports[] = 'Input';
                }
            }

            // 检查是否有@FormUploader注解
            if (isset($column['parsed_comment']['tags']['FormUploader'])) {
                $control = 'FormUploader';
                // 获取FormUploader的属性
                $fileType = $column['parsed_comment']['tags']['fileType'] ?? 'image';
                $limit = $column['parsed_comment']['tags']['limit'] ?? 1;
                $multiple = isset($column['parsed_comment']['tags']['multiple']) && $column['parsed_comment']['tags']['multiple'] === 'true';
                $componentProps['fileType'] = $fileType;
                $componentProps['limit'] = (int)$limit;
                if ($multiple) {
                    $componentProps['multiple'] = true;
                }
            }
            // 检查是否有@FormMediaSelector注解
            elseif (isset($column['parsed_comment']['tags']['FormMediaSelector'])) {
                $control = 'FormMediaSelector';
                // 获取FormMediaSelector的属性
                $mediaType = $column['parsed_comment']['tags']['mediaType'] ?? 'image';
                $maxCount = $column['parsed_comment']['tags']['maxCount'] ?? 1;
                $componentProps['mediaType'] = $mediaType;
                $componentProps['maxCount'] = (int)$maxCount;
            }
            // 从注释中获取表单类型
            elseif (isset($column['parsed_comment']['tags']['form'])) {
                $control = $column['parsed_comment']['tags']['form'];
                // 调试输出
                if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count') {
                    $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                    file_put_contents($logFile, date('Y-m-d H:i:s') . " 从@form注解获取control - 字段: {$column['Field']}, control: {$control}\n", FILE_APPEND);
                }
            } elseif (isset($column['parsed_comment']['tags']['control'])) {
                $control = $column['parsed_comment']['tags']['control'];
                // 调试输出
                if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count') {
                    $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                    file_put_contents($logFile, date('Y-m-d H:i:s') . " 从@control注解获取control - 字段: {$column['Field']}, control: {$control}\n", FILE_APPEND);
                }
            }

            // 调试输出
            if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count' || $column['Field'] === 'quantity' || $column['Field'] === 'sort' || $column['Field'] === 'order_num') {
                $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                $controlValue = isset($control) ? $control : 'null';
                file_put_contents($logFile, date('Y-m-d H:i:s') . " 第二个switch前 - 字段: {$column['Field']}, control: {$controlValue}\n", FILE_APPEND);
            }

            if (isset($control)) {
                // 调试输出
                if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count' || $column['Field'] === 'quantity' || $column['Field'] === 'sort' || $column['Field'] === 'order_num') {
                    $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                    file_put_contents($logFile, date('Y-m-d H:i:s') . " 进入第二个switch - 字段: {$column['Field']}, control: {$control}\n", FILE_APPEND);
                }
                switch ($control) {
                    case 'textarea':
                        $formType = 'textarea';
                        $imports[] = 'Input';
                        break;
                    case 'editor':
                        $formType = 'editor';
                        $imports[] = 'ArtWangEditor';
                        break;
                    case 'select':
                        $formType = 'select';
                        $imports[] = 'Select';
                        break;
                    case 'radio':
                        $formType = 'radio';
                        $imports[] = 'Radio';
                        break;
                    case 'checkbox':
                        $formType = 'checkbox';
                        $imports[] = 'Checkbox';
                        break;
                    case 'switch':
                        $formType = 'switch';
                        $imports[] = 'Switch';
                        break;
                    case 'date':
                        $formType = 'date-picker';
                        $componentProps['type'] = 'date';
                        $imports[] = 'DatePicker';
                        break;
                    case 'time':
                        $formType = 'date-picker';
                        $componentProps['type'] = 'datetime';
                        $imports[] = 'DatePicker';
                        break;
                    case 'datetime':
                        $formType = 'date-picker';
                        $componentProps['type'] = 'datetime';
                        $imports[] = 'DatePicker';
                        break;
                    case 'upload':
                        $formType = 'upload';
                        // 根据字段名自动判断文件类型
                        if (strpos($field, 'image') !== false) {
                            $componentProps['fileType'] = 'image';
                            $componentProps['useMediaSelector'] = true;
                        } elseif (strpos($field, 'video') !== false) {
                            $componentProps['fileType'] = 'video';
                            $componentProps['useMediaSelector'] = true;
                        } elseif (strpos($field, 'audio') !== false) {
                            $componentProps['fileType'] = 'audio';
                            $componentProps['useMediaSelector'] = true;
                        } else {
                            $componentProps['fileType'] = 'file';
                        }
                        $imports[] = 'FormUploader';
                        break;
                    case 'file':
                        $formType = 'upload';
                        $componentProps['fileType'] = 'file';
                        $imports[] = 'FormUploader';
                        break;
                    case 'uploads':
                        $formType = 'upload';
                        $componentProps['multiple'] = true;
                        // 根据字段名自动判断文件类型
                        if (strpos($field, 'image') !== false) {
                            $componentProps['fileType'] = 'image';
                            $componentProps['useMediaSelector'] = true;
                        } elseif (strpos($field, 'video') !== false) {
                            $componentProps['fileType'] = 'video';
                            $componentProps['useMediaSelector'] = true;
                        } elseif (strpos($field, 'audio') !== false) {
                            $componentProps['fileType'] = 'audio';
                            $componentProps['useMediaSelector'] = true;
                        } else {
                            $componentProps['fileType'] = 'file';
                        }
                        $imports[] = 'FormUploader';
                        break;
                    case 'files':
                        $formType = 'upload';
                        $componentProps['fileType'] = 'file';
                        $componentProps['multiple'] = true;
                        $imports[] = 'FormUploader';
                        break;
                    case 'color':
                        $formType = 'color-picker';
                        $imports[] = 'ColorPicker';
                        break;
                    case 'number':
                        $formType = 'number';
                        $imports[] = 'InputNumber';
                        break;
                    case 'FormUploader':
                        $formType = 'form-uploader';
                        $imports[] = 'FormUploader';
                        break;
                    case 'FormMediaSelector':
                        $formType = 'form-media-selector';
                        $imports[] = 'FormMediaSelector';
                        break;
                    default:
                        $formType = 'input';
                        $imports[] = 'Input';
                }
            }
            
            // 处理选项
            $options = [];
            if (!empty($column['parsed_comment']['options'])) {
                foreach ($column['parsed_comment']['options'] as $value => $text) {
                    $options[] = [
                        'label' => $text,
                        'value' => $value
                    ];
                }
            }
            
            // 调试输出
            if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count' || $column['Field'] === 'quantity' || $column['Field'] === 'sort' || $column['Field'] === 'order_num') {
                $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
                file_put_contents($logFile, date('Y-m-d H:i:s') . " 最终formType - 字段: {$column['Field']}, formType: {$formType}\n", FILE_APPEND);
            }

            // 生成表单项
            $item = [
                'label' => $label,
                'prop' => $field,
                'type' => $formType,
                'column' => $column  // 添加字段信息，用于获取正确的value-format
            ];


            
            // 添加组件属性
            if (!empty($componentProps)) {
                $item['props'] = $componentProps;
            }
            
            // 添加选项
            if (!empty($options)) {
                $item['options'] = $options;
            }
            
            $formItems[] = $item;
        }
        
        // 去重导入组件
        $imports = array_unique($imports);

        // 分离ElementPlus组件和自定义组件
        $elementPlusImports = [];
        $customImports = [];

        foreach ($imports as $import) {
            if (in_array($import, ['FormUploader', 'FormMediaSelector', 'ArtWangEditor'])) {
                $customImports[] = $import;
            } else {
                $elementPlusImports[] = $import;
            }
        }

        // 生成自定义组件导入语句（不包含ElementPlus）
        $customImportStatements = [];
        if (in_array('FormUploader', $customImports)) {
            $customImportStatements[] = "import FormUploader from '@/components/custom/FormUploader/index.vue'";
        }
        if (in_array('FormMediaSelector', $customImports)) {
            $customImportStatements[] = "import FormMediaSelector from '@/components/custom/FormMediaSelector/index.vue'";
        }
        if (in_array('ArtWangEditor', $customImports)) {
            $customImportStatements[] = "import ArtWangEditor from '@/components/core/forms/ArtWangEditor.vue'";
        }

        return [
            'items' => $formItems,
            'customImports' => implode("\n", $customImportStatements)
        ];
    }
    
    /**
     * 生成表单校验规则
     *
     * @param array $columns 字段信息
     * @return string 校验规则代码
     */
    protected function generateFormRules(array $columns): string
    {
        $rules = [];
        
        foreach ($columns as $column) {
            // 跳过不需要在表单中显示的字段
            if (!$this->isEditField($column)) {
                continue;
            }
            
            $field = $column['Field'];
            $label = $column['parsed_comment']['description'] ?: $field;
            $fieldRules = $this->generateFieldRules($field, $label, $column);
            
            if (!empty($fieldRules)) {
                $rules[$field] = $fieldRules;
            }
        }
        
        // 如果没有验证规则，返回空对象
        if (empty($rules)) {
            return '{}';
        }

        // 生成 JavaScript 对象格式的验证规则
        return $this->generateJavaScriptRules($rules);
    }

    /**
     * 生成 JavaScript 格式的验证规则
     *
     * @param array $rules 验证规则数组
     * @return string
     */
    protected function generateJavaScriptRules(array $rules): string
    {
        $jsRules = [];

        foreach ($rules as $field => $fieldRules) {
            $jsFieldRules = [];

            foreach ($fieldRules as $rule) {
                $jsRule = [];

                foreach ($rule as $key => $value) {
                    if ($key === 'validator') {
                        // validator 是函数，不需要引号
                        $jsRule[] = "        {$key}: {$value}";
                    } else {
                        // 其他属性需要 JSON 编码
                        $jsRule[] = "        {$key}: " . json_encode($value, JSON_UNESCAPED_UNICODE);
                    }
                }

                $jsFieldRules[] = "      {\n" . implode(",\n", $jsRule) . "\n      }";
            }

            $jsRules[] = "  {$field}: [\n" . implode(",\n", $jsFieldRules) . "\n  ]";
        }

        return "{\n" . implode(",\n", $jsRules) . "\n}";
    }

    /**
     * 生成详情项
     *
     * @param array $columns 表字段信息
     * @return array 详情项数据
     */
    protected function generateDetailItems(array $columns): array
    {
        $detailItems = [];

        foreach ($columns as $column) {
            // 跳过隐藏字段
            if ($this->isHiddenField($column)) {
                continue;
            }

            $field = $column['Field'];
            $label = $column['parsed_comment']['description'] ?: $field;

            // 判断字段类型
            $isImage = false;
            $isTag = false;
            $isStatus = false;
            $isSelect = false;
            $isRadio = false;
            $isColor = false;
            $isNormal = true;
            $tagTypeExpr = '';
            $tagLabelExpr = '';
            $selectOptionsExpr = '';
            $radioOptionsExpr = '';

            // 检查是否是图片字段
            if (strpos($field, 'image') !== false || strpos($field, 'avatar') !== false || strpos($field, 'photo') !== false) {
                $isImage = true;
                $isNormal = false;
            }
            // 检查是否是状态字段
            elseif ($field === 'status' || $field === 'is_enable' || $field === 'is_active') {
                $isStatus = true;
                $isNormal = false;
            }
            // 检查是否是颜色字段
            elseif ($field === 'color' || strpos($field, 'color') !== false) {
                $isColor = true;
                $isNormal = false;
            }
            // 检查是否是选择字段
            elseif (in_array($field, ['type', 'level', 'priority', 'category'])) {
                // 检查注释中是否有选项配置
                if (!empty($column['parsed_comment']['options'])) {
                    $isSelect = true;
                    $isNormal = false;
                    $options = $column['parsed_comment']['options'];
                    $optionsArray = [];
                    foreach ($options as $value => $text) {
                        $optionsArray[] = "'{$value}': '{$text}'";
                    }
                    $selectOptionsExpr = "{ " . implode(', ', $optionsArray) . " }";
                } else {
                    $isTag = true;
                    $isNormal = false;
                    $tagLabelExpr = "{{ detailData.{$field} || '-' }}";
                    $tagTypeExpr = "'primary'";
                }
            }
            // 检查是否是标签字段
            elseif ($field === 'tags' || strpos($field, 'tag') !== false) {
                $isTag = true;
                $isNormal = false;
                $tagLabelExpr = "{{ detailData.{$field} || '-' }}";
                $tagTypeExpr = "'primary'";
            }

            $detailItems[] = [
                'label' => $label,
                'prop' => $field,
                'isImage' => $isImage,
                'isTag' => $isTag,
                'isStatus' => $isStatus,
                'isSelect' => $isSelect,
                'isRadio' => $isRadio,
                'isColor' => $isColor,
                'isNormal' => $isNormal,
                'tagTypeExpr' => $tagTypeExpr,
                'tagLabelExpr' => $tagLabelExpr,
                'selectOptionsExpr' => $selectOptionsExpr,
                'radioOptionsExpr' => $radioOptionsExpr
            ];
        }

        return $detailItems;
    }

    /**
     * 生成字段验证规则
     *
     * @param string $field 字段名
     * @param string $label 标签
     * @param array $column 字段信息
     * @return array 验证规则
     */
    protected function generateFieldRules(string $field, string $label, array $column): array
    {
        $rules = [];
        $comment = $column['Comment'] ?? '';

        // 检查是否有 @required 标记
        if (isset($column['parsed_comment']['tags']['required']) ||
            strpos($comment, '@required') !== false) {

            // 根据字段类型确定触发方式
            $trigger = $this->getValidationTrigger($column);
            $message = $this->getRequiredMessage($field, $label);

            $rules[] = [
                'required' => true,
                'message' => $message,
                'trigger' => $trigger
            ];
        }

        // 检查是否有长度限制标记 @max:100
        if (preg_match('/@max:(\d+)/', $comment, $matches)) {
            $maxLength = (int)$matches[1];
            $rules[] = [
                'max' => $maxLength,
                'message' => "{$label}长度不能超过{$maxLength}个字符",
                'trigger' => 'blur'
            ];
        }

        // 检查是否有数字验证标记 @number
        if (strpos($comment, '@number') !== false) {
            $rules[] = [
                'validator' => "(rule: any, value: any, callback: any) => {
          if (value === '' || value === null || value === undefined) {
            callback(new Error('{$label}不能为空'))
          } else if (isNaN(Number(value))) {
            callback(new Error('{$label}必须是数字'))
          } else {
            callback()
          }
        }",
                'trigger' => 'blur'
            ];
        }

        // 检查是否有邮箱验证标记 @email
        if (strpos($comment, '@email') !== false) {
            $rules[] = [
                'type' => 'email',
                'message' => "请输入正确的邮箱地址",
                'trigger' => 'blur'
            ];
        }

        // 检查是否有URL验证标记 @url
        if (strpos($comment, '@url') !== false) {
            $rules[] = [
                'type' => 'url',
                'message' => "请输入正确的URL地址",
                'trigger' => 'blur'
            ];
        }

        return $rules;
    }

    /**
     * 获取验证触发方式
     *
     * @param array $column 字段信息
     * @return string
     */
    protected function getValidationTrigger(array $column): string
    {
        // 根据字段类型和表单组件类型确定触发方式
        $parsedComment = $column['parsed_comment'] ?? [];
        $tags = $parsedComment['tags'] ?? [];
        $formType = '';

        if (is_array($tags)) {
            $formType = $tags['form'] ?? '';
        }

        // 选择类组件使用 change 触发
        if (in_array($formType, ['select', 'radio', 'checkbox', 'switch', 'date-picker', 'time-picker'])) {
            return 'change';
        }

        // 默认使用 blur 触发
        return 'blur';
    }

    /**
     * 获取必填验证消息
     *
     * @param string $field 字段名
     * @param string $label 标签
     * @return string
     */
    protected function getRequiredMessage(string $field, string $label): string
    {
        // 根据字段名或类型生成合适的提示消息
        if (strpos($field, '_id') !== false || strpos($field, 'category') !== false) {
            return "请选择{$label}";
        }

        return "{$label}不能为空";
    }

    /**
     * 生成默认表单数据
     *
     * @param array $columns 字段信息
     * @return string 默认表单数据代码
     */
    protected function generateDefaultFormData(array $columns): string
    {
        $defaultData = [];
        
        foreach ($columns as $column) {
            // 跳过不需要在表单中显示的字段
            if (!$this->isEditField($column)) {
                continue;
            }
            
            $field = $column['Field'];
            $defaultValue = $this->getDefaultValue($column);
            
            $defaultData[] = "    \"{$field}\": {$defaultValue}";
        }
        
        // 返回JSON格式的默认数据
        return implode(",\n", $defaultData);
    }
    
    /**
     * 根据数据库字段类型获取日期组件的 value-format 格式
     *
     * @param array $column 字段信息
     * @return string 格式字符串
     */
    protected function getDateValueFormatByColumn(array $column): string
    {
        $type = $column['Type'];
        $typeInfo = $this->parseFieldType($type);

        switch ($typeInfo['type']) {
            case 'date':
                return 'YYYY-MM-DD';
            case 'time':
                return 'YYYY-MM-DD HH:mm:ss';
            case 'datetime':
            case 'timestamp':
                return 'YYYY-MM-DD HH:mm:ss';
            case 'year':
                return 'YYYY';
            default:
                return 'YYYY-MM-DD HH:mm:ss';
        }
    }

    /**
     * 获取日期组件的 value-format 格式
     *
     * @param string $dateType 日期类型
     * @return string 格式字符串
     */
    protected function getDateValueFormat(string $dateType): string
    {
        switch ($dateType) {
            case 'date':
                return 'YYYY-MM-DD';
            case 'time':
                return 'YYYY-MM-DD HH:mm:ss';
            case 'datetime':
                return 'YYYY-MM-DD HH:mm:ss';
            case 'year':
                return 'YYYY';
            case 'month':
                return 'YYYY-MM';
            case 'week':
                return 'YYYY-MM-DD';
            case 'daterange':
                return 'YYYY-MM-DD';
            case 'datetimerange':
                return 'YYYY-MM-DD HH:mm:ss';
            case 'monthrange':
                return 'YYYY-MM';
            case 'yearrange':
                return 'YYYY';
            default:
                return 'YYYY-MM-DD HH:mm:ss';
        }
    }

    /**
     * 获取字段默认值
     *
     * @param array $column 字段信息
     * @return string 默认值
     */
    protected function getDefaultValue(array $column): string
    {
        $field = $column['Field'];
        $type = $column['Type'];
        $typeInfo = $this->parseFieldType($type);

        // 检查字段注释中的控件类型
        $parsedComment = $column['parsed_comment'] ?? [];
        $tags = $parsedComment['tags'] ?? [];
        $formType = '';

        // 处理不同的数据结构
        if (is_array($tags)) {
            $formType = $tags['form'] ?? '';
        }

        // 对于特定控件类型，返回相应的默认值
        if ($formType === 'checkbox') {
            return '[]'; // checkbox 需要数组
        }

        // 设置默认值
        switch ($typeInfo['type']) {
            case 'tinyint':
                if ($field === 'status' || strpos($field, 'is_') === 0) {
                    return '1'; // 默认启用
                } else {
                    return '0';
                }
            case 'int':
            case 'bigint':
            case 'smallint':
            case 'mediumint':
                return '0';
            case 'decimal':
            case 'float':
            case 'double':
                return '0';
            case 'varchar':
            case 'char':
            case 'text':
            case 'longtext':
            case 'mediumtext':
                return '""';
            case 'date':
            case 'datetime':
            case 'timestamp':
                return '""';
            default:
                return '""';
        }
    }
    
    /**
     * 是否是隐藏字段
     *
     * @param array $column 字段信息
     * @return bool
     */
    protected function isHiddenField(array $column): bool
    {
        // 检查是否有隐藏标记
        if (isset($column['parsed_comment']['tags']['hide'])) {
            return true;
        }
        
        // 常见的隐藏字段
        $hiddenFields = ['password', 'salt', 'deleted_at'];
        if (in_array($column['Field'], $hiddenFields)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 是否是搜索字段
     *
     * @param array $column 字段信息
     * @return bool
     */
    protected function isSearchField(array $column): bool
    {
        // 输出调试信息到文件
        file_put_contents(
            app()->getRootPath() . "runtime/column_tags_debug_{$column['Field']}.json", 
            json_encode($column['parsed_comment'] ?? [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
        
        // 1. 检查字段注释中是否包含@search标记
        if (isset($column['parsed_comment']['tags']['search'])) {
            return true;
        }
        
        // 2. 检查字段注释原始文本是否包含@search:关键字
        if (isset($column['Comment']) && 
            preg_match('/@search:/', $column['Comment'])) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 是否是编辑字段
     *
     * @param array $column 字段信息
     * @return bool
     */
    protected function isEditField(array $column): bool
    {
        // 调试输出
        if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count') {
            $logFile = app()->getRootPath() . 'runtime/log/frontend_generator_debug.log';
            file_put_contents($logFile, date('Y-m-d H:i:s') . " isEditField - 字段: {$column['Field']}\n", FILE_APPEND);
        }

        // 检查是否有编辑标记
        if (isset($column['parsed_comment']['tags']['edit'])) {
            if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count') {
                error_log("isEditField - {$column['Field']} 有edit标记，返回true");
            }
            return true;
        }

        // 常见的不可编辑字段（包括ID字段）
        $nonEditableFields = ['id', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by'];
        if (in_array($column['Field'], $nonEditableFields)) {
            if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count') {
                error_log("isEditField - {$column['Field']} 在不可编辑字段列表中，返回false");
            }
            return false;
        }

        // 主键字段不可编辑
        if ($column['Key'] === 'PRI') {
            if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count') {
                error_log("isEditField - {$column['Field']} 是主键，返回false");
            }
            return false;
        }

        if ($column['Field'] === 'view_count' || $column['Field'] === 'like_count') {
            error_log("isEditField - {$column['Field']} 默认可编辑，返回true");
        }
        return true;
    }
    
    /**
     * 获取搜索类型
     *
     * @param array $column 字段信息
     * @return string 搜索类型
     */
    protected function getSearchType(array $column): string
    {
        if (!isset($column['parsed_comment']['tags']['search'])) {
            return 'eq';
        }
        
        $searchValue = $column['parsed_comment']['tags']['search'];
        if ($searchValue === true) {
            return 'eq';
        }
        
        return $searchValue;
    }
    
    /**
     * 解析字段类型
     *
     * @param string $type 字段类型
     * @return array 类型信息
     */
    protected function parseFieldType(string $type): array
    {
        $result = [
            'type' => '',
            'length' => null,
            'precision' => null,
            'scale' => null
        ];
        
        // 提取基本类型和参数
        if (preg_match('/^([a-z]+)(?:\(([^)]+)\))?/i', $type, $matches)) {
            $result['type'] = strtolower($matches[1]);
            
            // 如果有长度/精度参数
            if (isset($matches[2])) {
                // 检查是否有多个参数（如 decimal(10,2)）
                if (str_contains($matches[2], ',')) {
                    list($precision, $scale) = explode(',', $matches[2]);
                    $result['precision'] = (int) $precision;
                    $result['scale'] = (int) $scale;
                    $result['length'] = $result['precision'];
                } else {
                    $result['length'] = (int) $matches[2];
                }
            }
        }
        
        return $result;
    }
    
    /**
     * 表是否支持导出
     *
     * @param array $tableInfo 表结构信息
     * @return bool
     */
    protected function hasExportSupport(array $tableInfo): bool
    {
        return isset($tableInfo['parsed_comment']['tags']['exp']) && $tableInfo['parsed_comment']['tags']['exp'];
    }
    
    /**
     * 表是否支持导入
     *
     * @param array $tableInfo 表结构信息
     * @return bool
     */
    protected function hasImportSupport(array $tableInfo): bool
    {
        return isset($tableInfo['parsed_comment']['tags']['imp']) && $tableInfo['parsed_comment']['tags']['imp'];
    }

    /**
     * 检查是否支持批量删除功能
     *
     * @param array $tableInfo 表信息
     * @return bool
     */
    protected function hasBatchDeleteSupport(array $tableInfo): bool
    {
        return isset($tableInfo['parsed_comment']['tags']['batchdelete']) &&
               ($tableInfo['parsed_comment']['tags']['batchdelete'] === true ||
                $tableInfo['parsed_comment']['tags']['batchdelete'] === 'true' ||
                $tableInfo['parsed_comment']['tags']['batchdelete'] === 1 ||
                $tableInfo['parsed_comment']['tags']['batchdelete'] === '1');
    }
    
    /**
     * 转换为驼峰命名
     *
     * @param string $str 输入字符串
     * @return string 驼峰命名字符串
     */
    protected function convertToCamelCase(string $str): string
    {
        // 将下划线分隔的字符串转换为驼峰命名
        $parts = explode('_', $str);
        $camelCase = array_shift($parts); // 第一个部分保持小写

        foreach ($parts as $part) {
            $camelCase .= ucfirst(strtolower($part));
        }

        return $camelCase;
    }

    /**
     * 获取前端根目录
     *
     * @return string 前端根目录
     */
    protected function getFrontendRoot(): string
    {
        return app()->getRootPath() . 'frontend';
    }
    
    /**
     * 创建目录（如果不存在）
     *
     * @param string $dir 目录路径
     * @return bool
     */
    protected function makeDirIfNotExists(string $dir): bool
    {
        if (!is_dir($dir)) {
            return mkdir($dir, 0755, true);
        }
        return true;
    }
    
    /**
     * 生成自定义表单脚本
     *
     * @param array $columns 字段信息
     * @return string 表单脚本代码
     */
    protected function generateFormScript(array $columns): string
    {
        $apiMethods = [];
        $apiOptions = [];
        $relTableOptions = [];
        
        // 检查字段中是否有需要API选项或关联表选项的
        foreach ($columns as $column) {
            $field = $column['Field'];
            
            // 检查注释中是否有API选项标记
            if (isset($column['parsed_comment']['tags']['api'])) {
                $apiTag = $column['parsed_comment']['tags']['api'];
                if (is_string($apiTag) && !empty($apiTag)) {
                    // 解析API标记，格式: module/controller/method/labelField,valueField
                    $parts = explode('/', $apiTag);
                    if (count($parts) >= 4) {
                        $module = $parts[0];
                        $controller = $parts[1];
                        $method = $parts[2];
                        $fields = explode(',', $parts[3]);
                        $labelField = $fields[0] ?? 'name';
                        $valueField = $fields[1] ?? 'id';
                        
                        // 添加API方法
                        $apiMethods[] = "async fetch{$field}Options() {
    try {
        const res = await request.get({
            url: '/{$module}/{$controller}/{$method}'
        });
        if (res.code === 0) {
            this.{$field}Options = res.data.map(item => ({
                label: item.{$labelField},
                value: item.{$valueField}
            }));
        }
    } catch (error) {
        console.error('获取{$field}选项失败', error);
    }
}";
                        
                        // 添加API选项
                        $apiOptions[$field] = "{$field}Options: []";
                    }
                }
            }
            
            // 检查注释中是否有关联表选项标记
            if (isset($column['parsed_comment']['tags']['rel'])) {
                $relTag = $column['parsed_comment']['tags']['rel'];
                if (is_string($relTag) && !empty($relTag)) {
                    // 解析关联表标记，格式: table,labelField,valueField
                    $parts = explode(',', $relTag);
                    if (count($parts) >= 1) {
                        $table = $parts[0];
                        $labelField = $parts[1] ?? 'name';
                        $valueField = $parts[2] ?? 'id';
                        
                        // 添加关联表选项
                        $relTableOptions[$field] = "{$field}Options: [
    // 从 {$table} 表获取的选项将在这里
]";
                    }
                }
            }
        }
        
        // 组合脚本
        $script = '';
        
        // 添加API方法
        if (!empty($apiMethods)) {
            $script .= "// API方法\n" . implode("\n\n", $apiMethods) . "\n\n";
        }
        
        // 添加选项
        if (!empty($apiOptions) || !empty($relTableOptions)) {
            $script .= "// 选项数据\nconst options = reactive({\n  " . 
                implode(",\n  ", array_merge($apiOptions, $relTableOptions)) . 
                "\n});\n\n";
        }
        
        // 添加初始化代码
        if (!empty($apiOptions)) {
            $script .= "// 初始化选项数据\nonMounted(() => {\n  " . 
                implode("\n  ", array_map(function($field) {
                    return "fetch{$field}Options();";
                }, array_keys($apiOptions))) . 
                "\n});\n\n";
        }
        
        return $script;
    }
    
    /**
     * 将下划线命名转为驼峰命名
     *
     * @param string $str 输入字符串
     * @return string 驼峰命名字符串
     */
    protected function camelize(string $str): string
    {
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $str)));
    }
    
    /**
     * 检查是否有状态字段
     *
     * @param array $columns 表字段信息
     * @return bool
     */
    protected function hasStatusField(array $columns): bool
    {
        foreach ($columns as $column) {
            if ($column['Field'] === 'status') {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 格式化特殊列
     *
     * @param array $columns 特殊列信息
     * @return string 格式化后的特殊列字符串
     */
    protected function formatSpecialColumns(array $columns): string
    {
        if (empty($columns)) {
            return '';
        }
        
        $formattedColumns = [];
        foreach ($columns as $column) {
            if (isset($column['component'])) {
                $formattedColumns[] = "<{$column['component']} ";
                
                // 添加属性
                $attributes = [];
                foreach ($column as $key => $value) {
                    if ($key !== 'component' && $key !== 'comment') {
                        $attributes[] = $value;
                    }
                }
                
                $formattedColumns[count($formattedColumns) - 1] .= implode(' ', $attributes) . " />";
            }
        }
        
        return implode("\n            ", $formattedColumns);
    }
    
    /**
     * 生成表单项HTML
     *
     * @param array $item 表单项配置
     * @return string HTML代码
     */
    protected function generateFormItemHtml(array $item): string
    {
        $label = $item['label'];
        $prop = $item['prop'];
        $type = $item['type'];
        $props = $item['props'] ?? [];
        $options = $item['options'] ?? [];
        
        $html = "<ElFormItem label=\"{$label}\" prop=\"{$prop}\">\n";
        
        switch ($type) {
            case 'textarea':
                $html .= "  <ElInput v-model=\"formData.{$prop}\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入{$label}\" />\n";
                break;
            case 'editor':
                $html .= "  <ArtWangEditor v-model=\"formData.{$prop}\" :height=\"300\" />\n";
                break;
            case 'select':
                $html .= "  <ElSelect v-model=\"formData.{$prop}\" placeholder=\"请选择{$label}\" style=\"width: 100%\">\n";
                if (!empty($options)) {
                    foreach ($options as $option) {
                        $html .= "    <ElOption :label=\"'{$option['label']}'\" :value=\"{$option['value']}\" />\n";
                    }
                }
                $html .= "  </ElSelect>\n";
                break;
            case 'radio':
                $html .= "  <ElRadioGroup v-model=\"formData.{$prop}\">\n";
                if (!empty($options)) {
                    foreach ($options as $option) {
                        $html .= "    <ElRadio value=\"{$option['value']}\">{$option['label']}</ElRadio>\n";
                    }
                }
                $html .= "  </ElRadioGroup>\n";
                break;
            case 'checkbox':
                $html .= "  <ElCheckboxGroup v-model=\"formData.{$prop}\">\n";
                if (!empty($options)) {
                    foreach ($options as $option) {
                        $html .= "    <ElCheckbox :value=\"{$option['value']}\">{$option['label']}</ElCheckbox>\n";
                    }
                }
                $html .= "  </ElCheckboxGroup>\n";
                break;
            case 'switch':
                $html .= "  <ElSwitch v-model=\"formData.{$prop}\" />\n";
                break;
            case 'number':
                $html .= "  <ElInputNumber v-model=\"formData.{$prop}\" placeholder=\"请输入{$label}\" style=\"width: 100%\" controls-position=\"right\" />\n";
                break;
            case 'date-picker':
                $dateType = $props['type'] ?? 'date';
                // 使用字段信息获取正确的value-format
                if (isset($item['column'])) {
                    $valueFormat = $this->getDateValueFormatByColumn($item['column']);
                } else {
                    $valueFormat = $this->getDateValueFormat($dateType);
                }
                $html .= "  <ElDatePicker v-model=\"formData.{$prop}\" type=\"{$dateType}\" placeholder=\"请选择{$label}\" style=\"width: 100%\" value-format=\"{$valueFormat}\" />\n";
                break;
            case 'time-picker':
                $html .= "  <ElTimePicker v-model=\"formData.{$prop}\" placeholder=\"请选择{$label}\" style=\"width: 100%\" />\n";
                break;
            case 'upload':
                // 使用 FormUploader 组件替代 ElUpload
                $fileType = $props['fileType'] ?? 'file';
                $limit = $props['limit'] ?? 1;
                $isMultiple = isset($props['multiple']) && $props['multiple'];
                $useMediaSelector = isset($props['useMediaSelector']) && $props['useMediaSelector'];

                $html .= "  <FormUploader\n";
                $html .= "    v-model=\"formData.{$prop}\"\n";
                $html .= "    fileType=\"{$fileType}\"\n";
                $html .= "    :limit=\"{$limit}\"\n";
                if ($isMultiple) {
                    $html .= "    :multiple=\"true\"\n";
                }
                if ($useMediaSelector) {
                    $html .= "    :useMediaSelector=\"true\"\n";
                }
                $html .= "    buttonText=\"上传{$label}\"\n";
                $html .= "  />\n";
                break;
            case 'color-picker':
                $html .= "  <ElColorPicker v-model=\"formData.{$prop}\" />\n";
                break;
            case 'form-uploader':
                $fileType = $props['fileType'] ?? 'image';
                $limit = $props['limit'] ?? 1;
                $multiple = isset($props['multiple']) && $props['multiple'] ? ' :multiple="true"' : '';
                $html .= "  <FormUploader\n";
                $html .= "    v-model=\"formData.{$prop}\"\n";
                $html .= "    fileType=\"{$fileType}\"\n";
                $html .= "    :limit=\"{$limit}\"\n";
                if ($multiple) {
                    $html .= "    :multiple=\"true\"\n";
                }
                $html .= "  />\n";
                break;
            case 'form-media-selector':
                $mediaType = $props['mediaType'] ?? 'image';
                $maxCount = $props['maxCount'] ?? 1;
                $html .= "  <FormMediaSelector\n";
                $html .= "    v-model=\"formData.{$prop}\"\n";
                $html .= "    media-type=\"{$mediaType}\"\n";
                $html .= "    :max-count=\"{$maxCount}\"\n";
                $html .= "  />\n";
                break;
            default:
                $html .= "  <ElInput v-model=\"formData.{$prop}\" placeholder=\"请输入{$label}\" />\n";
        }
        
        $html .= "</ElFormItem>";
        
        return $html;
    }

    /**
     * 生成导入导出对话框文件
     *
     * @param array  $tableInfo 表结构信息
     * @param string $module    模块名称
     * @param array  $options   生成选项
     * @return array|null 生成的文件信息
     */
    protected function generateImportExportDialog(array $tableInfo, string $module, array $options = []): ?array
    {
        // 模板数据
        $data = [
            'moduleName'   => $module,
            'entityName'   => $this->convertToCamelCase($tableInfo['table_name_without_prefix']),
            'EntityName'   => $tableInfo['entity_name'],
            'comment'      => $tableInfo['parsed_comment']['title'],
            'hasImport'    => $this->hasImportSupport($tableInfo),
            'hasExport'    => $this->hasExportSupport($tableInfo)
        ];

        // 渲染模板
        $content = $this->templateEngine->render('import_export_dialog', $data);

        // 文件保存路径
        $filePath = app()->getRootPath() . "frontend/src/views/{$module}/{$tableInfo['table_name_without_prefix']}/import-export-dialog.vue";

        // 创建目录
        $this->makeDirIfNotExists(dirname($filePath));

        // 检查文件是否存在
        if (file_exists($filePath) && empty($options['overwrite'])) {
            // 文件存在且不允许覆盖
            return [
                'type'    => 'import-export-dialog',
                'path'    => $filePath,
                'content' => '导入导出对话框文件已存在，跳过生成',
                'skipped' => true
            ];
        }

        // 写入文件
        if (file_put_contents($filePath, $content)) {
            return [
                'type'    => 'import-export-dialog',
                'path'    => $filePath,
                'content' => $content
            ];
        }

        return null;
    }

    /**
     * 生成数据加载时的转换逻辑
     * @param array $columns 字段信息
     * @return array 转换逻辑数组
     */
    protected function generateDataConversions(array $columns): array
    {
        $conversions = [];

        foreach ($columns as $column) {
            $field = $column['Field'];
            $parsedComment = $column['parsed_comment'] ?? [];
            $tags = $parsedComment['tags'] ?? [];

            // 处理不同的数据结构
            if (is_array($tags)) {
                $formType = $tags['form'] ?? '';
            } else {
                $formType = '';
            }

            // checkbox 字段转换
            if ($formType === 'checkbox') {
                $conversions[] = [
                    'conversion' => "
        // 将字符串格式的 {$field} 转换为数字数组
        if (data.{$field} && typeof data.{$field} === 'string') {
          data.{$field} = data.{$field}.split(',')
            .filter(tag => tag.trim() !== '')
            .map(tag => parseInt(tag.trim()))
        }"
                ];
            }

            // time 字段转换
            if ($formType === 'time-picker' || $column['Type'] === 'time') {
                $conversions[] = [
                    'conversion' => "
        // 处理时间字段格式转换
        if (data.{$field} && typeof data.{$field} === 'string') {
          // ElTimePicker 期望 Date 对象，将 \"HH:mm:ss\" 格式转换为今天的 Date 对象
          const [hours, minutes, seconds] = data.{$field}.split(':').map(Number)
          const timeDate = new Date()
          timeDate.setHours(hours, minutes, seconds || 0, 0)
          data.{$field} = timeDate
        }"
                ];
            }

            // 数字字段转换 (decimal, float, double)
            $typeInfo = $this->parseFieldType($column['Type']);
            if (in_array($typeInfo['type'], ['decimal', 'float', 'double'])) {
                $conversions[] = [
                    'conversion' => "
        // 将字符串格式的 {$field} 转换为数字
        if (data.{$field} && typeof data.{$field} === 'string') {
          data.{$field} = parseFloat(data.{$field})
        }"
                ];
            }
        }

        return $conversions;
    }

    /**
     * 生成数据提交时的转换逻辑
     * @param array $columns 字段信息
     * @return array 转换逻辑数组
     */
    protected function generateSubmitConversions(array $columns): array
    {
        $conversions = [];

        foreach ($columns as $column) {
            $field = $column['Field'];
            $parsedComment = $column['parsed_comment'] ?? [];
            $tags = $parsedComment['tags'] ?? [];

            // 处理不同的数据结构
            if (is_array($tags)) {
                $formType = $tags['form'] ?? '';
            } else {
                $formType = '';
            }

            // checkbox 字段转换
            if ($formType === 'checkbox') {
                $conversions[] = [
                    'conversion' => "
        // 将数组格式的 {$field} 转换为字符串格式
        if (Array.isArray(submitData.{$field})) {
          submitData.{$field} = submitData.{$field}.join(',')
        }"
                ];
            }

            // time 字段转换
            if ($formType === 'time-picker' || $column['Type'] === 'time') {
                $conversions[] = [
                    'conversion' => "
        // 将 Date 对象格式的 {$field} 转换为字符串格式
        if (submitData.{$field} instanceof Date) {
          const hours = submitData.{$field}.getHours().toString().padStart(2, '0')
          const minutes = submitData.{$field}.getMinutes().toString().padStart(2, '0')
          const seconds = submitData.{$field}.getSeconds().toString().padStart(2, '0')
          submitData.{$field} = `\${hours}:\${minutes}:\${seconds}`
        }"
                ];
            }
        }

        return $conversions;
    }

    /**
     * 添加表格列组件配置
     *
     * @param array $columnConfig 列配置
     * @param array $column 字段信息
     * @param string $field 字段名
     * @param array $typeInfo 类型信息
     * @param array $tableInfo 表信息
     */
    protected function addTableColumnComponent(array &$columnConfig, array $column, string $field, array $typeInfo, array $tableInfo): void
    {
        // 根据字段类型和名称添加组件
        if ($field === 'status' || strpos($field, 'is_') === 0) {
            $columnConfig['component'] = 'SwitchColumn';
            // 根据字段名设置不同的文本
            if ($field === 'status') {
                $activeText = '启用';
                $inactiveText = '禁用';
            } elseif ($field === 'is_hot') {
                $activeText = '是';
                $inactiveText = '否';
            } elseif ($field === 'is_recommend') {
                $activeText = '是';
                $inactiveText = '否';
            } elseif ($field === 'is_top') {
                $activeText = '是';
                $inactiveText = '否';
            } elseif ($field === 'is_featured') {
                $activeText = '是';
                $inactiveText = '否';
            } elseif ($field === 'is_published') {
                $activeText = '是';
                $inactiveText = '否';
            } else {
                $activeText = '是';
                $inactiveText = '否';
            }
            $columnConfig['componentProps'] = "\n      activeValue: 1,\n      inactiveValue: 0,\n      activeText: '{$activeText}',\n      inactiveText: '{$inactiveText}',\n      updateApi: {$tableInfo['entity_name']}Api.updateField\n    ";
            $columnConfig['isSpecialColumn'] = true;
        } elseif (in_array($field, ['content', 'rich_content', 'summary', 'description', 'remark'])) {
            $columnConfig['component'] = 'LongTextColumn';
            $maxLength = $field === 'summary' ? 80 : 50;
            $columnConfig['componentProps'] = "\n      maxLength: {$maxLength}\n    ";
            $columnConfig['isSpecialColumn'] = true;
        } elseif ($field === 'tags' || strpos($field, 'tag') !== false) {
            $columnConfig['component'] = 'TagColumn';
            $componentProps = "\n      separator: ','";



            // 如果有选项配置，添加选项
            $optionsData = null;
            if (!empty($column['parsed_comment']['options'])) {
                $optionsData = $column['parsed_comment']['options'];
            } elseif (!empty($column['parsed_comment']['tags']['options'])) {
                // 解析 tags.options 字符串格式 "1=热门,2=推荐,3=置顶,4=精选"
                $optionsString = $column['parsed_comment']['tags']['options'];
                $optionsData = [];
                $pairs = explode(',', $optionsString);
                foreach ($pairs as $pair) {
                    if (strpos($pair, '=') !== false) {
                        list($value, $label) = explode('=', $pair, 2);
                        $optionsData[trim($value)] = trim($label);
                    }
                }
            }

            if (!empty($optionsData)) {
                $options = [];
                $typeMap = ['1' => 'danger', '2' => 'success', '3' => 'warning', '4' => 'primary'];
                foreach ($optionsData as $value => $label) {
                    $options[] = "{ value: '" . (string)$value . "', label: '" . $label . "', type: '" . ($typeMap[$value] ?? 'primary') . "' }";
                }
                $componentProps .= ",\n      options: [\n        " . implode(",\n        ", $options) . "\n      ]";
            }

            $componentProps .= "\n    ";
            $columnConfig['componentProps'] = $componentProps;
            $columnConfig['isSpecialColumn'] = true;
        } elseif (in_array($field, ['image', 'avatar', 'cover', 'logo']) || strpos($field, 'image') !== false) {
            $columnConfig['component'] = 'ImageColumn';
            $columnConfig['componentProps'] = "\n      width: 60,\n      height: 60,\n      preview: true\n    ";
            $columnConfig['isSpecialColumn'] = true;
        } elseif (in_array($field, ['file', 'attachment']) || strpos($field, 'file') !== false) {
            $columnConfig['component'] = 'DocumentColumn';
            $columnConfig['componentProps'] = "\n      emptyText: '无文件'\n    ";
            $columnConfig['isSpecialColumn'] = true;
        } elseif (in_array($field, ['url', 'link', 'website']) || strpos($field, 'url') !== false) {
            $columnConfig['component'] = 'LinkColumn';
            $columnConfig['componentProps'] = "\n      openInNewTab: true,\n      maxLength: 30\n    ";
            $columnConfig['isSpecialColumn'] = true;
        } elseif ($field === 'email' || strpos($field, 'email') !== false) {
            $columnConfig['component'] = 'LinkColumn';
            $columnConfig['componentProps'] = "\n      type: 'email',\n      maxLength: 25\n    ";
            $columnConfig['isSpecialColumn'] = true;
        } elseif (in_array($field, ['amount', 'price', 'cost', 'fee', 'discount']) || strpos($field, 'amount') !== false || strpos($field, 'price') !== false) {
            $columnConfig['component'] = 'CurrencyColumn';
            $columnConfig['componentProps'] = "\n      currency: 'CNY',\n      precision: 2\n    ";
            $columnConfig['isSpecialColumn'] = true;
        }
    }
}