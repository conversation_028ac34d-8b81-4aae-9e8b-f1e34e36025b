<?php
declare(strict_types=1);

namespace app\notice\model;

use app\common\core\base\BaseModel;

/**
 * 消息记录表模型
 *
 * @property int    $id            消息ID
 * @property int    $template_id   模板ID
 * @property string $title         消息标题
 * @property string $content       消息内容
 * @property string $type          消息大类：system系统,workflow工作流,business业务
 * @property string $module_code   业务模块编码：crm,erp,oa等
 * @property string $sub_type      消息子类型
 * @property string $business_id   业务ID
 * @property string $detail_url    PC端详情URL
 * @property string $mobile_url    移动端跳转URL
 * @property string $app_path      原生APP路径
 * @property string $mini_app_path 小程序页面路径
 * @property string $action_config 操作按钮配置JSON
 * @property int    $sender_id     发送人ID
 * @property string $sender_name   发送人姓名
 * @property string $send_time     发送时间
 * @property int    $priority      优先级：0普通，1重要，2紧急
 * @property int    $status        状态：0待发送，1已发送，2发送失败
 * @property string $send_channels 发送渠道：site站内信,email邮件,sms短信,wework微信,dingtalk钉钉
 * @property int    $creator_id    创建人
 * @property string $created_at    创建时间
 * @property string $updated_at    更新时间
 * @property string $deleted_at    删除时间
 * @property int    $tenant_id     租户ID
 */
class NoticeMessageModel extends BaseModel
{
	/**
	 * 数据表名称
	 *
	 * @var string
	 */
	protected $name = 'notice_message';
	
	protected string $dataRangeField = 'user_id';
	
	/**
	 * 字段类型转换
	 *
	 * @var array
	 */
	protected $type = [
		'id'          => 'integer',
		'template_id' => 'integer',
		'sender_id'   => 'integer',
		'send_time'   => 'datetime',
		'priority'    => 'integer',
		'status'      => 'integer',
		'creator_id'  => 'integer',
		'created_at'  => 'datetime',
		'updated_at'  => 'datetime',
		'deleted_at'  => 'datetime',
		'tenant_id'   => 'integer',
	];
	
	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'title'       => ['type' => 'like'],
			'type'        => ['type' => 'eq'],
			'sender_name' => ['type' => 'like'],
			'send_time'   => ['type' => 'date'],
		];
	}
	
	/**
	 * 获取允许单字段编辑的字段
	 *
	 * @return array
	 */
	public function getAllowUpdateFields(): array
	{
		return [
			'template_id',
			'title',
			'content',
			'type',
			'module_code',
			'sub_type',
			'business_id',
			'detail_url',
			'mobile_url',
			'app_path',
			'mini_app_path',
			'action_config',
			'sender_id',
			'sender_name',
			'send_time',
			'priority',
			'status',
			'send_channels',
		];
	}
	
	/**
	 * 获取允许排序的字段
	 *
	 * @return array
	 */
	public function getAllowSortFields(): array
	{
		return [
			'id',
			'created_at',
			'send_time',
		];
	}
	
	/**
	 * 关联接收人表
	 *
	 * @return \think\model\relation\HasMany
	 */
	public function recipients()
	{
		return $this->hasMany(NoticeRecipientModel::class, 'message_id', 'id');
	}
}