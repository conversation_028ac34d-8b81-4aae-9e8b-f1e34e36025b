# CRM商机阶段管理API接口文档

## 接口概述

本文档描述了CRM系统中商机阶段管理相关的API接口，包括阶段管理、流转控制、统计分析等功能。

## 基础信息

- **Base URL**: `/api/crm`
- **认证方式**: Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 1. 商机阶段管理接口

### 1.1 获取阶段列表

**接口地址**: `GET /business-stage/list`

**请求参数**:
```json
{
    "tenant_id": 1,          // 可选，租户ID，默认当前租户
    "status": 1,             // 可选，状态筛选 0=停用 1=启用
    "keyword": "接洽",        // 可选，关键词搜索
    "page": 1,               // 可选，页码，默认1
    "limit": 20              // 可选，每页数量，默认20
}
```

**响应示例**:
```json
{
    "code": 1,
    "message": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "tenant_id": 0,
                "stage_name": "初步接洽",
                "stage_code": "initial",
                "order_num": 1,
                "description": "首次接触客户，了解基本需求",
                "color": "#909399",
                "icon": "el-icon-phone",
                "is_initial": 1,
                "is_final": 0,
                "is_success": 0,
                "avg_duration_days": 5,
                "success_rate": 15.00,
                "status": 1,
                "created_at": "2024-01-01 10:00:00"
            }
        ],
        "total": 7,
        "page": 1,
        "limit": 20
    }
}
```

### 1.2 获取阶段详情

**接口地址**: `GET /business-stage/{id}`

**路径参数**:
- `id`: 阶段ID

**响应示例**:
```json
{
    "code": 1,
    "message": "获取成功",
    "data": {
        "id": 1,
        "stage_name": "初步接洽",
        "stage_code": "initial",
        "order_num": 1,
        "description": "首次接触客户，了解基本需求",
        "color": "#909399",
        "icon": "el-icon-phone",
        "is_initial": 1,
        "is_final": 0,
        "is_success": 0,
        "required_fields": ["customer_id", "business_name"],
        "auto_rules": {
            "timeout_days": 7,
            "auto_remind": true
        },
        "status": 1
    }
}
```

### 1.3 创建阶段

**接口地址**: `POST /business-stage/create`

**请求参数**:
```json
{
    "stage_name": "初步接洽",
    "stage_code": "initial",
    "order_num": 1,
    "description": "首次接触客户，了解基本需求",
    "color": "#909399",
    "icon": "el-icon-phone",
    "is_initial": 1,
    "is_final": 0,
    "is_success": 0,
    "required_fields": ["customer_id", "business_name"],
    "auto_rules": {
        "timeout_days": 7,
        "auto_remind": true
    }
}
```

**响应示例**:
```json
{
    "code": 1,
    "message": "创建成功",
    "data": {
        "id": 8,
        "stage_name": "初步接洽",
        "stage_code": "initial"
    }
}
```

### 1.4 更新阶段

**接口地址**: `PUT /business-stage/{id}`

**路径参数**:
- `id`: 阶段ID

**请求参数**: 同创建接口

### 1.5 删除阶段

**接口地址**: `DELETE /business-stage/{id}`

**路径参数**:
- `id`: 阶段ID

**响应示例**:
```json
{
    "code": 1,
    "message": "删除成功"
}
```

**注意事项**:
- 删除前会检查是否有商机使用该阶段
- 如果有商机使用，需要先迁移商机到其他阶段

### 1.6 批量排序

**接口地址**: `POST /business-stage/sort`

**请求参数**:
```json
{
    "stages": [
        {"id": 1, "order_num": 1},
        {"id": 2, "order_num": 2},
        {"id": 3, "order_num": 3}
    ]
}
```

## 2. 商机阶段流转接口

### 2.1 获取可流转阶段

**接口地址**: `GET /business/{business_id}/next-stages`

**路径参数**:
- `business_id`: 商机ID

**响应示例**:
```json
{
    "code": 1,
    "message": "获取成功",
    "data": [
        {
            "stage_id": 2,
            "stage_name": "需求确认",
            "stage_code": "demand",
            "color": "#E6A23C",
            "icon": "el-icon-edit",
            "is_allowed": true,
            "conditions": [],
            "description": "明确客户具体需求和预算范围"
        },
        {
            "stage_id": 7,
            "stage_name": "失败关闭",
            "stage_code": "closed_lost",
            "color": "#F56C6C",
            "icon": "el-icon-error",
            "is_allowed": true,
            "conditions": [],
            "description": "商机失败或主动放弃"
        }
    ]
}
```

### 2.2 商机阶段变更

**接口地址**: `POST /business/change-stage`

**请求参数**:
```json
{
    "business_id": 1,
    "to_stage_id": 2,
    "change_reason": "客户需求明确",
    "remark": "客户确认了具体的功能需求和预算范围"
}
```

**响应示例**:
```json
{
    "code": 1,
    "message": "阶段变更成功",
    "data": {
        "business_id": 1,
        "from_stage_id": 1,
        "to_stage_id": 2,
        "from_stage_name": "初步接洽",
        "to_stage_name": "需求确认",
        "duration_days": 5,
        "change_time": "2024-01-06 14:30:00"
    }
}
```

### 2.3 批量阶段变更

**接口地址**: `POST /business/batch-change-stage`

**请求参数**:
```json
{
    "business_ids": [1, 2, 3],
    "to_stage_id": 2,
    "change_reason": "批量推进",
    "remark": "批量将商机推进到需求确认阶段"
}
```

### 2.4 获取阶段变更记录

**接口地址**: `GET /business/{business_id}/stage-records`

**路径参数**:
- `business_id`: 商机ID

**请求参数**:
```json
{
    "page": 1,
    "limit": 10
}
```

**响应示例**:
```json
{
    "code": 1,
    "message": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "business_id": 1,
                "from_stage_name": "初步接洽",
                "to_stage_name": "需求确认",
                "duration_days": 5,
                "change_reason": "客户需求明确",
                "remark": "客户确认了具体的功能需求",
                "creator_name": "张三",
                "created_at": "2024-01-06 14:30:00"
            }
        ],
        "total": 1
    }
}
```

## 3. 商机转合同接口

### 3.1 商机转合同

**接口地址**: `POST /business/convert-to-contract`

**请求参数**:
```json
{
    "business_id": 1,
    "contract_data": {
        "contract_name": "XX项目开发合同",
        "contract_type": "开发合同",
        "contract_amount": 100000.00,
        "sign_date": "2024-01-10",
        "start_date": "2024-01-15",
        "end_date": "2024-06-15",
        "payment_terms": "分三期付款",
        "delivery_terms": "按阶段交付",
        "description": "项目开发合同详细描述"
    }
}
```

**响应示例**:
```json
{
    "code": 1,
    "message": "转换成功",
    "data": {
        "contract_id": 1,
        "contract_no": "HT202401001",
        "business_id": 1,
        "contract_name": "XX项目开发合同",
        "contract_amount": 100000.00
    }
}
```

## 4. 统计分析接口

### 4.1 阶段统计

**接口地址**: `GET /business-stage/statistics`

**请求参数**:
```json
{
    "tenant_id": 1,          // 可选，租户ID
    "date_range": {          // 可选，时间范围
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    }
}
```

**响应示例**:
```json
{
    "code": 1,
    "message": "获取成功",
    "data": [
        {
            "stage_id": 1,
            "stage_name": "初步接洽",
            "stage_code": "initial",
            "order_num": 1,
            "business_count": 15,
            "active_count": 12,
            "won_count": 2,
            "lost_count": 1,
            "total_amount": 500000.00,
            "avg_amount": 33333.33,
            "avg_duration_days": 5.2,
            "conversion_rate": 13.33
        }
    ]
}
```

### 4.2 销售漏斗分析

**接口地址**: `GET /business/funnel-analysis`

**请求参数**:
```json
{
    "tenant_id": 1,
    "date_range": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    },
    "owner_user_id": 1       // 可选，负责人筛选
}
```

**响应示例**:
```json
{
    "code": 1,
    "message": "获取成功",
    "data": {
        "funnel_data": [
            {
                "stage_name": "初步接洽",
                "count": 100,
                "amount": 1000000.00,
                "conversion_rate": 100.00
            },
            {
                "stage_name": "需求确认",
                "count": 80,
                "amount": 900000.00,
                "conversion_rate": 80.00
            }
        ],
        "summary": {
            "total_business": 100,
            "total_amount": 1000000.00,
            "won_count": 15,
            "won_amount": 300000.00,
            "overall_conversion_rate": 15.00
        }
    }
}
```

### 4.3 阶段停留时间分析

**接口地址**: `GET /business-stage/duration-analysis`

**响应示例**:
```json
{
    "code": 1,
    "message": "获取成功",
    "data": [
        {
            "stage_name": "初步接洽",
            "avg_duration": 5.2,
            "min_duration": 1,
            "max_duration": 15,
            "median_duration": 4,
            "sample_count": 50
        }
    ]
}
```

## 5. 流转规则管理接口

### 5.1 获取流转规则

**接口地址**: `GET /business-stage-flow/list`

**请求参数**:
```json
{
    "tenant_id": 1,
    "from_stage_id": 1,      // 可选，源阶段筛选
    "to_stage_id": 2         // 可选，目标阶段筛选
}
```

### 5.2 创建流转规则

**接口地址**: `POST /business-stage-flow/create`

**请求参数**:
```json
{
    "from_stage_id": 1,
    "to_stage_id": 2,
    "is_allowed": 1,
    "conditions": {
        "min_amount": 10000,
        "required_fields": ["expected_date"]
    },
    "auto_actions": {
        "send_notification": true,
        "update_fields": {
            "priority": "high"
        }
    },
    "remark": "需求明确后可推进"
}
```

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 1000 | 阶段不存在 |
| 1001 | 阶段编码已存在 |
| 1002 | 阶段正在使用中，无法删除 |
| 1003 | 不允许的阶段流转 |
| 1004 | 商机不存在 |
| 1005 | 商机状态不允许变更 |
| 1006 | 缺少必填字段 |
| 1007 | 权限不足 |
| 1008 | 流转条件不满足 |

## 7. 使用示例

### 7.1 创建自定义阶段流程

```javascript
// 1. 创建自定义阶段
const stageData = {
    stage_name: "技术评估",
    stage_code: "tech_review",
    order_num: 3,
    description: "技术团队评估项目可行性",
    color: "#722ED1",
    icon: "el-icon-cpu"
};

const response = await api.post('/business-stage/create', stageData);

// 2. 设置流转规则
const flowData = {
    from_stage_id: 2, // 需求确认
    to_stage_id: response.data.id, // 新创建的技术评估阶段
    is_allowed: 1,
    conditions: {
        min_amount: 50000 // 金额大于5万才需要技术评估
    }
};

await api.post('/business-stage-flow/create', flowData);
```

### 7.2 商机阶段推进

```javascript
// 推进商机阶段
const changeData = {
    business_id: 1,
    to_stage_id: 3,
    change_reason: "技术评估通过",
    remark: "技术团队确认项目可行性，可以进入方案报价阶段"
};

const result = await api.post('/business/change-stage', changeData);
console.log('阶段变更成功:', result.data);
```
