<?php
declare(strict_types=1);

namespace app\common\utils;

use DateTime;
use DateTimeInterface;
use Exception;

/**
 * 日期计算工具类
 * 提供天数、时长等自动计算功能
 * 主要用于工作流表单的业务场景
 */
class DateCalculator
{
    /**
     * 计算两个日期之间的天数（包含开始和结束日期）
     * 主要用于出差申请等业务场景
     * 
     * @param string|DateTimeInterface $startDate 开始日期
     * @param string|DateTimeInterface $endDate 结束日期
     * @return int 天数，如果日期无效返回0
     * 
     * @example
     * // 计算出差天数
     * DateCalculator::calculateDays('2025-07-29', '2025-07-31'); // 返回 3
     * DateCalculator::calculateDays('2025-07-29 09:00', '2025-07-29 18:00'); // 返回 1（同一天）
     * DateCalculator::calculateDays('2025-07-29', '2025-07-28'); // 返回 0（结束日期早于开始日期）
     */
    public static function calculateDays($startDate, $endDate): int
    {
        try {
            // 转换为DateTime对象
            $start = self::toDateTime($startDate);
            $end = self::toDateTime($endDate);
            
            if (!$start || !$end) {
                return 0;
            }
            
            // 如果结束日期早于开始日期，返回0
            if ($end < $start) {
                return 0;
            }
            
            // 将时间设置为当天的开始时间（00:00:00），确保按天计算
            $startDay = clone $start;
            $startDay->setTime(0, 0, 0);
            
            $endDay = clone $end;
            $endDay->setTime(0, 0, 0);
            
            // 计算天数差异并加1（包含开始和结束日期）
            $interval = $startDay->diff($endDay);
            $days = $interval->days + 1;
            
            return max(0, $days);
            
        } catch (Exception $e) {
            // 日期解析失败，返回0
            return 0;
        }
    }
    
    /**
     * 计算两个时间之间的小时数（精确到小数点后1位）
     * 主要用于外出申请等业务场景
     * 
     * @param string|DateTimeInterface $startTime 开始时间
     * @param string|DateTimeInterface $endTime 结束时间
     * @return float 小时数，如果时间无效返回0
     * 
     * @example
     * // 计算外出时长
     * DateCalculator::calculateHours('2025-07-28 14:00', '2025-07-28 17:00'); // 返回 3.0
     * DateCalculator::calculateHours('2025-07-28 14:30', '2025-07-28 17:15'); // 返回 2.8
     * DateCalculator::calculateHours('2025-07-28 17:00', '2025-07-28 14:00'); // 返回 0（结束时间早于开始时间）
     */
    public static function calculateHours($startTime, $endTime): float
    {
        try {
            // 转换为DateTime对象
            $start = self::toDateTime($startTime);
            $end = self::toDateTime($endTime);
            
            if (!$start || !$end) {
                return 0.0;
            }
            
            // 如果结束时间早于或等于开始时间，返回0
            if ($end <= $start) {
                return 0.0;
            }
            
            // 计算时间差异（秒）
            $diffSeconds = $end->getTimestamp() - $start->getTimestamp();
            
            // 转换为小时并保留1位小数
            $diffHours = $diffSeconds / 3600;
            
            return round($diffHours, 1);
            
        } catch (Exception $e) {
            // 时间解析失败，返回0
            return 0.0;
        }
    }
    
    /**
     * 计算两个日期之间的分钟数
     * 
     * @param string|DateTimeInterface $startTime 开始时间
     * @param string|DateTimeInterface $endTime 结束时间
     * @return int 分钟数，如果时间无效返回0
     * 
     * @example
     * DateCalculator::calculateMinutes('2025-07-28 14:00', '2025-07-28 17:30'); // 返回 210
     */
    public static function calculateMinutes($startTime, $endTime): int
    {
        try {
            $start = self::toDateTime($startTime);
            $end = self::toDateTime($endTime);
            
            if (!$start || !$end) {
                return 0;
            }
            
            if ($end <= $start) {
                return 0;
            }
            
            $diffSeconds = $end->getTimestamp() - $start->getTimestamp();
            return intval($diffSeconds / 60);
            
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * 计算工作日天数（排除周末）
     * 
     * @param string|DateTimeInterface $startDate 开始日期
     * @param string|DateTimeInterface $endDate 结束日期
     * @return int 工作日天数
     * 
     * @example
     * DateCalculator::calculateWorkdays('2025-07-28', '2025-08-01'); // 返回工作日天数
     */
    public static function calculateWorkdays($startDate, $endDate): int
    {
        try {
            $start = self::toDateTime($startDate);
            $end = self::toDateTime($endDate);
            
            if (!$start || !$end || $end < $start) {
                return 0;
            }
            
            $workdays = 0;
            $current = clone $start;
            
            while ($current <= $end) {
                $dayOfWeek = (int)$current->format('w'); // 0=Sunday, 1=Monday, ..., 6=Saturday
                
                // 排除周末（周六=6，周日=0）
                if ($dayOfWeek !== 0 && $dayOfWeek !== 6) {
                    $workdays++;
                }
                
                $current->modify('+1 day');
            }
            
            return $workdays;
            
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * 格式化时长为可读字符串
     * 
     * @param float $hours 小时数
     * @return string 格式化后的时长字符串
     * 
     * @example
     * DateCalculator::formatDuration(2.5); // 返回 "2小时30分钟"
     * DateCalculator::formatDuration(1.0); // 返回 "1小时"
     * DateCalculator::formatDuration(0.5); // 返回 "30分钟"
     */
    public static function formatDuration(float $hours): string
    {
        if ($hours <= 0) {
            return '0分钟';
        }
        
        $totalMinutes = intval($hours * 60);
        $h = intval($totalMinutes / 60);
        $m = $totalMinutes % 60;
        
        $result = '';
        if ($h > 0) {
            $result .= $h . '小时';
        }
        if ($m > 0) {
            $result .= $m . '分钟';
        }
        
        return $result ?: '0分钟';
    }
    
    /**
     * 检查日期是否为工作日
     * 
     * @param string|DateTimeInterface $date 日期
     * @return bool 是否为工作日
     */
    public static function isWorkday($date): bool
    {
        try {
            $dateTime = self::toDateTime($date);
            if (!$dateTime) {
                return false;
            }
            
            $dayOfWeek = (int)$dateTime->format('w');
            return $dayOfWeek !== 0 && $dayOfWeek !== 6; // 排除周末
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 将输入转换为DateTime对象
     * 
     * @param string|DateTimeInterface $input 输入的日期时间
     * @return DateTime|null DateTime对象或null
     */
    private static function toDateTime($input): ?DateTime
    {
        if ($input instanceof DateTimeInterface) {
            return DateTime::createFromInterface($input);
        }
        
        if (is_string($input)) {
            try {
                return new DateTime($input);
            } catch (Exception $e) {
                return null;
            }
        }
        
        return null;
    }
    
    /**
     * 获取当前日期时间字符串
     * 
     * @param string $format 格式，默认 'Y-m-d H:i:s'
     * @return string 格式化后的日期时间字符串
     */
    public static function now(string $format = 'Y-m-d H:i:s'): string
    {
        return date($format);
    }
    
    /**
     * 获取今天的日期字符串
     * 
     * @param string $format 格式，默认 'Y-m-d'
     * @return string 今天的日期字符串
     */
    public static function today(string $format = 'Y-m-d'): string
    {
        return date($format);
    }
}
