# 简化版项目任务管理设计方案

## 🎯 设计理念

### 核心原则
- **简单实用**：只保留最核心的项目和任务管理功能
- **飞书风格**：借鉴飞书的简洁UI设计和交互体验
- **快速上手**：降低学习成本，用户可以快速上手使用
- **轻量级**：最小化数据库表和代码复杂度

### 功能范围
- ✅ 项目创建和管理
- ✅ 任务创建和分配
- ✅ 任务状态跟踪
- ✅ 项目成员管理
- ✅ 任务评论和附件
- ❌ 复杂的工作流
- ❌ 甘特图
- ❌ 复杂的统计报表
- ❌ 项目模板

## 📊 数据库设计

### 核心表结构（仅4个表）

```mermaid
erDiagram
    project_project ||--o{ project_task : "一对多"
    project_project ||--o{ project_member : "一对多"
    project_task ||--o{ project_task_comment : "一对多"
    
    project_member }|--|| system_admin : "成员关联"
    project_task }|--|| system_admin : "负责人"
```

#### 1. project_project (项目表)
```sql
-- 核心字段
name            项目名称
description     项目描述  
status          项目状态 (1进行中 2已完成 3已暂停 4已取消)
priority        优先级 (1低 2中 3高)
start_date      开始日期
end_date        截止日期
progress        项目进度(%)
owner_id        项目负责人ID
color           项目颜色 (用于UI展示)
is_archived     是否归档
```

#### 2. project_task (任务表)
```sql
-- 核心字段
project_id      项目ID
title           任务标题
description     任务描述
status          任务状态 (1待办 2进行中 3已完成 4已关闭)
priority        优先级 (1低 2中 3高)
assignee_id     负责人ID
start_date      开始日期
due_date        截止日期
estimated_hours 预估工时
actual_hours    实际工时
sort            排序
```

#### 3. project_member (项目成员表)
```sql
-- 核心字段
project_id      项目ID
user_id         用户ID
role            角色 (owner负责人 member成员)
joined_at       加入时间
```

#### 4. project_task_comment (任务评论表)
```sql
-- 核心字段
task_id         任务ID
content         评论内容
attachments     附件列表JSON
```

## 🎨 UI设计方案

### 飞书风格设计要素

#### 色彩系统
```css
--primary: #1664FF;      /* 飞书蓝 */
--success: #00BC70;      /* 成功绿 */
--warning: #FF8800;      /* 警告橙 */
--danger: #F54A45;       /* 危险红 */
--text: #1F2329;         /* 主文本 */
--text-light: #646A73;   /* 次要文本 */
--border: #E5E6EB;       /* 边框 */
--bg: #F7F8FA;           /* 背景 */
```

#### 核心页面设计

### 1. 项目列表页
```
┌─────────────────────────────────────────────────────────┐
│ 🏠 项目管理                              [+ 新建项目]    │
├─────────────────────────────────────────────────────────┤
│ 🔍 [搜索项目...] [状态筛选▼] [负责人筛选▼]              │
├─────────────────────────────────────────────────────────┤
│ 📋 项目列表                              [列表] [卡片]   │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 🎯 产品V2.0开发        进行中  65%   张三  2024-03-31│ │
│ │ 📱 移动端优化          进行中  30%   李四  2024-04-30│ │
│ │ 🎨 UI设计改版         已完成  100%  王五  2024-01-31│ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2. 项目详情页（看板风格）
```
┌─────────────────────────────────────────────────────────┐
│ 🎯 产品V2.0开发                          [编辑] [设置]   │
│ 进度: 65% | 成员: 3人 | 任务: 8个 | 截止: 2024-03-31    │
├─────────────────────────────────────────────────────────┤
│ [看板视图] [列表视图]                    [+ 新建任务]    │
├─────────────────────────────────────────────────────────┤
│ 待办 (2)        │ 进行中 (3)      │ 已完成 (3)          │
│ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────┐ │
│ │🔴 用户权限   │ │ │🟡 登录功能   │ │ │✅ 需求分析       │ │
│ │张三 1/16-30 │ │ │张三 1/1-15  │ │ │张三 已完成       │ │
│ └─────────────┘ │ └─────────────┘ │ └─────────────────┘ │
│ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────┐ │
│ │🟠 数据统计   │ │ │🟢 移动适配   │ │ │✅ 原型设计       │ │
│ │待分配 2/1-15│ │ │李四 2/1-20  │ │ │王五 已完成       │ │
│ └─────────────┘ │ └─────────────┘ │ └─────────────────┘ │
│ [+ 添加任务]   │ │               │ │                   │
└─────────────────────────────────────────────────────────┘
```

### 3. 任务详情弹窗
```
┌─────────────────────────────────────────────────────────┐
│ 📝 任务详情 - 用户登录功能开发            [编辑] [关闭] │
├─────────────────────────────────────────────────────────┤
│ 状态: [进行中▼] 优先级: [高▼] 负责人: [张三▼]           │
│ 开始: 2024-01-01  截止: 2024-01-15  工时: 40h/32h      │
├─────────────────────────────────────────────────────────┤
│ 📄 描述                                                  │
│ 实现用户登录、注册、找回密码等功能...                    │
├─────────────────────────────────────────────────────────┤
│ 💬 评论 (3)                              [发表评论]     │
│ 👤 张三  2024-01-12 10:30                               │
│ 登录接口已完成，正在进行前端对接...                      │
│ ─────────────────────────────────────────────────────── │
│ 👤 李四  2024-01-12 14:20                               │
│ 前端页面已完成，可以开始联调                             │
└─────────────────────────────────────────────────────────┘
```

## 🧩 核心组件

### 1. 项目卡片组件
```vue
<template>
  <div class="project-card" :style="{ borderLeftColor: project.color }">
    <div class="card-header">
      <h3>{{ project.name }}</h3>
      <el-dropdown>
        <el-button type="text" icon="el-icon-more" />
      </el-dropdown>
    </div>
    
    <div class="card-body">
      <p class="description">{{ project.description }}</p>
      <div class="progress">
        <span>{{ project.progress }}%</span>
        <el-progress :percentage="project.progress" />
      </div>
    </div>
    
    <div class="card-footer">
      <span class="owner">👤 {{ project.owner_name }}</span>
      <span class="date">📅 {{ project.end_date }}</span>
      <el-tag :type="statusType">{{ statusText }}</el-tag>
    </div>
  </div>
</template>
```

### 2. 任务卡片组件
```vue
<template>
  <div class="task-card" 
       :class="`priority-${task.priority}`"
       draggable="true"
       @click="showDetail">
    <div class="task-header">
      <span class="priority-dot"></span>
      <h4>{{ task.title }}</h4>
    </div>
    
    <div class="task-meta">
      <span class="assignee">👤 {{ task.assignee_name || '待分配' }}</span>
      <span class="date">📅 {{ task.due_date }}</span>
    </div>
  </div>
</template>
```

### 3. 看板列组件
```vue
<template>
  <div class="kanban-column">
    <div class="column-header">
      <h3>{{ status.name }} ({{ tasks.length }})</h3>
      <el-button type="text" icon="el-icon-plus" @click="addTask" />
    </div>
    
    <div class="column-body" @drop="onDrop" @dragover.prevent>
      <task-card 
        v-for="task in tasks" 
        :key="task.id" 
        :task="task" />
    </div>
  </div>
</template>
```

## 🚀 实施方案

### 第一步：数据库初始化
```bash
# 1. 执行简化版数据库脚本
mysql -u username -p database_name < app/project/simple_database.sql

# 2. 执行菜单配置
mysql -u username -p database_name < app/project/simple_menu.sql
```

### 第二步：生成基础代码
```bash
# 按顺序执行CRUD生成器
php think generator:crud project_project --module=project --frontend --overwrite
php think generator:crud project_member --module=project --frontend --overwrite  
php think generator:crud project_task --module=project --frontend --overwrite
php think generator:crud project_task_comment --module=project --frontend --overwrite
```

### 第三步：前端开发重点

#### 1. 项目列表页优化
- 卡片视图和列表视图切换
- 项目状态筛选
- 搜索功能

#### 2. 项目详情页开发
- 看板式任务展示
- 拖拽改变任务状态
- 任务快速创建

#### 3. 任务管理优化
- 任务详情弹窗
- 评论功能
- 文件附件上传

## 📱 移动端适配

### 响应式设计
```css
/* 移动端 */
@media (max-width: 768px) {
  .kanban-board {
    flex-direction: column;
  }
  
  .project-card {
    margin-bottom: 12px;
  }
}
```

## 🎯 核心功能清单

### 项目管理
- [x] 项目创建、编辑、删除
- [x] 项目状态管理
- [x] 项目成员管理
- [x] 项目进度跟踪

### 任务管理  
- [x] 任务创建、编辑、删除
- [x] 任务分配和状态变更
- [x] 任务评论和附件
- [x] 看板式任务展示

### 用户体验
- [x] 飞书风格UI设计
- [x] 拖拽操作支持
- [x] 移动端响应式
- [x] 快捷操作

## 📈 后续扩展

如果需要更多功能，可以逐步添加：
- 工时记录
- 简单统计
- 任务依赖
- 消息通知

---

*这个简化版方案专注于核心功能，确保用户能够快速上手，同时保持飞书风格的简洁体验。*
