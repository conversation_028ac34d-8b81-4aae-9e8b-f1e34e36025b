<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\crm\model\CrmContract;
use app\crm\model\CrmContractReceivable;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\service\WorkflowEngineService;
use app\workflow\constants\WorkflowStatusConstant;
use think\facade\Db;
use think\facade\Log;
use app\common\core\crud\traits\ExportableTrait;
use app\common\core\crud\traits\ImportableTrait;


/**
 * 回款记录表服务类
 */
class CrmContractReceivableService extends BaseService implements FormServiceInterface
{
	use CrudServiceTrait, ExportableTrait, ImportableTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmContractReceivable();
		parent::__construct();
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'customer_id' => ['type' => 'eq'],
			
			'contract_id' => ['type' => 'eq'],
			
			'receivable_number' => ['type' => 'like'],
			
			'amount' => ['type' => 'between'],
			
			'received_date' => ['type' => 'date'],
			
			'payment_method' => ['type' => 'eq'],
			
			'bank_name' => ['type' => 'like'],
			
			'bank_account' => ['type' => 'like'],
			
			'approval_status' => ['type' => 'eq'],
			
			'workflow_instance_id' => ['type' => 'eq'],
			
			'submit_time' => ['type' => 'date'],
			
			'approval_time' => ['type' => 'date'],
			
			'owner_user_id' => ['type' => 'eq'],
		
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'contract_id'       => 'require|integer|gt:0',
			'amount'            => 'require|float|gt:0',
			'received_date'     => 'require|date',
			'payment_method'    => 'max:50',
			'bank_name'         => 'max:100',
			'bank_account'      => 'max:50',
			'receivable_number' => 'require|max:50',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 重写添加方法，添加业务验证
	 *
	 * @param array $data 数据
	 * @return int 返回新增ID
	 * @throws BusinessException
	 */
	public function add(array $data): int
	{
		// 1. 预处理数据
		$data = $this->preprocessReceivableData($data, 'create');
		
		// 2. 验证数据
		$data = $this->validateReceivableData($data, 'create');
		
		// 3. 执行添加
		$result = $this->crudService->add($data);
		if (!$result) {
			throw new BusinessException('回款记录创建失败');
		}
		
		return $result;
	}
	
	/**
	 * 重写编辑方法，添加业务验证
	 *
	 * @param array $data  数据
	 * @param array $where 条件
	 * @return bool 是否成功
	 * @throws BusinessException
	 */
	public function edit(array $data, array $where): bool
	{
		// 1. 获取原回款记录
		$receivable = $this->model->where($where)
		                          ->find();
		if (!$receivable) {
			throw new BusinessException('回款记录不存在');
		}
		
		// 2. 检查是否可以编辑
		if ($receivable->approval_status > 1) {
			throw new BusinessException('已审批的回款记录不能编辑');
		}
		
		// 3. 预处理数据
		$data = $this->preprocessReceivableData($data, 'update', $receivable);
		
		// 4. 验证数据
		$data = $this->validateReceivableData($data, 'update');
		
		// 5. 执行更新
		return $this->crudService->edit($data, $where);
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	/**
	 * 作废回款记录
	 *
	 * @param int    $receivableId 回款记录ID
	 * @param string $reason       作废原因
	 * @return bool
	 * @throws BusinessException
	 */
	public function voidReceivable(int $receivableId, string $reason = ''): bool
	{
		Db::startTrans();
		try {
			// 1. 获取回款记录信息
			$receivable = $this->model->with(['workflow'])
			                          ->find($receivableId);
			if (!$receivable) {
				throw new BusinessException('回款记录不存在');
			}
			
			// 2. 检查回款记录审批状态
			if ($receivable['approval_status'] == 6) {
				throw new BusinessException('回款记录已经是作废状态');
			}
			
			// 3. 验证作废原因
			if (empty(trim($reason))) {
				throw new BusinessException('作废原因不能为空');
			}
			
			// 4. 如果有工作流实例且已通过，需要作废工作流实例
			if ($receivable['workflow_instance_id'] && $receivable['approval_status'] == 2) {
				$workflowEngineService = WorkflowEngineService::getInstance();
				$voidResult            = $workflowEngineService->voidApprovedInstance($receivable['workflow_instance_id'], $reason, // 使用用户输入的作废原因
					request()->adminId ?? 0);
				
				if (!$voidResult) {
					throw new BusinessException('工作流实例作废失败');
				}
			}
			
			// 5. 更新回款记录状态为作废
			$result = $receivable->saveByUpdate([
				'approval_status'  => 6,
				// 6=已作废
				'approval_time'    => date('Y-m-d H:i:s'),
				'approval_opinion' => $reason
				// 保存作废原因
			]);
			
			if (!$result) {
				throw new BusinessException('回款记录作废失败');
			}
			
			// 6. 重新计算合同的付款状态
			if ($receivable['contract_id']) {
				$contractService = CrmContractService::getInstance();
				$contractService->recalculatePaymentStatus($receivable['contract_id']);
			}
			
			Db::commit();
			return true;
			
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException('作废失败：' . $e->getMessage());
		}
	}
	
	// ==================== FormServiceInterface 实现 ====================
	
	/**
	 * 获取表单数据
	 *
	 * @param int $id 记录ID
	 * @return array 表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = $this->model->find($id);
		return $model
			? $model->toArray()
			: [];
	}
	
	/**
	 * 保存表单数据
	 *
	 * @param array $data 表单数据
	 * @return array [id,formData]
	 */
	public function saveForm(array $data): array
	{
		$result = $this->create($data);
		return [
			'id'       => $result['id'] ?? 0,
			'formData' => $result
		];
	}
	
	/**
	 * 更新表单数据
	 *
	 * @param int   $id   记录ID
	 * @param array $data 表单数据
	 * @return bool 更新结果
	 */
	public function updateForm(int $id, array $data): bool
	{
		return $this->update($id, $data);
	}
	
	/**
	 * 删除表单数据
	 *
	 * @param int $id 记录ID
	 * @return bool 删除结果
	 */
	public function deleteForm(int $id): bool
	{
		return $this->delete([$id]);
	}
	
	/**
	 * 更新表单状态
	 *
	 * @param int   $id     记录ID
	 * @param int   $status 状态值
	 * @param array $extra  额外数据
	 * @return bool 更新结果
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		$updateData = array_merge(['status' => $status], $extra);
		return $this->update($id, $updateData);
	}
	
	/**
	 * 获取实例标题
	 *
	 * @param $formData 表单数据
	 * @return string 实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		if (is_array($formData) && isset($formData['receivable_amount'])) {
			return "回款记录 - {$formData['receivable_amount']}元";
		}
		
		if (is_numeric($formData)) {
			$model = $this->model->find($formData);
			if ($model) {
				return "回款记录 - {$model->receivable_amount}元";
			}
		}
		
		return '回款记录';
	}
	
	/**
	 * 验证表单数据
	 *
	 * @param array  $data  表单数据
	 * @param string $scene 验证场景
	 * @return array 验证后的数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		$errors = [];
		
		// 基础字段验证
		if (empty($data['contract_id'])) {
			$errors[] = '合同ID不能为空';
		}
		
		if (empty($data['receivable_amount']) || $data['receivable_amount'] <= 0) {
			$errors[] = '回款金额必须大于0';
		}
		
		if (empty($data['receivable_date'])) {
			$errors[] = '回款日期不能为空';
		}
		
		if (!empty($errors)) {
			throw new BusinessException(implode(', ', $errors));
		}
		
		return $data;
	}
	
	/**
	 * 根据参数获取表单数据
	 *
	 * @param array $params 参数
	 * @return array 表单数据
	 */
	public function getFormDataByParams(array $params): array
	{
		// 过滤掉不需要的参数
		$allowedFields = [
			'id',
			'contract_id',
			'receivable_amount',
			'receivable_date',
			'payment_method',
			'remark',
			'status'
		];
		
		return array_intersect_key($params, array_flip($allowedFields));
	}
	
	// ==================== 数据预处理和验证方法 ====================
	
	/**
	 * 预处理回款数据
	 *
	 * @param array       $data               原始数据
	 * @param string      $scene              场景：create|update
	 * @param object|null $originalReceivable 原回款数据（更新时）
	 * @return array 预处理后的数据
	 */
	private function preprocessReceivableData(array $data, string $scene, $originalReceivable = null): array
	{
		// 1. 处理金额字段
		if (isset($data['amount'])) {
			$data['amount'] = (float)$data['amount'];
		}
		
		// 2. 处理日期字段
		if (isset($data['received_date']) && empty($data['received_date'])) {
			$data['received_date'] = null;
		}
		
		// 3. 新建时设置默认值
		if ($scene === 'create') {
			$data['approval_status']      = 0; // 草稿状态
			$data['workflow_instance_id'] = 0;
			$data['owner_user_id']        = $data['owner_user_id'] ?? get_user_id();
		}
		
		return $data;
	}
	
	/**
	 * 验证回款数据
	 *
	 * @param array  $data  数据
	 * @param string $scene 场景：create|update
	 * @return array 验证后的数据
	 * @throws BusinessException
	 */
	private function validateReceivableData(array $data, string $scene): array
	{
		// 1. 基础验证规则
		$rules = [
			'contract_id'       => 'require|integer|gt:0',
			'amount'            => 'require|float|gt:0',
			'received_date'     => 'require|date',
			'receivable_number' => 'require|max:50',
			'payment_method'    => 'max:50',
			'bank_name'         => 'max:100',
			'bank_account'      => 'max:50',
		];
		
		// 2. 根据场景调整规则
		if ($scene === 'update') {
			// 更新时可能不需要验证某些字段
		}
		
		// 3. 执行基础验证
		$validate = validate($rules);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		// 4. 业务逻辑验证
		$this->validateReceivableBusinessLogic($data, $scene);
		
		return $data;
	}
	
	/**
	 * 回款业务逻辑验证
	 *
	 * @param array  $data  数据
	 * @param string $scene 场景
	 * @throws BusinessException
	 */
	private function validateReceivableBusinessLogic(array $data, string $scene): void
	{
		// 1. 验证回款金额必须大于0
		if (isset($data['amount']) && $data['amount'] <= 0) {
			throw new BusinessException('回款金额必须大于0');
		}
		
		// 2. 验证合同是否存在
		if (isset($data['contract_id'])) {
			$contract = CrmContract::find($data['contract_id']);
			if (!$contract) {
				throw new BusinessException('关联的合同不存在');
			}
		}
		
		// 3. 验证回款编号唯一性
		if (isset($data['receivable_number']) && $scene === 'create') {
			$exists = $this->model->where('receivable_number', $data['receivable_number'])
			                      ->find();
			if ($exists) {
				throw new BusinessException('回款编号已存在');
			}
		}
	}
	
	/**
	 * 工作流状态变更后的统一业务处理
	 * 处理审批完成后的所有业务逻辑
	 *
	 * @param int   $businessId 回款记录ID
	 * @param int   $status     新状态
	 * @param array $extra      额外数据
	 * @return bool 处理结果
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		Log::info('回款审批结果统一处理', [
			'receivable_id' => $businessId,
			'status'        => $status,
			'extra'         => $extra
		]);
		
		try {
			if ($status === WorkflowStatusConstant::STATUS_COMPLETED) {
				// 审批通过：处理所有业务逻辑
				$this->handleReceivableApproved($businessId, $extra);
			}
			elseif ($status === WorkflowStatusConstant::STATUS_REJECTED) {
				// 审批拒绝：处理拒绝逻辑
				$this->handleReceivableRejected($businessId, $extra);
			}
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('回款审批结果处理失败', [
				'receivable_id' => $businessId,
				'status'        => $status,
				'error'         => $e->getMessage(),
				'trace'         => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 处理回款审批通过（统一处理所有业务逻辑）
	 *
	 * @param int   $receivableId 回款记录ID
	 * @param array $extra        额外数据
	 * @return void
	 * @throws BusinessException
	 */
	private function handleReceivableApproved(int $receivableId, array $extra): void
	{
		// 获取回款记录
		$receivable = $this->model->find($receivableId);
		if (!$receivable) {
			throw new BusinessException('回款记录不存在');
		}
		
		// 1. 核心业务逻辑：更新合同付款状态
		$this->updateContractPaymentStatus($receivable->contract_id);
		
		// 2. 记录操作日志
		$this->logApprovalOperation($receivableId, '审批通过', $extra['opinion'] ?? '');
		
		Log::info('回款审批通过处理完成', [
			'receivable_id' => $receivableId,
			'contract_id'   => $receivable->contract_id
		]);
	}
	
	/**
	 * 处理回款审批拒绝
	 *
	 * @param int   $receivableId 回款记录ID
	 * @param array $extra        额外数据
	 * @return void
	 */
	private function handleReceivableRejected(int $receivableId, array $extra): void
	{
		// 记录拒绝日志
		$this->logApprovalOperation($receivableId, '审批拒绝', $extra['opinion'] ?? '');
		
		Log::info('回款审批拒绝处理完成', ['receivable_id' => $receivableId]);
	}
	
	/**
	 * 更新合同付款状态（调用现有的recalculatePaymentStatus方法）
	 *
	 * @param int $contractId 合同ID
	 * @return void
	 * @throws BusinessException
	 */
	private function updateContractPaymentStatus(int $contractId): void
	{
		try {
			$contractService = \app\crm\service\CrmContractService::getInstance();
			$contractService->recalculatePaymentStatus($contractId);
			
			Log::info('合同付款状态更新成功', ['contract_id' => $contractId]);
		}
		catch (\Exception $e) {
			Log::error('更新合同付款状态失败', [
				'contract_id' => $contractId,
				'error'       => $e->getMessage()
			]);
			throw new BusinessException('更新合同付款状态失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 记录审批操作日志
	 *
	 * @param int    $receivableId 回款记录ID
	 * @param string $operation    操作类型
	 * @param string $remark       备注
	 * @return void
	 */
	private function logApprovalOperation(int $receivableId, string $operation, string $remark): void
	{
		$logData = [
			'module'      => 'crm_receivable',
			'business_id' => $receivableId,
			'operation'   => $operation,
			'remark'      => $remark,
			'operator_id' => request()->adminId ?? 0,
			'created_at'  => date('Y-m-d H:i:s')
		];
		
		Log::info('回款审批操作记录', $logData);
	}
}