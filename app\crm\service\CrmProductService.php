<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmProduct;
use app\crm\model\CrmBusinessProduct;
use app\crm\model\CrmContractProduct;
use app\daily\model\DailyPriceItem;
use app\ims\service\ImsSupplierService;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 产品表服务类
 */
class CrmProductService extends BaseService
{
	
	use ExportableTrait;
	
	
	use ImportableTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmProduct();
		parent::__construct();
		$this->crudService->setEnableDataPermission(false);
	}

	/**
	 * 获取产品列表时包含供应商信息
	 */
	public function getList(array $where = [], array $with = []): array
	{
		$defaultWith = ['category', 'unit', 'supplier'];
		$with = array_merge($defaultWith, $with);

		return parent::getList($where, $with);
	}

	/**
	 * 根据供应商筛选产品
	 */
	public function getProductsBySupplier(int $supplierId): array
	{
		return $this->model->where('supplier_id', $supplierId)
		                  ->where('status', 1)
		                  ->with(['category', 'unit', 'supplier'])
		                  ->select()
		                  ->toArray();
	}

	/**
	 * 获取产品选择器数据
	 */
	public function getProductOptions(int $supplierId = 0): array
	{
		$where = [['status', '=', 1]];

		if ($supplierId > 0) {
			$where[] = ['supplier_id', '=', $supplierId];
		}

		return $this->model->where($where)
		                  ->field('id,name,code,spec,supplier_id')
		                  ->with(['supplier' => function($query) {
			                  $query->field('id,name');
		                  }])
		                  ->select()
		                  ->toArray();
	}
	
	
	/**
	 * 删除产品前的完整检查
	 */
	public function delete($id): bool
	{
		$product = $this->model->find($id);
		if (!$product) {
			throw new \Exception('产品不存在');
		}

		$errors = [];

		// 检查商机关联
		$businessCount = CrmBusinessProduct::where('product_id', $id)->count();
		if ($businessCount > 0) {
			$errors[] = "关联了 {$businessCount} 个商机";
		}

		// 检查合同关联
		$contractCount = CrmContractProduct::where('product_id', $id)->count();
		if ($contractCount > 0) {
			$errors[] = "关联了 {$contractCount} 个合同";
		}

		// 检查报价记录
		$priceCount = DailyPriceItem::where('product_id', $id)->count();
		if ($priceCount > 0) {
			$errors[] = "有 {$priceCount} 条报价记录";
		}

		if (!empty($errors)) {
			throw new \Exception("无法删除产品「{$product->name}」，原因：" . implode('、', $errors) . "。建议停用而非删除。");
		}

		return parent::delete($id);
	}

	/**
	 * 停用产品（推荐操作）
	 */
	public function disable($id): bool
	{
		return $this->updateField($id, 'status', 0);
	}

	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'name' => 'require|max:50',
			'category_id' => 'require|integer|gt:0',
			'unit_id' => 'require|integer|gt:0',
			'supplier_id' => 'require|integer|gt:0', // 新增必填验证
			'price' => 'float|egt:0',
			'cost' => 'float|egt:0',
		];

		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 获取搜索字段配置
	 */
	protected function getSearchFields(): array
	{
		return [
			'name' => ['type' => 'like'],
			'code' => ['type' => 'like'],
			'category_id' => ['type' => 'eq'],
			'unit_id' => ['type' => 'eq'],
			'supplier_id' => ['type' => 'eq'], // 新增
			'status' => ['type' => 'eq'],
		];
	}

	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}

		// 逐个检查每个产品是否可以删除
		foreach ($ids as $id) {
			$this->delete($id); // 复用单个删除的检查逻辑
		}

		return true;
	}

	/**
	 * 获取产品选项（用于下拉选择）
	 */
	public function getOptions(array $params = []): array
	{
		// 构建查询条件
		$where = [];
		$where[] = ['status', '=', 1]; // 只获取启用的产品

		// 如果指定了供应商ID，则按供应商筛选
		if (!empty($params['supplier_id'])) {
			$where[] = ['supplier_id', '=', $params['supplier_id']];
		}

		// 获取产品列表
		$products = $this->model
			->where($where)
			->with(['category','supplier'])
			->order('id', 'desc')
			->select()
			->toArray();

		// 格式化为选项格式
		$options = [];
		foreach ($products as $product) {
			$label = $product['name'];
			if (!empty($product['spec'])) {
				$label .= " - {$product['spec']}";
			}

			$options[] = [
				'label' => $label,
				'value' => $product['id'],
				'name' => $product['name'],
				'spec' => $product['spec'] ?? '',
				'price' => $product['price'] ?? 0,
				'cost' => $product['cost'] ?? 0,
				'unit_name' => $product['unit_name'] ?? '',
				'category_name' => $product['category']['name'] ?? '',
				'supplier_id' => $product['supplier_id'] ?? null,
				'supplier_name' => $product['supplier']['name'] ?? '',
			];
		}

		return $options;
	}
}