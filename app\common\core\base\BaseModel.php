<?php
declare(strict_types=1);

namespace app\common\core\base;

use app\common\core\traits\model\RewriteTrait;
use app\common\core\traits\model\TenantSoftDelete;
use think\db\exception\DbException;
use think\facade\Db;
use think\facade\Log;
use think\Model;

/**
 * 模型基类
 * 所有业务模型应继承此类
 */
abstract class BaseModel extends Model
{
	use TenantSoftDelete, RewriteTrait;
	
	/**
	 * 创建时间字段
	 *
	 * @var string
	 */
	protected $createTime = 'created_at';
	
	/**
	 * 更新时间字段
	 *
	 * @var string
	 */
	protected $updateTime = 'updated_at';
	
	/**
	 * 软删除时间字段
	 *
	 * @var string
	 */
	protected string $deleteTime = 'deleted_at';
	
	/**
	 * 软删除默认值
	 *
	 * @var mixed
	 */
	protected $defaultSoftDelete = null;
	
	/**
	 * 是否自动写入时间戳
	 *
	 * @var bool
	 */
	protected $autoWriteTimestamp = true;
	
	/**
	 * 是否启用软删除
	 *
	 * @var bool
	 */
	protected bool $isSoftDelete = true;
	
	/**
	 * 租户字段名
	 *
	 * @var string
	 */
	protected string $tenantField = 'tenant_id';
	
	/**
	 * 数据权限字段
	 *
	 * @var string
	 */
	protected string $dataRangeField = 'creator_id';
	
	/**
	 * 是否启用租户隔离
	 *
	 * @var bool
	 */
	protected bool $enableTenantIsolation = true;
	
	
	/**
	 * 默认隐藏字段
	 *
	 * @var array
	 */
	protected array $defaultHiddenFields = [
		'updated_at',
		'deleted_at',
	];
	
	/**
	 * 构造函数
	 *
	 * @param array|object $data 初始数据
	 */
	public function __construct(array|object $data = [])
	{
		parent::__construct($data);
		
		// 隐藏字段
		$this->hidden = array_merge($this->hidden, $this->defaultHiddenFields);
		
		// 初始化（子类可重写）
		$this->initialize();
	}
	
	/**
	 * 子类实现自定义初始化逻辑
	 *
	 * @return void
	 */
	protected function initialize(): void
	{
		// 子类可以重写此方法实现自定义初始化
	}
	
	/**
	 * 获取租户字段名
	 *
	 * @return string
	 */
	public function getTenantField(): string
	{
		return $this->tenantField;
	}
	
	/**
	 * 获取数据权限字段名
	 *
	 * @return string
	 */
	public function getDataRangeField(): string
	{
		return $this->dataRangeField;
	}
	
	/**
	 * 设置是否启用租户隔离
	 *
	 * @param bool $enable 是否启用
	 * @return $this
	 */
	public function setEnableTenantIsolation(bool $enable): self
	{
		$this->enableTenantIsolation = $enable;
		return $this;
	}
	
	
	/**
	 * 重写获取软删除字段方法
	 *
	 * @param bool $read 是否为读取操作
	 * @return bool|string 如果不启用软删除返回false，否则返回字段名
	 */
	public function getDeleteTimeField(bool $read = false): bool|string
	{
		// 如果禁用软删除，直接返回false
		if (!$this->isSoftDelete) {
			return false;
		}
		
		// 获取软删除字段名
		$field = property_exists($this, 'deleteTime') && isset($this->deleteTime)
			? $this->deleteTime
			: 'deleted_at';
		
		// 处理表前缀
		if (!str_contains($field, '.')) {
			$field = $read
				? '__TABLE__.' . $field
				: $field;
		}
		elseif (!$read && str_contains($field, '.')) {
			$array = explode('.', $field);
			$field = array_pop($array);
		}
		
		return $field;
	}
	
	/**
	 * 检查模型是否有指定字段
	 *
	 * @param string $field 字段名
	 * @return bool
	 */
	public function hasField(string $field): bool
	{
		// 获取模型的所有字段
		$fields = $this->getFields();
		
		// 检查字段是否存在
		return isset($fields[$field]);
	}
	
	/**
	 * 获取字段详细信息
	 *
	 * @return array 字段信息数组
	 */
	public function getFieldsInfo(): array
	{
		static $fieldsInfo = null;
		
		if ($fieldsInfo === null) {
			$fieldsInfo = [];
			
			try {
				// 获取表结构信息
				$tableName = $this->getTable();
				$fields    = Db::query("SHOW FULL COLUMNS FROM `{$tableName}`");
				
				foreach ($fields as $field) {
					$fieldName = $field['Field'];
					$comment   = $field['Comment'] ?? '';
					
					// 解析字段注释中的配置信息
					$config = $this->parseFieldComment($comment);
					
					$fieldsInfo[$fieldName] = [
						'name'      => $fieldName,
						'type'      => $this->parseFieldType($field['Type']),
						'null'      => $field['Null'] === 'YES',
						'key'       => $field['Key'],
						'default'   => $field['Default'],
						'extra'     => $field['Extra'],
						'comment'   => $comment,
						'title'     => $config['title'] ?? $fieldName,
						'validate'  => $config['validate'] ?? [],
						'options'   => $config['options'] ?? [],
						'formatter' => $config['formatter'] ?? null,
					];
				}
			}
			catch (\Exception $e) {
				Log::error('获取字段信息失败: ' . $e->getMessage());
			}
		}
		
		return $fieldsInfo;
	}
	
	/**
	 * 解析字段注释
	 *
	 * @param string $comment 字段注释
	 * @return array 解析后的配置
	 */
	protected function parseFieldComment(string $comment): array
	{
		$config = [];
		
		// 提取标题（注释中第一个空格前的内容）
		if (preg_match('/^([^@\s]+)/', $comment, $matches)) {
			$config['title'] = trim($matches[1]);
		}
		
		// 解析 @rule 验证规则
		if (preg_match('/@rule:([^\s@]+)/', $comment, $matches)) {
			$rules              = explode('|', $matches[1]);
			$config['validate'] = $rules;
		}
		
		// 解析 @options 选项
		if (preg_match('/@options:([^\s@]+)/', $comment, $matches)) {
			$optionsStr = $matches[1];
			$options    = [];
			$pairs      = explode(',', $optionsStr);
			foreach ($pairs as $pair) {
				if (strpos($pair, '=') !== false) {
					list($key, $value) = explode('=', $pair, 2);
					$options[trim($key)] = trim($value);
				}
			}
			$config['options'] = $options;
		}
		
		// 解析格式化器
		if (preg_match('/@formatter:([^\s@]+)/', $comment, $matches)) {
			$config['formatter'] = $matches[1];
		}
		
		return $config;
	}
	
	/**
	 * 解析字段类型
	 *
	 * @param string $type 数据库字段类型
	 * @return string 标准化的字段类型
	 */
	protected function parseFieldType(string $type): string
	{
		// 移除长度限制，只保留基础类型
		$type = preg_replace('/\([^)]*\)/', '', $type);
		$type = strtolower($type);
		
		// 类型映射
		$typeMap = [
			'varchar'    => 'string',
			'char'       => 'string',
			'text'       => 'text',
			'longtext'   => 'text',
			'mediumtext' => 'text',
			'tinytext'   => 'text',
			'int'        => 'integer',
			'tinyint'    => 'integer',
			'smallint'   => 'integer',
			'mediumint'  => 'integer',
			'bigint'     => 'integer',
			'decimal'    => 'decimal',
			'float'      => 'float',
			'double'     => 'float',
			'date'       => 'date',
			'datetime'   => 'datetime',
			'timestamp'  => 'datetime',
			'time'       => 'time',
			'year'       => 'year',
			'enum'       => 'enum',
			'set'        => 'set',
			'json'       => 'json',
		];
		
		return $typeMap[$type] ?? 'string';
	}
	
	/**
	 * 创建记录
	 *
	 * 通过此方法创建记录时会自动填充租户ID和创建者ID
	 * - 如果模型有tenant_id字段，则自动填充当前请求的租户ID
	 * - 如果模型有creator_id字段，则自动填充当前请求的管理员ID
	 * 使用此方法可以明确表示这是一个新增操作，提高代码可读性
	 *
	 * @param array $data 需要保存的数据
	 * @return int 新增记录的主键ID
	 * @throws \Exception 当权限字段验证失败时抛出异常
	 */
	public function saveByCreate(array $data): int
	{
		// 验证和填充权限字段
		$data = $this->validateAndFillPermissionFields($data, true);
		
		// 重置数据确保独立新增
		//		$this->data($data);
		
		// 使用事务确保数据一致性
		$this->startTrans();
		try {
			
			// 执行保存操作，明确指定为新增
			$result = $this->save($data);
			
			if ($result) {
				// 获取新插入的ID
				$pkValue = $this->getData('id');
				
				if ($pkValue) {
					// 提交事务
					$this->commit();
					return intval($pkValue);
				}
			}
			
			// 回滚事务
			$this->rollback();
			throw new \Exception('数据保存失败');
		}
		catch (\Throwable $e) {
			// 回滚事务
			$this->rollback();
			throw new \Exception($e->getMessage());
		}
	}
	
	/**
	 * 更新记录
	 *
	 * 通过此方法更新记录时会自动移除租户ID和创建者ID
	 * - 如果数据中包含tenant_id字段，会自动移除，防止租户隔离被破坏
	 * - 如果数据中包含creator_id字段，会自动移除，防止创建者被修改
	 * 使用此方法可以明确表示这是一个更新操作，提高代码可读性和数据安全性
	 *
	 * @param array $data 需要更新的数据
	 * @return bool 更新是否成功
	 * @throws \Exception 当尝试更新受保护字段时抛出异常
	 */
	public function saveByUpdate(array $data): bool
	{
		// 验证和清理权限字段
		$data = $this->validateAndFillPermissionFields($data, false);
		
		try {
			return $this->save($data);
		}
		catch (\Throwable $e) {
			Log::error('更新记录失败: ' . $e->getMessage(), [
				'model' => get_class($this),
				'id'    => $this->getKey(),
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new \Exception('更新记录失败');
		}
	}
	
	/**
	 * 验证和填充权限字段
	 *
	 * @param array $data     数据数组
	 * @param bool  $isCreate 是否为创建操作
	 * @return array 处理后的数据
	 * @throws \Exception 当权限验证失败时抛出异常
	 */
	protected function validateAndFillPermissionFields(array $data, bool $isCreate): array
	{
		$request  = request();
		$tenantId = $request->tenantId ?? 0;
		$adminId  = $request->adminId ?? 0;
		
		if ($isCreate) {
			// 创建操作：自动填充权限字段
			
			// 验证租户ID的有效性
			if ($tenantId < 0) {
				//				throw new \Exception('无效的租户ID，无法创建记录');
				throw new \Exception('参数错误');
			}
			
			// 验证管理员ID的有效性
			if ($adminId < 0) {
				throw new \Exception('无效的管理员ID');
			}
			
			// 自动填充租户ID
			if ($this->hasField($this->tenantField)) {
				// 如果手动指定了租户ID，验证权限
				if (isset($data[$this->tenantField])) {
					$specifiedTenantId = intval($data[$this->tenantField]);
					if (!is_super_admin() && !is_tenant_super_admin() && $specifiedTenantId !== $tenantId) {
						throw new \Exception('无权限在指定租户下创建记录');
					}
				}
				else {
					// 自动填充当前租户ID
					$data[$this->tenantField] = $tenantId;
				}
			}
			
			// 自动填充创建者ID
			if ($this->hasField($this->dataRangeField)) {
				// 如果手动指定了创建者ID，验证权限
				/*if (isset($data[$this->dataRangeField])) {
					$specifiedCreatorId = intval($data[$this->dataRangeField]);
					if (!is_super_admin() && $specifiedCreatorId !== $adminId) {
						// 检查是否有代理创建权限
						if (!$this->hasProxyCreatePermission($adminId, $specifiedCreatorId, $tenantId)) {
							throw new \Exception('无权限代理其他用户创建记录');
						}
					}
				}
				else {
					// 自动填充当前管理员ID
					$data[$this->dataRangeField] = $adminId;
				}*/
				$data[$this->dataRangeField] = $adminId;
			}
		}
		else {
			// 更新操作：移除受保护的权限字段
			
			$protectedFields = [];
			
			// 检查是否尝试修改租户ID
			if ($this->hasField($this->tenantField) && isset($data[$this->tenantField])) {
				$protectedFields[] = $this->tenantField;
				unset($data[$this->tenantField]);
			}
			
			// 检查是否尝试修改创建者ID
			if ($this->hasField($this->dataRangeField) && isset($data[$this->dataRangeField])) {
				$protectedFields[] = $this->dataRangeField;
				unset($data[$this->dataRangeField]);
			}
			
			// 记录受保护字段的修改尝试
			if (!empty($protectedFields)) {
				Log::warning('尝试修改受保护的权限字段', [
					'model'            => get_class($this),
					'admin_id'         => $adminId,
					'tenant_id'        => $tenantId,
					'protected_fields' => $protectedFields,
					'record_id'        => $this->getKey()
				]);
			}
		}
		if (isset($data['id'])) {
			unset($data['id']);
		}
		return $data;
	}
	
	/**
	 * 检查是否有代理创建权限
	 *
	 * @param int $adminId         当前管理员ID
	 * @param int $targetCreatorId 目标创建者ID
	 * @param int $tenantId        租户ID
	 * @return bool
	 */
	protected function hasProxyCreatePermission(int $adminId, int $targetCreatorId, int $tenantId): bool
	{
		// 这里可以实现具体的代理权限检查逻辑
		// 例如：检查是否为上级部门、是否有特殊权限等
		
		try {
			// 获取当前用户的数据权限范围
			$dataPermissionIds = \app\common\utils\DataPermissionCacheUtil::getUserDataPermission($adminId, $tenantId);
			
			// 如果目标创建者在当前用户的数据权限范围内，则允许代理创建
			return in_array($targetCreatorId, $dataPermissionIds);
		}
		catch (\Exception $e) {
			Log::error('检查代理创建权限失败: ' . $e->getMessage(), [
				'admin_id'          => $adminId,
				'target_creator_id' => $targetCreatorId,
				'tenant_id'         => $tenantId
			]);
			return false;
		}
	}
	
	/**
	 * 批量保存记录
	 *
	 * 优化批量插入，每次最多插入100条，如果大于100条，就分批插入
	 * 自动处理租户隔离和创建人信息，确保数据权限的完整性
	 * 自动使用事务处理，确保数据一致性，发生异常时自动回滚
	 *
	 * @param array $dataList 数据列表
	 * @param int   $limit    每批次插入的最大数量
	 * @return int 成功插入的数量或false
	 */
	public function batchSave(array $dataList, int $limit = 100)
	{
		// 空数据直接返回0
		$res = 0;
		if (empty($dataList)) {
			return $res;
		}
		
		// 预处理数据：为每条记录填充权限字段
		$processedDataList = [];
		foreach ($dataList as $data) {
			$processedData       = $this->validateAndFillPermissionFields($data, true);
			$processedDataList[] = $processedData;
		}
		
		$totalCount  = count($processedDataList);
		$insertCount = 0;
		
		// 开启事务
		Db::startTrans();
		try {
			// 根据数据量选择处理方式
			if ($totalCount <= $limit) {
				// 数据量在限制范围内，直接插入
				$insertCount = $this->processBatch($processedDataList, $limit);
			}
			else {
				// 数据量超过限制，分批处理
				$batches = array_chunk($processedDataList, $limit);
				foreach ($batches as $batchData) {
					$insertCount += $this->processBatch($batchData, $limit);
				}
			}
			
			// 提交事务
			Db::commit();
			$res = $insertCount;
		}
		catch (\Throwable $e) {
			// 回滚事务
			Db::rollback();
			// 记录错误日志
			Log::error('批量保存失败: ' . $e->getMessage(), [
				'model'      => get_class($this),
				'data_count' => $totalCount,
				'trace'      => $e->getTraceAsString()
			]);
			throw new \Exception('批量保存失败：' . $e->getMessage());
		}
		
		return $res;
	}
	
	/**
	 * 处理单批次数据插入
	 *
	 * 内部方法，用于batchSave的代码复用
	 *
	 * @param array $batch 单批次数据
	 * @param int   $limit 批次大小限制
	 * @return int 成功插入的记录数
	 * @throws DbException 插入失败时抛出异常
	 */
	protected function processBatch(array $batch, int $limit): int
	{
		$result = $this->insertAll($batch, $limit);
		if (!$result) {
			throw new DbException('批量插入失败');
		}
		return $result;
	}
	
} 