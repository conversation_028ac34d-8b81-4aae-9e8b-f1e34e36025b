<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

/**
 * 控制器导入导出功能Trait
 *
 * 提供导入导出相关的控制器方法，方便集成到现有控制器
 */
trait ControllerImportExportTrait
{
	/**
	 * 获取导出字段配置
	 */
	public function exportFields()
	{
		if (!method_exists($this->service, 'getExportFields')) {
			return $this->error('当前模块不支持导出功能');
		}
		
		try {
			$scene  = $this->request->param('scene', '');
			$fields = $this->service->getExportFields($scene);
			return $this->success('获取成功', $fields);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 导出数据
	 */
	public function export()
	{
		if (!method_exists($this->service, 'exportData')) {
			return $this->error('当前模块不支持导出功能');
		}
		
		try {
			$params = $this->request->param();
			$scene  = $this->request->param('scene', '');
			$format = $this->request->param('format', 'xlsx');
			
			$file     = $this->service->exportData($params, $scene, $format);
			$fileName = basename($file);
			
			// 返回文件下载URL
			$downloadUrl = $this->buildDownloadUrl('download_export', ['file' => $fileName]);
			
			return $this->success('导出成功', [
				'url'  => $downloadUrl,
				'file' => $fileName
			]);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 下载导出文件
	 */
	public function downloadExport()
	{
		try {
			$fileName = $this->request->param('file');
			if (empty($fileName)) {
				return $this->error('文件名不能为空');
			}
			
			$filePath = runtime_path() . 'export/' . $fileName;
			if (!file_exists($filePath)) {
				return $this->error('文件不存在');
			}
			
			return download($filePath, $fileName);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取导入字段配置
	 */
	public function importFields()
	{
		if (!method_exists($this->service, 'getImportFields')) {
			return $this->error('当前模块不支持导入功能');
		}
		
		try {
			$scene  = $this->request->param('scene', '');
			$fields = $this->service->getImportFields($scene);
			return $this->success('获取成功', $fields);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取导入模板
	 */
	public function importTemplate()
	{
		if (!method_exists($this->service, 'generateImportTemplate')) {
			return $this->error('当前模块不支持导入功能');
		}
		
		try {
			$scene    = $this->request->param('scene', '');
			$file     = $this->service->generateImportTemplate($scene);
			$fileName = basename($file);
			
			// 返回文件下载URL
			$downloadUrl = $this->buildDownloadUrl('download_template', ['file' => $fileName]);
			
			return $this->success('生成成功', [
				'url'  => $downloadUrl,
				'file' => $fileName
			]);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 下载导入模板
	 */
	public function downloadTemplate()
	{
		try {
			$fileName = $this->request->param('file');
			if (empty($fileName)) {
				return $this->error('文件名不能为空');
			}
			
			$filePath = runtime_path() . 'import/' . $fileName;
			if (!file_exists($filePath)) {
				return $this->error('文件不存在');
			}
			
			return download($filePath, $fileName);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 上传并导入数据
	 */
	public function import()
	{
		if (!method_exists($this->service, 'importData')) {
			return $this->error('当前模块不支持导入功能');
		}
		
		try {
			$file = $this->request->file('file');
			if (!$file) {
				return $this->error('请上传文件');
			}
			
			// 检查文件类型
			$ext      = $file->getOriginalExtension();
			$allowExt = [
				'xlsx',
				'xls',
				'csv'
			];
			if (!in_array($ext, $allowExt)) {
				return $this->error('上传文件类型错误，仅支持' . implode('/', $allowExt) . '格式');
			}
			
			// 获取上传配置
			$config = $this->request->param('config/a', []);
			
			// 导入数据
			$result = $this->service->importData($file->getRealPath(), $config);
			
			return $this->success('导入成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 构建下载URL
	 *
	 * @param string $action 下载动作名
	 * @param array  $params URL参数
	 * @return string 下载URL
	 */
	protected function buildDownloadUrl(string $action, array $params = []): string
	{
		// 获取当前控制器名称和当前模块
		$controllerClass = get_class($this);
		$moduleName      = app('http')->getName();
		
		// 解析控制器名称
		$controllerNameParts = explode('\\', $controllerClass);
		$controllerName      = str_replace('Controller', '', end($controllerNameParts));
		
		// 转换驼峰命名为下划线命名
		$controllerName = $this->camelToSnake($controllerName);
		
		// 构建URL - 包含 /api/ 前缀，确保没有双斜杠
		$baseUrl = rtrim(request()->domain(), '/');
		$url     = $baseUrl . '/api/' . $moduleName . '/' . $controllerName . '/' . $action;
		
		// 添加参数
		if (!empty($params)) {
			$url .= '?' . http_build_query($params);
		}
		
		return $url;
	}
	
	/**
	 * 将驼峰命名转换为下划线命名
	 */
	private function camelToSnake(string $input): string
	{
		return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
	}
} 