<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;

/**
 * 用户岗位关联模型
 */
class AdminPostModel extends BaseModel
{
    
    // 设置表名
    protected $name = 'system_admin_post';
    
    // 设置字段信息
    protected $schema = [
        'id'         => 'int',
        'admin_id'    => 'int',
        'post_id'    => 'int',
        'creator_id' => 'int',
        'updater_id' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'tenant_id'  => 'int',
    ];
    
    // 关联用户
    public function user()
    {
        return $this->belongsTo(AdminModel::class, 'admin_id', 'id');
    }
    
    // 关联岗位
    public function post()
    {
        return $this->belongsTo(JobModelModel::class, 'post_id', 'id');
    }
} 