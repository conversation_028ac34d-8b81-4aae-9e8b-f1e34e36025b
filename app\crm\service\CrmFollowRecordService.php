<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmFollowRecord;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 跟进记录表服务类
 */
class CrmFollowRecordService extends BaseService
{
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmFollowRecord();
		parent::__construct();
	}
	
	/**
	 * 初始化配置
	 */
	protected function initialize(): void
	{
		// 跟进记录需要数据权限过滤
		$this->crudService->setEnableDataPermission(true);
		
		// 设置字段场景
		$this->crudService->setFieldScenes([
			'list'   => [
				'id',
				'related_type',
				'related_id',
				'follow_type',
				'content',
				'follow_date',
				'next_plan',
				'next_date',
				'attachments',
				'creator_id',
				'created_at'
			],
			'detail' => ['*'],
			'select' => [
				'id',
				'content',
				'follow_date'
			]
		]);
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'related_type' => ['type' => 'eq'],
			
			'related_id' => ['type' => 'eq'],
			
			'follow_type' => ['type' => 'eq'],
			
			'follow_date' => ['type' => 'date'],
			
			'next_date' => ['type' => 'date'],
		
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			// 在这里定义验证规则
			// 例如：'username' => 'require|unique:crm_follow_record',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	public function getFollowRecordList($params): array
	{
		// 拼接where
		$page  = intval($params['page'] ?? 1);
		$limit = intval($params['limit'] ?? 10);
		$where = [];
		if (!empty($params['related_type'])) {
			$where[] = [
				'related_type',
				'=',
				$params['related_type']
			];
		}
		if (!empty($params['follow_type'])) {
			$where[] = [
				'follow_type',
				'=',
				$params['follow_type']
			];
		}
		if (!empty($params['next_date'])) {
			// 日期查询,$params['next_date']格式:2025-07-15
			$startDate = $params['next_date'] . ' 00:00:00';
			$endDate   = $params['next_date'] . ' 23:59:59';
			
			$where[] = [
				'next_date',
				'between',
				[
					$startDate,
					$endDate
				]
			];
		}
		$list = $this->crudService->getPageList($where, [
			'created_at' => 'desc'
		], $page, $limit, ['creator']);
		
		foreach ($list as &$item) {
			$item['creator'] = $item->creator;
			$related_type    = $item->related_type;
			$related_name    = '';
			if ($related_type == 'lead') {
				$related_name = $item->lead->lead_name ?? '';
			}
			elseif ($related_type == 'customer') {
				$related_name = $item->customer->customer_name ?? '';
			}
			elseif ($related_type == 'business') {
				$related_name = $item->business->business_name ?? '';
			}
			$item['related_name'] = $related_name;
		}
		return [
			'list'  => $list,
			'total' => CrmFollowRecord::where($where)
			                          ->count(),
			'page'  => $page,
			'limit' => $limit
		];
	}
} 