<?php

namespace app\common\lib\upload\driver;

use app\common\lib\upload\UploadStorageInterface;
use Qcloud\Cos\Client;

class TencentDriver implements UploadStorageInterface
{
	public function getUploadToken(array $config, array $params = []): string|array
	{
		$client = new Client([
			'region' => $config['region'],
			'schema' => 'https',
			'credentials' => [
				'secretId' => $config['secret_id'],
				'secretKey' => $config['secret_key'],
			],
		]);
		
		// 生成预签名上传URL
		$fileName = md5(microtime(true) . mt_rand(1000, 9999)) . '.' . ($params['extension'] ?? 'tmp');
		$savePath = $config['base_path'] . date('Y/m/d') . '/' . $fileName;
		
		$signedUrl = $client->getObjectUrl(
			$config['bucket'],
			$savePath,
			'+30 minutes',
			[
				'params' => [
					'tenant_id' => $params['tenant_id'] ?? 0,
					'cate_id' => $params['cate_id'] ?? 0,
				],
			]
		);
		
		return [
			'token' => $signedUrl,
			'key' => $savePath,
			'domain' => $config['domain'],
		];
	}
	
	public function upload(array $file, array $config): array
	{
		$client = new Client([
			'region' => $config['region'],
			'schema' => 'https',
			'credentials' => [
				'secretId' => $config['secret_id'],
				'secretKey' => $config['secret_key'],
			],
		]);
		
		$fileName = md5(microtime(true) . mt_rand(1000, 9999)) . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
		$savePath = $config['base_path'] . date('Y/m/d') . '/' . $fileName;
		
		$result = $client->putObject([
			'Bucket' => $config['bucket'],
			'Key' => $savePath,
			'Body' => fopen($file['tmp_name'], 'rb'),
		]);
		
		return [
			'name' => $file['name'],
			'real_name' => $file['name'],
			'path' => $savePath,
			'url' => $config['domain'] . '/' . $savePath,
			'extension' => pathinfo($file['name'], PATHINFO_EXTENSION),
			'size' => $file['size'],
			'mime_type' => $file['type'],
			'storage' => 'txoss',
			'storage_id' => $result['ETag'],
		];
	}
	
	public function delete(string $filePath, array $config): bool
	{
		$client = new Client([
			'region' => $config['region'],
			'schema' => 'https',
			'credentials' => [
				'secretId' => $config['secret_id'],
				'secretKey' => $config['secret_key'],
			],
		]);
		
		$client->deleteObject([
			'Bucket' => $config['bucket'],
			'Key' => $filePath,
		]);
		
		return true;
	}
	
	public function callback(array $params, array $config): array
	{
		// 腾讯云主要使用前端直传+后端确认的方式，不依赖回调
		return [];
	}
}