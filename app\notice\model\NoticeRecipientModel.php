<?php
declare(strict_types=1);

namespace app\notice\model;

use app\common\core\base\BaseModel;

/**
 * 消息接收表模型
 *
 * @property int    $id                 ID
 * @property int    $message_id         消息ID
 * @property int    $user_id            用户ID
 * @property string $user_name          用户姓名
 * @property int    $read_status        读取状态：0未读，1已读
 * @property string $read_time          阅读时间
 * @property string $read_client        阅读终端：pc桌面端,mobile移动端,wework企业微信,dingtalk钉钉
 * @property int    $site_delivered     站内信是否已投递：0否，1是
 * @property int    $email_delivered    邮件是否已投递：0否，1是
 * @property int    $sms_delivered      短信是否已投递：0否，1是
 * @property int    $wework_delivered   微信是否已投递：0否，1是
 * @property int    $dingtalk_delivered 钉钉是否已投递：0否，1是
 * @property int    $is_deleted         是否删除：0否，1是
 * @property int    $creator_id         创建人
 * @property string $created_at         创建时间
 * @property string $updated_at         更新时间
 * @property int    $tenant_id          租户ID
 */
class NoticeRecipientModel extends BaseModel
{
	/**
	 * 数据表名称
	 *
	 * @var string
	 */
	protected $name = 'notice_recipient';
	
	/**
	 * 自动写入时间戳
	 *
	 * @var bool
	 */
	protected $autoWriteTimestamp = true;
	
	/**
	 * 创建时间字段
	 *
	 * @var string
	 */
	protected $createTime = 'created_at';
	
	/**
	 * 更新时间字段
	 *
	 * @var string
	 */
	protected $updateTime = 'updated_at';
	
	protected bool $isSoftDelete = false;
	
	/**
	 * 字段类型转换
	 *
	 * @var array
	 */
	protected $type = [
		'id'                 => 'integer',
		'message_id'         => 'integer',
		'user_id'            => 'integer',
		'read_status'        => 'integer',
		'read_time'          => 'datetime',
		'site_delivered'     => 'integer',
		'email_delivered'    => 'integer',
		'sms_delivered'      => 'integer',
		'wework_delivered'   => 'integer',
		'dingtalk_delivered' => 'integer',
		'is_deleted'         => 'integer',
		'creator_id'         => 'integer',
		'created_at'         => 'datetime',
		'updated_at'         => 'datetime',
		'tenant_id'          => 'integer',
	
	];
	
	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'user_name'   => ['type' => 'like'],
			'read_status' => ['type' => 'eq'],
			'read_time'   => ['type' => 'date'],
			'created_at'  => ['type' => 'date'],
			'updated_at'  => ['type' => 'date'],
		];
	}
	
	/**
	 * 获取允许单字段编辑的字段
	 *
	 * @return array
	 */
	public function getAllowUpdateFields(): array
	{
		return [
			'message_id',
			'user_id',
			'user_name',
			'read_status',
			'read_time',
			'read_client',
			'site_delivered',
			'email_delivered',
			'sms_delivered',
			'wework_delivered',
			'dingtalk_delivered',
			'is_deleted',
		];
	}
	
	/**
	 * 获取允许排序的字段
	 *
	 * @return array
	 */
	public function getAllowSortFields(): array
	{
		return [
			'id',
			'created_at',
			'updated_at',
			'read_time',
		];
	}

	/**
	 * 关联消息表
	 *
	 * @return \think\model\relation\BelongsTo
	 */
	public function message()
	{
		return $this->belongsTo(NoticeMessageModel::class, 'message_id', 'id');
	}
}