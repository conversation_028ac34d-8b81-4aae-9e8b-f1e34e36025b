# 产品关联供应商功能测试清单

## 🎯 实施完成情况

### ✅ 已完成的工作

#### 阶段一：数据库结构调整
- [x] 创建数据库迁移脚本 `database/migrations/add_supplier_to_product.sql`
- [x] 为产品表添加 `supplier_id` 字段
- [x] 添加索引和外键约束

#### 阶段二：后端模型层适配
- [x] 更新 `CrmProduct` 模型
  - [x] 添加 `supplier_id` 字段类型转换
  - [x] 添加 `supplier` 关联方法
  - [x] 更新 `$append` 属性
- [x] 更新 `ImsSupplier` 模型
  - [x] 添加 `products` 关联方法
  - [x] 添加 `canDelete()` 检查方法
  - [x] 添加 `getProductCountAttr()` 属性

#### 阶段三：后端服务层适配
- [x] 更新 `CrmProductService` 服务
  - [x] 重写 `getList()` 方法包含供应商信息
  - [x] 添加 `getProductsBySupplier()` 方法
  - [x] 添加 `getProductOptions()` 方法
  - [x] 重写 `delete()` 方法添加关联检查
  - [x] 添加 `disable()` 方法
  - [x] 更新验证规则添加供应商必填
  - [x] 更新搜索字段配置
- [x] 更新 `ImsSupplierService` 服务
  - [x] 重写 `delete()` 方法添加关联检查
  - [x] 添加 `getOptions()` 方法
  - [x] 重写 `batchDelete()` 方法添加关联检查

#### 阶段四：后端控制器适配
- [x] 更新 `CrmProductController` 控制器
  - [x] 添加 `getSupplierOptions()` 方法
  - [x] 更新 `selector()` 方法支持供应商筛选
  - [x] 添加 `disable()` 方法
- [x] 更新 `ImsSupplierController` 控制器
  - [x] 重写 `delete()` 方法处理关联检查
  - [x] 重写 `batchDelete()` 方法处理关联检查
  - [x] 添加 `options()` 方法

#### 阶段五：前端界面适配
- [x] 创建 `SupplierSelector.vue` 组件
- [x] 更新产品表单对话框 `form-dialog.vue`
  - [x] 添加供应商选择器
  - [x] 更新表单数据和验证规则
  - [x] 添加供应商变更处理函数
- [x] 更新产品列表页面 `list.vue`
  - [x] 添加供应商搜索筛选
  - [x] 添加供应商列显示

## 🧪 测试步骤

### 1. 数据库测试
```sql
-- 执行数据库迁移脚本
source database/migrations/add_supplier_to_product.sql;

-- 验证表结构
DESCRIBE crm_product;

-- 验证外键约束
SHOW CREATE TABLE crm_product;
```

### 2. 后端API测试

#### 2.1 供应商管理测试
```bash
# 获取供应商选项
GET /ims/ims_supplier/options

# 删除有产品的供应商（应该失败）
DELETE /ims/ims_supplier/delete/{id}

# 批量删除有产品的供应商（应该失败）
POST /ims/ims_supplier/batchDelete
```

#### 2.2 产品管理测试
```bash
# 创建产品（必须包含supplier_id）
POST /crm/crm_product/add
{
  "name": "测试产品",
  "category_id": 1,
  "unit_id": 1,
  "supplier_id": 1
}

# 获取产品列表（应包含供应商信息）
GET /crm/crm_product/list

# 按供应商筛选产品
GET /crm/crm_product/list?supplier_id=1

# 产品选择器（支持供应商筛选）
GET /crm/crm_product/selector?supplier_id=1

# 删除有关联数据的产品（应该失败）
DELETE /crm/crm_product/delete/{id}

# 停用产品
POST /crm/crm_product/disable/{id}
```

### 3. 前端界面测试

#### 3.1 产品表单测试
- [ ] 打开产品添加表单
- [ ] 验证供应商选择器是否正常加载选项
- [ ] 验证供应商字段必填验证
- [ ] 提交表单验证数据是否正确保存

#### 3.2 产品列表测试
- [ ] 打开产品列表页面
- [ ] 验证供应商列是否正常显示
- [ ] 测试供应商筛选功能
- [ ] 验证搜索表单中的供应商选择器

#### 3.3 供应商管理测试
- [ ] 尝试删除有产品的供应商
- [ ] 验证错误提示是否友好
- [ ] 测试批量删除功能

### 4. 数据完整性测试

#### 4.1 外键约束测试
```sql
-- 尝试插入无效的供应商ID（应该失败）
INSERT INTO crm_product (name, category_id, unit_id, supplier_id) 
VALUES ('测试产品', 1, 1, 999);

-- 尝试删除有产品的供应商（应该失败）
DELETE FROM ims_supplier WHERE id = 1;
```

#### 4.2 业务逻辑测试
- [ ] 创建产品后尝试删除供应商
- [ ] 创建商机产品关联后尝试删除产品
- [ ] 创建合同产品关联后尝试删除产品
- [ ] 创建报价记录后尝试删除产品

## 🚨 注意事项

### 数据库执行顺序
1. 先执行数据库迁移脚本
2. 确保有测试用的供应商数据
3. 再测试产品相关功能

### 测试数据准备
```sql
-- 插入测试供应商
INSERT INTO ims_supplier (name, code, contact_name, status) 
VALUES 
('测试供应商A', 'SUP001', '张三', 1),
('测试供应商B', 'SUP002', '李四', 1);

-- 插入测试产品分类
INSERT INTO crm_product_category (name, status) 
VALUES ('测试分类', 1);

-- 插入测试产品单位
INSERT INTO crm_product_unit (unit_name, status) 
VALUES ('个', 1);
```

### 错误处理验证
- [ ] 验证删除有关联数据时的错误提示
- [ ] 验证表单验证错误提示
- [ ] 验证API错误响应格式

## 📝 预期结果

### 成功标准
1. 所有API接口正常响应
2. 前端界面正常显示和交互
3. 数据完整性约束正常工作
4. 错误提示友好且准确
5. 搜索和筛选功能正常

### 性能要求
1. 产品列表加载时间 < 2秒
2. 供应商选择器加载时间 < 1秒
3. 表单提交响应时间 < 1秒

## 🔧 故障排除

### 常见问题
1. **外键约束错误**：检查供应商数据是否存在
2. **前端组件不显示**：检查组件导入路径
3. **API接口404**：检查路由配置
4. **表单验证失败**：检查验证规则配置

### 调试建议
1. 查看浏览器控制台错误
2. 查看网络请求响应
3. 查看后端日志
4. 检查数据库约束
