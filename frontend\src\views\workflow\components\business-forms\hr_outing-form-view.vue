<template>
  <div class="outing-form-view">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="开始时间">
        {{ formData.start_time || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="结束时间">
        {{ formData.end_time || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="外出时长">
        {{ formData.duration || 0 }} 小时
      </el-descriptions-item>

      <el-descriptions-item label="外出事由" :span="2">
        {{ formData.purpose || '-' }}
      </el-descriptions-item>

      <el-descriptions-item
        label="附件"
        :span="2"
        v-if="attachmentList && attachmentList.length > 0"
      >
        <div class="attachment-list">
          <div v-for="(item, index) in attachmentList" :key="index" class="attachment-item">
            <!-- 图片类型显示预览 -->
            <div v-if="isImageFile(item.url)" class="image-attachment">
              <el-image
                :src="item.url"
                :preview-src-list="imageUrls"
                :initial-index="getImageIndex(item.url)"
                fit="cover"
                class="attachment-image"
                :alt="item.name"
                preview-teleported
                :hide-on-click-modal="true"
              />
            </div>
            <!-- 非图片类型显示链接 -->
            <div v-else class="file-attachment">
              <el-link :href="item.url" target="_blank" type="primary">
                {{ item.name }}
              </el-link>
              <span v-if="item.size" class="file-size">({{ formatFileSize(item.size) }})</span>
            </div>
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem, ElLink, ElImage } from 'element-plus'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'hr_outing'
    }
  })

  // 附件项接口定义
  interface AttachmentItem {
    url: string
    name: string
    size: number | null
  }

  // 处理附件数据，支持字符串数组和对象数组
  const attachmentList = computed((): AttachmentItem[] => {
    if (!props.formData.attachment) return []

    const attachments = Array.isArray(props.formData.attachment)
      ? props.formData.attachment
      : [props.formData.attachment]

    return attachments
      .map((item: any) => {
        if (typeof item === 'string') {
          // 如果是字符串URL，构造文件对象
          const url = item
          const fileName = getFileNameFromUrl(url)
          return {
            url,
            name: fileName,
            size: null
          }
        } else if (typeof item === 'object' && item.url) {
          // 如果是文件对象，直接使用
          return {
            url: item.url,
            name: item.name || getFileNameFromUrl(item.url),
            size: item.size || null
          }
        }
        return null
      })
      .filter((item): item is AttachmentItem => item !== null)
  })

  // 获取所有图片URL用于预览
  const imageUrls = computed(() => {
    return attachmentList.value.filter((item) => isImageFile(item.url)).map((item) => item.url)
  })

  // 从URL中提取文件名
  const getFileNameFromUrl = (url: string): string => {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname
      const fileName = pathname.split('/').pop() || 'unknown'

      // 如果文件名看起来像哈希值，尝试生成更友好的名称
      if (/^[a-f0-9]{32}\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName)) {
        const ext = fileName.split('.').pop()
        return `图片文件.${ext}`
      }

      return fileName
    } catch {
      return '附件文件'
    }
  }

  // 判断是否为图片文件
  const isImageFile = (url: string): boolean => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
    const extension = url.split('.').pop()?.toLowerCase()
    return extension ? imageExtensions.includes(extension) : false
  }

  // 获取图片在预览列表中的索引
  const getImageIndex = (url: string): number => {
    return imageUrls.value.indexOf(url)
  }

  /**
   * 格式化文件大小
   */
  const formatFileSize = (size: number | null): string => {
    if (!size) return ''
    const units = ['B', 'KB', 'MB', 'GB']
    let index = 0
    let fileSize = size
    while (fileSize >= 1024 && index < units.length - 1) {
      fileSize /= 1024
      index++
    }
    return `${fileSize.toFixed(1)}${units[index]}`
  }
</script>

<style scoped lang="scss">
  .attachment-list {
    .attachment-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      // 图片附件样式
      .image-attachment {
        display: inline-block;
        margin-right: 12px;
        margin-bottom: 12px;

        .attachment-image {
          width: 80px;
          height: 80px;
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          cursor: pointer;

          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }
        }
      }

      // 文件附件样式
      .file-attachment {
        display: flex;
        align-items: center;
        gap: 8px;

        .file-size {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
</style>
