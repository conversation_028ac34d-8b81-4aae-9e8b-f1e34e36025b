# 前端表单适配状态报告

## 📋 适配概述

**适配时间：** 2025-07-28  
**适配原则：** 完全按照现有 `hr_leave-form.vue` 的结构和接口进行适配  
**目标目录：** `frontend/src/views/workflow/components/business-forms/`  

## ✅ 已完成适配（4个业务类型）

### 1. **hr_outing** - 外出申请 ✅
- ✅ `hr_outing-form.vue` - 表单组件
- ✅ `hr_outing-form-view.vue` - 详情组件

**表单字段：**
- 开始时间、结束时间（自动计算时长）
- 外出时长（小时）
- 外出目的（必填）
- 备注

### 2. **ims_outbound_approval** - 出库申请 ✅
- ✅ `ims_outbound_approval-form.vue` - 表单组件
- ✅ `ims_outbound_approval-form-view.vue` - 详情组件

**表单字段：**
- 出库单号、出库类型、出库日期
- 出库仓库、客户名称、所在部门
- 出库明细表格（产品、数量、单价、小计）
- 总数量、总金额自动计算
- 备注

### 3. **finance_payment_approval** - 付款申请 ✅
- ✅ `finance_payment_approval-form.vue` - 表单组件
- ✅ `finance_payment_approval-form-view.vue` - 详情组件

**表单字段：**
- 付款单号、支付对象
- 收款账号、收款银行
- 付款金额、支付日期、付款方式
- 付款事由（必填）
- 备注

### 4. **office_sample_mail** - 样品邮寄申请 ✅
- ✅ `office_sample_mail-form.vue` - 表单组件
- ✅ `office_sample_mail-form-view.vue` - 详情组件

**表单字段：**
- 样品名称（必填）
- 样品描述（必填）
- 寄件人电话
- 备注

## 🔧 待创建适配（5个业务类型）

### 5. **ims_inbound_approval** - 入库申请 🔧
- [ ] `ims_inbound_approval-form.vue` - 表单组件
- [ ] `ims_inbound_approval-form-view.vue` - 详情组件

### 6. **ims_shipment_approval** - 出货申请 🔧
- [ ] `ims_shipment_approval-form.vue` - 表单组件
- [ ] `ims_shipment_approval-form-view.vue` - 详情组件

### 7. **ims_purchase_approval** - 采购申请 🔧
- [ ] `ims_purchase_approval-form.vue` - 表单组件
- [ ] `ims_purchase_approval-form-view.vue` - 详情组件

### 8. **finance_expense_reimbursement** - 报销申请 🔧
- [ ] `finance_expense_reimbursement-form.vue` - 表单组件
- [ ] `finance_expense_reimbursement-form-view.vue` - 详情组件

### 9. **hr_business_trip** - 出差申请 🔧
- [ ] `hr_business_trip-form.vue` - 表单组件
- [ ] `hr_business_trip-form-view.vue` - 详情组件

## 🏗️ 技术实现规范

### **统一接口规范**
所有表单组件都严格按照 `hr_leave-form.vue` 的接口实现：

```typescript
// Props
interface Props {
  modelValue: boolean
  formId?: number | string
  definitionId?: number | string
}

// Events
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
  (e: 'save', data: any): void
  (e: 'submit', data: any): void
}

// 暴露方法
defineExpose({
  showForm,      // 供FormManager调用
  setFormData,   // 供FormManager调用
  formRef,
  formData,
  saving,
  submitting
})
```

### **表单加载机制**
```typescript
// FormManager调用方式
import(`../components/business-forms/${formState.type}-form.vue`)

// 详情查看调用方式
import(`../components/business-forms/${props.businessCode}-form-view.vue`)
```

### **数据处理流程**
1. **showForm(id?)** - 显示表单，如果有ID则加载数据
2. **setFormData(data)** - 设置表单数据
3. **handleSave()** - 保存草稿，emit('save', data)
4. **handleSubmit()** - 提交审批，emit('submit', data)

### **状态管理**
```typescript
// 可编辑状态判断
const isEditable = computed(() => {
  return !formData.approval_status || 
         formData.approval_status === 0 ||  // 草稿
         formData.approval_status === 3     // 已拒绝
})
```

## 📊 适配进度统计

| 状态 | 数量 | 业务类型 |
|------|------|----------|
| ✅ **已完成** | 4个 | hr_outing, ims_outbound_approval, finance_payment_approval, office_sample_mail |
| 🔧 **待创建** | 5个 | ims_inbound_approval, ims_shipment_approval, ims_purchase_approval, finance_expense_reimbursement, hr_business_trip |
| 📊 **总计** | 9个 | 全部新增业务类型 |

**完成度：** 44.4% (4/9)

## 🎯 特殊功能实现

### **明细表格支持**
- ✅ **ims_outbound_approval** - 出库明细表格（产品选择、数量、单价、小计）
- 🔧 **ims_inbound_approval** - 入库明细表格（待实现）
- 🔧 **ims_purchase_approval** - 采购明细表格（待实现）
- 🔧 **finance_expense_reimbursement** - 报销明细表格（待实现）
- 🔧 **hr_business_trip** - 行程明细表格（待实现）

### **自动计算功能**
- ✅ **hr_outing** - 根据开始/结束时间自动计算时长
- ✅ **ims_outbound_approval** - 自动计算总数量和总金额
- 🔧 其他表单的自动计算功能（待实现）

### **数据验证规则**
- ✅ 必填字段验证
- ✅ 数据类型验证（数字、日期等）
- ✅ 业务规则验证（金额>0、时长>0等）
- ✅ 明细数据验证（至少一条明细等）

## 🚀 测试验证

### **已验证功能**
- ✅ 表单组件动态加载（解决了原始错误）
- ✅ 详情组件正常显示
- ✅ 表单数据保存和提交
- ✅ 表单验证规则生效
- ✅ 编辑状态控制正确

### **测试用例**
1. **新建表单** - 空表单显示和数据输入
2. **编辑表单** - 加载现有数据和修改
3. **只读查看** - 已审批表单的只读显示
4. **数据验证** - 必填字段和格式验证
5. **明细操作** - 添加、删除、计算明细

## 📝 下一步计划

1. **继续创建剩余5个表单组件**
2. **完善明细表格功能**
3. **集成数据选项API**
4. **完善表单验证规则**
5. **进行全面测试验证**

---

**前端表单适配** | **严格按照现有规范** | **4/9已完成**
