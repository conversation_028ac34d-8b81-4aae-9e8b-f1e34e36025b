<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmProductUnitService;

/**
 * 产品单位表控制器
 */
class CrmProductUnitController extends BaseController
{
    use CrudControllerTrait, ControllerImportExportTrait;
    /**
     * @var CrmProductUnitService
     */
    protected $service;

    /**
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();

        // 使用单例模式获取Service实例
        $this->service = CrmProductUnitService::getInstance();
    }

    /**
     * 获取产品单位选项
     */
    public function options()
    {
        $result = $this->service->getOptions();
        return $this->success('获取成功', $result);
    }

    /**
     * 状态切换
     */
    public function status($id)
    {
        $status = $this->request->post('status');
        $result = $this->service->updateField($id, 'status', $status);
        return $this->success('状态更新成功', $result);
    }

}