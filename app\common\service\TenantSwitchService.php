<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;
use app\system\model\TenantModel;
use app\common\utils\TokenUtil;

/**
 * 租户切换服务
 * 为系统超级管理员提供租户切换功能
 * 使用 Cache 存储，与系统 Token 机制保持一致
 */
class TenantSwitchService
{
	/**
	 * 工作模式常量
	 */
	const MODE_SYSTEM = 'system';      // 系统管理模式
	const MODE_TENANT = 'tenant';      // 租户管理模式
	
	/**
	 * 缓存键前缀
	 */
	const CACHE_PREFIX = 'tenant_switch:';
	
	/**
	 * 获取当前管理员ID
	 */
	protected function getCurrentAdminId(): int
	{
		return request()->adminId ?? 0;
	}
	
	/**
	 * 获取当前 Token 的过期时间
	 */
	protected function getTokenExpire(): int
	{
		$token = request()->header('Authorization');
		if (!$token) {
			return 3600; // 默认1小时
		}
		
		$tokenInfo = TokenUtil::getTokenInfo($token);
		if (!$tokenInfo) {
			return 3600; // 默认1小时
		}
		
		// 计算剩余过期时间
		$createTime    = $tokenInfo['create_time'] ?? time();
		$currentTime   = time();
		$defaultExpire = 7200; // Token 默认2小时
		
		$remainingTime = $defaultExpire - ($currentTime - $createTime);
		return max($remainingTime, 300); // 最少5分钟
	}
	
	/**
	 * 生成缓存键
	 */
	protected function getCacheKey(string $type): string
	{
		$adminId = $this->getCurrentAdminId();
		return self::CACHE_PREFIX . $type . ':' . $adminId;
	}
	
	/**
	 * 获取当前工作模式
	 */
	public function getCurrentMode(): string
	{
		$cacheKey = $this->getCacheKey('mode');
		return Cache::get($cacheKey, self::MODE_SYSTEM);
	}
	
	/**
	 * 获取当前切换的租户ID
	 */
	public function getCurrentSwitchedTenantId(): int
	{
		$cacheKey = $this->getCacheKey('tenant');
		return (int)Cache::get($cacheKey, 0);
	}
	
	/**
	 * 检查是否为系统管理模式
	 */
	public function isSystemMode(): bool
	{
		return $this->getCurrentMode() === self::MODE_SYSTEM;
	}
	
	/**
	 * 检查是否为租户管理模式
	 */
	public function isTenantMode(): bool
	{
		return $this->getCurrentMode() === self::MODE_TENANT;
	}
	
	/**
	 * 切换到系统管理模式
	 */
	public function switchToSystemMode(): bool
	{
		if (!is_super_admin()) {
			throw new \Exception('只有系统超级管理员才能切换到系统管理模式');
		}
		
		$expire = $this->getTokenExpire();
		
		// 设置工作模式为系统模式
		$modeCacheKey = $this->getCacheKey('mode');
		Cache::tag('tenant_switch')
		     ->set($modeCacheKey, self::MODE_SYSTEM, $expire);
		
		// 删除租户切换信息
		$tenantCacheKey = $this->getCacheKey('tenant');
		Cache::delete($tenantCacheKey);
		
		// 记录切换日志
		$this->logModeSwitch(self::MODE_SYSTEM, 0);
		
		return true;
	}
	
	/**
	 * 切换到租户管理模式
	 */
	public function switchToTenantMode(int $tenantId): bool
	{
		if (!is_super_admin()) {
			throw new \Exception('只有系统超级管理员才能切换到租户管理模式');
		}
		
		// 验证租户是否存在
		$tenant = TenantModel::find($tenantId);
		if (!$tenant) {
			throw new \Exception('租户不存在');
		}
		
		if ($tenant->status !== 1) {
			throw new \Exception('租户已被禁用，无法切换');
		}
		
		$expire = $this->getTokenExpire();
		
		// 设置工作模式为租户模式
		$modeCacheKey = $this->getCacheKey('mode');
		Cache::tag('tenant_switch')
		     ->set($modeCacheKey, self::MODE_TENANT, $expire);
		
		// 设置切换的租户ID
		$tenantCacheKey = $this->getCacheKey('tenant');
		Cache::tag('tenant_switch')
		     ->set($tenantCacheKey, $tenantId, $expire);
		
		// 记录切换日志
		$this->logModeSwitch(self::MODE_TENANT, $tenantId);
		
		return true;
	}
	
	/**
	 * 获取可切换的租户列表
	 */
	public function getAvailableTenants(): array
	{
		if (!is_super_admin()) {
			return [];
		}
		
		$cacheKey = self::CACHE_PREFIX . 'available_tenants';
		
		return Cache::remember($cacheKey, function () {
			return TenantModel::where('status', 1)
			                  ->field('id,name,code,status,created_time')
			                  ->order('id desc')
			                  ->select()
			                  ->toArray();
		}, 300); // 缓存5分钟
	}
	
	/**
	 * 获取当前租户信息
	 */
	public function getCurrentTenantInfo(): ?array
	{
		$tenantId = $this->getCurrentSwitchedTenantId();
		if ($tenantId <= 0) {
			return null;
		}
		
		$cacheKey = self::CACHE_PREFIX . 'tenant_info:' . $tenantId;
		
		return Cache::remember($cacheKey, function () use ($tenantId) {
			$tenant = TenantModel::find($tenantId);
			return $tenant?->toArray();
		}, 600); // 缓存10分钟
	}
	
	/**
	 * 清除租户切换缓存
	 */
	public function clearCache(int $tenantId = 0): void
	{
		if ($tenantId > 0) {
			Cache::delete(self::CACHE_PREFIX . 'tenant_info:' . $tenantId);
		}
		else {
			Cache::delete(self::CACHE_PREFIX . 'available_tenants');
		}
	}
	
	/**
	 * 清除指定用户的租户切换状态
	 * 用于用户登出时清理
	 */
	public function clearUserSwitchState(int $adminId): void
	{
		$modeCacheKey   = self::CACHE_PREFIX . 'mode:' . $adminId;
		$tenantCacheKey = self::CACHE_PREFIX . 'tenant:' . $adminId;
		
		Cache::delete($modeCacheKey);
		Cache::delete($tenantCacheKey);
		
		Log::info('清除用户租户切换状态', [
			'admin_id'  => $adminId,
			'timestamp' => date('Y-m-d H:i:s')
		]);
	}
	
	/**
	 * 记录模式切换日志
	 */
	protected function logModeSwitch(string $mode, int $tenantId): void
	{
		$adminId = request()->adminId ?? 0;
		
		// 从 Token 获取管理员信息
		$token     = request()->header('Authorization');
		$adminInfo = [];
		if ($token) {
			$tokenInfo = TokenUtil::getTokenInfo($token);
			$adminInfo = $tokenInfo['data'] ?? [];
		}
		
		// 获取切换前的模式
		$fromMode = $this->getCurrentMode();
		
		Log::info('系统超级管理员模式切换', [
			'admin_id'   => $adminId,
			'admin_name' => $adminInfo['username'] ?? '',
			'from_mode'  => $fromMode,
			'to_mode'    => $mode,
			'tenant_id'  => $tenantId,
			'ip'         => request()->ip(),
			'user_agent' => request()->header('User-Agent'),
			'timestamp'  => date('Y-m-d H:i:s')
		]);
	}
	
	/**
	 * 获取权限上下文
	 * 用于权限检查时确定当前的权限范围
	 */
	public function getPermissionContext(): array
	{
		$context = [
			'is_super_admin'     => is_super_admin(),
			'work_mode'          => $this->getCurrentMode(),
			'original_tenant_id' => request()->tenantId ?? 0,
			'switched_tenant_id' => $this->getCurrentSwitchedTenantId(),
		];
		
		if ($context['is_super_admin']) {
			if ($this->isSystemMode()) {
				$context['permission_scope']    = 'system';
				$context['effective_tenant_id'] = 0; // 系统级，无租户限制
			}
			else {
				$context['permission_scope']    = 'tenant';
				$context['effective_tenant_id'] = $context['switched_tenant_id'];
			}
		}
		else {
			$context['permission_scope']    = 'tenant';
			$context['effective_tenant_id'] = $context['original_tenant_id'];
		}
		
		return $context;
	}
	
	/**
	 * 检查当前是否应该应用租户隔离
	 *
	 * 重要：为了数据安全，所有用户（包括超级管理员）都必须应用租户隔离
	 * 这确保了数据的严格隔离，防止跨租户数据泄露
	 */
	public function shouldApplyTenantIsolation(): bool
	{
		// 所有用户都必须应用租户隔离，无例外
		// 这是最安全的做法，确保数据严格按租户隔离
		return true;
	}
	
	/**
	 * 获取有效的租户ID（用于权限过滤）
	 */
	public function getEffectiveTenantId(): int
	{
		$context = $this->getPermissionContext();
		return $context['effective_tenant_id'];
	}
}
