<?php
declare(strict_types=1);

namespace app\system\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\system\service\SystemArticleService;

/**
 * 文章表控制器
 */
class ArticleController extends BaseController
{
	
	use CrudControllerTrait;
	
	/**
	 * @var SystemArticleService
	 */
	protected SystemArticleService $service;
	
	
	/**
	 * 构造函数
	 */
	public function initialize(): void
	{
		parent::initialize();
		$this->service = SystemArticleService::getInstance();
	}
} 