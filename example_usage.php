<?php
/**
 * WorkflowInstance JSON 字段数字类型保持功能使用示例
 * 
 * 展示如何在实际业务场景中使用改进后的 WorkflowInstance 模型
 */

// 这是一个使用示例，展示在实际业务中如何使用

echo "=== WorkflowInstance JSON 字段使用示例 ===\n\n";

/**
 * 示例 1: 创建请假申请工作流实例
 */
echo "示例 1: 创建请假申请工作流实例\n";
echo "----------------------------------------\n";

$leaveApplicationData = [
    'definition_id' => 1,
    'business_code' => 'leave_application',
    'business_id' => 12345,
    'title' => '张三的年假申请',
    'status' => 1,
    'submitter_id' => 1001,
    'submitter_name' => '张三',
    'current_node' => 'dept_approval',
    
    // form_data - 请假申请表单数据
    'form_data' => [
        'applicant_id' => 1001,
        'applicant_name' => '张三',
        'dept_id' => 2001,
        'dept_name' => '技术部',
        'leave_type' => 3,              // 年假
        'start_date' => '2025-08-01',
        'end_date' => '2025-08-05',
        'total_days' => 5,              // 整数类型
        'total_hours' => 40.0,          // 浮点数类型
        'remaining_annual_leave' => 15, // 剩余年假天数
        'is_emergency' => false,
        'reason' => '家庭旅行',
        'contact_phone' => '***********',
        
        // 嵌套数据 - 请假明细
        'leave_details' => [
            [
                'date' => '2025-08-01',
                'hours' => 8.0,
                'type' => 'full_day'
            ],
            [
                'date' => '2025-08-02', 
                'hours' => 8.0,
                'type' => 'full_day'
            ],
            [
                'date' => '2025-08-05',
                'hours' => 4.0,
                'type' => 'half_day'
            ]
        ]
    ],
    
    // cc_users - 抄送用户ID列表
    'cc_users' => [1002, 1003, 1004],
    
    // process_data - 工作流配置
    'process_data' => [
        'workflow_type' => 'leave_approval',
        'version' => 2.1,
        'auto_approve_days_limit' => 3,
        'require_hr_approval_days' => 5,
        'nodes' => [
            [
                'id' => 'start',
                'name' => '开始',
                'type' => 1,
                'timeout_hours' => 0
            ],
            [
                'id' => 'dept_approval',
                'name' => '部门审批',
                'type' => 2,
                'timeout_hours' => 24,
                'approver_id' => 2001
            ],
            [
                'id' => 'hr_approval',
                'name' => 'HR审批',
                'type' => 2,
                'timeout_hours' => 48,
                'approver_id' => 3001
            ]
        ]
    ]
];

echo "创建请假申请数据:\n";
echo "- 申请人ID: " . $leaveApplicationData['form_data']['applicant_id'] . " (类型: " . gettype($leaveApplicationData['form_data']['applicant_id']) . ")\n";
echo "- 请假天数: " . $leaveApplicationData['form_data']['total_days'] . " (类型: " . gettype($leaveApplicationData['form_data']['total_days']) . ")\n";
echo "- 请假小时数: " . $leaveApplicationData['form_data']['total_hours'] . " (类型: " . gettype($leaveApplicationData['form_data']['total_hours']) . ")\n";
echo "- 抄送用户: [" . implode(', ', $leaveApplicationData['cc_users']) . "]\n";
echo "- 工作流版本: " . $leaveApplicationData['process_data']['version'] . " (类型: " . gettype($leaveApplicationData['process_data']['version']) . ")\n\n";

/**
 * 示例 2: 创建采购申请工作流实例
 */
echo "示例 2: 创建采购申请工作流实例\n";
echo "----------------------------------------\n";

$procurementData = [
    'definition_id' => 2,
    'business_code' => 'procurement_application',
    'business_id' => 67890,
    'title' => '办公用品采购申请',
    'status' => 1,
    'submitter_id' => 1005,
    'submitter_name' => '李四',
    'current_node' => 'finance_review',
    
    // form_data - 采购申请表单数据
    'form_data' => [
        'applicant_id' => 1005,
        'applicant_name' => '李四',
        'dept_id' => 2002,
        'dept_name' => '行政部',
        'application_date' => '2025-07-29',
        'expected_delivery_date' => '2025-08-15',
        'total_amount' => 15680.50,     // 总金额 - 浮点数
        'tax_amount' => 1568.05,        // 税额 - 浮点数
        'currency' => 'CNY',
        'payment_method' => 'bank_transfer',
        'is_urgent' => true,
        'supplier_id' => 5001,
        'supplier_name' => '办公用品供应商A',
        
        // 采购明细
        'items' => [
            [
                'item_id' => 1,
                'item_name' => '激光打印机',
                'category_id' => 101,
                'unit_price' => 2500.00,
                'quantity' => 2,
                'total_price' => 5000.00,
                'specification' => 'HP LaserJet Pro M404n'
            ],
            [
                'item_id' => 2,
                'item_name' => '办公椅',
                'category_id' => 102,
                'unit_price' => 680.50,
                'quantity' => 15,
                'total_price' => 10207.50,
                'specification' => '人体工学办公椅'
            ],
            [
                'item_id' => 3,
                'item_name' => 'A4复印纸',
                'category_id' => 103,
                'unit_price' => 23.50,
                'quantity' => 20,
                'total_price' => 470.00,
                'specification' => '70g/㎡ 500张/包'
            ]
        ],
        
        // 预算信息
        'budget_info' => [
            'budget_year' => 2025,
            'budget_category_id' => 201,
            'allocated_budget' => 50000.00,
            'used_budget' => 12500.00,
            'remaining_budget' => 37500.00
        ]
    ],
    
    // cc_users - 抄送相关人员
    'cc_users' => [1006, 1007, 1008, 1009],
    
    // process_data - 采购审批流程配置
    'process_data' => [
        'workflow_type' => 'procurement_approval',
        'version' => 1.5,
        'auto_approve_limit' => 1000.00,
        'finance_review_limit' => 5000.00,
        'ceo_approval_limit' => 50000.00,
        'nodes' => [
            [
                'id' => 'start',
                'name' => '提交申请',
                'type' => 1,
                'timeout_hours' => 0
            ],
            [
                'id' => 'dept_review',
                'name' => '部门审核',
                'type' => 2,
                'timeout_hours' => 24,
                'approver_role' => 'dept_manager'
            ],
            [
                'id' => 'finance_review',
                'name' => '财务审核',
                'type' => 2,
                'timeout_hours' => 48,
                'approver_role' => 'finance_manager'
            ],
            [
                'id' => 'ceo_approval',
                'name' => 'CEO审批',
                'type' => 2,
                'timeout_hours' => 72,
                'approver_role' => 'ceo',
                'condition' => 'amount > 50000'
            ]
        ],
        'escalation_rules' => [
            [
                'node_id' => 'dept_review',
                'escalation_hours' => 48,
                'escalation_to' => 'finance_review'
            ],
            [
                'node_id' => 'finance_review',
                'escalation_hours' => 96,
                'escalation_to' => 'ceo_approval'
            ]
        ]
    ]
];

echo "创建采购申请数据:\n";
echo "- 总金额: ¥" . number_format($procurementData['form_data']['total_amount'], 2) . " (类型: " . gettype($procurementData['form_data']['total_amount']) . ")\n";
echo "- 商品数量: " . count($procurementData['form_data']['items']) . " 项\n";
echo "- 第一项单价: ¥" . $procurementData['form_data']['items'][0]['unit_price'] . " (类型: " . gettype($procurementData['form_data']['items'][0]['unit_price']) . ")\n";
echo "- 第一项数量: " . $procurementData['form_data']['items'][0]['quantity'] . " (类型: " . gettype($procurementData['form_data']['items'][0]['quantity']) . ")\n";
echo "- 自动审批限额: ¥" . $procurementData['process_data']['auto_approve_limit'] . " (类型: " . gettype($procurementData['process_data']['auto_approve_limit']) . ")\n";
echo "- 抄送人数: " . count($procurementData['cc_users']) . " 人\n\n";

/**
 * 示例 3: 模拟数据库操作后的类型验证
 */
echo "示例 3: 数据类型验证\n";
echo "----------------------------------------\n";

// 模拟 JSON 序列化和反序列化过程
function simulateDbOperation($data) {
    // 序列化 (保存到数据库)
    $json = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    
    // 反序列化 (从数据库读取)
    $decoded = json_decode($json, true);
    
    // 应用数字类型保持逻辑
    return preserveNumericTypes($decoded);
}

function preserveNumericTypes($data) {
    if (!is_array($data)) {
        if (is_string($data) && is_numeric($data)) {
            return strpos($data, '.') !== false ? (float)$data : (int)$data;
        }
        return $data;
    }
    
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $data[$key] = preserveNumericTypes($value);
        } elseif (is_string($value) && is_numeric($value)) {
            $data[$key] = strpos($value, '.') !== false ? (float)$value : (int)$value;
        }
    }
    
    return $data;
}

// 测试请假申请数据
$processedLeaveData = simulateDbOperation($leaveApplicationData['form_data']);
echo "请假申请数据类型验证:\n";
echo "- total_days: " . gettype($processedLeaveData['total_days']) . " ✅\n";
echo "- total_hours: " . gettype($processedLeaveData['total_hours']) . " ✅\n";
echo "- applicant_id: " . gettype($processedLeaveData['applicant_id']) . " ✅\n";

// 测试采购申请数据
$processedProcurementData = simulateDbOperation($procurementData['form_data']);
echo "\n采购申请数据类型验证:\n";
echo "- total_amount: " . gettype($processedProcurementData['total_amount']) . " ✅\n";
echo "- items[0].unit_price: " . gettype($processedProcurementData['items'][0]['unit_price']) . " ✅\n";
echo "- items[0].quantity: " . gettype($processedProcurementData['items'][0]['quantity']) . " ✅\n";
echo "- budget_info.allocated_budget: " . gettype($processedProcurementData['budget_info']['allocated_budget']) . " ✅\n";

echo "\n🎉 所有数字类型都得到正确保持！\n";

echo "\n=== 使用示例完成 ===\n";
