<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

/**
 * 验证器Trait
 * 提供数据验证功能
 */
trait ValidatorTrait
{
	/**
	 * 验证规则配置
	 */
	protected array $validateRules = [];
	
	/**
	 * 验证场景配置
	 */
	protected array $validateScene = [];
	
	/**
	 * 验证数据
	 *
	 * @param array  $data  待验证数据
	 * @param string $scene 验证场景
	 * @return array 验证后的数据
	 * @throws \Exception 验证失败时抛出异常
	 */
	public function validateData(array $data, string $scene = '')
	{
		// 获取验证规则
		$rules = $this->getValidateRules();
		if (empty($rules)) {
			return $data;
		}
		
		// 创建验证器
		$validate = validate();
		$validate->rule($rules);
		
		// 设置验证场景
		if (!empty($scene)) {
			$scenes = $this->getValidateScene();
			if (isset($scenes[$scene])) {
				$validate->scene($scenes[$scene]);
			}
		}
		
		// 验证数据
		if (!$validate->check($data)) {
			throw new \Exception($validate->getError());
		}
		
		return $data;
	}
	
	/**
	 * 获取验证规则
	 *
	 * @return array
	 */
	protected function getValidateRules()
	{
		// 优先从模型获取
		if ($this->model && method_exists($this->model, 'getValidateRules')) {
			return $this->model->getValidateRules();
		}
		
		return $this->validateRules ?? [];
	}
	
	/**
	 * 获取验证场景
	 *
	 * @return array
	 */
	protected function getValidateScene()
	{
		// 优先从模型获取
		if ($this->model && method_exists($this->model, 'getValidateScene')) {
			return $this->model->getValidateScene();
		}
		
		// 尝试从字段场景生成
		//        $autoScenes = $this->generateValidateSceneFromFieldScene();
		
		return $this->validateScene ?? [];
	}
	
	/**
	 * 从字段场景自动生成验证场景
	 *
	 * @return array
	 */
	protected function generateValidateSceneFromFieldScene()
	{
		$scenes      = [];
		$fieldScenes = $this->getValidateScene();
		$rules       = $this->getValidateRules();
		
		if (empty($fieldScenes) || empty($rules)) {
			return $scenes;
		}
		
		foreach ($fieldScenes as $sceneName => $fields) {
			// 提取平级字段
			$sceneFields = [];
			foreach ($fields as $key => $value) {
				if (is_int($key)) {
					$sceneFields[] = $value;
				}
			}
			
			// 只保留有验证规则的字段
			$validFields = array_intersect(array_keys($rules), $sceneFields);
			if (!empty($validFields)) {
				$scenes[$sceneName] = $validFields;
			}
		}
		
		return $scenes;
	}
	
	/**
	 * 从数据库表结构自动生成验证规则
	 *
	 * @return array
	 */
	protected function generateValidateRulesFromTableSchema()
	{
		if (!$this->model) {
			return [];
		}
		
		$rules     = [];
		$tableName = $this->model->getTable();
		$schema    = \think\facade\Db::getFields($tableName);
		
		foreach ($schema as $field => $info) {
			$rule = [];
			
			// 必填字段
			if ($info['nullable'] === false && $info['default'] === null && !in_array($field, [
					'id',
					'created_at',
					'updated_at'
				])) {
				$rule[] = 'require';
			}
			
			// 根据字段类型设置规则
			switch ($info['type']) {
				case 'int':
				case 'tinyint':
				case 'smallint':
				case 'mediumint':
				case 'bigint':
					$rule[] = 'integer';
					break;
				case 'decimal':
				case 'float':
				case 'double':
					$rule[] = 'float';
					break;
				case 'varchar':
				case 'char':
					if (isset($info['maxlength'])) {
						$rule[] = 'length:1,' . $info['maxlength'];
					}
					break;
				case 'date':
					$rule[] = 'date';
					break;
				case 'datetime':
				case 'timestamp':
					$rule[] = 'datetime';
					break;
			}
			
			// 根据字段名称推断规则
			if (strpos($field, 'email') !== false) {
				$rule[] = 'email';
			}
			elseif (strpos($field, 'mobile') !== false || strpos($field, 'phone') !== false) {
				$rule[] = 'mobile';
			}
			elseif (strpos($field, 'url') !== false) {
				$rule[] = 'url';
			}
			elseif ($field === 'ip') {
				$rule[] = 'ip';
			}
			
			if (!empty($rule)) {
				$rules[$field] = implode('|', $rule);
			}
		}
		
		return $rules;
	}
} 