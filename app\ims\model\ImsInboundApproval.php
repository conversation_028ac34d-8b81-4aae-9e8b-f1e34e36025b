<?php
declare(strict_types=1);

namespace app\ims\model;

use app\common\core\base\BaseModel;
use app\workflow\constants\WorkflowStatusConstant;

/**
 * 入库申请表模型
 */
class ImsInboundApproval extends BaseModel
{
    // 设置表名
    protected $name = 'ims_inbound_approval';
    
    // 设置主键
    protected $pk = 'id';
    
    // 字段类型转换
    protected $type = [
        'workflow_instance_id' => 'integer',
        'approval_status' => 'integer',
        'submitter_id' => 'integer',
        'warehouse_id' => 'integer',
        'supplier_id' => 'integer',
        'source_warehouse_id' => 'integer',
        'total_amount' => 'float',
        'total_quantity' => 'float',
        'creator_id' => 'integer',
    ];
    
    // 审批状态常量
    const STATUS_DRAFT = WorkflowStatusConstant::STATUS_DRAFT;
    const STATUS_PROCESSING = WorkflowStatusConstant::STATUS_PROCESSING;
    const STATUS_COMPLETED = WorkflowStatusConstant::STATUS_COMPLETED;
    const STATUS_REJECTED = WorkflowStatusConstant::STATUS_REJECTED;
    const STATUS_TERMINATED = WorkflowStatusConstant::STATUS_TERMINATED;
    const STATUS_RECALLED = WorkflowStatusConstant::STATUS_RECALLED;
    const STATUS_VOID = WorkflowStatusConstant::STATUS_VOID;
    
    // 入库类型常量
    const TYPE_PURCHASE = 1;  // 采购入库
    const TYPE_TRANSFER = 2;  // 调拨入库
    const TYPE_RETURN = 3;    // 退货入库
    const TYPE_OTHER = 4;     // 其他入库
    
    /**
     * 获取默认搜索字段
     */
    public function getDefaultSearchFields(): array
    {
        return [
            'inbound_no' => ['type' => 'like'],
            'inbound_type' => ['type' => 'eq'],
            'approval_status' => ['type' => 'eq'],
            'warehouse_id' => ['type' => 'eq'],
            'supplier_id' => ['type' => 'eq'],
            'source_warehouse_id' => ['type' => 'eq'],
            'inbound_date' => ['type' => 'date'],
            'total_amount' => ['type' => 'between'],
            'total_quantity' => ['type' => 'between'],
            'submit_time' => ['type' => 'datetime'],
            'approval_time' => ['type' => 'datetime'],
            'created_at' => ['type' => 'date'],
        ];
    }
    
    /**
     * 关联入库明细
     */
    public function items()
    {
        return $this->hasMany(ImsInboundItem::class, 'inbound_id', 'id');
    }
    
    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(\app\ims\model\ImsWarehouse::class, 'warehouse_id', 'id')->bind([
            'warehouse_name' => 'name'
        ]);
    }
    
    /**
     * 关联供应商
     */
    public function supplier()
    {
        return $this->belongsTo(\app\ims\model\ImsSupplier::class, 'supplier_id', 'id')->bind([
            'supplier_name' => 'name'
        ]);
    }
    
    /**
     * 关联来源仓库
     */
    public function sourceWarehouse()
    {
        return $this->belongsTo(\app\ims\model\ImsWarehouse::class, 'source_warehouse_id', 'id')->bind([
            'source_warehouse_name' => 'name'
        ]);
    }
    
    /**
     * 关联提交人
     */
    public function submitter()
    {
        return $this->belongsTo(\app\system\model\SystemAdmin::class, 'submitter_id', 'id')->bind([
            'submitter_name' => 'username'
        ]);
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(\app\system\model\SystemAdmin::class, 'creator_id', 'id')->bind([
            'creator_name' => 'username'
        ]);
    }
    
    /**
     * 获取入库类型文本
     */
    public function getInboundTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_PURCHASE => '采购入库',
            self::TYPE_TRANSFER => '调拨入库',
            self::TYPE_RETURN => '退货入库',
            self::TYPE_OTHER => '其他入库',
        ];
        
        return $types[$data['inbound_type']] ?? '未知';
    }
    
    /**
     * 获取审批状态文本
     */
    public function getApprovalStatusTextAttr($value, $data)
    {
        $statuses = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PROCESSING => '审批中',
            self::STATUS_COMPLETED => '已通过',
            self::STATUS_REJECTED => '已拒绝',
            self::STATUS_TERMINATED => '已终止',
            self::STATUS_RECALLED => '已撤回',
            self::STATUS_VOID => '已作废',
        ];
        
        return $statuses[$data['approval_status']] ?? '未知';
    }
}
