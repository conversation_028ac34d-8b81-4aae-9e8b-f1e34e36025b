<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmContractService;
use think\response\Json;

/**
 * 合同表控制器
 */
class CrmContractController extends BaseController
{
    use CrudControllerTrait, ControllerImportExportTrait;
    /**
     * @var CrmContractService
     */
    protected CrmContractService $service;

    /**
     * todo 业务模型 下划线转大驼峰 可以得到Model和控制器
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();

        // 使用单例模式获取Service实例
        $this->service = CrmContractService::getInstance();
    }
	
	/**
	 * 获取列表
	 * 支持通过 approval_status 参数筛选审批状态：
	 * - 0=草稿
	 * - 1=审批中
	 * - 2=已通过
	 * - 3=已拒绝
	 * - 4=已终止
	 * - 5=已撤回
	 * - 6=已作废
	 *
	 * 使用方式：GET /crm/contract?approval_status=1
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		$result = $this->service->search($params,[],[
			'creator'
		]);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id],[
			'creator','owner'
		]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}

    /**
     * 状态切换
     */
    public function status($id)
    {
        $status = $this->request->post('status');
        $result = $this->service->updateField($id, 'status', $status);
        return $this->success('状态更新成功', $result);
    }

    /**
     * 作废合同
     */
    public function void(int $id): Json
    {
        try {
            $reason = $this->request->post('reason', '');
            if (empty(trim($reason))) {
                return $this->error('作废原因不能为空');
            }

            $result = $this->service->voidContract($id, trim($reason));
            return $this->success('合同作废成功', $result);
        } catch (\Exception $e) {
            return $this->error('作废失败：' . $e->getMessage());
        }
    }

    /**
     * 重新计算合同付款状态
     */
    public function recalculatePaymentStatus(int $id): Json
    {
        try {
            $result = $this->service->recalculatePaymentStatus($id);
            return $result
                ? $this->success('付款状态计算成功')
                : $this->error('付款状态计算失败');
        } catch (\Exception $e) {
            return $this->error('计算失败：' . $e->getMessage());
        }
    }

    /**
     * 批量修复合同付款状态（管理员功能）
     */
    public function fixPaymentStatus(): Json
    {
        try {
            $limit = $this->request->param('limit', 50);
            $result = $this->service->fixAllContractPaymentStatus($limit);
            return $this->success('修复完成', $result);
        } catch (\Exception $e) {
            return $this->error('修复失败：' . $e->getMessage());
        }
    }

    // ==================== 合同产品管理接口 ====================

    /**
     * 创建合同并关联产品
     *
     * @return Json
     */
    public function createWithProducts(): Json
    {
        try {
            $contractData = $this->request->post('contract', []);
            $products = $this->request->post('products', []);

            if (empty($products)) {
                return $this->error('请至少选择一个产品');
            }

            $result = $this->service->createWithProducts($contractData, $products);
            return $this->success('合同创建成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新合同产品
     *
     * @param int $id 合同ID
     * @return Json
     */
    public function updateProducts(int $id): Json
    {
        try {
            $products = $this->request->post('products', []);

            if (empty($products)) {
                return $this->error('请至少选择一个产品');
            }

            $result = $this->service->updateProducts($id, $products);
            return $this->success('产品更新成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取合同产品列表
     *
     * @param int $id 合同ID
     * @return Json
     */
    public function getProducts(int $id): Json
    {
        try {
            $products = $this->service->getContractProducts($id);
            return $this->success('获取成功', $products);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 计算产品小计
     *
     * @return Json
     */
    public function calculateSubtotal(): Json
    {
        try {
            $quantity = (float)$this->request->post('quantity', 0);
            $unitPrice = (float)$this->request->post('unit_price', 0);
            $discountRate = (float)$this->request->post('discount_rate', 0);

            if ($quantity <= 0 || $unitPrice <= 0) {
                return $this->error('数量和单价必须大于0');
            }

            $result = $this->service->calculateSubtotal($quantity, $unitPrice, $discountRate);
            return $this->success('计算成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取合同详情（包含产品信息）
     *
     * @param int $id 合同ID
     * @return Json
     */
    public function detailWithProducts(int $id): Json
    {
        try {
            // 获取合同基本信息
            $contract = $this->service->getOne(['id' => $id], ['creator', 'customer']);
            if ($contract->isEmpty()) {
                return $this->error('合同不存在');
            }

            // 获取合同产品列表
            $products = $this->service->getContractProducts($id);

            $result = $contract->toArray();
            $result['products'] = $products;

            return $this->success('获取成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }





    /**
     * 删除合同（重写CrudControllerTrait的delete方法）
     * 只允许删除草稿状态的合同
     */
    public function delete(): Json
    {
        $id = (int)$this->request->param('id');

        if (!$id) {
            return $this->error('参数错误');
        }

        try {
            // 获取合同信息
            $contract = $this->service->getOne(['id' => $id]);
            if ($contract->isEmpty()) {
                return $this->error('合同不存在');
            }

            // 检查审批状态 - 只允许删除草稿状态
            if ($contract->approval_status !== 0) {
                return $this->error('只有草稿状态的合同才能删除，其他状态请使用作废功能');
            }

            // 检查是否有关联的回款记录
            $receivableCount = $contract->receivables()->count();
            if ($receivableCount > 0) {
                return $this->error('该合同存在回款记录，无法删除');
            }

            // 执行删除
            $result = $this->service->delete([$id]);
            return $result
                ? $this->success('删除成功')
                : $this->error('删除失败');

        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 作废合同
     */
    public function voidContract(): Json
    {
        $id = (int)$this->request->param('id');

        try {
            $result = $this->service->voidContract($id);
            return $this->success('作废成功', ['result' => $result]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

}