<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期计算方式对比分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .comparison-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .method-inclusive {
            background-color: #e8f5e8;
        }
        .method-difference {
            background-color: #fff3e0;
        }
        .scenario {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .scenario h3 {
            margin-top: 0;
            color: #333;
        }
        .highlight {
            background-color: #ffffcc;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .recommendation {
            background-color: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>日期计算方式对比分析</h1>
    
    <div class="recommendation">
        <h3>🎯 核心问题</h3>
        <p>在出差申请等业务场景中，<strong>"7月29日到7月31日"应该算几天？</strong></p>
        <ul>
            <li><strong>方式一（包含首尾）：</strong>3天 - 包含29日、30日、31日</li>
            <li><strong>方式二（纯时间差）：</strong>2天 - 31日减去29日的差值</li>
        </ul>
    </div>

    <h2>📊 算法对比测试</h2>
    <table class="comparison-table">
        <thead>
            <tr>
                <th>开始日期</th>
                <th>结束日期</th>
                <th class="method-inclusive">包含首尾算法<br>(+1)</th>
                <th class="method-difference">纯时间差算法<br>(不+1)</th>
                <th>业务场景分析</th>
            </tr>
        </thead>
        <tbody id="comparison-results">
        </tbody>
    </table>

    <div class="scenario">
        <h3>🏢 业务场景分析</h3>
        
        <h4>1. 出差申请场景</h4>
        <p><strong>用户说：</strong>"我要从7月29日出差到7月31日"</p>
        <p><strong>用户期望：</strong>出差3天，需要3天的住宿费、餐费预算</p>
        <p><strong>实际占用：</strong>29日、30日、31日三个工作日</p>
        <p><strong>推荐算法：</strong><span class="highlight">包含首尾算法（+1）</span></p>
        
        <h4>2. 外出申请场景</h4>
        <p><strong>用户说：</strong>"我要从14:00外出到17:00"</p>
        <p><strong>用户期望：</strong>外出3小时</p>
        <p><strong>实际时长：</strong>3小时的工作时间</p>
        <p><strong>推荐算法：</strong><span class="highlight">纯时间差算法</span></p>
        
        <h4>3. 酒店住宿场景</h4>
        <p><strong>用户说：</strong>"我要从7月29日住到7月31日"</p>
        <p><strong>酒店理解：</strong>住宿2晚（29-30日一晚，30-31日一晚）</p>
        <p><strong>行业标准：</strong><span class="highlight">纯时间差算法</span></p>
    </div>

    <div class="recommendation">
        <h3>💡 最终建议</h3>
        <p><strong>保持当前的+1实现</strong>，原因：</p>
        <ol>
            <li><strong>符合业务语义：</strong>出差申请的"天数"概念就是包含首尾日期</li>
            <li><strong>符合用户期望：</strong>用户说"出差3天"就是指3个自然日</li>
            <li><strong>行业标准：</strong>请假、出差等HR系统普遍采用这种算法</li>
            <li><strong>数据一致性：</strong>修改算法会影响现有数据</li>
        </ol>
    </div>

    <script>
        // 包含首尾日期的算法（当前实现）
        function calculateDaysInclusive(startDate, endDate) {
            const start = new Date(startDate)
            const end = new Date(endDate)
            
            if (isNaN(start.getTime()) || isNaN(end.getTime()) || end < start) {
                return 0
            }
            
            // 设置为当天00:00:00
            const startDay = new Date(start.getFullYear(), start.getMonth(), start.getDate())
            const endDay = new Date(end.getFullYear(), end.getMonth(), end.getDate())
            
            // 计算差值并+1
            const diffTime = endDay.getTime() - startDay.getTime()
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1
            
            return diffDays
        }

        // 纯时间差算法
        function calculateDaysDifference(startDate, endDate) {
            const start = new Date(startDate)
            const end = new Date(endDate)
            
            if (isNaN(start.getTime()) || isNaN(end.getTime()) || end < start) {
                return 0
            }
            
            // 设置为当天00:00:00
            const startDay = new Date(start.getFullYear(), start.getMonth(), start.getDate())
            const endDay = new Date(end.getFullYear(), end.getMonth(), end.getDate())
            
            // 只计算差值，不+1
            const diffTime = endDay.getTime() - startDay.getTime()
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
            
            return diffDays
        }

        // 测试用例
        const testCases = [
            {
                start: '2025-07-29',
                end: '2025-07-29',
                scenario: '同一天（当天往返）'
            },
            {
                start: '2025-07-29',
                end: '2025-07-30',
                scenario: '连续两天'
            },
            {
                start: '2025-07-29',
                end: '2025-07-31',
                scenario: '连续三天（典型出差）'
            },
            {
                start: '2025-07-29',
                end: '2025-08-02',
                scenario: '跨月出差'
            },
            {
                start: '2025-07-01',
                end: '2025-07-31',
                scenario: '整月出差'
            },
            {
                start: '2025-07-29 09:00',
                end: '2025-07-31 18:00',
                scenario: '带时间的日期'
            }
        ];

        // 生成对比表格
        function generateComparisonTable() {
            const tbody = document.getElementById('comparison-results');
            
            testCases.forEach(testCase => {
                const inclusiveResult = calculateDaysInclusive(testCase.start, testCase.end);
                const differenceResult = calculateDaysDifference(testCase.start, testCase.end);
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${testCase.start}</td>
                    <td>${testCase.end}</td>
                    <td class="method-inclusive"><strong>${inclusiveResult}天</strong></td>
                    <td class="method-difference"><strong>${differenceResult}天</strong></td>
                    <td>${testCase.scenario}</td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 页面加载完成后生成表格
        document.addEventListener('DOMContentLoaded', generateComparisonTable);
    </script>
</body>
</html>
