<?php
/**
 * 办公采购工作流集成测试脚本
 * 
 * 测试办公采购模块的模式二工作流集成功能
 * 
 * 运行方式：
 * php think run test_office_procurement_workflow_integration.php
 */

use app\office\model\OfficeProcurement;
use app\office\service\OfficeProcurementService;
use app\workflow\factory\DynamicWorkflowFactory;
use app\workflow\service\UnifiedWorkflowService;
use app\workflow\model\WorkflowType;
use think\facade\Db;
use think\facade\Log;

class OfficeProcurementWorkflowIntegrationTest
{
    private array $testResults = [];
    private int $passedTests = 0;
    private int $totalTests = 0;
    
    public function runAllTests(): void
    {
        echo "=== 办公采购工作流集成测试开始 ===\n\n";
        
        // 1. 数据库结构测试
        $this->testDatabaseStructure();
        
        // 2. 模型功能测试
        $this->testModelFunctionality();
        
        // 3. 服务层功能测试
        $this->testServiceFunctionality();
        
        // 4. 工作流集成测试
        $this->testWorkflowIntegration();
        
        // 5. 动态工厂测试
        $this->testDynamicFactory();
        
        // 6. ApplicationController集成测试
        $this->testApplicationControllerIntegration();
        
        // 输出测试结果
        $this->outputTestResults();
    }
    
    private function testDatabaseStructure(): void
    {
        echo "1. 数据库结构测试\n";
        echo "==================\n";
        
        // 检查表是否存在
        $this->runTest('表存在性检查', function() {
            $exists = Db::query("SHOW TABLES LIKE 'office_procurement'");
            return !empty($exists);
        });
        
        // 检查必要字段
        $this->runTest('必要字段完整性', function() {
            $columns = Db::query("SHOW COLUMNS FROM office_procurement");
            $columnNames = array_column($columns, 'Field');
            
            $requiredFields = [
                'id', 'tenant_id', 'workflow_instance_id', 'approval_status',
                'submit_time', 'approval_time', 'submitter_id',
                'procurement_type', 'delivery_date', 'item_name', 'supplier_name',
                'unit_price', 'quantity', 'payment_amount', 'payment_amount_words',
                'payee_name', 'bank_name', 'bank_account', 'payment_method',
                'remark', 'attachment', 'creator_id', 'created_at', 'updated_at', 'deleted_at'
            ];
            
            foreach ($requiredFields as $field) {
                if (!in_array($field, $columnNames)) {
                    return false;
                }
            }
            return true;
        });
        
        echo "\n";
    }
    
    private function testModelFunctionality(): void
    {
        echo "2. 模型功能测试\n";
        echo "================\n";
        
        // 模型实例化测试
        $this->runTest('模型实例化', function() {
            $model = new \app\office\model\OfficeProcurement();
            return $model instanceof \app\office\model\OfficeProcurement;
        });

        // 常量定义测试
        $this->runTest('常量定义', function() {
            return defined('app\office\model\OfficeProcurement::STATUS_DRAFT') &&
                   defined('app\office\model\OfficeProcurement::PROCUREMENT_TYPE_OFFICE_SUPPLIES') &&
                   defined('app\office\model\OfficeProcurement::PAYMENT_METHOD_BANK_TRANSFER');
        });
        
        echo "\n";
    }
    
    private function testServiceFunctionality(): void
    {
        echo "3. 服务层功能测试\n";
        echo "==================\n";
        
        // 服务实例化测试
        $this->runTest('服务实例化', function() {
            $service = new \app\office\service\OfficeProcurementService();
            return $service instanceof \app\office\service\OfficeProcurementService;
        });

        // FormServiceInterface实现测试
        $this->runTest('FormServiceInterface实现', function() {
            $service = new \app\office\service\OfficeProcurementService();
            return $service instanceof \app\workflow\interfaces\FormServiceInterface;
        });

        // 必要方法存在性测试
        $this->runTest('必要方法存在', function() {
            $service = new \app\office\service\OfficeProcurementService();
            $requiredMethods = [
                'getFormData', 'saveForm', 'updateForm', 'deleteForm',
                'updateFormStatus', 'getInstanceTitle', 'validateFormData'
            ];

            foreach ($requiredMethods as $method) {
                if (!method_exists($service, $method)) {
                    return false;
                }
            }
            return true;
        });
        
        echo "\n";
    }
    
    private function testWorkflowIntegration(): void
    {
        echo "4. 工作流集成测试\n";
        echo "==================\n";
        
        // workflow_type配置测试
        $this->runTest('workflow_type配置', function() {
            $workflowType = WorkflowType::where('business_code', 'office_procurement')->find();
            return !empty($workflowType) && $workflowType['status'] == 1;
        });
        
        // UnifiedWorkflowService集成测试
        $this->runTest('UnifiedWorkflowService集成', function() {
            $unifiedService = new UnifiedWorkflowService();
            return method_exists($unifiedService, 'executeWorkflowOperation');
        });
        
        echo "\n";
    }
    
    private function testDynamicFactory(): void
    {
        echo "5. 动态工厂测试\n";
        echo "================\n";
        
        // 动态工厂创建Service测试
        $this->runTest('动态工厂创建Service', function() {
            $service = DynamicWorkflowFactory::createFormServiceByBusinessCode('office_procurement');
            return $service instanceof \app\office\service\OfficeProcurementService;
        });
        
        echo "\n";
    }
    
    private function testApplicationControllerIntegration(): void
    {
        echo "6. ApplicationController集成测试\n";
        echo "=================================\n";
        
        // 检查ApplicationController是否包含office_procurement
        $this->runTest('ApplicationController支持', function() {
            $controller = new \app\workflow\controller\ApplicationController();
            
            // 通过反射获取支持的业务类型
            $reflection = new ReflectionClass($controller);
            $method = $reflection->getMethod('getSupportedBusinessTypes');
            $method->setAccessible(true);
            
            // 模拟请求对象
            $request = new \think\Request();
            $controller->request = $request;
            
            $result = $method->invoke($controller);
            $data = $result->getData();
            
            foreach ($data['data'] as $businessType) {
                if ($businessType['code'] === 'office_procurement') {
                    return true;
                }
            }
            return false;
        });
        
        echo "\n";
    }
    
    private function runTest(string $testName, callable $testFunction): void
    {
        $this->totalTests++;
        
        try {
            $result = $testFunction();
            if ($result) {
                echo "✅ {$testName}: 通过\n";
                $this->testResults[$testName] = '通过';
                $this->passedTests++;
            } else {
                echo "❌ {$testName}: 失败\n";
                $this->testResults[$testName] = '失败';
            }
        } catch (\Exception $e) {
            echo "❌ {$testName}: 异常 - {$e->getMessage()}\n";
            $this->testResults[$testName] = '异常: ' . $e->getMessage();
        }
    }
    
    private function outputTestResults(): void
    {
        echo "=== 测试结果汇总 ===\n";
        echo "总测试数: {$this->totalTests}\n";
        echo "通过数量: {$this->passedTests}\n";
        echo "失败数量: " . ($this->totalTests - $this->passedTests) . "\n";
        echo "成功率: " . round(($this->passedTests / $this->totalTests) * 100, 2) . "%\n\n";
        
        if ($this->passedTests === $this->totalTests) {
            echo "🎉 所有测试通过！办公采购工作流集成成功！\n";
        } else {
            echo "⚠️  部分测试失败，请检查相关配置和实现。\n";
        }
        
        echo "\n=== 详细测试结果 ===\n";
        foreach ($this->testResults as $testName => $result) {
            echo "{$testName}: {$result}\n";
        }
    }
}

// 运行测试
try {
    $test = new OfficeProcurementWorkflowIntegrationTest();
    $test->runAllTests();
} catch (\Exception $e) {
    echo "测试执行失败: " . $e->getMessage() . "\n";
    echo "错误追踪: " . $e->getTraceAsString() . "\n";
}
