# 每日报价单审批组件涨跌幅适配测试指南

## 📋 修改内容总结

### 🎯 **主要修改**

#### 1. **涨跌幅列宽调整**
- 从 `width="100"` 调整为 `width="150"`
- 适配新的数值+百分比显示格式

#### 2. **显示逻辑更新**
- 原来：只显示百分比 `+5.20%`
- 现在：显示数值+百分比 `+5.20(+5.20%)`

#### 3. **数据源适配**
- 原来：使用 `row.price_change_rate`
- 现在：使用 `row.price_change` 和 `row.change_rate`

#### 4. **新增格式化方法**
```javascript
const formatPriceChangeWithAmount = (row: any) => {
  const priceChange = row.price_change
  const changeRate = row.change_rate
  
  // 如果没有价格变动数据，显示无变动
  if (!priceChange && !changeRate) {
    return '无变动'
  }
  
  // 如果价格变动为0，显示无变动
  if (priceChange === 0) {
    return '无变动'
  }
  
  // 格式化价格变动和比例
  const sign = priceChange > 0 ? '+' : ''
  const priceText = `${sign}${(priceChange || 0).toFixed(2)}`
  const rateText = `${sign}${(changeRate || 0).toFixed(2)}%`
  
  return `${priceText}(${rateText})`
}
```

## 🧪 测试用例

### 测试数据结构
```javascript
const testFormData = {
  id: 1078,
  price_date: "2025-07-27",
  total_items: 3,
  approval_status: 1,
  created_at: "2025-07-27 13:36:15",
  items: [
    {
      id: 1,
      supplier: { name: "供应商A" },
      product: { name: "产品A", spec: "规格A" },
      unit_price: 105.50,
      old_price: 100.00,
      price_change: 5.50,
      change_rate: 5.50,
      stock_price: 95.00,
      stock_qty: 50,
      policy_remark: "测试涨价"
    },
    {
      id: 2,
      supplier: { name: "供应商B" },
      product: { name: "产品B", spec: "规格B" },
      unit_price: 190.00,
      old_price: 200.00,
      price_change: -10.00,
      change_rate: -5.00,
      stock_price: 180.00,
      stock_qty: 30,
      policy_remark: "测试降价"
    },
    {
      id: 3,
      supplier: { name: "供应商C" },
      product: { name: "产品C", spec: "规格C" },
      unit_price: 150.00,
      old_price: 150.00,
      price_change: 0.00,
      change_rate: 0.00,
      stock_price: 140.00,
      stock_qty: 20,
      policy_remark: "无变动"
    }
  ]
}
```

### 预期显示效果

#### 涨跌幅列显示
1. **涨价情况**：
   - 数据：`price_change: 5.50, change_rate: 5.50`
   - 显示：`+5.50(+5.50%)`
   - 样式：红色字体 (price-rise)

2. **降价情况**：
   - 数据：`price_change: -10.00, change_rate: -5.00`
   - 显示：`-10.00(-5.00%)`
   - 样式：绿色字体 (price-fall)

3. **无变动情况**：
   - 数据：`price_change: 0.00, change_rate: 0.00`
   - 显示：`无变动`
   - 样式：灰色字体 (price-stable)

#### 审批信息显示
- 审批状态：根据 `approval_status` 显示对应的标签和颜色
- 提交时间、审批时间等信息正常显示

## 🔧 测试步骤

### 1. **组件渲染测试**
```vue
<template>
  <daily-price-order-form-view :form-data="testFormData" />
</template>

<script setup>
import DailyPriceOrderFormView from '@/views/workflow/components/business-forms/daily_price_order-form-view.vue'

const testFormData = {
  // 使用上面的测试数据
}
</script>
```

### 2. **涨跌幅显示验证**
- [ ] 涨价显示格式：`+5.50(+5.50%)`，红色
- [ ] 降价显示格式：`-10.00(-5.00%)`，绿色
- [ ] 无变动显示：`无变动`，灰色
- [ ] 列宽适配：150px 宽度足够显示完整内容

### 3. **数据兼容性测试**
- [ ] 新数据格式（包含 price_change, change_rate）正常显示
- [ ] 旧数据格式（只有 price_change_rate）降级处理
- [ ] 缺失数据显示为"无变动"

### 4. **样式验证**
- [ ] 涨价：红色字体 (#f56c6c)
- [ ] 降价：绿色字体 (#67c23a)
- [ ] 无变动：灰色字体 (#909399)
- [ ] 字体加粗效果正常

## 📝 验证清单

### 功能验证
- [ ] 涨跌幅数值+百分比格式正确显示
- [ ] 样式类正确应用
- [ ] 无变动情况正确处理
- [ ] 审批信息正常显示

### 兼容性验证
- [ ] 新数据结构完全支持
- [ ] 旧数据结构降级处理
- [ ] 异常数据不会导致组件崩溃

### 视觉验证
- [ ] 列宽适配新格式
- [ ] 颜色区分清晰
- [ ] 字体样式一致
- [ ] 整体布局协调

## 🚨 注意事项

1. **数据结构依赖**：确保传入的 formData 包含完整的 items 数据
2. **字段优先级**：优先使用 `price_change` 和 `change_rate`，兼容 `price_change_rate`
3. **空值处理**：所有可能为空的字段都有默认值处理
4. **类型安全**：所有方法都有适当的类型检查

## 🔄 回滚方案

如果新版本有问题，可以快速回滚到原来的显示方式：

```vue
<!-- 回滚到原来的显示方式 -->
<el-table-column label="涨跌幅" width="100" align="center">
  <template #default="{ row }">
    <span :class="getPriceChangeClass(row.price_change_rate)">
      {{ formatPriceChangeRate(row.price_change_rate) }}
    </span>
  </template>
</el-table-column>
```

## 📞 技术支持

如遇问题，请检查：
1. formData 数据结构是否正确
2. items 数组是否包含必要字段
3. 浏览器控制台是否有错误信息
4. 组件是否正确导入和使用
