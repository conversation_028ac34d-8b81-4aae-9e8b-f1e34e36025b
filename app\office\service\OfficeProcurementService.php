<?php
declare(strict_types=1);

namespace app\office\service;

use app\common\core\base\BaseService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use app\office\model\OfficeProcurement;
use app\common\exception\BusinessException;
use app\common\utils\NumberConverter;
use app\common\utils\PaymentMethodHelper;
use think\facade\Log;

/**
 * 办公采购申请服务类
 */
class OfficeProcurementService extends BaseService implements FormServiceInterface
{
	protected string $modelClass = OfficeProcurement::class;
	
	public function __construct()
	{
		$this->model = new OfficeProcurement();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = $this->model->with([
			'submitter',
			'creator'
		])
		                     ->find($id);
		
		if (!$model) {
			throw new BusinessException('采购申请记录不存在');
		}
		
		return $model->toArray();
	}
	
	/**
	 * 创建表单数据
	 */
	public function saveForm(array $data): array
	{
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			// 验证数据
			$validatedData = $this->validateFormData($formData, 'create');
			
			// 预处理数据
			$validatedData = $this->preprocessFormData($validatedData);
			
			// 创建主记录
			$id = $this->model->saveByCreate($validatedData);
			
			// 返回完整数据
			$formData = $this->getFormData($id);
			
			return [
				$id,
				$formData
			];
			
		}
		catch (\Exception $e) {
			Log::error('办公采购申请创建失败: ' . $e->getMessage(), [
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('办公采购申请创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				throw new BusinessException('办公采购申请记录不存在');
			}
			
			// 验证数据
			$validatedData = $this->validateFormData($data, 'update');
			
			// 预处理数据
			$validatedData = $this->preprocessFormData($validatedData);
			
			// 更新记录
			return $model->saveByUpdate($validatedData);
			
		}
		catch (\Exception $e) {
			Log::error('办公采购申请更新失败: ' . $e->getMessage(), [
				'id'    => $id,
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('办公采购申请更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			$updateData = ['approval_status' => $status];
			
			// 处理额外数据
			if (!empty($extra['workflow_instance_id'])) {
				$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
			}
			
			if (!empty($extra['submit_time'])) {
				$updateData['submit_time'] = $extra['submit_time'];
			}
			
			if (!empty($extra['approval_time'])) {
				$updateData['approval_time'] = $extra['approval_time'];
			}
			
			return $model->saveByUpdate($updateData);
			
		}
		catch (\Exception $e) {
			Log::error('办公采购申请状态更新失败: ' . $e->getMessage(), [
				'id'     => $id,
				'status' => $status,
				'extra'  => $extra,
				'trace'  => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 删除表单
	 */
	public function deleteForm(int $id): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			// 软删除主记录
			$model->delete();
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('办公采购申请删除失败: ' . $e->getMessage(), [
				'id'    => $id,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 获取流程实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		if (is_array($formData)) {
			$submitterName   = $formData['submitter_name'] ?? '';
			$paymentAmount = $formData['payment_amount'] ?? 0;
			
			return "{$submitterName}采购-{$paymentAmount}元";
		}
		
		return '办公采购申请';
	}
	
	/**
	 * 验证表单数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		$rules = [
			'delivery_date'  => 'require|date',
			'item_name'      => 'require|max:200',
			'supplier_name'  => 'require|max:200',
			'unit_price'     => 'require|float|gt:0',
			'quantity'       => 'require|integer|gt:0',
			'payment_amount' => 'require|float|gt:0',
			//			'payment_amount_words' => 'require|max:500',
			'payee_name'     => 'require|max:100',
			'bank_name'      => 'require|max:200',
			'bank_account'   => 'require|max:50',
			'payment_method' => 'require|integer',
		];
		
		// 可选字段
		if (!empty($data['procurement_type'])) {
			$rules['procurement_type'] = 'integer|in:1,2,3';
		}
		
		if (!empty($data['remark'])) {
			$rules['remark'] = 'max:1000';
		}
		
		$message = [
			'delivery_date.require'    => '请选择交货日期',
			'delivery_date.date'       => '交货日期格式错误',
			'item_name.require'        => '请填写物品名称',
			'item_name.max'            => '物品名称长度不能超过200个字符',
			'supplier_name.require'    => '请填写供应商名称',
			'supplier_name.max'        => '供应商名称长度不能超过200个字符',
			'unit_price.require'       => '请填写单价',
			'unit_price.float'         => '单价格式错误',
			'unit_price.gt'            => '单价必须大于0',
			'quantity.require'         => '请填写数量',
			'quantity.integer'         => '数量格式错误',
			'quantity.gt'              => '数量必须大于0',
			'payment_amount.require'   => '请填写付款金额',
			'payment_amount.float'     => '付款金额格式错误',
			'payment_amount.gt'        => '付款金额必须大于0',
			'payee_name.require'       => '请填写收款人名称',
			'payee_name.max'           => '收款人名称长度不能超过100个字符',
			'bank_name.require'        => '请填写开户银行名称',
			'bank_name.max'            => '开户银行名称长度不能超过200个字符',
			'bank_account.require'     => '请填写银行账号',
			'bank_account.max'         => '银行账号长度不能超过50个字符',
			'payment_method.require'   => '请选择支付方式',
			'payment_method.integer'   => '支付方式格式错误',
			'procurement_type.integer' => '采购类型格式错误',
			'procurement_type.in'      => '请选择采购类型',
			'remark.max'               => '备注长度不能超过1000个字符',
		];
		
		$validate = validate($rules, $message);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		return $data;
	}
	
	/**
	 * 预处理表单数据
	 */
	private function preprocessFormData(array $data): array
	{
		// 计算付款金额（单价 * 数量）
		if (isset($data['unit_price']) && isset($data['quantity'])) {
			$data['payment_amount'] = NumberConverter::safeMultiply($data['unit_price'], $data['quantity']);
		}
		
		// 验证支付方式
		if (isset($data['payment_method'])) {
			if (!PaymentMethodHelper::isValid($data['payment_method'])) {
				throw new BusinessException('无效的支付方式');
			}
		}
		
		// 自动生成金额大写
		if (isset($data['payment_amount'])) {
			$data['payment_amount_words'] = NumberConverter::convertToChineseNumber($data['payment_amount']);
		}
		
		return $data;
	}
	
	/**
	 * 工作流状态变更后的处理
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		try {
			$procurement = $this->model->find($businessId);
			if (!$procurement) {
				return false;
			}
			
			// 根据状态进行相应处理
			switch ($status) {
				case 2: // 审批通过
					// 可以在这里添加审批通过后的业务逻辑
					// 例如：发送通知、更新库存等
					break;
				case 3: // 审批驳回
					// 可以在这里添加审批驳回后的业务逻辑
					break;
				case 4: // 审批终止
					// 可以在这里添加审批终止后的业务逻辑
					break;
			}
			
			return true;
			
		}
		catch (\Exception $e) {
			// 记录日志
			Log::error('办公采购工作流状态变更处理失败: ' . $e->getMessage());
			return false;
		}
	}
}
