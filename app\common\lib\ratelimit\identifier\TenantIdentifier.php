<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\identifier;

use think\facade\Request;
use app\common\middleware\TenantMiddleware;

/**
 * 租户ID标识实现
 */
class TenantIdentifier implements IdentifierInterface
{
    /**
     * 获取标识值
     *
     * @return string 标识值（租户ID）
     */
    public function getValue(): string
    {
        // 从请求中获取租户ID
        $tenantId = Request::middleware('tenant_id');
        
        if (empty($tenantId)) {
            // 尝试从Header中获取租户ID
            $tenantId = Request::header('tenant-id');
        }
        
        if (empty($tenantId)) {
            // 尝试从JWT中获取租户ID
            try {
                $tenantId = app()->jwt->getUser()->tenant_id ?? 0;
            } catch (\Throwable $e) {
                $tenantId = 0;
            }
        }
        
        // 如果无法获取租户ID，则使用0作为标识（系统级）
        if (empty($tenantId)) {
            $tenantId = 0;
        }
        
        return 'tenant:' . $tenantId;
    }
} 