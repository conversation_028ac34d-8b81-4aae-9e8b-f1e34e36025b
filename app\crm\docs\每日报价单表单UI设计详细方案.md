# 每日报价单表单UI设计详细方案

## 🎨 整体布局设计

### 页面结构层次
```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│ 📋 每日报价单 - 新增/编辑                                    [最小化] [关闭]              │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ 🔖 表单头部信息区域                                                                      │
│ ┌─────────────────────────────────────────────────────────────────────────────────────┐ │
│ │ 报价单编号: [BJ20250721001] (自动生成)    报价日期: [2025-07-21] 📅                │ │
│ │ 报价单标题: [今日钢材报价单_____________________________]                          │ │
│ │ 备注说明:   [价格调整说明________________________________] (可选)                  │ │
│ └─────────────────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ 📊 审批状态展示区域 (根据状态动态显示)                                                  │
│ ┌─────────────────────────────────────────────────────────────────────────────────────┐ │
│ │ 当前状态: [草稿] 🟡  │  提交人: 张三  │  提交时间: --  │  审批人: --                │ │
│ │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │ │
│ │ 审批进度: ●草稿 ○审批中 ○已通过                                                   │ │
│ └─────────────────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ 📦 产品明细表格区域                                                                      │
│ ┌─────────────────────────────────────────────────────────────────────────────────────┐ │
│ │ [+ 添加产品] [📤 导入模板] [🔄 从昨日复制]                                          │ │
│ │ ─────────────────────────────────────────────────────────────────────────────────── │ │
│ │ 供应商    │产品名称│规格│单价    │涨幅      │库存价格│库存数量│优惠政策│操作        │ │
│ │ ─────────┼───────┼───┼────────┼──────────┼────────┼────────┼────────┼────────────│ │
│ │ 🏭宁提集团│螺纹钢  │20 │ 4500   │+100(+2.3%)│ 4600  │ 1000   │批量优惠│[📝][🗑️]   │ │
│ │ 🏭宁提集团│螺纹钢  │25 │ 4600   │ 0(0.0%)  │ 4700  │ 800    │        │[📝][🗑️]   │ │
│ │ 🏭润江石化│圆钢    │16 │ 4200   │-50(-1.2%)│ 4300  │ 1200   │降价促销│[📝][🗑️]   │ │
│ │ ─────────┼───────┼───┼────────┼──────────┼────────┼────────┼────────┼────────────│ │
│ │ 统计: 总计3项产品 | 涨价1项 | 降价1项 | 无变化1项 | 平均涨幅: +0.37%                │ │
│ └─────────────────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ 🔧 底部操作区域                                                                          │
│ ┌─────────────────────────────────────────────────────────────────────────────────────┐ │
│ │ [💾 保存草稿] [📤 提交审批] [🔄 撤回审批] [🗑️ 删除] [📋 返回列表]                   │ │
│ │ ※ 草稿状态下可编辑，提交审批后不可修改                                              │ │
│ └─────────────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────────┘
```

## 📝 表单头部信息区设计

### 字段布局
```html
<div class="form-header">
  <el-row :gutter="20">
    <el-col :span="12">
      <el-form-item label="报价单编号" prop="order_number">
        <el-input v-model="formData.order_number" :disabled="true" placeholder="系统自动生成">
          <template #prefix>
            <el-icon><Document /></el-icon>
          </template>
        </el-input>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="报价日期" prop="price_date">
        <el-date-picker 
          v-model="formData.price_date" 
          type="date" 
          placeholder="选择报价日期"
          :disabled="!isEditable"
          style="width: 100%"
        />
      </el-form-item>
    </el-col>
  </el-row>
  
  <el-row :gutter="20">
    <el-col :span="24">
      <el-form-item label="报价单标题" prop="title">
        <el-input 
          v-model="formData.title" 
          placeholder="请输入报价单标题，如：今日钢材报价单"
          :disabled="!isEditable"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-col>
  </el-row>
  
  <el-row :gutter="20">
    <el-col :span="24">
      <el-form-item label="备注说明" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea" 
          :rows="2"
          placeholder="可选：价格调整说明、市场变化原因等"
          :disabled="!isEditable"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-col>
  </el-row>
</div>
```

### 字段验证规则
```javascript
const formRules = {
  title: [
    { required: true, message: '请输入报价单标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度在2到200个字符', trigger: 'blur' }
  ],
  price_date: [
    { required: true, message: '请选择报价日期', trigger: 'change' }
  ]
}
```

## 📊 审批状态展示区设计

### 状态卡片组件
```vue
<template>
  <div class="approval-status-card" :class="statusClass">
    <div class="status-header">
      <div class="status-info">
        <span class="status-label">当前状态:</span>
        <el-tag :type="statusTagType" size="large">
          {{ statusText }}
        </el-tag>
      </div>
      <div class="approval-info" v-if="formData.approval_status > 0">
        <span>提交人: {{ submitterName }}</span>
        <span>提交时间: {{ formatTime(formData.submit_time) }}</span>
        <span v-if="formData.approval_time">审批时间: {{ formatTime(formData.approval_time) }}</span>
      </div>
    </div>
    
    <div class="progress-bar" v-if="formData.approval_status > 0">
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="草稿" description="编辑中"></el-step>
        <el-step title="审批中" description="等待审批"></el-step>
        <el-step title="已通过" description="审批完成"></el-step>
      </el-steps>
    </div>
    
    <div class="approval-opinion" v-if="approvalOpinion">
      <el-alert 
        :title="approvalOpinion" 
        :type="opinionType"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>
```

### 状态样式定义
```css
.approval-status-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  background: #fafafa;
}

.approval-status-card.draft { border-left: 4px solid #909399; }
.approval-status-card.pending { border-left: 4px solid #409eff; }
.approval-status-card.approved { border-left: 4px solid #67c23a; }
.approval-status-card.rejected { border-left: 4px solid #f56c6c; }
.approval-status-card.terminated { border-left: 4px solid #e6a23c; }
.approval-status-card.recalled { border-left: 4px solid #909399; }
.approval-status-card.voided { border-left: 4px solid #606266; }
```

## 📦 产品明细表格设计

### 表格头部工具栏
```html
<div class="table-toolbar">
  <div class="toolbar-left">
    <el-button 
      type="primary" 
      :icon="Plus" 
      @click="addProduct"
      :disabled="!isEditable"
    >
      添加产品
    </el-button>
    <el-button 
      :icon="Upload" 
      @click="importTemplate"
      :disabled="!isEditable"
    >
      导入模板
    </el-button>
    <el-button 
      :icon="CopyDocument" 
      @click="copyFromYesterday"
      :disabled="!isEditable"
    >
      从昨日复制
    </el-button>
  </div>
  <div class="toolbar-right">
    <el-button :icon="Download" @click="exportTemplate">
      导出模板
    </el-button>
  </div>
</div>
```

### 明细表格结构
```html
<el-table 
  :data="priceItems" 
  border 
  stripe
  :summary-method="getSummaries"
  show-summary
  class="price-items-table"
>
  <!-- 供应商列 -->
  <el-table-column prop="supplier_name" label="供应商" width="120" fixed="left">
    <template #default="{ row, $index }">
      <div class="supplier-cell">
        <el-icon><Factory /></el-icon>
        <span>{{ row.supplier_name }}</span>
      </div>
    </template>
  </el-table-column>
  
  <!-- 产品信息列 -->
  <el-table-column label="产品信息" width="200">
    <template #default="{ row }">
      <div class="product-info">
        <div class="product-name">{{ row.product_name }}</div>
        <div class="product-spec">规格: {{ row.product_spec }}</div>
        <div class="product-code">编码: {{ row.product_code }}</div>
      </div>
    </template>
  </el-table-column>
  
  <!-- 价格信息列 -->
  <el-table-column label="价格信息" width="300">
    <template #default="{ row, $index }">
      <div class="price-info">
        <div class="price-row">
          <span class="label">单价:</span>
          <el-input-number
            v-model="row.unit_price"
            :precision="2"
            :step="0.01"
            :min="0"
            :disabled="!isEditable"
            @change="calculatePriceChange(row, $index)"
            class="price-input"
          />
        </div>
        <div class="price-change" :class="getPriceChangeClass(row.price_change)">
          <span class="label">涨幅:</span>
          <span class="change-amount">{{ formatPriceChange(row.price_change) }}</span>
          <span class="change-rate">({{ formatChangeRate(row.change_rate) }}%)</span>
          <el-icon v-if="row.price_change > 0"><ArrowUp /></el-icon>
          <el-icon v-else-if="row.price_change < 0"><ArrowDown /></el-icon>
          <el-icon v-else><Minus /></el-icon>
        </div>
      </div>
    </template>
  </el-table-column>
  
  <!-- 库存信息列 -->
  <el-table-column label="库存信息" width="200">
    <template #default="{ row }">
      <div class="stock-info">
        <div class="stock-row">
          <span class="label">库存价:</span>
          <el-input-number
            v-model="row.stock_price"
            :precision="2"
            :step="0.01"
            :min="0"
            :disabled="!isEditable"
            class="stock-input"
          />
        </div>
        <div class="stock-row">
          <span class="label">库存量:</span>
          <el-input-number
            v-model="row.stock_qty"
            :precision="2"
            :step="1"
            :min="0"
            :disabled="!isEditable"
            class="stock-input"
          />
        </div>
      </div>
    </template>
  </el-table-column>
  
  <!-- 优惠政策列 -->
  <el-table-column prop="policy_remark" label="优惠政策" width="150">
    <template #default="{ row }">
      <el-input
        v-model="row.policy_remark"
        placeholder="优惠说明"
        :disabled="!isEditable"
        maxlength="200"
      />
    </template>
  </el-table-column>
  
  <!-- 操作列 -->
  <el-table-column label="操作" width="120" fixed="right">
    <template #default="{ row, $index }">
      <div class="action-buttons">
        <el-button 
          type="primary" 
          :icon="Edit" 
          size="small"
          @click="editItem(row, $index)"
          :disabled="!isEditable"
        />
        <el-button 
          type="danger" 
          :icon="Delete" 
          size="small"
          @click="deleteItem($index)"
          :disabled="!isEditable"
        />
        <el-button 
          type="info" 
          :icon="TrendCharts" 
          size="small"
          @click="showPriceTrend(row)"
        />
      </div>
    </template>
  </el-table-column>
</el-table>
```

### 表格统计行
```javascript
const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '统计'
    } else if (index === 1) {
      sums[index] = `总计${data.length}项产品`
    } else if (index === 2) {
      const riseCount = data.filter(item => item.price_change > 0).length
      const fallCount = data.filter(item => item.price_change < 0).length
      const noChangeCount = data.filter(item => item.price_change === 0).length
      sums[index] = `涨价${riseCount}项 | 降价${fallCount}项 | 无变化${noChangeCount}项`
    } else if (index === 3) {
      const avgChange = data.reduce((sum, item) => sum + item.change_rate, 0) / data.length
      sums[index] = `平均涨幅: ${avgChange.toFixed(2)}%`
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}
```

## 🔧 底部操作区设计

### 操作按钮组
```html
<div class="form-footer">
  <div class="button-group">
    <!-- 草稿状态按钮 -->
    <template v-if="formData.approval_status === 0">
      <el-button 
        type="primary" 
        :icon="DocumentAdd"
        @click="saveDraft"
        :loading="saving"
      >
        保存草稿
      </el-button>
      <el-button 
        type="success" 
        :icon="Promotion"
        @click="submitApproval"
        :disabled="!canSubmit"
        :loading="submitting"
      >
        提交审批
      </el-button>
      <el-button 
        type="danger" 
        :icon="Delete"
        @click="deleteOrder"
        :loading="deleting"
      >
        删除
      </el-button>
    </template>
    
    <!-- 审批中状态按钮 -->
    <template v-else-if="formData.approval_status === 1">
      <el-button 
        type="warning" 
        :icon="RefreshLeft"
        @click="recallApproval"
        :loading="recalling"
      >
        撤回审批
      </el-button>
      <el-button 
        type="info" 
        :icon="View"
        @click="viewApprovalProgress"
      >
        查看进度
      </el-button>
    </template>
    
    <!-- 已通过状态按钮 -->
    <template v-else-if="formData.approval_status === 2">
      <el-button 
        type="success" 
        :icon="Download"
        @click="exportPriceList"
      >
        导出报价单
      </el-button>
      <el-button 
        type="primary" 
        :icon="CopyDocument"
        @click="copyAsNew"
      >
        复制为新报价单
      </el-button>
      <el-button 
        type="danger" 
        :icon="CircleClose"
        @click="voidOrder"
      >
        作废
      </el-button>
    </template>
    
    <!-- 已拒绝状态按钮 -->
    <template v-else-if="formData.approval_status === 3">
      <el-button 
        type="primary" 
        :icon="Edit"
        @click="enableEdit"
      >
        修改
      </el-button>
      <el-button 
        type="success" 
        :icon="Promotion"
        @click="resubmitApproval"
        :disabled="!canSubmit"
      >
        重新提交
      </el-button>
    </template>
    
    <!-- 通用按钮 -->
    <el-button 
      :icon="Back"
      @click="goBack"
    >
      返回列表
    </el-button>
  </div>
  
  <div class="form-tips">
    <el-alert 
      :title="getStatusTip()" 
      type="info" 
      :closable="false"
      show-icon
    />
  </div>
</div>
```

### 状态提示信息
```javascript
const getStatusTip = () => {
  switch (formData.approval_status) {
    case 0:
      return '草稿状态下可自由编辑，完成后请提交审批'
    case 1:
      return '审批中，如需修改请先撤回审批'
    case 2:
      return '审批已通过，报价单已生效，可导出使用'
    case 3:
      return '审批被拒绝，请根据审批意见修改后重新提交'
    case 4:
      return '审批已终止，报价单已失效'
    case 5:
      return '审批已撤回，可继续编辑后重新提交'
    case 6:
      return '报价单已作废，不可再使用'
    default:
      return '请完善报价单信息'
  }
}
```

这个表单UI设计充分考虑了审批流程的各种状态，提供了直观的状态展示和相应的操作功能，确保用户能够清楚地了解当前状态并进行正确的操作。
