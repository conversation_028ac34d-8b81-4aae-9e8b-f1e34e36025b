<?php
declare(strict_types=1);

namespace app\notice\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\utils\CacheUtil;
use app\notice\model\NoticeQueueModel;
use app\notice\model\NoticeMessageModel;
use app\notice\model\NoticeRecipientModel;
use app\notice\service\channel\ChannelFactory;
use app\notice\service\interfaces\QueueServiceInterface;
use think\facade\Log;
use think\facade\Cache;

/**
 * todo 不使用model操作，使用封装的CrudServiceTrait中的对应方法操作
 * 消息发送队列表服务类
 */
class NoticeQueueService extends BaseService implements QueueServiceInterface
{
	
	use CrudServiceTrait;
	/**
	 * 单例实例
	 *
	 * @var NoticeQueueService|null
	 */
	protected static ?NoticeQueueService $instance = null;
	
	/**
	 * 构造函数
	 */
	protected function __construct()
	{
		$this->model = new NoticeQueueModel();
		parent::__construct();
		
		// 初始化配置
		$this->initialize();
	}
	
	/**
	 * 初始化方法
	 */
	protected function initialize(): void
	{
		// 队列处理不受数据权限限制
		$this->crudService->setEnableDataPermission(false);
		
		// 设置字段场景
		$this->crudService->setFieldScenes([
			'list'   => [
				'id',
				'message_id',
				'channel',
				'recipient_ids',
				'status',
				'scheduled_time',
				'process_time',
				'retry_count',
				'error_message',
				'priority',
				'tenant_id'
			],
			'detail' => ['*']
		]);
	}
	
	/**
	 * 获取单例
	 *
	 * @return static
	 */
	public static function getInstance(): self
	{
		if (is_null(static::$instance)) {
			static::$instance = new static();
		}
		return static::$instance;
	}
	
	/**
	 * 添加消息到队列
	 *
	 * @param int|string $messageId    消息ID
	 * @param string     $channel      通道编码
	 * @param array      $recipientIds 接收人ID数组
	 * @param array      $options      选项参数
	 * @return int 成功返回队列ID，失败返回0
	 */
	public function addToQueue(int|string $messageId, string $channel, array $recipientIds, array $options = []): int
	{
		try {
			// 检查消息是否存在
			$message = NoticeMessageModel::find($messageId);
			if (!$message) {
				Log::error("添加消息到队列失败: 消息不存在 ID={$messageId}");
				return 0;
			}
			
			// 检查接收人是否存在
			if (empty($recipientIds)) {
				Log::error("添加消息到队列失败: 接收人为空 MessageID={$messageId}");
				return 0;
			}
			
			// 创建队列记录
			$queueData = [
				'message_id'     => $messageId,
				'channel'        => $channel,
				'recipient_ids'  => json_encode($recipientIds, JSON_UNESCAPED_UNICODE),
				'status'         => 0,
				// 待处理
				'retry_count'    => 0,
				'scheduled_time' => $options['scheduled_time'] ?? date('Y-m-d H:i:s'),
				'tenant_id'      => $message->tenant_id,
				// 使用消息的租户ID
				'priority'       => $options['priority'] ?? 0
			];
			
			// 使用CrudService添加队列项
			$queueId = $this->crudService->add($queueData);
			
			if (!$queueId) {
				Log::error("添加消息到队列失败: 插入队列记录失败");
				return 0;
			}
			
			return $queueId;
		}
		catch (\Exception $e) {
			Log::error("添加消息到队列异常: " . $e->getMessage());
			return 0;
		}
	}
	
	/**
	 * 批量添加消息到队列
	 *
	 * @param array $queueItems 队列项数组，每项包含 messageId, channel, recipientIds
	 * @return bool 是否成功
	 */
	public function batchAddToQueue(array $queueItems): bool
	{
		try {
			// 开启事务
			$this->startTrans();
			$success = true;
			
			foreach ($queueItems as $item) {
				if (empty($item['messageId']) || empty($item['channel']) || empty($item['recipientIds'])) {
					continue;
				}
				
				// 检查消息是否存在
				$message = NoticeMessageModel::find($item['messageId']);
				if (!$message) {
					continue;
				}
				
				$queueData = [
					'message_id'     => $item['messageId'],
					'channel'        => $item['channel'],
					'recipient_ids'  => json_encode($item['recipientIds'], JSON_UNESCAPED_UNICODE),
					'status'         => 0,
					// 待处理
					'retry_count'    => 0,
					'scheduled_time' => $item['scheduledTime'] ?? date('Y-m-d H:i:s'),
					'tenant_id'      => $message->tenant_id,
					'priority'       => $item['priority'] ?? 0
				];
				
				// 使用CrudService添加
				$result = $this->crudService->add($queueData);
				if (!$result) {
					$success = false;
					break;
				}
			}
			
			if ($success) {
				$this->commit();
				return true;
			}
			else {
				$this->rollback();
				return false;
			}
		}
		catch (\Exception $e) {
			$this->rollback();
			Log::error("批量添加消息到队列异常: " . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 获取租户过滤状态
	 *
	 * @return bool
	 */
	public function getTenantFilter(): bool
	{
		// 检查CrudService是否有此方法
		if (method_exists($this->crudService, 'getTenantFilter')) {
			// 直接调用CrudService的方法
			return $this->crudService->getTenantFilter();
		}
		
		// 默认返回true，表示启用租户过滤
		return true;
	}
	
	/**
	 * 设置租户过滤状态
	 *
	 * @param bool $status
	 * @return void
	 */
	public function setTenantFilter(bool $status): void
	{
		// 检查CrudService是否有此方法
		if (method_exists($this->crudService, 'setTenantFilter')) {
			// 直接调用CrudService的方法
			$this->crudService->setTenantFilter($status);
		}
		
		// 如果没有此方法，记录一个警告
		Log::warning('CrudService没有setTenantFilter方法，无法设置租户过滤状态');
	}
	
	/**
	 * 执行自定义查询回调
	 *
	 * @param \Closure $callback 查询回调函数
	 * @return mixed 回调函数的返回值
	 */
	public function queryCallback(\Closure $callback)
	{
		// 检查CrudService是否有此方法
		if (method_exists($this->crudService, 'queryCallback')) {
			// 直接调用CrudService的方法
			return $this->crudService->queryCallback($callback);
		}
		
		// 如果没有此方法，直接在模型上执行回调
		$query = $this->model->db();
		return $callback($query);
	}
	
	/**
	 * 处理队列中的消息
	 *
	 * @param int $limit 处理数量限制
	 * @return array 处理结果 ['success' => 成功数, 'failed' => 失败数]
	 */
	public function processQueue(int $limit = 10): array
	{
		$success = 0;
		$failed  = 0;
		
		try {
			// 保存当前租户和数据权限状态
			$oldDataPermission = $this->crudService->getEnableDataPermission();
			$oldTenantFilter   = $this->getTenantFilter();
			
			// 关闭数据权限和租户过滤，以处理所有队列项
			$this->crudService->setEnableDataPermission(false);
//			$this->setTenantFilter(false);
			
			// 使用自定义查询回调查询待处理的队列项
			$queueItems = $this->queryCallback(function ($query) use ($limit) {
				return $query->where('status', 0)
				             ->order('scheduled_time', 'asc')
				             ->limit($limit)
				             ->select();
			});
			
			if ($queueItems->isEmpty()) {
				// 恢复原始设置
				$this->crudService->setEnableDataPermission($oldDataPermission);
//				$this->setTenantFilter($oldTenantFilter);
				
				return [
					'success' => 0,
					'failed'  => 0
				];
			}
			
			foreach ($queueItems as $item) {
				// 在处理非当前租户的消息时记录日志
				if ($item->tenant_id != request()->tenantId) {
					Log::info("跨租户消息处理: 当前租户ID={}, 消息租户ID={}, 消息ID={}", [
						request()->tenantId,
						$item->tenant_id,
						$item->message_id
					]);
				}
				
				// 标记为处理中
				$item->status       = 1; // 处理中
				$item->process_time = date('Y-m-d H:i:s');
				$item->save();
				
				// 获取消息和接收人
				$message = NoticeMessageModel::find($item->message_id);
				if (!$message) {
					$this->markQueueItemFailed($item, '消息不存在');
					$failed++;
					continue;
				}
				
				// 解析接收人ID
				$recipientIds = json_decode($item->recipient_ids, true)
					?: [];
				if (empty($recipientIds)) {
					$this->markQueueItemFailed($item, '接收人为空');
					$failed++;
					continue;
				}
				
				// 获取通道服务
				$channelService = ChannelFactory::getChannel($item->channel);
				if (!$channelService) {
					$this->markQueueItemFailed($item, "通道不存在: {$item->channel}");
					$failed++;
					continue;
				}
				
				// 发送消息
				$sendResult = $channelService->send($message->toArray(), $recipientIds);
				
				if ($sendResult) {
					// 发送成功
					$item->status = 2; // 已处理
					$item->save();
					$success++;
					
					// 清除用户未读消息缓存
					if ($item->channel === 'site') {
						foreach ($recipientIds as $userId) {
							CacheUtil::delete(NoticeMessageService::UNREAD_COUNT_CACHE_PREFIX . $userId);
						}
					}
				}
				else {
					// 发送失败
					$this->markQueueItemFailed($item, '发送失败');
					$failed++;
				}
			}
			
			// 恢复原始设置
			$this->crudService->setEnableDataPermission($oldDataPermission);
//			$this->setTenantFilter($oldTenantFilter);
			
			return [
				'success' => $success,
				'failed'  => $failed
			];
		}
		catch (\Exception $e) {
			Log::error("处理队列异常: " . $e->getMessage());
			
			// 确保恢复原始设置
			if (isset($oldDataPermission)) {
				$this->crudService->setEnableDataPermission($oldDataPermission);
			}
			
			return [
				'success' => $success,
				'failed'  => $failed + 1
			];
		}
	}
	
	/**
	 * 标记队列项为失败
	 *
	 * @param NoticeQueueModel $item         队列项
	 * @param string           $errorMessage 错误信息
	 * @return bool 是否成功
	 */
	protected function markQueueItemFailed(NoticeQueueModel $item, string $errorMessage): bool
	{
		try {
			$item->status        = 3; // 处理失败
			$item->error_message = $errorMessage;
			$item->retry_count   += 1;
			return $item->save();
		}
		catch (\Exception $e) {
			Log::error("标记队列项失败异常: " . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 重试队列项
	 *
	 * @param int $queueId 队列ID
	 * @return bool 是否成功
	 */
	public function retryQueueItem(int $queueId): bool
	{
		try {
			// 使用自定义查询方式获取队列项
			$item = $this->model->find($queueId);
			
			if (!$item || $item->status != 3) { // 只能重试失败的项
				return false;
			}
			
			// 更新队列项状态
			$updateData = [
				'status'         => 0,
				// 重置为待处理
				'scheduled_time' => date('Y-m-d H:i:s'),
				'process_time'   => null,
				'error_message'  => null
			];
			
			// 直接使用模型更新
			$item->save($updateData);
			return true;
		}
		catch (\Exception $e) {
			Log::error("重试队列项异常: " . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 批量重试队列项
	 *
	 * @param array $queueIds 队列ID数组
	 * @return array 处理结果 ['success' => 成功数, 'failed' => 失败数]
	 */
	public function batchRetryQueueItems(array $queueIds): array
	{
		$success = 0;
		$failed  = 0;
		
		try {
			foreach ($queueIds as $queueId) {
				$result = $this->retryQueueItem($queueId);
				if ($result) {
					$success++;
				}
				else {
					$failed++;
				}
			}
		}
		catch (\Exception $e) {
			Log::error("批量重试队列项异常: " . $e->getMessage());
		}
		
		return [
			'success' => $success,
			'failed'  => $failed
		];
	}
	
	/**
	 * 获取队列统计信息
	 *
	 * @return array 统计信息
	 */
	public function getQueueStats(): array
	{
		try {
			// 临时保存当前状态
			$oldDataPermission = $this->crudService->getEnableDataPermission();
			$oldTenantFilter   = $this->getTenantFilter();
			
			// 关闭数据权限和租户过滤
			$this->crudService->setEnableDataPermission(false);
//			$this->setTenantFilter(false);
			
			// 使用自定义查询
			$stats = $this->queryCallback(function ($query) {
				// 待处理数量
				$pending = $query->where('status', 0)
				                 ->count();
				// 处理中数量
				$processing = $query->where('status', 1)
				                    ->count();
				// 已处理数量
				$processed = $query->where('status', 2)
				                   ->count();
				// 失败数量
				$failed = $query->where('status', 3)
				                ->count();
				
				return [
					'pending'    => $pending,
					'processing' => $processing,
					'processed'  => $processed,
					'failed'     => $failed,
					'total'      => $pending + $processing + $processed + $failed
				];
			});
			
			// 恢复原始设置
			$this->crudService->setEnableDataPermission($oldDataPermission);
//			$this->setTenantFilter($oldTenantFilter);
			
			return $stats;
		}
		catch (\Exception $e) {
			Log::error("获取队列统计信息异常: " . $e->getMessage());
			return [
				'pending'    => 0,
				'processing' => 0,
				'processed'  => 0,
				'failed'     => 0,
				'total'      => 0
			];
		}
	}
	
	/**
	 * 清理已处理的队列项
	 *
	 * @param int $days 保留天数
	 * @return int 清理数量
	 */
	public function cleanProcessedItems(int $days = 7): int
	{
		try {
			// 计算截止时间
			$cutoffTime = date('Y-m-d H:i:s', strtotime("-{$days} days"));
			
			// 保存当前设置
			$oldDataPermission = $this->crudService->getEnableDataPermission();
			$oldTenantFilter   = $this->getTenantFilter();
			
			// 关闭数据权限和租户过滤
			$this->crudService->setEnableDataPermission(false);
//			$this->setTenantFilter(false);
			
			// 使用自定义查询删除数据
			$count = $this->queryCallback(function ($query) use ($cutoffTime) {
				return $query->where('status', 'in', [
					2,
					3
				]) // 已处理或失败的
				             ->where('process_time', '<', $cutoffTime)
				             ->delete();
			});
			
			// 恢复原始设置
			$this->crudService->setEnableDataPermission($oldDataPermission);
//			$this->setTenantFilter($oldTenantFilter);
			
			return $count;
		}
		catch (\Exception $e) {
			Log::error("清理队列项异常: " . $e->getMessage());
			return 0;
		}
	}
} 