<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmLead;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 线索表服务类
 */
class CrmLeadService extends BaseService
{

    use ExportableTrait,ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmLead();
        parent::__construct();
    }

    /**
     * 初始化配置
     */
    protected function initialize(): void
    {
        // 线索列表需要数据权限过滤
        $this->crudService->setEnableDataPermission(true);

        // 设置字段场景
        $this->crudService->setFieldScenes([
            'list' => [
                'id', 'lead_name', 'company', 'position', 'mobile', 'phone', 'email',
                'status', 'source', 'level', 'industry', 'address', 'remark',
                'is_transformed', 'transformed_id', 'transformed_time', 'owner_user_id',
                'last_followed_at', 'next_followed_at', 'in_pool', 'creator_id', 'created_at'
            ],
            'detail' => ['*'],
            'select' => ['id', 'lead_name', 'company', 'mobile']
        ]);
    }
    
    /**
     * 获取搜索字段配置
     *
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

            'lead_name' => ['type' => 'like'],

            'company' => ['type' => 'like'],

            'mobile' => ['type' => 'like'],

            'email' => ['type' => 'like'],

            'status' => ['type' => 'eq'],

            'source' => ['type' => 'eq'],

            'level' => ['type' => 'eq'],

            'industry' => ['type' => 'eq'],

            'is_transformed' => ['type' => 'eq'],

            'transformed_time' => ['type' => 'date'],

            'owner_user_id' => ['type' => 'eq'],

            'last_followed_at' => ['type' => 'date'],

            'next_followed_at' => ['type' => 'date'],

            'in_pool' => ['type' => 'eq'],

        ];
    }
    
    /**
     * 获取验证规则
     *
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            // 在这里定义验证规则
            // 例如：'username' => 'require|unique:crm_lead',
        ];
        
        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     *
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * 获取线索池数据（临时禁用数据权限）
     *
     * @param array $params 查询参数
     * @return array
     */
    public function getPoolLeads(array $params): array
    {
        // 保存当前数据权限状态
        $originalPermission = $this->crudService->getEnableDataPermission();

        try {
            // 临时禁用数据权限
            $this->crudService->setEnableDataPermission(false);

            // 添加线索池查询条件
            $params['in_pool'] = 1;
            $params['owner_user_id'] = 0; // 无负责人的线索

            // 执行查询
            $result = $this->search($params);

            return $result;
        } finally {
            // 恢复原始数据权限状态
            $this->crudService->setEnableDataPermission($originalPermission);
        }
    }

    /**
     * 认领线索
     *
     * @param int $leadId 线索ID
     * @param int $userId 用户ID
     * @return bool
     */
    public function claimLead(int $leadId, int $userId): bool
    {
        // 保存当前数据权限状态
        $originalPermission = $this->crudService->getEnableDataPermission();

        try {
            // 临时禁用数据权限（认领操作需要访问线索池中的线索）
            $this->crudService->setEnableDataPermission(false);

            // 验证线索是否在线索池中
            $lead = $this->getOne(['id' => $leadId, 'in_pool' => 1, 'owner_user_id' => 0]);
            if (!$lead) {
                throw new \Exception('线索不存在或不在线索池中');
            }

            // 更新线索负责人和状态
            $result = $this->edit([
                'owner_user_id' => $userId,
                'in_pool' => 0,
                'updated_id' => $userId
            ], ['id' => $leadId]);

            return $result;
        } finally {
            // 恢复原始数据权限状态
            $this->crudService->setEnableDataPermission($originalPermission);
        }
    }

    /**
     * 分配线索
     *
     * @param int $leadId 线索ID
     * @param int $fromUserId 原负责人ID
     * @param int $toUserId 新负责人ID
     * @return bool
     */
    public function assignLead(int $leadId, int $fromUserId, int $toUserId): bool
    {
        // 保存当前数据权限状态
        $originalPermission = $this->crudService->getEnableDataPermission();

        try {
            // 临时禁用数据权限（分配操作可能需要跨用户访问）
            $this->crudService->setEnableDataPermission(false);

            // 验证线索存在
            $lead = $this->getOne(['id' => $leadId]);
            if (!$lead) {
                throw new \Exception('线索不存在');
            }

            // 更新线索负责人
            $result = $this->edit([
                'owner_user_id' => $toUserId,
                'in_pool' => 0,
                'updated_id' => get_user_id()
            ], ['id' => $leadId]);

            return $result;
        } finally {
            // 恢复原始数据权限状态
            $this->crudService->setEnableDataPermission($originalPermission);
        }
    }
}