<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\system\model\SystemTestCrudComprehensive;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 综合测试表-包含所有字段类型服务类
 */
class SystemTestCrudComprehensiveService extends BaseService
{

    use ExportableTrait;


    use ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new SystemTestCrudComprehensive();
        parent::__construct();
    }
    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

            'title' => ['type' => 'like'],

            'subtitle' => ['type' => 'like'],

            'status' => ['type' => 'eq'],

            'type' => ['type' => 'eq'],

            'category' => ['type' => 'eq'],

            'priority' => ['type' => 'eq'],

            'level' => ['type' => 'eq'],

            'mobile' => ['type' => 'like'],

            'amount' => ['type' => 'between'],

            'price' => ['type' => 'between'],

            'quantity' => ['type' => 'between'],

            'date' => ['type' => 'date'],

            'start_date' => ['type' => 'date'],

            'end_date' => ['type' => 'date'],

            'datetime' => ['type' => 'datetime'],

            'start_time' => ['type' => 'datetime'],

            'end_time' => ['type' => 'datetime'],

            'is_hot' => ['type' => 'eq'],

            'is_recommend' => ['type' => 'eq'],

            'is_top' => ['type' => 'eq'],

            'is_featured' => ['type' => 'eq'],

            'is_published' => ['type' => 'eq'],

            'score' => ['type' => 'between'],

            'rating' => ['type' => 'between'],

            'keywords' => ['type' => 'like'],

            'source' => ['type' => 'like'],

            'author' => ['type' => 'like'],

            'editor' => ['type' => 'like'],

            'publisher' => ['type' => 'like'],

            'region' => ['type' => 'eq'],

            'country' => ['type' => 'eq'],

            'language' => ['type' => 'eq'],

            'currency' => ['type' => 'eq'],

            'ip_address' => ['type' => 'like'],

            'remark' => ['type' => 'like'],

            'published_at' => ['type' => 'datetime'],

            'expired_at' => ['type' => 'datetime'],

            'created_at' => ['type' => 'datetime'],

            'updated_at' => ['type' => 'datetime'],

        ];
    }
    
    /**
     * 获取验证规则
     * 
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            // 在这里定义验证规则
            // 例如：'username' => 'require|unique:system_test_crud_comprehensive',
        ];
        
        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('id', $ids)->delete();
    }
} 