<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmBusinessProduct;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 商机产品关联表服务类
 */
class CrmBusinessProductService extends BaseService
{

    use ExportableTrait;


    use ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmBusinessProduct();
        parent::__construct();
    }
    
    /**
     * 获取搜索字段配置
     * 
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

        ];
    }
    
    /**
     * 获取验证规则 - 基于crm_data.sql字段约束
     *
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            'business_id' => 'require|integer|gt:0',
            'product_spec_id' => 'require|integer|gt:0',
            'spec_info' => 'max:500',
            'quantity' => 'require|float|gt:0',
            'unit_price' => 'require|float|egt:0',
            'discount_rate' => 'float|between:0,100',
            'discount_amount' => 'float|egt:0',
            'subtotal' => 'require|float|egt:0',
            'remark' => 'max:500',
        ];

        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('id', $ids)->delete();
    }
} 