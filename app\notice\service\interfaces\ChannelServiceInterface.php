<?php
declare(strict_types=1);

namespace app\notice\service\interfaces;

/**
 * 消息通道服务接口
 */
interface ChannelServiceInterface
{
    /**
     * 发送消息到指定通道
     *
     * @param array $message 消息数据
     * @param array $recipients 接收人列表
     * @param array $options 选项参数
     * @return bool 是否发送成功
     */
    public function send(array $message, array $recipients, array $options = []): bool;
    
    /**
     * 获取通道配置
     *
     * @param string $channel 通道编码
     * @return array|null 通道配置
     */
    public function getChannelConfig(string $channel): ?array;
    
    /**
     * 更新通道配置
     *
     * @param string $channel 通道编码
     * @param array $config 通道配置
     * @return bool 是否成功
     */
    public function updateChannelConfig(string $channel, array $config): bool;
    
    /**
     * 启用通道
     *
     * @param string $channel 通道编码
     * @return bool 是否成功
     */
    public function enableChannel(string $channel): bool;
    
    /**
     * 禁用通道
     *
     * @param string $channel 通道编码
     * @return bool 是否成功
     */
    public function disableChannel(string $channel): bool;
    
    /**
     * 获取所有可用通道
     *
     * @return array 通道列表
     */
    public function getAvailableChannels(): array;
    
    /**
     * 测试通道配置
     *
     * @param string $channel 通道编码
     * @param array $config 通道配置
     * @return array 测试结果 ['success' => true|false, 'message' => '结果信息']
     */
    public function testChannelConfig(string $channel, array $config): array;
} 