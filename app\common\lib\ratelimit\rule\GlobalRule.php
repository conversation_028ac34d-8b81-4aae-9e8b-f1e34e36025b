<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\rule;

use app\common\lib\ratelimit\algorithm\AlgorithmInterface;
use app\common\lib\ratelimit\identifier\IdentifierInterface;

/**
 * 全局限流规则
 */
class GlobalRule extends Rule
{
    /**
     * 规则类型
     */
    protected string $type = 'global';
    
    /**
     * 构造函数
     *
     * @param int $id 规则ID
     * @param string $name 规则名称
     * @param string $key 键名
     * @param AlgorithmInterface $algorithm 限流算法
     * @param IdentifierInterface $identifier 标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     */
    public function __construct(
        int $id,
        string $name,
        string $key,
        AlgorithmInterface $algorithm,
        IdentifierInterface $identifier,
        int $limitCount,
        int $timeWindow
    ) {
        parent::__construct($id, $name, $key, $algorithm, $identifier, $limitCount, $timeWindow);
    }
    
    /**
     * 获取完整键名
     * 
     * @return string 完整键名
     */
    protected function getKey(): string
    {
        return 'global:' . $this->key;
    }
} 