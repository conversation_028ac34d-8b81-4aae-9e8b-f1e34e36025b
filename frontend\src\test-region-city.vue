<template>
  <div style="padding: 20px;">
    <h2>地区选择器城市测试</h2>
    
    <div style="margin-bottom: 20px;">
      <h3>测试场景：出差申请中的出发地和目的地选择</h3>
    </div>
    
    <div style="margin-bottom: 20px;">
      <h4>出发地选择（level=2, return-type="object"）：</h4>
      <RegionSelector
        v-model="departureRegion"
        placeholder="请选择出发地"
        :level="2"
        return-type="object"
        :field-mapping="{
          province: 'province',
          city: 'city'
        }"
        style="width: 300px"
        @change="handleDepartureChange"
      />
      <div style="margin-top: 10px;">
        <strong>选择结果：</strong>{{ JSON.stringify(departureRegion, null, 2) }}
      </div>
      <div style="margin-top: 5px;">
        <strong>提取的城市名：</strong>{{ departureCity }}
      </div>
    </div>
    
    <div style="margin-bottom: 20px;">
      <h4>目的地选择（level=2, return-type="object"）：</h4>
      <RegionSelector
        v-model="destinationRegion"
        placeholder="请选择目的地"
        :level="2"
        return-type="object"
        :field-mapping="{
          province: 'province',
          city: 'city'
        }"
        style="width: 300px"
        @change="handleDestinationChange"
      />
      <div style="margin-top: 10px;">
        <strong>选择结果：</strong>{{ JSON.stringify(destinationRegion, null, 2) }}
      </div>
      <div style="margin-top: 5px;">
        <strong>提取的城市名：</strong>{{ destinationCity }}
      </div>
    </div>
    
    <div style="margin-bottom: 20px;">
      <h4>模拟表单数据：</h4>
      <pre>{{ JSON.stringify(formItem, null, 2) }}</pre>
    </div>
    
    <div style="margin-bottom: 20px;">
      <el-button @click="setTestData">设置测试数据</el-button>
      <el-button @click="clearData">清空数据</el-button>
    </div>
    
    <div>
      <h3>变化日志：</h3>
      <div v-for="(log, index) in logs" :key="index" style="margin-bottom: 5px;">
        {{ log }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import RegionSelector from '@/components/custom/RegionSelector/index.vue'

const departureRegion = ref({})
const destinationRegion = ref({})
const logs = ref<string[]>([])

const departureCity = computed(() => {
  return departureRegion.value?.city || ''
})

const destinationCity = computed(() => {
  return destinationRegion.value?.city || ''
})

const formItem = computed(() => {
  return {
    departure_city: departureCity.value,
    destination_city: destinationCity.value,
    departure_region: departureRegion.value,
    destination_region: destinationRegion.value
  }
})

const handleDepartureChange = (value: any, selectedData: any) => {
  logs.value.unshift(`出发地变化: ${JSON.stringify(value)} | 城市: ${value?.city || ''}`)
  console.log('出发地变化:', value, selectedData)
}

const handleDestinationChange = (value: any, selectedData: any) => {
  logs.value.unshift(`目的地变化: ${JSON.stringify(value)} | 城市: ${value?.city || ''}`)
  console.log('目的地变化:', value, selectedData)
}

const setTestData = () => {
  departureRegion.value = { province: '北京市', city: '市辖区' }
  destinationRegion.value = { province: '上海市', city: '市辖区' }
  logs.value.unshift('设置测试数据: 北京 -> 上海')
}

const clearData = () => {
  departureRegion.value = {}
  destinationRegion.value = {}
  logs.value.unshift('清空所有数据')
}
</script>
