<?php
/**
 * 表格列组件映射配置
 */
return [
    // 字段名称模式映射
    'field_name_patterns' => [
        'status' => 'switch',
        'enable' => 'switch',
        'visible' => 'switch',
        'active' => 'switch',
        'image' => 'image',
        'photo' => 'image',
        'avatar' => 'image',
        'icon' => 'image',
        'logo' => 'image',
        'file' => 'document',
        'attachment' => 'document',
        'url' => 'link',
        'link' => 'link',
        'email' => 'copyable',
        'money' => 'currency',
        'price' => 'currency',
        'amount' => 'currency',
        'content' => 'long_text',
        'description' => 'long_text',
        'detail' => 'long_text',
        'remark' => 'long_text',
        'tags' => 'tag',
        'progress' => 'progress',
        'percent' => 'progress',
        'qrcode' => 'qrcode',
        'video' => 'media',
        'audio' => 'media'
    ],
    
    // 字段类型模式映射
    'field_type_patterns' => [
        'tinyint(1)' => 'switch',
        'datetime' => 'text',
        'timestamp' => 'text',
        'date' => 'text',
        'decimal' => 'currency',
        'text' => 'long_text',
        'mediumtext' => 'long_text',
        'longtext' => 'long_text'
    ],
    
    // 格式化标记映射
    'formatter_mappings' => [
        'status' => 'status',
        'enum' => 'tag',
        'datetime' => 'text',
        'date' => 'text',
        'currency' => 'currency',
        'money' => 'currency',
        'image' => 'image',
        'file' => 'document',
        'link' => 'link',
        'email' => 'copyable',
        'qrcode' => 'qrcode',
        'media' => 'media',
        'longtext' => 'long_text'
    ],
    
    // 新格式标记映射
    'component_mappings' => [
        'switch' => 'switch',
        'tag' => 'tag',
        'image' => 'image',
        'file' => 'document',
        'currency' => 'currency',
        'money' => 'currency',
        'longtext' => 'long_text',
        'copyable' => 'copyable',
        'link' => 'link',
        'progress' => 'progress',
        'qrcode' => 'qrcode',
        'editable' => 'editable'
    ],
    
    // 组件配置
    'switch' => [
        'component' => 'SwitchColumn',
        'props' => [
            'activeValue' => 1,
            'inactiveValue' => 0,
            'updateApi' => '{api}.updateField'
        ],
        'isDirectComponent' => true,
        'needApiMethod' => true
    ],
    
    'status' => [
        'component' => 'TagColumn',
        'props' => [
            'options' => []
        ],
        'isDirectComponent' => true,
        'needOptions' => true
    ],
    
    'image' => [
        'component' => 'ImageColumn',
        'props' => [
            'width' => 60,
            'height' => 60,
            'previewable' => true
        ],
        'isDirectComponent' => true
    ],
    
    'document' => [
        'component' => 'DocumentColumn',
        'props' => [],
        'isDirectComponent' => true
    ],
    
    'link' => [
        'component' => 'LinkColumn',
        'props' => [
            'type' => 'primary'
        ],
        'isDirectComponent' => true
    ],
    
    'text' => [
        'component' => 'text-column',
        'props' => []
    ],
    
    'currency' => [
        'component' => 'CurrencyColumn',
        'props' => [
            'currency' => 'CNY',
            'digits' => 2
        ],
        'isDirectComponent' => true
    ],
    
    'tag' => [
        'component' => 'TagColumn',
        'props' => [],
        'isDirectComponent' => true,
        'needOptions' => true
    ],
    
    'long_text' => [
        'component' => 'LongTextColumn',
        'props' => [
            'maxLines' => 3,
            'tooltip' => true
        ],
        'isDirectComponent' => true
    ],
    
    'copyable' => [
        'component' => 'CopyableColumn',
        'props' => [],
        'isDirectComponent' => true
    ],
    
    'progress' => [
        'component' => 'ProgressColumn',
        'props' => [
            'max' => 100
        ],
        'isDirectComponent' => true
    ],
    
    'qrcode' => [
        'component' => 'QrcodeColumn',
        'props' => [
            'width' => 120,
            'previewSize' => 70
        ],
        'isDirectComponent' => true
    ],
    
    'media' => [
        'component' => 'MediaColumn',
        'props' => [
            'width' => 120,
            'autoGenerateCover' => false
        ],
        'isDirectComponent' => true
    ],
    
    'editable' => [
        'component' => 'EditableColumn',
        'props' => [
            'editType' => 'text',
            'updateApi' => '{api}.updateField'
        ],
        'isDirectComponent' => true,
        'needApiMethod' => true
    ],

    'action' => [
        'component' => 'ActionColumn',
        'props' => [],
        'isDirectComponent' => true,
        'needOptions' => true
    ]
]; 