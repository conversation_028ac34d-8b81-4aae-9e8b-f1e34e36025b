# 每日报价单价格变动字段保存修复报告

## 📋 问题描述

在每日报价单系统中，`daily_price_item` 表的 `price_change` 和 `change_rate` 字段没有被正确保存，导致：

1. 数据库中这两个字段值为 0 或 NULL
2. 后端获取器方法无法正确计算涨跌幅格式
3. 审批流程中的 formData 缺少完整的价格变动信息

## 🔍 根本原因分析

### 1. 前端数据结构不完整
- **问题**: 前端只计算并传递 `price_change_rate`（百分比）
- **缺失**: 没有传递 `old_price`、`price_change`、`change_rate` 字段
- **影响**: 后端无法获得完整的价格变动数据

### 2. 后端计算逻辑依赖缺失
- **问题**: `saveOrderItems` 方法依赖 `old_price` 字段进行计算
- **现状**: 前端没有提供 `old_price`，导致计算失败
- **结果**: 价格变动字段保存为默认值 0

### 3. 数据流程断裂
- **设计**: 前端从昨日价格计算涨跌幅
- **实现**: 没有将昨日价格作为 `old_price` 传递给后端
- **后果**: 前后端数据不一致

## 🛠️ 修复方案

### 1. 前端修复

#### A. 更新类型定义
```typescript
// frontend/src/api/daily/dailyPriceOrder.ts
export interface DailyPriceItem {
  // ... 其他字段
  old_price?: number // 昨日价格
  price_change?: number // 价格变动金额
  change_rate?: number // 变动比例
  price_change_rate?: number | null // 涨幅百分比（前端计算用）
  // ... 其他字段
}
```

#### B. 修复计算方法
```javascript
// frontend/src/views/daily/daily_price_order/components/PriceItemTable.vue
const calculatePriceChange = (item: DailyPriceItem) => {
  // ... 验证逻辑
  
  const currentPrice = item.unit_price
  const yesterdayPrice = yesterdayData.price
  const priceChange = currentPrice - yesterdayPrice
  const changeRate = (priceChange / yesterdayPrice) * 100

  // 设置完整的价格变动数据
  item.old_price = yesterdayPrice
  item.price_change = priceChange
  item.change_rate = changeRate
  item.price_change_rate = changeRate  // 保持兼容性
  item.is_manual_price = 1
}
```

### 2. 后端修复

#### A. 优化保存逻辑
```php
// app/daily/service/DailyPriceOrderService.php
private function saveOrderItems(int $orderId, array $items): void
{
    foreach ($items as $index => $item) {
        // 确保价格变动字段的完整性
        if (isset($item['unit_price'])) {
            // 如果前端已经计算了价格变动数据，直接使用
            if (isset($item['old_price']) && isset($item['price_change']) && isset($item['change_rate'])) {
                // 前端已计算，直接使用
            }
            // 如果只有 old_price，重新计算
            elseif (isset($item['old_price'])) {
                $item['price_change'] = $item['unit_price'] - $item['old_price'];
                $item['change_rate'] = $item['old_price'] > 0 ? 
                    ($item['price_change'] / $item['old_price']) * 100 : 0;
            }
            // 如果没有 old_price，设置默认值
            else {
                $item['old_price'] = 0;
                $item['price_change'] = 0;
                $item['change_rate'] = 0;
            }
        }
        
        // 清理前端临时字段
        unset($item['price_change_rate'], $item['isDuplicate']);
        
        // 保存数据
        $res = (new DailyPriceItem())->saveByCreate($item);
    }
}
```

#### B. 修复获取器方法
```php
// app/daily/model/DailyPriceItem.php
public function getPriceChangeFormatAttr($value, $data)
{
    $priceChange = $data['price_change'] ?? 0;
    $changeRate = $data['change_rate'] ?? 0;

    if ($priceChange == 0) {
        return '无变动';  // 修复：显示"无变动"而不是"-"
    }

    $sign = $priceChange > 0 ? '+' : '';
    $priceText = $sign . number_format($priceChange, 2);
    $rateText = $sign . number_format($changeRate, 2) . '%';

    return "{$priceText}({$rateText})";
}
```

## 📊 修复效果

### 1. 数据完整性
- ✅ `old_price` 字段正确保存昨日价格
- ✅ `price_change` 字段正确保存价格变动金额
- ✅ `change_rate` 字段正确保存变动比例

### 2. 显示格式
- ✅ 涨价显示：`+5.20(+5.20%)`
- ✅ 降价显示：`-3.50(-3.50%)`
- ✅ 无变动显示：`无变动`

### 3. 审批流程
- ✅ formData 包含完整的价格变动信息
- ✅ 审批人可以看到详细的涨跌幅数据
- ✅ 工作流实例数据完整

## 🧪 测试验证

### 测试用例1：涨价场景
```
昨日价格: 100.00
今日价格: 105.50
预期结果:
- old_price: 100.00
- price_change: 5.50
- change_rate: 5.50
- 显示格式: +5.50(+5.50%)
```

### 测试用例2：降价场景
```
昨日价格: 200.00
今日价格: 190.00
预期结果:
- old_price: 200.00
- price_change: -10.00
- change_rate: -5.00
- 显示格式: -10.00(-5.00%)
```

### 测试用例3：无变动场景
```
昨日价格: 150.00
今日价格: 150.00
预期结果:
- old_price: 150.00
- price_change: 0.00
- change_rate: 0.00
- 显示格式: 无变动
```

## 📝 验证清单

- [ ] 前端计算方法正确传递完整数据
- [ ] 后端保存逻辑正确处理价格变动字段
- [ ] 数据库中字段值正确保存
- [ ] 获取器方法正确格式化显示
- [ ] 审批流程中 formData 包含完整信息
- [ ] 各种价格变动场景显示正确

## 🚀 部署建议

1. **备份数据**: 修复前备份相关数据表
2. **测试验证**: 在测试环境充分验证修复效果
3. **分步部署**: 先部署后端修复，再部署前端修复
4. **数据修复**: 对历史数据进行价格变动字段的补充计算

## 📞 技术支持

如遇问题，请检查：
1. 前端是否正确传递 `old_price` 等字段
2. 后端是否正确处理价格变动计算
3. 数据库字段类型是否正确
4. 获取器方法是否正确调用
