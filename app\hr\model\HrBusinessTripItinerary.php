<?php
declare(strict_types=1);

namespace app\hr\model;

use app\common\core\base\BaseModel;

/**
 * 出差行程明细表模型
 */
class HrBusinessTripItinerary extends BaseModel
{
	// 设置表名
	protected $name = 'hr_business_trip_itinerary';
	
	// 设置主键
	protected $pk = 'id';
	
	// 字段类型转换
	protected $type = [
		'business_trip_id' => 'integer',
		'trip_mode'        => 'integer',
		'duration'         => 'float',
		'transport_type'   => 'integer',
	];
	
	// 单程往返常量
	const MODE_ROUND_TRIP = 1;    // 往返
	const MODE_ONE_WAY    = 2;    // 单程
	
	// 交通工具常量
	const TRANSPORT_PLANE = 1;    // 飞机
	const TRANSPORT_TRAIN = 2;    // 高铁
	const TRANSPORT_RAIL  = 3;    // 火车
	const TRANSPORT_CAR   = 4;    // 汽车
	const TRANSPORT_OTHER = 5;    // 其他
	
	
	public function setDepartureCityCodeAttr($value)
	{
		if (empty($value)) {
			return '';
		}

		// 如果已经是字符串，直接返回
		if (is_string($value)) {
			return $value;
		}

		// 如果是数组，转换为 JSON 字符串
		if (is_array($value)) {
			return json_encode($value, JSON_UNESCAPED_UNICODE);
		}

		return '';
	}

	public function getDepartureCityCodeAttr($value)
	{
		if (empty($value)) {
			return [];
		}

		// 如果已经是数组，直接返回
		if (is_array($value)) {
			return $value;
		}

		// 如果是字符串，尝试 JSON 解析
		if (is_string($value)) {
			$decoded = json_decode($value, true);
			if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
				return $decoded;
			}
			// 如果不是 JSON，尝试按逗号分割（兼容旧数据）
			return explode(',', $value);
		}

		return [];
	}

	public function setDestinationCityCodeAttr($value)
	{
		if (empty($value)) {
			return '';
		}

		// 如果已经是字符串，直接返回
		if (is_string($value)) {
			return $value;
		}

		// 如果是数组，转换为 JSON 字符串
		if (is_array($value)) {
			return json_encode($value, JSON_UNESCAPED_UNICODE);
		}

		return '';
	}

	public function getDestinationCityCodeAttr($value)
	{
		if (empty($value)) {
			return [];
		}

		// 如果已经是数组，直接返回
		if (is_array($value)) {
			return $value;
		}

		// 如果是字符串，尝试 JSON 解析
		if (is_string($value)) {
			$decoded = json_decode($value, true);
			if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
				return $decoded;
			}
			// 如果不是 JSON，尝试按逗号分割（兼容旧数据）
			return explode(',', $value);
		}

		return [];
	}
	
	/**
	 * 获取默认搜索字段
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'business_trip_id' => ['type' => 'eq'],
			'departure_city'   => ['type' => 'like'],
			'destination_city' => ['type' => 'like'],
			'trip_mode'        => ['type' => 'eq'],
			'start_time'       => ['type' => 'datetime'],
			'end_time'         => ['type' => 'datetime'],
			'duration'         => ['type' => 'between'],
			'transport_type'   => ['type' => 'eq'],
			'created_at'       => ['type' => 'date'],
		];
	}
	
	/**
	 * 关联出差申请
	 */
	public function businessTrip()
	{
		return $this->belongsTo(HrBusinessTrip::class, 'business_trip_id', 'id');
	}
	
	/**
	 * 获取单程往返文本
	 */
	public function getTripModeTextAttr($value, $data)
	{
		$modes = [
			self::MODE_ROUND_TRIP => '往返',
			self::MODE_ONE_WAY    => '单程',
		];
		
		return $modes[$data['trip_mode']] ?? '未知';
	}
	
	/**
	 * 获取交通工具文本
	 */
	public function getTransportTypeTextAttr($value, $data)
	{
		$types = [
			self::TRANSPORT_PLANE => '飞机',
			self::TRANSPORT_TRAIN => '高铁',
			self::TRANSPORT_RAIL  => '火车',
			self::TRANSPORT_CAR   => '汽车',
			self::TRANSPORT_OTHER => '其他',
		];
		
		return $types[$data['transport_type']] ?? '未知';
	}
}