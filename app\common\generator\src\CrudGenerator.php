<?php
declare(strict_types=1);

namespace app\common\generator\src;

use app\common\generator\src\interfaces\GeneratorInterface;
use app\common\generator\src\generators\BackendGenerator;
use app\common\generator\src\generators\FrontendGenerator;
use app\common\generator\src\parsers\TableInfoParser;
use app\common\generator\src\engines\TemplateEngine;
use think\facade\Config;

/**
 * CRUD代码生成器主类
 * 负责协调前后端代码生成流程
 */
class CrudGenerator implements GeneratorInterface
{
	/**
	 * 模板引擎
	 *
	 * @var TemplateEngine
	 */
	protected TemplateEngine $templateEngine;
	
	/**
	 * 后端代码生成器
	 *
	 * @var BackendGenerator
	 */
	protected BackendGenerator $backendGenerator;
	
	/**
	 * 前端代码生成器
	 *
	 * @var FrontendGenerator
	 */
	protected FrontendGenerator $frontendGenerator;
	
	/**
	 * 表结构解析器
	 *
	 * @var TableInfoParser
	 */
	protected TableInfoParser $tableInfoParser;
	
	/**
	 * 全局配置
	 *
	 * @var array
	 */
	protected array $config;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		// 初始化模板引擎
		$this->templateEngine = new TemplateEngine();
		
		// 初始化表结构解析器
		$this->tableInfoParser = new TableInfoParser();
		
		// 加载配置
		$this->loadConfig();
		
		// 初始化代码生成器
		$this->backendGenerator  = new BackendGenerator($this->templateEngine, $this->config);
		$this->frontendGenerator = new FrontendGenerator($this->templateEngine, $this->config);
	}
	
	/**
	 * 加载配置
	 */
	protected function loadConfig(): void
	{
		// 基础配置路径
		$basePath = app()->getRootPath() . 'app/common/generator/config/';
		
		// 加载全局配置
		$globalConfig = include $basePath . 'generator_config.php';
		
		// 加载列组件映射配置
		$columnMapping = include $basePath . 'column_mapping.php';
		
		// 加载表单组件映射配置
		$formMapping = include $basePath . 'form_mapping.php';
		
		// 合并配置
		$this->config = [
			'global'         => $globalConfig,
			'column_mapping' => $columnMapping,
			'form_mapping'   => $formMapping
		];
	}
	
	/**
	 * 生成CRUD代码
	 *
	 * @param mixed $data    表名或表结构数据
	 * @param array $options 生成选项
	 * @return array 生成结果
	 */
	public function generate(mixed $data, array $options = []): array
	{
		// 检查参数类型
		if (!is_string($data)) {
			throw new \InvalidArgumentException('参数必须是字符串类型的表名');
		}
		
		$table = $data;
		
		// 解析表结构
		$tableInfo = $this->tableInfoParser->parse($table);
		
		// 调试输出
		file_put_contents(app()->getRootPath() . 'runtime/table_info_debug.txt', json_encode($tableInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
		
		// 生成结果
		$files = [];
		
		// 生成后端代码
		$backendFiles = $this->backendGenerator->generate($tableInfo, $options);
		$files = array_merge($files, $backendFiles);
		
		// 如果需要生成前端代码
		if (!empty($options['frontend'])) {
			$frontendFiles = $this->frontendGenerator->generate($tableInfo, $options);
			$files = array_merge($files, $frontendFiles);
		}
		
		return $files;
	}
}