<?php
// 测试工作流配置脚本
require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

try {
    echo "开始配置工作流类型...\n";
    
    // 工作流类型配置
    $workflowTypes = [
        ['name' => '出差申请', 'module_code' => 'hr', 'business_code' => 'hr_business_trip'],
        ['name' => '外出申请', 'module_code' => 'hr', 'business_code' => 'hr_outing'],
        ['name' => '样品邮寄申请', 'module_code' => 'office', 'business_code' => 'office_sample_mail'],
        ['name' => '付款申请', 'module_code' => 'finance', 'business_code' => 'finance_payment_approval'],
        ['name' => '报销申请', 'module_code' => 'finance', 'business_code' => 'finance_expense_reimbursement'],
        ['name' => '出库申请', 'module_code' => 'ims', 'business_code' => 'ims_outbound_approval'],
        ['name' => '出货申请', 'module_code' => 'ims', 'business_code' => 'ims_shipment_approval'],
    ];
    
    foreach ($workflowTypes as $type) {
        // 检查是否已存在
        $existing = Db::name('workflow_type')
                     ->where('business_code', $type['business_code'])
                     ->find();
        
        if (!$existing) {
            $data = array_merge($type, [
                'status' => 1,
                'creator_id' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'tenant_id' => 0
            ]);
            
            $id = Db::name('workflow_type')->insertGetId($data);
            echo "✅ 创建工作流类型: {$type['name']} (ID: {$id})\n";
        } else {
            echo "✅ 工作流类型已存在: {$type['name']} (ID: {$existing['id']})\n";
        }
    }
    
    echo "\n检查工作流定义...\n";
    
    // 标准流程配置
    $standardFlowConfig = [
        'nodeConfig' => [
            'nodeId' => 'start',
            'nodeName' => '发起人',
            'type' => 'promoter'
        ],
        'childNode' => [
            'nodeId' => 'approval1',
            'nodeName' => '审批人',
            'type' => 'approval',
            'props' => [
                'assignedUser' => [
                    ['name' => '管理员', 'id' => 1]
                ]
            ]
        ]
    ];
    
    // 为每个工作流类型创建定义
    foreach ($workflowTypes as $type) {
        $workflowType = Db::name('workflow_type')
                         ->where('business_code', $type['business_code'])
                         ->find();
        
        if ($workflowType) {
            $existing = Db::name('workflow_definition')
                         ->where('type_id', $workflowType['id'])
                         ->find();
            
            if (!$existing) {
                $data = [
                    'name' => $type['name'] . '标准审批流程',
                    'type_id' => $workflowType['id'],
                    'flow_config' => json_encode($standardFlowConfig),
                    'status' => 1,
                    'is_template' => 0,
                    'remark' => $type['name'] . '标准审批流程',
                    'creator_id' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'tenant_id' => 0
                ];
                
                $id = Db::name('workflow_definition')->insertGetId($data);
                echo "✅ 创建工作流定义: {$data['name']} (ID: {$id})\n";
            } else {
                echo "✅ 工作流定义已存在: {$type['name']}标准审批流程\n";
            }
        }
    }
    
    echo "\n配置完成！\n";
    echo "\n最终配置结果:\n";
    
    // 显示最终配置
    $results = Db::name('workflow_type')
                ->alias('wt')
                ->leftJoin('workflow_definition wd', 'wt.id = wd.type_id')
                ->where('wt.business_code', 'in', array_column($workflowTypes, 'business_code'))
                ->field('wt.id as type_id, wt.name as type_name, wt.business_code, wt.status, wd.id as definition_id, wd.name as definition_name')
                ->order('wt.business_code')
                ->select();
    
    foreach ($results as $result) {
        $status = $result['definition_id'] ? '✅' : '❌';
        echo "{$status} {$result['type_name']} ({$result['business_code']}) - 定义ID: {$result['definition_id']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
