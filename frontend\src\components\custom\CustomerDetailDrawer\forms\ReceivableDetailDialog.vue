<template>
  <el-dialog 
    v-model="visible" 
    title="回款详情" 
    width="800px" 
    :close-on-click-modal="false"
    class="receivable-detail-dialog"
  >
    <!-- 固定的标题和摘要区域 -->
    <div class="dialog-header">
      <div class="summary-info">
        <div class="amount-info">
          <span class="amount-label">回款金额：</span>
          <span class="amount-value">¥{{ formatAmount(receivableData?.amount) }}</span>
        </div>
        <div class="status-info">
          <span class="status-label">审批状态：</span>
          <el-tag :type="getStatusType(receivableData)">
            {{ getStatusText(receivableData) }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 可滚动的内容区域 -->
    <div class="dialog-content" v-loading="loading">
      <div class="detail-sections" v-if="receivableData">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>回款金额：</label>
              <span>¥{{ formatAmount(receivableData.amount) }}</span>
            </div>
            <div class="detail-item">
              <label>回款日期：</label>
              <span>{{ receivableData.received_date || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>付款方式：</label>
              <span>{{ receivableData.payment_method || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ receivableData.created_at || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 银行信息 -->
        <div class="detail-section" v-if="receivableData.bank_name || receivableData.bank_account">
          <h4 class="section-title">银行信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>收款银行：</label>
              <span>{{ receivableData.bank_name || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>银行账号：</label>
              <span>{{ receivableData.bank_account || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 审批信息 -->
        <div class="detail-section" v-if="receivableData.workflow">
          <h4 class="section-title">审批信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>审批状态：</label>
              <el-tag :type="getStatusType(receivableData)">
                {{ getStatusText(receivableData) }}
              </el-tag>
            </div>
            <div class="detail-item" v-if="receivableData.workflow.submit_time">
              <label>提交时间：</label>
              <span>{{ receivableData.workflow.submit_time }}</span>
            </div>
            <div class="detail-item" v-if="receivableData.workflow.complete_time">
              <label>完成时间：</label>
              <span>{{ receivableData.workflow.complete_time }}</span>
            </div>
            <div class="detail-item full-width" v-if="receivableData.workflow.approval_comment">
              <label>审批意见：</label>
              <span>{{ receivableData.workflow.approval_comment }}</span>
            </div>
          </div>
        </div>

        <!-- 回款凭证 -->
        <div class="detail-section" v-if="receivableData.voucher_files">
          <h4 class="section-title">回款凭证</h4>
          <div class="voucher-files">
            <template v-if="voucherFileList.length > 0">
              <div class="file-list">
                <div 
                  v-for="(file, index) in voucherFileList" 
                  :key="index" 
                  class="file-item"
                >
                  <el-image
                    :src="file"
                    :preview-src-list="voucherFileList"
                    :initial-index="index"
                    fit="cover"
                    class="voucher-image"
                    preview-teleported
                  />
                </div>
              </div>
            </template>
            <span v-else class="no-files">暂无凭证文件</span>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="detail-section" v-if="receivableData.remark">
          <h4 class="section-title">备注信息</h4>
          <div class="remark-content">
            {{ receivableData.remark }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'

  // 组件属性
  interface Props {
    modelValue: boolean
    receivableId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    receivableId: 0
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
  }>()

  // 响应式数据
  const loading = ref(false)
  const receivableData = ref<any>(null)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 计算凭证文件列表
  const voucherFileList = computed(() => {
    if (!receivableData.value?.voucher_files) return []
    try {
      const files = typeof receivableData.value.voucher_files === 'string' 
        ? JSON.parse(receivableData.value.voucher_files) 
        : receivableData.value.voucher_files
      return Array.isArray(files) ? files : []
    } catch {
      return []
    }
  })

  // 监听对话框显示状态
  watch(visible, (newVal) => {
    if (newVal && props.receivableId) {
      loadReceivableDetail()
    }
  })

  // 加载回款详情
  const loadReceivableDetail = async () => {
    if (!props.receivableId) return
    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getReceivableDetail(props.receivableId)
      if (res.code === ApiStatus.success) {
        receivableData.value = res.data
      }
    } catch (error) {
      console.error('获取回款详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 格式化金额
  const formatAmount = (amount: any) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return num?.toLocaleString() || '0'
  }

  // 获取状态标签类型
  const getStatusType = (receivable: any): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
    const status = receivable?.approval_status ?? 0
    const statusMap: Record<number, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
      0: 'info', // 草稿
      1: 'warning', // 审批中
      2: 'success', // 已通过
      3: 'danger', // 已拒绝
      4: 'info', // 已终止
      5: 'info', // 已撤回
      6: 'danger' // 已作废
    }
    return statusMap[status] || 'info'
  }

  // 获取状态文本
  const getStatusText = (receivable: any) => {
    const status = receivable?.approval_status ?? 0
    const statusMap: Record<number, string> = {
      0: '草稿',
      1: '审批中',
      2: '已通过',
      3: '已拒绝',
      4: '已终止',
      5: '已撤回',
      6: '已作废'
    }
    return statusMap[status] || '草稿'
  }
</script>

<style scoped lang="scss">
  .receivable-detail-dialog {
    :deep(.el-dialog__body) {
      padding: 0;
      max-height: 70vh;
      display: flex;
      flex-direction: column;
    }

    .dialog-header {
      padding: 20px 20px 0 20px;
      border-bottom: 1px solid #ebeef5;
      flex-shrink: 0;

      .summary-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background: #f5f7fa;
        border-radius: 6px;
        margin-bottom: 20px;

        .amount-info {
          .amount-label {
            font-size: 14px;
            color: #606266;
            margin-right: 8px;
          }

          .amount-value {
            font-size: 18px;
            font-weight: 600;
            color: #409eff;
          }
        }

        .status-info {
          .status-label {
            font-size: 14px;
            color: #606266;
            margin-right: 8px;
          }
        }
      }
    }

    .dialog-content {
      flex: 1;
      overflow-y: auto;
      padding: 0 20px 20px 20px;

      .detail-sections {
        .detail-section {
          margin-bottom: 24px;

          .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
          }

          .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;

            .detail-item {
              display: flex;
              align-items: flex-start;

              &.full-width {
                grid-column: 1 / -1;
                flex-direction: column;
                gap: 8px;
              }

              label {
                font-weight: 500;
                color: #606266;
                min-width: 100px;
                margin-right: 12px;
                flex-shrink: 0;
              }

              span {
                color: #303133;
                word-break: break-all;
              }
            }
          }

          .voucher-files {
            .file-list {
              display: flex;
              flex-wrap: wrap;
              gap: 12px;

              .file-item {
                .voucher-image {
                  width: 120px;
                  height: 120px;
                  border-radius: 6px;
                  border: 1px solid #dcdfe6;
                  cursor: pointer;
                  transition: all 0.3s;

                  &:hover {
                    border-color: #409eff;
                    transform: scale(1.05);
                  }
                }
              }
            }

            .no-files {
              color: #909399;
              font-style: italic;
            }
          }

          .remark-content {
            padding: 16px;
            background: #f5f7fa;
            border-radius: 6px;
            color: #303133;
            line-height: 1.6;
            white-space: pre-wrap;
          }
        }
      }
    }

    .dialog-footer {
      text-align: right;
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
</style>
