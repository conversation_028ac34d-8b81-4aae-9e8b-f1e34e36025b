<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 合同产品明细表模型 - 基于crm_data.sql表结构
 */
class CrmContractProduct extends BaseModel
{
	use CreatorTrait;

    // 设置表名
    protected $name = 'crm_contract_product';

    // 字段类型转换
    protected $type = [
        'contract_id' => 'integer',
        'product_id' => 'integer',  // 调整为直接关联产品ID
        'quantity' => 'float',
        'unit_price' => 'float',
        'discount_rate' => 'float',
        'discount_amount' => 'float',
        'subtotal' => 'float',
    ];

    /**
     * 关联产品（直接关联）
     */
    public function product()
    {
        return $this->belongsTo(CrmProduct::class, 'product_id', 'id');
    }

    /**
     * 关联合同
     */
    public function contract()
    {
        return $this->belongsTo(CrmContract::class, 'contract_id', 'id');
    }

    public function getImpSceneFields(): array
    {
        return [
            'contract_id' => [
                'label' => '合同ID',
                'type'  => 'number',
            ],
            'product_id' => [
                'label' => '产品ID',
                'type'  => 'number',
            ],
            'product_info' => [
                'label' => '产品信息快照',
                'type'  => 'text',
            ],
            'quantity' => [
                'label' => '数量',
                'type'  => 'number',
            ],
            'unit_price' => [
                'label' => '成交单价',
                'type'  => 'number',
            ],
            'discount_rate' => [
                'label' => '折扣率(%)',
                'type'  => 'number',
            ],
            'discount_amount' => [
                'label' => '折扣金额',
                'type'  => 'number',
            ],
            'subtotal' => [
                'label' => '小计金额',
                'type'  => 'number',
            ]
        ];
    }
}