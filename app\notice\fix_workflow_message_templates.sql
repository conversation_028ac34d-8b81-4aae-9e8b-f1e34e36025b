-- 修复工作流消息模板的BUG
-- 问题1: 缺少 workflow_task_cc 模板
-- 问题2: workflow_task_approved 模板变量不匹配

-- 1. 添加缺失的抄送通知模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES ('workflow_task_cc', '工作流抄送通知', '您收到一个抄送：${title}',
        '您收到一个抄送通知\n流程标题：${title}\n提交人：${name}\n抄送时间：${created_at}\n请知悉。',
        'workflow', 'site,wework', 1, 1, 1)
ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `title` = VALUES(`title`),
    `content` = VALUES(`content`),
    `module_code` = VALUES(`module_code`),
    `send_channels` = VALUES(`send_channels`),
    `status` = VALUES(`status`);

-- 2. 修复 workflow_task_approved 模板的变量配置
UPDATE `notice_template` 
SET `content` = '您的申请已审批完成\n流程标题：${title}\n审批结果：${result}\n审批意见：${opinion}\n审批人：${approver_name}\n审批时间：${completed_at}'
WHERE `code` = 'workflow_task_approved';

-- 3. 修复 workflow_task_approval 模板的变量配置
UPDATE `notice_template` 
SET `content` = '您有一个新的待审批任务\n流程标题：${title}\n当前环节：${task_name}\n提交人：${submitter_name}\n提交时间：${created_at}\n请及时处理！'
WHERE `code` = 'workflow_task_approval';

-- 4. 添加工作流终止通知模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES ('workflow_task_terminated', '工作流终止通知', '流程已终止：${title}',
        '您的申请已被终止\n流程标题：${title}\n终止结果：${result}\n终止时间：${terminate_time}\n终止人：${terminate_by}\n终止原因：${reason}',
        'workflow', 'site,email', 1, 1, 1)
ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `title` = VALUES(`title`),
    `content` = VALUES(`content`),
    `module_code` = VALUES(`module_code`),
    `send_channels` = VALUES(`send_channels`),
    `status` = VALUES(`status`);

-- 5. 添加工作流转交通知模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES ('workflow_task_transfer', '工作流转交通知', '您有一个转交任务：${title}',
        '您收到一个转交任务\n流程标题：${title}\n当前环节：${task_name}\n转交人：${transfer_from}\n转交时间：${transfer_time}\n转交原因：${transfer_reason}\n请及时处理！',
        'workflow', 'site,wework', 1, 1, 1)
ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `title` = VALUES(`title`),
    `content` = VALUES(`content`),
    `module_code` = VALUES(`module_code`),
    `send_channels` = VALUES(`send_channels`),
    `status` = VALUES(`status`);

-- 6. 添加工作流催办通知模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES ('workflow_task_urge', '工作流催办通知', '催办提醒：${title}',
        '您有一个待办任务被催办\n流程标题：${title}\n当前环节：${task_name}\n催办人：${urger_name}\n催办时间：${urge_time}\n催办原因：${urge_reason}\n请尽快处理！',
        'workflow', 'site,sms,wework', 1, 1, 1)
ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `title` = VALUES(`title`),
    `content` = VALUES(`content`),
    `module_code` = VALUES(`module_code`),
    `send_channels` = VALUES(`send_channels`),
    `status` = VALUES(`status`);

-- 7. 添加申请通知模板
INSERT INTO `notice_template` (`code`, `name`, `title`, `content`, `module_code`, `send_channels`, `status`, `creator_id`, `tenant_id`)
VALUES ('workflow_request', '工作流申请通知', '新申请提醒：${title}',
        '您收到一个新的申请\n流程标题：${title}\n申请人：${name}\n申请时间：${created_at}\n请知悉。',
        'workflow', 'site', 1, 1, 1)
ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `title` = VALUES(`title`),
    `content` = VALUES(`content`),
    `module_code` = VALUES(`module_code`),
    `send_channels` = VALUES(`send_channels`),
    `status` = VALUES(`status`);
