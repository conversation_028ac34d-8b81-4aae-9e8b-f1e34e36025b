# 消息中心变量映射规范和修复方案

## 📊 分析结果概览

**分析时间**: 2025-07-16
**更新时间**: 2025-07-16
**总模板数**: 21个
**已修复**: Workflow模块7个模板
**修复状态**: ✅ 核心问题已解决

## 🎯 变量映射规范

### 正确的映射机制
1. **代码传入**: 使用英文键名（如 `title`、`task_name`）
2. **模板配置**: 字段路径使用英文（如 `title`、`task_name`）
3. **模板内容**: 显示中文变量名（如 `${流程标题}`、`${任务名称}`）
4. **自动映射**: 消息中心自动完成英文键名到中文变量名的映射

### 规范原则
1. **统一映射机制**: 所有模块都使用相同的变量映射方式
2. **语义清晰**: 英文键名和中文变量名都应该清晰表达含义
3. **简洁明了**: 避免过长的变量名
4. **一致性**: 相同含义的变量在不同模板中使用相同的英文键名和中文显示名

### 标准变量映射对照表

| 英文键名 | 中文变量名 | 模板显示 | 使用场景 |
|----------|------------|----------|----------|
| `title` | `流程标题` | `${流程标题}` | 工作流标题 |
| `content` | `内容` | `${内容}` | 通用内容 |
| `name` | `名称` | `${名称}` | 通用名称 |
| `task_name` | `任务名称` | `${任务名称}` | 工作流任务 |
| `node_name` | `节点名称` | `${节点名称}` | 工作流节点 |
| `submitter_name` | `提交人` | `${提交人}` | 工作流提交人 |
| `approver_name` | `审批人` | `${审批人}` | 审批相关 |
| `created_at` | `提交时间` | `${提交时间}` | 时间相关 |
| `completed_at` | `审批时间` | `${审批时间}` | 审批时间 |
| `result` | `审批结果` | `${审批结果}` | 审批结果 |
| `opinion` | `审批意见` | `${审批意见}` | 审批意见 |
| `result` | `结果` / `审批结果` | 结果相关 |
| `opinion` | `意见` / `审批意见` | 意见相关 |
| `reason` | `原因` / `催办原因` | 原因相关 |
| `customer_name` | `客户名称` | CRM客户 |
| `customer_phone` | `客户电话` | CRM客户电话 |
| `business_name` | `商机名称` | CRM商机 |
| `lead_name` | `线索名称` | CRM线索 |
| `contract_no` | `合同编号` | CRM合同 |
| `contract_amount` | `合同金额` | CRM合同金额 |
| `quotation_no` | `报价单编号` | CRM报价单 |
| `final_amount` | `最终金额` | CRM金额 |
| `old_stage` | `原阶段` | CRM阶段变更 |
| `new_stage` | `新阶段` | CRM阶段变更 |
| `change_reason` | `变更原因` | CRM变更原因 |
| `cc_time` | `抄送时间` | 工作流抄送 |
| `transfer_time` | `转交时间` | 工作流转交 |
| `terminate_time` | `终止时间` | 工作流终止 |
| `terminate_by` | `终止人` | 工作流终止 |
| `from_user` | `转交人` | 工作流转交 |
| `to_user` | `接收人` | 工作流转交 |
| `urger_name` | `催办人` | 工作流催办 |

## 🔍 违反规范的模板分析

### CRM模块 (5个模板全部违反)

#### 1. crm_lead_convert
**当前变量**: `lead_name`, `customer_name`  
**应改为**: `线索名称`, `客户名称`

#### 2. crm_customer_assign  
**当前变量**: `customer_name`, `customer_phone`  
**应改为**: `客户名称`, `客户电话`

#### 3. crm_business_stage_change
**当前变量**: `business_name`, `old_stage`, `new_stage`, `change_reason`  
**应改为**: `商机名称`, `原阶段`, `新阶段`, `变更原因`

#### 4. crm_quotation_create
**当前变量**: `quotation_no`, `customer_name`, `final_amount`  
**应改为**: `报价单编号`, `客户名称`, `最终金额`

#### 5. crm_contract_approval
**当前变量**: `contract_no`, `customer_name`, `contract_amount`  
**应改为**: `合同编号`, `客户名称`, `合同金额`

### Workflow模块 (2个模板违反)

#### 1. workflow_task_cc
**当前变量**: `title`, `submitter_name`, `node_name`, `cc_time`  
**应改为**: `流程标题`, `提交人`, `节点名称`, `抄送时间`

#### 2. workflow_task_terminated
**当前变量**: `title`, `result`, `terminate_time`, `terminate_by`, `reason`  
**应改为**: `流程标题`, `终止结果`, `终止时间`, `终止人`, `终止原因`

## 🛠️ 修复SQL脚本

### CRM模块修复

```sql
-- 1. 修复 crm_lead_convert 模板
UPDATE notice_template SET 
    content = '您的线索"${线索名称}"已成功转化为客户"${客户名称}"，请及时跟进。'
WHERE code = 'crm_lead_convert';

-- 2. 修复 crm_customer_assign 模板
UPDATE notice_template SET 
    content = '客户"${客户名称}"已分配给您，请及时联系客户并建立良好关系。联系电话：${客户电话}'
WHERE code = 'crm_customer_assign';

-- 3. 修复 crm_business_stage_change 模板
UPDATE notice_template SET 
    content = '商机"${商机名称}"的阶段已从"${原阶段}"变更为"${新阶段}"，变更原因：${变更原因}'
WHERE code = 'crm_business_stage_change';

-- 4. 修复 crm_quotation_create 模板
UPDATE notice_template SET 
    content = '报价单"${报价单编号}"已创建，客户：${客户名称}，金额：¥${最终金额}，请及时发送给客户。'
WHERE code = 'crm_quotation_create';

-- 5. 修复 crm_contract_approval 模板
UPDATE notice_template SET 
    content = '合同"${合同编号}"需要您的审批，客户：${客户名称}，金额：¥${合同金额}，请及时处理。'
WHERE code = 'crm_contract_approval';
```

### Workflow模块修复

```sql
-- 6. 修复 workflow_task_cc 模板
UPDATE notice_template SET 
    title = '您收到一个抄送：${流程标题}',
    content = '您收到一个抄送通知
流程标题：${流程标题}
提交人：${提交人}
节点名称：${节点名称}
抄送时间：${抄送时间}
请知悉。'
WHERE code = 'workflow_task_cc';

-- 7. 修复 workflow_task_terminated 模板
UPDATE notice_template SET 
    title = '流程已终止：${流程标题}',
    content = '您的申请已被终止
流程标题：${流程标题}
终止结果：${终止结果}
终止时间：${终止时间}
终止人：${终止人}
终止原因：${终止原因}'
WHERE code = 'workflow_task_terminated';
```

## 📝 代码修复方案

### CRM模块代码修复

需要修复CRM模块中的消息发送代码，使用中文变量键名：

```php
// CRM线索转化通知
$variables = [
    '线索名称' => $lead['lead_name'],
    '客户名称' => $customer['customer_name']
];

// CRM客户分配通知
$variables = [
    '客户名称' => $customer['customer_name'],
    '客户电话' => $customer['phone']
];

// CRM商机阶段变更通知
$variables = [
    '商机名称' => $business['business_name'],
    '原阶段'   => $oldStage['stage_name'],
    '新阶段'   => $newStage['stage_name'],
    '变更原因' => $reason
];

// CRM报价单创建通知
$variables = [
    '报价单编号' => $quotation['quotation_no'],
    '客户名称'   => $customer['customer_name'],
    '最终金额'   => $quotation['final_amount']
];

// CRM合同审批通知
$variables = [
    '合同编号' => $contract['contract_no'],
    '客户名称' => $customer['customer_name'],
    '合同金额' => $contract['contract_amount']
];
```

### Workflow模块代码修复

需要修复workflow模块中的部分代码：

```php
// workflow_task_cc 抄送通知
$variables = [
    '流程标题' => $instance['title'],
    '提交人'   => $instance['submitter_name'],
    '节点名称' => $node['node_name'],
    '抄送时间' => date('Y-m-d H:i:s')
];

// workflow_task_terminated 终止通知
$variables = [
    '流程标题' => $instance['title'],
    '终止结果' => '已终止',
    '终止时间' => date('Y-m-d H:i:s'),
    '终止人'   => $terminator['name'],
    '终止原因' => $reason
];
```

## 📋 实施计划

### 阶段1: 数据库模板修复 (立即执行)
1. 执行上述SQL脚本修复模板内容
2. 验证模板修复结果

### 阶段2: 代码修复 (高优先级)
1. 修复CRM模块的消息发送代码
2. 修复Workflow模块的部分代码
3. 更新相关服务类

### 阶段3: 测试验证 (必须执行)
1. 测试所有修复的模板
2. 验证消息发送和变量替换
3. 确保用户体验正常

### 阶段4: 规范建立 (长期维护)
1. 建立中文变量名规范文档
2. 在开发规范中明确要求
3. 建立代码审查检查点

## 🧪 验证测试脚本

```php
// 测试所有修复的模板
$testCases = [
    'crm_lead_convert' => [
        '线索名称' => '张三的咨询',
        '客户名称' => '张三'
    ],
    'crm_customer_assign' => [
        '客户名称' => '张三',
        '客户电话' => '13800138000'
    ],
    'workflow_task_cc' => [
        '流程标题' => '请假申请',
        '提交人'   => '张三',
        '节点名称' => '部门审批',
        '抄送时间' => date('Y-m-d H:i:s')
    ]
    // ... 其他测试用例
];

foreach ($testCases as $templateCode => $variables) {
    $result = NoticeDispatcherService::getInstance()->send(
        explode('_', $templateCode)[0], // 模块名
        substr($templateCode, strpos($templateCode, '_') + 1), // 动作名
        $variables,
        [1] // 测试用户ID
    );
    
    echo ($result ? "✅" : "❌") . " {$templateCode}\n";
}
```

## 🎯 预期效果

修复完成后：
- ✅ 所有21个模板都使用中文变量名
- ✅ 变量命名统一规范
- ✅ 消息发送功能完全正常
- ✅ 用户体验更加友好
- ✅ 代码可维护性提升

## 📚 规范文档

建议创建《消息中心变量命名规范》文档，包含：
1. 中文变量名使用原则
2. 标准变量名对照表
3. 新模板创建规范
4. 代码实现规范
5. 测试验证要求

这样可以确保后续开发都遵循统一的中文变量名规范。
