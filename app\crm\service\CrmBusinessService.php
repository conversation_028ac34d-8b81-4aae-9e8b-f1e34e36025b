<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\crm\model\CrmBusiness;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 商机表服务类
 */
class CrmBusinessService extends BaseService
{

    use ExportableTrait;


    use ImportableTrait;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmBusiness();
        parent::__construct();
    }
    
    /**
     * 获取搜索字段配置 - 基于crm_data.sql表结构
     *
     * @return array
     */
    protected function getSearchFields(): array
    {
        return [

            'business_name' => ['type' => 'like'],

            'customer_id' => ['type' => 'eq'],

            'amount' => ['type' => 'between'],

            'probability' => ['type' => 'between'],

            'status' => ['type' => 'eq'],

            'stage' => ['type' => 'eq'],

            'source' => ['type' => 'eq'],

            'type' => ['type' => 'eq'],

            'expected_date' => ['type' => 'date'],

            'closed_date' => ['type' => 'date'],

            'closed_reason' => ['type' => 'like'],

            'owner_user_id' => ['type' => 'eq'],

            'last_followed_at' => ['type' => 'date'],

            'next_followed_at' => ['type' => 'date'],

        ];
    }
    
    /**
     * 获取验证规则 - 基于crm_data.sql字段约束
     *
     * @param string $scene 场景
     * @return array
     */
    protected function getValidationRules(string $scene): array
    {
        // 基础规则
        $rules = [
            'customer_id' => 'require|integer|gt:0',
            'business_name' => 'require|max:200',
            'amount' => 'require|float|egt:0',
            'probability' => 'float|between:0,100',
            'stage' => 'require|max:50',
            'status' => 'integer|in:0,1,2',
            'source' => 'max:50',
            'type' => 'max:50',
            'expected_date' => 'date',
            'closed_date' => 'date',
            'closed_reason' => 'max:500',
            'description' => 'max:1000',
            'owner_user_id' => 'require|integer|gt:0',
        ];

        // 根据场景返回规则
        return match($scene) {
            'add' => $rules,
            'edit' => $rules,
            default => [],
        };
    }
    
    /**
     * 批量删除
     * 
     * @param array|int $ids 要删除的ID数组或单个ID
     * @return bool
     */
    public function batchDelete($ids): bool
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }
        
        return $this->model->whereIn('id', $ids)->delete();
    }
} 