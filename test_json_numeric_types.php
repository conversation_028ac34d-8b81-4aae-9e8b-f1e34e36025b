<?php
/**
 * 测试 WorkflowInstance JSON 字段数字类型保持功能
 * 
 * 使用方法：
 * php test_json_numeric_types.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\workflow\model\WorkflowInstance;

// 模拟测试数据
$testData = [
    'definition_id' => 1,
    'business_code' => 'test_business',
    'business_id' => 123,
    'title' => '测试工作流实例',
    'status' => 1,
    'submitter_id' => 1001,
    'submitter_name' => '测试用户',
    'current_node' => 'start_node',
    
    // 测试 form_data - 包含各种数字类型
    'form_data' => [
        'amount' => 1500.50,        // 浮点数
        'quantity' => 10,           // 整数
        'user_id' => 2001,          // ID类型
        'dept_id' => 3001,          // ID类型
        'is_urgent' => true,        // 布尔值
        'description' => '测试描述', // 字符串
        'items' => [                // 嵌套数组
            [
                'id' => 1,
                'price' => 99.99,
                'count' => 5
            ],
            [
                'id' => 2,
                'price' => 199.00,
                'count' => 3
            ]
        ],
        'metadata' => [             // 嵌套对象
            'category_id' => 4001,
            'priority' => 1,
            'score' => 85.5
        ]
    ],
    
    // 测试 cc_users - 用户ID数组
    'cc_users' => [1001, 1002, 1003],
    
    // 测试 process_data - 流程配置数据
    'process_data' => [
        'nodes' => [
            [
                'id' => 'node_1',
                'type' => 'start',
                'position' => ['x' => 100, 'y' => 200],
                'config' => [
                    'timeout' => 3600,
                    'retry_count' => 3,
                    'priority' => 1
                ]
            ],
            [
                'id' => 'node_2', 
                'type' => 'approval',
                'position' => ['x' => 300, 'y' => 200],
                'config' => [
                    'timeout' => 7200,
                    'retry_count' => 5,
                    'priority' => 2
                ]
            ]
        ],
        'edges' => [
            [
                'id' => 'edge_1',
                'source' => 'node_1',
                'target' => 'node_2',
                'weight' => 1.0
            ]
        ],
        'settings' => [
            'max_duration' => 86400,
            'auto_approve_amount' => 1000.00,
            'notification_interval' => 3600
        ]
    ]
];

echo "=== WorkflowInstance JSON 字段数字类型测试 ===\n\n";

try {
    // 创建模型实例
    $instance = new WorkflowInstance();
    
    // 设置测试数据
    foreach ($testData as $key => $value) {
        $instance->$key = $value;
    }
    
    echo "1. 原始数据类型检查:\n";
    echo "form_data.amount 类型: " . gettype($testData['form_data']['amount']) . " 值: " . $testData['form_data']['amount'] . "\n";
    echo "form_data.quantity 类型: " . gettype($testData['form_data']['quantity']) . " 值: " . $testData['form_data']['quantity'] . "\n";
    echo "cc_users[0] 类型: " . gettype($testData['cc_users'][0]) . " 值: " . $testData['cc_users'][0] . "\n";
    echo "process_data.settings.max_duration 类型: " . gettype($testData['process_data']['settings']['max_duration']) . " 值: " . $testData['process_data']['settings']['max_duration'] . "\n\n";
    
    echo "2. 模型处理后数据类型检查:\n";
    $formData = $instance->form_data;
    $ccUsers = $instance->cc_users;
    $processData = $instance->process_data;
    
    echo "form_data.amount 类型: " . gettype($formData['amount']) . " 值: " . $formData['amount'] . "\n";
    echo "form_data.quantity 类型: " . gettype($formData['quantity']) . " 值: " . $formData['quantity'] . "\n";
    echo "form_data.items[0].price 类型: " . gettype($formData['items'][0]['price']) . " 值: " . $formData['items'][0]['price'] . "\n";
    echo "cc_users[0] 类型: " . gettype($ccUsers[0]) . " 值: " . $ccUsers[0] . "\n";
    echo "process_data.settings.max_duration 类型: " . gettype($processData['settings']['max_duration']) . " 值: " . $processData['settings']['max_duration'] . "\n";
    echo "process_data.nodes[0].config.timeout 类型: " . gettype($processData['nodes'][0]['config']['timeout']) . " 值: " . $processData['nodes'][0]['config']['timeout'] . "\n\n";
    
    echo "3. JSON 序列化测试:\n";
    $formDataJson = json_encode($formData, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    echo "form_data JSON: " . $formDataJson . "\n\n";
    
    $ccUsersJson = json_encode($ccUsers, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    echo "cc_users JSON: " . $ccUsersJson . "\n\n";
    
    echo "4. 类型验证结果:\n";
    $success = true;
    
    // 验证数字类型是否保持
    if (!is_float($formData['amount'])) {
        echo "❌ form_data.amount 应该是 float 类型\n";
        $success = false;
    } else {
        echo "✅ form_data.amount 正确保持为 float 类型\n";
    }
    
    if (!is_int($formData['quantity'])) {
        echo "❌ form_data.quantity 应该是 int 类型\n";
        $success = false;
    } else {
        echo "✅ form_data.quantity 正确保持为 int 类型\n";
    }
    
    if (!is_int($ccUsers[0])) {
        echo "❌ cc_users[0] 应该是 int 类型\n";
        $success = false;
    } else {
        echo "✅ cc_users[0] 正确保持为 int 类型\n";
    }
    
    if (!is_int($processData['settings']['max_duration'])) {
        echo "❌ process_data.settings.max_duration 应该是 int 类型\n";
        $success = false;
    } else {
        echo "✅ process_data.settings.max_duration 正确保持为 int 类型\n";
    }
    
    // 验证嵌套数组中的数字类型
    if (!is_float($formData['items'][0]['price'])) {
        echo "❌ form_data.items[0].price 应该是 float 类型\n";
        $success = false;
    } else {
        echo "✅ form_data.items[0].price 正确保持为 float 类型\n";
    }
    
    echo "\n" . ($success ? "🎉 所有测试通过！数字类型正确保持。" : "❌ 部分测试失败，请检查实现。") . "\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
