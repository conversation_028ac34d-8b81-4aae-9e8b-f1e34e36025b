<?php
declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\system\model\MenuModel;
use app\system\service\PermissionService;

/**
 * 菜单权限标识一致性检查命令
 */
class MenuConsistencyCheck extends Command
{
    protected function configure()
    {
        $this->setName('menu:check-consistency')
             ->setDescription('检查菜单权限标识与控制器的一致性');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始检查菜单权限标识一致性...');
        
        $inconsistentMenus = $this->checkPermissionConsistency();
        
        if (empty($inconsistentMenus)) {
            $output->writeln('<info>✅ 所有菜单权限标识都与控制器一致</info>');
        } else {
            $output->writeln('<error>❌ 发现不一致的菜单项:</error>');
            foreach ($inconsistentMenus as $menu) {
                $output->writeln("  - ID: {$menu['menu_id']}, 标题: {$menu['title']}, 权限: {$menu['permission']}, 错误: {$menu['error']}");
            }
        }
        
        return 0;
    }
    
    /**
     * 检查权限一致性
     *
     * @return array 不一致的菜单列表
     */
    private function checkPermissionConsistency(): array
    {
        $inconsistentMenus = [];
        
        try {
            // 获取所有菜单
            $menus = MenuModel::where('status', 1)
                             ->where('name', '<>', '')
                             ->select();
            
            $permissionService = app(PermissionService::class);
            
            foreach ($menus as $menu) {
                $permission = $menu->name;
                
                // 检查权限标识格式
                if (!$this->isValidPermissionFormat($permission)) {
                    $inconsistentMenus[] = [
                        'menu_id' => $menu->id,
                        'title' => $menu->title,
                        'permission' => $permission,
                        'error' => '权限标识格式不正确，应为 module:controller:action 格式'
                    ];
                    continue;
                }
                
                // 分解权限标识
                $parts = explode(':', $permission);
                if (count($parts) !== 3) {
                    $inconsistentMenus[] = [
                        'menu_id' => $menu->id,
                        'title' => $menu->title,
                        'permission' => $permission,
                        'error' => '权限标识应包含3个部分：module:controller:action'
                    ];
                    continue;
                }
                
                [$module, $controller, $action] = $parts;
                
                // 检查控制器文件是否存在
                $controllerClass = "app\\{$module}\\controller\\" . ucfirst($controller) . 'Controller';
                if (!class_exists($controllerClass)) {
                    $inconsistentMenus[] = [
                        'menu_id' => $menu->id,
                        'title' => $menu->title,
                        'permission' => $permission,
                        'error' => "控制器类 {$controllerClass} 不存在"
                    ];
                    continue;
                }
                
                // 检查方法是否存在
                if (!method_exists($controllerClass, $action)) {
                    $inconsistentMenus[] = [
                        'menu_id' => $menu->id,
                        'title' => $menu->title,
                        'permission' => $permission,
                        'error' => "控制器方法 {$controllerClass}::{$action} 不存在"
                    ];
                    continue;
                }
            }
            
        } catch (\Exception $e) {
            $inconsistentMenus[] = [
                'menu_id' => 0,
                'title' => '系统错误',
                'permission' => '',
                'error' => '检查过程中发生错误: ' . $e->getMessage()
            ];
        }
        
        return $inconsistentMenus;
    }
    
    /**
     * 检查权限标识格式是否有效
     *
     * @param string $permission 权限标识
     * @return bool
     */
    private function isValidPermissionFormat(string $permission): bool
    {
        // 权限标识应为 module:controller:action 格式
        return preg_match('/^[a-z_]+:[a-z_]+:[a-z_]+$/i', $permission) === 1;
    }
}
