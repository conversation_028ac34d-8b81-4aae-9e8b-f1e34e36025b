<?php
declare(strict_types=1);

namespace app\common\exception;

use think\exception\Handle;
use think\exception\HttpException;
use think\exception\ValidateException;
use think\facade\Log;
use think\facade\Request;
use think\Response;
use Throwable;

/**
 * 异常处理器
 */
class ApiHandler extends Handle
{
	/**
	 * HTTP 状态码
	 *
	 * @var int
	 */
	private int $statusCode = 500;
	
	/**
	 * 记录异常信息
	 *
	 * @param Throwable $exception 异常
	 * @return void
	 */
	public function report(Throwable $exception): void
	{
		// 记录异常日志
		Log::error($exception->getMessage(), [
			'file'   => $exception->getFile(),
			'line'   => $exception->getLine(),
			'trace'  => $this->app->isDebug()
				? $exception->getTraceAsString()
				: '',
			'code'   => $exception->getCode(),
			'url'    => Request::url(),
			'method' => Request::method(),
			'param'  => Request::param(),
		]);
		
		// 调用父类方法
		parent::report($exception);
	}
	
	/**
	 * 渲染异常
	 *
	 * @param \think\Request $request 请求对象
	 * @param Throwable      $e       异常
	 * @return Response
	 */
	public function render($request, Throwable $e): Response
	{
		// 根据异常类型设置状态码
		if ($e instanceof HttpException) {
			$this->statusCode = $e->getStatusCode();
		}
		elseif ($e instanceof AuthException) {
			$this->statusCode = 401;
		}
		elseif ($e instanceof PermissionException) {
			$this->statusCode = 403;
		}
		elseif ($e instanceof ValidateException) {
			$this->statusCode = 422;
		}
		else {
			$this->statusCode = 500;
		}
		
		
		// 生产环境下显示友好的错误页面
		return $this->renderApiError($e);
	}
	
	/**
	 * 渲染API错误响应
	 *
	 * @param Throwable $e 异常
	 * @return Response
	 */
	private function renderApiError(Throwable $e): Response
	{
		$result = [
			'code'    => $this->statusCode,
			'message' => $this->getErrorMessage($e),
			'data'    => null,
			'time'    => time(),
		];
		
		// 调试模式下显示更多信息
		if ($this->app->isDebug()) {
			$result['debug'] = [
				'file'  => $e->getFile(),
				'line'  => $e->getLine(),
				'trace' => explode("\n", $e->getTraceAsString()),
			];
		}
		
		if ($e instanceof WindException) {
			return Response::create([
				'code'    => $e->getCode(),
				'message' => $e->getMessage(),
				'data'    => [],
			], 'json', $this->statusCode);
		}
		
		return json($result, $this->statusCode);
	}
	
	/**
	 * 获取友好的错误消息
	 *
	 * @param Throwable $e 异常
	 * @return string
	 */
	private function getErrorMessage(Throwable $e): string
	{
		// 如果不是调试模式并且是服务器错误，返回友好提示
		if (!$this->app->isDebug() && $this->statusCode == 500) {
			return '服务器内部错误，请稍后再试';
		}
		
		// 返回异常消息
		return $e->getMessage();
	}
} 