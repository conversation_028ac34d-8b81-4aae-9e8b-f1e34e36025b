-- =====================================================
-- 每日报价模块精简权限配置SQL（基于实际使用情况）
-- 创建时间：2025-07-22
-- 说明：只包含实际使用的菜单和权限，清理冗余配置
-- =====================================================

-- 1. 创建每日报价菜单
INSERT INTO `system_menu` (
    `id`, `parent_id`, `title`, `name`, `path`, `component`,
    `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`,
    `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`
) VALUES (
    2600, 0, '每日报价', 'daily:daily_price_order:index', '/daily_price_order', '/daily/daily_price_order/list',
    1, 'icon-price', 250, 0, 1, 1,
    1, '每日报价单管理', 1, NOW(), NOW(), NULL
);

-- =====================================================
-- 实际使用的按钮权限（parent_id = 2600）
-- =====================================================

-- 基础CRUD权限（实际使用的5个）
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2601, 2600, '新增', 'daily:daily_price_order:add', '', '', 2, '', 1, 0, 0, 1, 1, '新增报价单', 1, NOW(), NOW(), NULL),
(2602, 2600, '编辑', 'daily:daily_price_order:edit', '', '', 2, '', 2, 0, 0, 1, 1, '编辑报价单', 1, NOW(), NOW(), NULL),
(2603, 2600, '删除', 'daily:daily_price_order:delete', '', '', 2, '', 3, 0, 0, 1, 1, '删除报价单', 1, NOW(), NOW(), NULL),
(2604, 2600, '详情', 'daily:daily_price_order:detail', '', '', 2, '', 4, 0, 0, 1, 1, '查看报价单详情', 1, NOW(), NOW(), NULL),
(2605, 2600, '列表', 'daily:daily_price_order:list', '', '', 2, '', 5, 0, 0, 1, 1, '查看报价单列表', 1, NOW(), NOW(), NULL);

-- 审批相关权限（实际使用的3个）
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2607, 2600, '提交审批', 'daily:daily_price_order:submit_approval', '', '', 2, '', 10, 0, 0, 1, 1, '提交报价单审批', 1, NOW(), NOW(), NULL),
(2608, 2600, '撤回审批', 'daily:daily_price_order:recall_approval', '', '', 2, '', 11, 0, 0, 1, 1, '撤回报价单审批', 1, NOW(), NOW(), NULL),
(2609, 2600, '作废报价单', 'daily:daily_price_order:void_order', '', '', 2, '', 12, 0, 0, 1, 1, '作废报价单', 1, NOW(), NOW(), NULL);

-- 业务操作权限（实际使用的3个）
INSERT INTO `system_menu` (`id`, `parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2620, 2600, '获取供应商列表', 'daily:daily_price_order:get_supplier_list', '', '', 2, '', 30, 0, 0, 1, 1, '获取供应商下拉列表', 1, NOW(), NOW(), NULL),
(2621, 2600, '获取产品列表', 'daily:daily_price_order:get_product_list', '', '', 2, '', 31, 0, 0, 1, 1, '获取产品下拉列表', 1, NOW(), NOW(), NULL),
(2622, 2600, '获取昨日价格', 'daily:daily_price_order:get_yesterday_prices', '', '', 2, '', 32, 0, 0, 1, 1, '获取昨日价格数据', 1, NOW(), NOW(), NULL);

-- =====================================================
-- 权限说明
-- =====================================================

/*
精简后的权限结构：
├── 每日报价 (2600) - 菜单项
    ├── 基础CRUD权限（5个）
    │   ├── 新增 (2601)
    │   ├── 编辑 (2602)
    │   ├── 删除 (2603)
    │   ├── 详情 (2604)
    │   └── 列表 (2605)
    ├── 审批相关权限（3个）
    │   ├── 提交审批 (2607)
    │   ├── 撤回审批 (2608)
    │   └── 作废报价单 (2609)
    └── 业务操作权限（3个）
        ├── 获取供应商列表 (2620)
        ├── 获取产品列表 (2621)
        └── 获取昨日价格 (2622)

总计：1个菜单 + 11个按钮权限

已清理的冗余权限：
- 导出/导入相关权限（未使用）
- 统计数据权限（未使用）
- 从昨日复制权限（未使用）
- 价格历史权限（未使用）
- 检查重复日期权限（未使用）
- 创建扩展方法权限（未使用）

前端权限控制示例：
- v-if="$auth('daily:daily_price_order:add')" // 新增按钮
- v-if="$auth('daily:daily_price_order:edit')" // 编辑按钮
- v-if="$auth('daily:daily_price_order:submit_approval')" // 提交审批按钮

后端权限验证示例：
- @RequiresPermissions("daily:daily_price_order:add")
- @RequiresPermissions("daily:daily_price_order:submit_approval")
*/

-- 验证SQL
SELECT 
    m.id,
    m.parent_id,
    m.title,
    m.name,
    m.type,
    CASE m.type 
        WHEN 1 THEN '菜单项' 
        WHEN 2 THEN '按钮权限' 
        ELSE '其他' 
    END as type_text,
    m.sort,
    m.status
FROM system_menu m 
WHERE m.id BETWEEN 2600 AND 2622
ORDER BY m.parent_id, m.sort;
