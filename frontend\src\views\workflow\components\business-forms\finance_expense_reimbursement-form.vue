<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="finance-expense-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
        class="expense-form"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="报销金额(元)" prop="total_amount">
              <ElInputNumber
                v-model="formData.total_amount"
                :min="0"
                :precision="2"
                :step="1"
                style="width: 100%"
                :disabled="!isEditable"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="报销类型" prop="expense_type">
              <ElSelect
                v-model="formData.expense_type"
                placeholder="请选择报销类型"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="option in expenseTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="附件">
          <FormUploader
            v-model="formData.attachment"
            :disabled="!isEditable"
            file-type="file"
            :limit="10"
            multiple
            accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
          />
        </ElFormItem>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>
      </ElForm>

      <!-- 报销明细 - 独立的卡片区域 -->
      <ElCard class="items-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">报销明细</span>
          </div>
        </template>

        <ExpenseItemTable
          v-model="formData.items"
          :readonly="!isEditable"
          :item-template="getItemTemplate"
          @change="onItemsChange"
        />

        <!-- 明细总额提示 -->
        <!--        <div v-if="formData.items.length > 0" class="items-summary">
                  <span class="summary-text">明细总额: </span>
                  <span class="summary-amount">¥{{ itemsTotalAmount.toFixed(2) }}</span>
                </div>-->
      </ElCard>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import FormUploader from '@/components/custom/FormUploader/index.vue'
  import ExpenseItemTable from '@/components/business/ExpenseItemTable.vue'
  import { getExpenseTypeOptions } from '@/constants/finance'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface FinanceExpenseFormData {
    id?: number
    total_amount: number
    expense_type: number
    items: any[]
    attachment: any[]
    remark: string
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `报销申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起报销申请'
  })

  /** 表单数据 */
  const formData = reactive<FinanceExpenseFormData & any>({
    total_amount: 0,
    expense_type: null,
    items: [],
    attachment: [],
    remark: '',
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  /** 报销类型选项 */
  const expenseTypeOptions = computed(() => getExpenseTypeOptions())

  // 明细表格相关方法
  const getItemTemplate = () => ({
    description: '',
    amount: 0
  })

  // 计算明细总额
  const itemsTotalAmount = computed(() => {
    return formData.items.reduce((sum: number, item: any) => sum + (item.amount || 0), 0)
  })

  const onItemsChange = (items: any[]) => {
    formData.items = items
    // 可选：自动同步明细总额到主表单金额
    // formData.amount = itemsTotalAmount.value
  }

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    total_amount: [
      { required: true, message: '请输入报销金额', trigger: 'blur' },
      { type: 'number', min: 0.01, message: '报销金额必须大于0', trigger: 'blur' }
    ],
    expense_type: [
      { required: true, message: '请选择报销类型', trigger: 'change' },
      { type: 'number', min: 1, message: '请选择有效的报销类型', trigger: 'change' }
    ]
  }

  // ==================== 方法定义 ====================

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('finance_expense_reimbursement-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      total_amount: 0,
      expense_type: null,
      items: [],
      attachment: [],
      remark: '',
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id
        formData.expense_type = response.data.expense_type || null
        // 确保items是数组
        if (!Array.isArray(formData.items)) {
          formData.items = []
        }

        console.log('报销申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('finance_expense_reimbursement-form setFormData called with:', data)
    Object.assign(formData, data)
    if (!Array.isArray(formData.items)) {
      formData.items = []
    }
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('finance_expense_reimbursement-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: FinanceExpenseFormData = {
        total_amount: formData.total_amount,
        expense_type: formData.expense_type,
        items: formData.items,
        attachment: formData.attachment,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('报销申请保存数据:', submitData)
      emit('save', submitData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: FinanceExpenseFormData = {
        total_amount: formData.total_amount,
        expense_type: formData.expense_type,
        items: formData.items,
        attachment: formData.attachment,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('报销申请提交数据:', submitData)
      emit('submit', submitData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .finance-expense-dialog {
    // 参考请假表单的对话框样式
    :deep(.el-dialog) {
      margin-top: 5vh !important;
      margin-bottom: 5vh !important;
      display: flex;
      flex-direction: column;
      max-height: 90vh;
    }

    :deep(.el-dialog__body) {
      overflow: auto;
      padding: 20px;
      max-height: 65vh;
    }

    .dialog-content {
      max-height: 60vh;
      padding: 10px 30px;
      overflow-y: auto;
      border-bottom: 1px solid #eaeaea;
    }

    .expense-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      .el-textarea {
        .el-textarea__inner {
          resize: vertical;
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 16px;

      .el-button {
        min-width: 100px;
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  // 卡片样式
  .items-card {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .items-summary {
      margin-top: 16px;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 8px;
      text-align: right;
      border-top: 1px solid #e4e7ed;

      .summary-text {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }

      .summary-amount {
        font-size: 18px;
        font-weight: 600;
        color: #f56c6c;
        margin-left: 8px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .finance-expense-dialog {
      .dialog-footer {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
</style>
