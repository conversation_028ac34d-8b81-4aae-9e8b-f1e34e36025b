---
description: 
globs: 
alwaysApply: true
---
{
  "name": "API接口规范",
  "description": "多租户前后端分离框架系统的API接口规范",
  "glob": "**/*.php"
}

# API接口规范

本系统采用RESTful风格的API设计，统一的响应格式，规范的错误处理机制。

## 请求规范

### 请求方法

- **GET**: 查询资源，如获取列表、详情
- **POST**: 创建资源
- **PUT**: 更新资源
- **DELETE**: 删除资源

### 请求路径

- 路径采用小写字母，单词间用短横线分隔
- 资源采用复数形式
- 路径层次不宜过深，一般不超过3层

```
/api/system/admins                # 获取管理员列表
/api/system/admins/{id}           # 获取指定管理员
/api/system/roles/{id}/menus      # 获取指定角色的菜单
```

### 请求参数

- GET请求参数通过URL查询字符串传递
- POST/PUT请求参数通过请求体传递，格式为JSON
- 文件上传使用multipart/form-data格式

## 响应规范

### 响应格式

所有API响应统一使用JSON格式，包含以下字段:

```json
{
  "code": 0,       // 状态码，0表示成功，非0表示失败
  "message": "success", // 提示信息
  "data": {}       // 响应数据
}
```

### 分页响应格式

列表查询接口返回的分页数据格式如下:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [],     // 数据列表
    "pagination": {
      "total": 100,  // 总记录数
      "page": 1,     // 当前页码
      "page_size": 10 // 每页记录数
    }
  }
}
```

## 错误处理

### 错误码规范

- 0: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

### 错误响应格式

```json
{
  "code": 400,
  "message": "参数错误",
  "data": {
    "field": "用户名不能为空"
  }
}
```

## 接口认证

### Token认证

- 登录成功后获取Token
- 请求接口时在Header中携带Token
- Token格式: `Authorization: Bearer {token}`

```php
// 验证Token
public function checkToken()
{
    // 获取请求头中的Token
    $token = request()->header('Authorization');
    if (empty($token)) {
        return $this->error('未授权', 401);
    }
    
    // 解析Token
    $token = str_replace('Bearer ', '', $token);
    try {
        $payload = $this->jwtService->decode($token);
        // 验证Token有效性
        if (!$this->jwtService->validate($payload)) {
            return $this->error('Token已过期', 401);
        }
        
        // 获取用户信息
        $userId = $payload['uid'];
        $admin = AdminModel::find($userId);
        if (empty($admin)) {
            return $this->error('用户不存在', 401);
        }
        
        // 将用户信息存入请求
        request()->admin = $admin;
        
        return true;
    } catch (\Exception $e) {
        return $this->error('Token无效', 401);
    }
}
```

## 接口文档

### 接口注释规范

使用PHPDoc注释接口，便于生成API文档:

```php
/**
 * 获取管理员列表
 * 
 * @route GET /api/system/admins
 * @param string $keyword 搜索关键词
 * @param int $page 页码
 * @param int $page_size 每页记录数
 * @return array
 */
public function index()
{
    // 接口实现
}
```

## 常用接口

### 认证接口

- **登录**: `POST /api/auth/login`
- **退出**: `POST /api/auth/logout`
- **获取当前用户信息**: `GET /api/auth/info`

### 用户管理接口

- **获取用户列表**: `GET /api/system/admins`
- **创建用户**: `POST /api/system/admins`
- **更新用户**: `PUT /api/system/admins/{id}`
- **删除用户**: `DELETE /api/system/admins/{id}`
- **分配角色**: `POST /api/system/admins/{id}/roles`

### 角色管理接口

- **获取角色列表**: `GET /api/system/roles`
- **创建角色**: `POST /api/system/roles`
- **更新角色**: `PUT /api/system/roles/{id}`
- **删除角色**: `DELETE /api/system/roles/{id}`
- **分配权限**: `POST /api/system/roles/{id}/menus`

### 菜单管理接口

- **获取菜单列表**: `GET /api/system/menus`
- **创建菜单**: `POST /api/system/menus`
- **更新菜单**: `PUT /api/system/menus/{id}`
- **删除菜单**: `DELETE /api/system/menus/{id}`
