<?php
declare(strict_types=1);

namespace app\notice\service\interfaces;

/**
 * 消息调度器接口
 */
interface NoticeDispatcherInterface
{
    /**
     * 发送模块消息
     * 
     * @param string $module 模块名称
     * @param string $action 动作名称
     * @param array $data 业务数据
     * @param array $recipients 接收人ID数组
     * @param array $options 选项
     * @return int|bool 成功返回消息ID，失败返回false
     */
    public function send(string $module, string $action, array $data, array $recipients, array $options = []);
} 