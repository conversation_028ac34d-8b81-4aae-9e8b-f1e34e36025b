<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmLeadService;
use app\crm\service\CrmFollowRecordService;
use app\crm\service\LeadConversionService;
use think\facade\Log;
use think\response\Json;

/**
 * 线索表控制器
 */
class CrmLeadController extends BaseController
{
	use ControllerImportExportTrait;
	
	/**
	 * @var CrmLeadService
	 */
	protected CrmLeadService $service;
	
	/**
	 * @var CrmFollowRecordService
	 */
	protected CrmFollowRecordService $followService;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service       = CrmLeadService::getInstance();
		$this->followService = CrmFollowRecordService::getInstance();
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		// todo order排序传参，如果使用多条件排序如何处理？现在的封装方向还是有问题！
		$params['sort_field'] = 'created_at';
		$params['sort_order'] = 'desc';
		$result               = $this->service->search($params);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		$info = $this->service->getOne(['id' => $id]);
		if ($info->isEmpty()) {
			return $this->error('数据不存在');
		}
		return $this->success('获取成功', $info);
	}
	
	/**
	 * 新增
	 *
	 * @return Json
	 */
	public function add(): Json
	{
		try {
			$params = $this->request->post();
			$filter = [
				'transformed_time',
				'last_followed_at',
				'next_followed_at',
				'created_at',
				'updated_at'
			];
			foreach ($params as $key => $value) {
				if (in_array($key, $filter)) {
					unset($params[$key]);
				}
			}
			return $this->service->add($params)
				? $this->success('添加成功')
				: $this->error('添加失败');
		}
		catch (\Exception $e) {
			Log::error('CRUD Add Debug - Exception: ' . $e->getMessage());
			return $this->error('添加失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新
	 *
	 * @param int $id
	 * @return Json
	 */
	public function edit(int $id): Json
	{
		$params = $this->request->post();
		
		try {
			$filter = [
				'transformed_time',
				'last_followed_at',
				'next_followed_at',
				'created_at',
				'updated_at'
			];
			foreach ($params as $key => $value) {
				if (in_array($key, $filter) && empty($value)) {
					$params[$key] = null;
				}
			}
			$result = $this->service->edit($params, ['id' => $id]);
			if ($result) {
				return $this->success('更新成功');
			}
			else {
				return $this->error('更新失败');
			}
		}
		catch (\Exception $e) {
			Log::error('CRUD Edit Debug - Exception: ' . $e->getMessage());
			error_log('CRUD Edit Debug - Exception: ' . $e->getMessage());
			return $this->error('更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 删除单个记录
	 *
	 * @return Json
	 */
	public function delete(): Json
	{
		// 获取URL参数中的ID
		$id = $this->request->param('id');
		
		if (empty($id)) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->delete([$id]);
		
		if ($result) {
			return $this->success('删除成功');
		}
		else {
			return $this->error('删除失败');
		}
	}
	
	/**
	 * 批量删除
	 *
	 * @return Json
	 */
	public function batchDelete(): Json
	{
		// 获取POST body中的ids数组
		$ids = $this->request->post('ids/a');
		
		if (empty($ids) || !is_array($ids)) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->delete($ids);
		
		if ($result) {
			return $this->success('批量删除成功');
		}
		else {
			return $this->error('批量删除失败');
		}
	}
	
	/**
	 * 更新字段值
	 *
	 * @return Json
	 */
	public function updateField(): Json
	{
		$id    = $this->request->post('id');
		$field = $this->request->post('field');
		$value = $this->request->post('value');
		
		if (empty($id) || empty($field)) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->updateField($id, $field, $value);
		if ($result) {
			return $this->success('更新成功');
		}
		else {
			return $this->error('更新失败');
		}
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * 检查线索转化条件
	 */
	public function checkConversion($id)
	{
		try {
			$conversionService = LeadConversionService::getInstance();
			$result            = $conversionService->checkConversionConditions((int)$id);
			
			if ($result['can_convert']) {
				return $this->success('检查完成', $result);
			}
			else {
				return $this->error($result['message']);
			}
		}
		catch (\Exception $e) {
			return $this->error('检查失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 线索转化为客户
	 */
	public function convertToCustomer($id)
	{
		try {
			$customerData = $this->request->post('customer_data', []);
			$contactData  = $this->request->post('contact_data', []);
			
			$conversionService = LeadConversionService::getInstance();
			$result            = $conversionService->convertToCustomer((int)$id, $customerData, $contactData);
			
			return $this->success($result['message'], $result['data']);
		}
		catch (\Exception $e) {
			return $this->error('转化失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 添加跟进记录
	 *
	 * @param int $id 线索ID
	 * @return Json
	 */
	public function addFollowRecord(int $id): Json
	{
		try {
			$followData = $this->request->post();
			
			// 验证线索是否存在且有权限访问
			$lead = $this->service->getOne(['id' => $id]);
			if (!$lead) {
				return $this->error('线索不存在或无权限访问');
			}
			
			// 准备跟进记录数据
			$followData['related_type'] = 'lead';
			$followData['related_id']   = $id;
			$followData['creator_id']   = get_user_id();
			
			// 添加跟进记录
			$result = $this->followService->add($followData);
			
			if ($result) {
				// 更新线索的最后跟进时间
				$this->service->updateField($id, 'last_followed_at', date('Y-m-d H:i:s'));
				
				// 如果设置了下次跟进时间，也更新
				if (!empty($followData['next_date'])) {
					$this->service->updateField($id, 'next_followed_at', $followData['next_date']);
				}
				
				return $this->success('跟进记录添加成功');
			}
			else {
				return $this->error('跟进记录添加失败');
			}
		}
		catch (\Exception $e) {
			Log::error('添加跟进记录失败: ' . $e->getMessage());
			return $this->error('添加跟进记录失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 获取线索跟进记录
	 *
	 * @param int $id 线索ID
	 * @return Json
	 */
	public function getFollowRecords(int $id): Json
	{
		try {
			// 验证线索是否存在且有权限访问
			$lead = $this->service->getOne(['id' => $id]);
			if (!$lead) {
				return $this->error('线索不存在或无权限访问');
			}
			
			// 获取跟进记录
			$params                 = $this->request->param();
			$params['related_type'] = 'lead';
			$params['related_id']   = $id;
			
			$result = $this->followService->search($params);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			Log::error('获取跟进记录失败: ' . $e->getMessage());
			return $this->error('获取跟进记录失败: ' . $e->getMessage());
		}
	}
	
}