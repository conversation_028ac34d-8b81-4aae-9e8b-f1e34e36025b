<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\storage;

use think\facade\Cache;
use think\facade\Config;

/**
 * Redis存储实现
 */
class RedisStorage implements StorageInterface
{
    /**
     * 缓存前缀
     */
    protected string $prefix = 'ratelimit:';
    
    /**
     * 构造函数
     *
     * @param string|null $prefix 自定义前缀
     */
    public function __construct(?string $prefix = null)
    {
        if ($prefix !== null) {
            $this->prefix = $prefix;
        }
    }
    
    /**
     * 生成完整的键名
     *
     * @param string $key 键名
     * @return string 完整键名
     */
    protected function getKey(string $key): string
    {
        return $this->prefix . $key;
    }
    
    /**
     * 递增计数器
     *
     * @param string $key 存储键名
     * @param int $expire 过期时间(秒)
     * @return int 递增后的值
     */
    public function increment(string $key, int $expire): int
    {
        $redisKey = $this->getKey($key);
        $redis = Cache::store('redis')->handler();
        
        // 使用管道执行原子操作
        $result = $redis->pipeline()
            ->incr($redisKey)
            ->expire($redisKey, $expire)
            ->exec();
        
        return (int)$result[0];
    }
    
    /**
     * 获取计数器值
     *
     * @param string $key 存储键名
     * @return int 计数器值
     */
    public function get(string $key): int
    {
        $redisKey = $this->getKey($key);
        $value = Cache::store('redis')->get($redisKey);
        return $value ? (int)$value : 0;
    }
    
    /**
     * 重置计数器
     *
     * @param string $key 存储键名
     * @return bool 是否成功
     */
    public function reset(string $key): bool
    {
        $redisKey = $this->getKey($key);
        return Cache::store('redis')->delete($redisKey);
    }
    
    /**
     * 获取计数器过期剩余时间
     *
     * @param string $key 存储键名
     * @return int 剩余过期时间(秒)
     */
    public function ttl(string $key): int
    {
        $redisKey = $this->getKey($key);
        $ttl = Cache::store('redis')->handler()->ttl($redisKey);
        return $ttl > 0 ? $ttl : 0;
    }
    
    /**
     * 批量递增计数器（滑动窗口）
     *
     * @param array $keys 键名数组
     * @param int $expire 过期时间(秒)
     * @return array 各键递增后的值
     */
    public function batchIncrement(array $keys, int $expire): array
    {
        $redis = Cache::store('redis')->handler();
        $pipeline = $redis->pipeline();
        
        $redisKeys = [];
        foreach ($keys as $key) {
            $redisKey = $this->getKey($key);
            $redisKeys[] = $redisKey;
            $pipeline->incr($redisKey);
            $pipeline->expire($redisKey, $expire);
        }
        
        $results = $pipeline->exec();
        
        // 提取每个键的递增结果
        $values = [];
        for ($i = 0; $i < count($keys); $i++) {
            $values[$keys[$i]] = (int)$results[$i * 2];
        }
        
        return $values;
    }
    
    /**
     * 批量获取计数器值
     *
     * @param array $keys 键名数组
     * @return array 各键的计数器值
     */
    public function batchGet(array $keys): array
    {
        $redis = Cache::store('redis')->handler();
        $redisKeys = array_map([$this, 'getKey'], $keys);
        $values = $redis->mget($redisKeys);
        
        $result = [];
        foreach ($keys as $i => $key) {
            $result[$key] = $values[$i] ? (int)$values[$i] : 0;
        }
        
        return $result;
    }
} 