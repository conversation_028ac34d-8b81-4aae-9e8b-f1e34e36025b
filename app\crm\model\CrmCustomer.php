<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\system\model\AdminModel;

/**
 * 客户表模型 - 基于crm_data.sql表结构
 */
class CrmCustomer extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_customer';
	
	// 字段类型转换
	protected $type = [
		'level'              => 'integer',
		'status'             => 'integer',
		'in_sea'             => 'integer',
		'sea_id'             => 'integer',
		'owner_user_id'      => 'integer',
		'annual_revenue'     => 'float',
		'employee_count'     => 'integer',
		'registered_capital' => 'float',
		'into_sea_time'      => 'datetime',
		'last_followed_at'   => 'datetime',
		'next_followed_at'   => 'datetime',
	];
	
	// 关联联系人
	public function contacts()
	{
		return $this->hasMany(CrmContact::class, 'customer_id', 'id');
	}
	
	// 关联商机
	public function businesses()
	{
		return $this->hasMany(CrmBusiness::class, 'customer_id', 'id');
	}
	
	// 关联合同
	public function contracts()
	{
		return $this->hasMany(CrmContract::class, 'customer_id', 'id');
	}
	
	// 关联跟进记录
	public function follow()
	{
		return $this->hasMany(CrmFollowRecord::class, 'related_id', 'id')
		            ->where('related_type', 'customer');
	}
	
	// 关联客户共享
	public function shares()
	{
		return $this->hasMany(CrmCustomerShare::class, 'customer_id', 'id');
	}
	
	// 获取主要联系人
	public function primary()
	{
		return $this->hasOne(CrmContact::class, 'customer_id', 'id')
		            ->where('is_primary', 1);
	}
	
	public function owner()
	{
		return $this->belongsTo(AdminModel::class, 'owner_user_id', 'id')->bind([
			'owner_name' => 'real_name'
		]);
	}
	
	public function getImpSceneFields(): array
	{
		return [
			'customer_name'      => [
				'label' => '客户名称',
				'type'  => 'text',
			],
			'industry'           => [
				'label' => '所属行业',
				'type'  => 'text',
			],
			'level'              => [
				'label'   => '客户级别',
				'type'    => 'select',
				'options' => [
					1 => '普通',
					2 => '重要',
					3 => '战略'
				]
			],
			'source'             => [
				'label' => '客户来源',
				'type'  => 'text',
			],
			'phone'              => [
				'label' => '电话',
				'type'  => 'text',
			],
			'website'            => [
				'label' => '网站',
				'type'  => 'text',
			],
			'region_province'    => [
				'label' => '省份',
				'type'  => 'text',
			],
			'region_city'        => [
				'label' => '城市',
				'type'  => 'text',
			],
			'region_district'    => [
				'label' => '区/县',
				'type'  => 'text',
			],
			'address'            => [
				'label' => '详细地址',
				'type'  => 'textarea',
			],
			'zip_code'           => [
				'label' => '邮政编码',
				'type'  => 'text',
			],
			'credit_code'        => [
				'label' => '信用代码',
				'type'  => 'text',
			],
			'annual_revenue'     => [
				'label' => '年销售额',
				'type'  => 'number',
			],
			'employee_count'     => [
				'label' => '员工数量',
				'type'  => 'number',
			],
			'registered_capital' => [
				'label' => '注册资金',
				'type'  => 'number',
			]
		];
	}
	
	// 获取默认搜索字段
	public function getDefaultSearchFields(): array
	{
		return [
			'customer_name' => ['type' => 'like'],
			'level'         => ['type' => 'eq'],
			'phone'         => ['type' => 'eq'],
			'status'        => ['type' => 'eq'],
			'in_sea'        => ['type' => 'eq'],
			'lock_status'   => ['type' => 'eq'],
			'into_sea_time' => ['type' => 'eq'],
		];
	}
}