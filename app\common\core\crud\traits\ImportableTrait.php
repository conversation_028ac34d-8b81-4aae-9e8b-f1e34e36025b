<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use think\facade\Db;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * 数据导入功能特性
 *
 * 提供数据导入功能，可独立使用，不依赖于导出功能
 */
trait ImportableTrait
{
	
	
	protected bool $enableImport = false;   // 是否启用导入功能
	
	protected int $importBatchSize = 100;   // 导入批处理大小
	
	protected string $importScene = 'import'; // 默认导入场景
	
	/**
	 * 获取导入字段配置
	 *
	 * @param string $scene 导入场景
	 * @return array 导入字段配置
	 */
	public function getImportFields(string $scene = ''): array
	{
		// 从模型的场景中获取导入字段列表
		$fields = $this->getSceneFields($scene, 'imp');
		
		// 获取字段配置信息
		$fieldsConfig = $this->getFieldsConfig($fields, 'imp');
		
		// 解析字段格式配置
		return $this->parseFieldFormats($fieldsConfig);
	}
	
	/**
	 * 生成导入模板
	 *
	 * @param string $scene 场景名称
	 * @return string 生成的Excel文件路径
	 * @throws \Exception 生成模板失败时抛出异常
	 */
	public function generateImportTemplate(string $scene = ''): string
	{
		// 获取导入字段配置
		$fields = $this->getImportFields($scene);
		
		if (empty($fields)) {
			throw new \Exception('没有可导入的字段');
		}
		
		// 创建模板文件
		return $this->createTemplateFile($fields);
	}
	
	/**
	 * 导入数据
	 *
	 * @param string $file   上传的文件路径
	 * @param array  $config 导入配置
	 * @return array 导入结果
	 * @throws \Exception 导入失败时抛出异常
	 */
	public function importData(string $file, array $config = []): array
	{
		// 获取导入字段配置
		$scene  = $config['scene'] ?? '';
		$fields = $this->getImportFields($scene);
		
		if (empty($fields)) {
			throw new \Exception('没有可导入的字段');
		}
		
		// 解析导入文件
		$data = $this->parseImportFile($file, $fields);
		
		if (empty($data)) {
			throw new \Exception('导入文件中没有有效数据');
		}
		
		// 保存导入数据
		return $this->saveImportData($data, $config);
	}
	
	/**
	 * 解析导入文件
	 *
	 * @param string $file   文件路径
	 * @param array  $fields 字段配置
	 * @return array 解析后的数据
	 */
	protected function parseImportFile(string $file, array $fields): array
	{
		// 读取Excel文件
		$spreadsheet = IOFactory::load($file);
		$sheet       = $spreadsheet->getActiveSheet();
		
		// 获取标题行
		$headers = [];
		$column  = 1;
		while (true) {
			$header = $sheet->getCellByColumnAndRow($column, 1)
			                ->getValue();
			if (empty($header)) {
				break;
			}
			$headers[$column] = $header;
			$column++;
		}
		
		if (empty($headers)) {
			throw new \Exception('导入文件缺少标题行');
		}
		
		// 将标题映射到字段
		$fieldMap = [];
		foreach ($fields as $index => $field) {
			$columnIndex = array_search($field['title'], $headers);
			if ($columnIndex !== false) {
				$fieldMap[$columnIndex] = $field;
			}
		}
		
		if (empty($fieldMap)) {
			throw new \Exception('导入文件的列标题与系统字段不匹配');
		}
		
		// 读取数据行
		$data     = [];
		$rowIndex = 2; // 从第二行开始
		$errors   = [];
		
		while (true) {
			$row     = [];
			$hasData = false;
			
			// 读取一行数据
			foreach ($fieldMap as $columnIndex => $field) {
				$value = $sheet->getCellByColumnAndRow($columnIndex, $rowIndex)
				               ->getValue();
				
				// 格式化数据
				if (isset($field['format']) && $value !== null && $value !== '') {
					$this->initFormatProcessor();
					$options = [];
					
					// 处理枚举选项
					if ($field['format'] === 'enum' || $field['format'] === 'status') {
						$options['map'] = $this->getOptionsMap($field);
					}
					
					try {
						$value = $this->formatProcessor->parseForImport($value, $field['format'], $options);
					}
					catch (\Exception $e) {
						$errors[] = "第{$rowIndex}行，{$field['title']}列: " . $e->getMessage();
						$value    = null;
					}
				}
				
				if ($value !== null && $value !== '') {
					$hasData = true;
				}
				
				$row[$field['name']] = $value;
			}
			
			// 如果没有数据，认为已经到达文件末尾
			if (!$hasData) {
				break;
			}
			
			// 验证必填字段
			foreach ($fieldMap as $field) {
				if (!empty($field['required']) && empty($row[$field['name']])) {
					$errors[] = "第{$rowIndex}行，{$field['title']}列: 必填字段不能为空";
				}
			}
			
			// 如果有错误，跳过该行
			if (empty($errors)) {
				$data[] = $row;
			}
			
			$rowIndex++;
		}
		
		// 如果有错误，抛出异常
		if (!empty($errors)) {
			throw new \Exception('导入数据验证失败：' . implode('; ', $errors));
		}
		
		return $data;
	}
	
	/**
	 * 保存导入的数据
	 *
	 * @param array $data   数据
	 * @param array $config 配置
	 * @return array 导入结果
	 * @throws \Exception 保存失败时抛出异常
	 */
	protected function saveImportData(array $data, array $config): array
	{
		// 批量大小
		$batchSize = $config['batch_size'] ?? 100;
		
		// 开启事务
		Db::startTrans();
		
		try {
			$success = 0;
			$fail    = 0;
			
			// 分批处理数据
			$batches = array_chunk($data, $batchSize);
			
			foreach ($batches as $batch) {
				// 调用模型的批量插入方法
				if ($this->model) {
					$this->model->saveAll($batch);
					$success += count($batch);
				}
				else {
					// 如果没有模型，使用通用的数据库操作
					foreach ($batch as $row) {
						try {
							// 获取表名
							$table = $this->getImportTable();
							
							// 插入数据
							Db::table($table)
							  ->insert($row);
							$success++;
						}
						catch (\Exception $e) {
							$fail++;
						}
					}
				}
			}
			
			// 提交事务
			Db::commit();
			
			return [
				'success' => $success,
				'fail'    => $fail,
				'total'   => count($data)
			];
		}
		catch (\Exception $e) {
			// 回滚事务
			Db::rollback();
			throw $e;
		}
	}
	
	/**
	 * 创建模板文件
	 *
	 * @param array $fields 字段配置
	 * @return string 生成的文件路径
	 */
	public function createTemplateFile(array $fields): string
	{
		// 创建工作表
		$spreadsheet = new Spreadsheet();
		$sheet       = $spreadsheet->getActiveSheet();
		
		// 设置标题行
		$col = 1;
		foreach ($fields as $field) {
			$title = $field['title'];
			
			// 为必填字段添加标记
			if (!empty($field['required'])) {
				$title .= ' *';
			}
			
			$sheet->setCellValueByColumnAndRow($col, 1, $title);
			
			// 设置标题单元格样式
			$columnLetter = $this->getColumnLetter($col);
			$sheet->getStyle("{$columnLetter}1")
			      ->getFont()
			      ->setBold(true);
			
			// 如果是枚举类型，添加数据验证
			if (in_array($field['format'] ?? '', [
				'enum',
				'status'
			])) {
				$options = $this->getOptionsMap($field);
				
				if (!empty($options)) {
					// 在第二行添加注释说明可选值
					$optionsText = "可选值: " . implode(', ', array_values($options));
					$sheet->setCellValueByColumnAndRow($col, 2, $optionsText);
					
					// 创建下拉列表
					$validation = $sheet->getCell("{$columnLetter}3")
					                    ->getDataValidation();
					$validation->setType(DataValidation::TYPE_LIST)
					           ->setErrorStyle(DataValidation::STYLE_INFORMATION)
					           ->setAllowBlank(true)
					           ->setShowInputMessage(true)
					           ->setShowErrorMessage(true)
					           ->setShowDropDown(true)
					           ->setErrorTitle('输入错误')
					           ->setError('值不在列表中')
					           ->setPromptTitle('选择' . $field['title'])
					           ->setPrompt('请从下拉列表中选择')
					           ->setFormula1(sprintf('"%s"', implode(',', array_values($options))));
					
					// 将验证应用到多个单元格
					$sheet->setDataValidation("{$columnLetter}3:{$columnLetter}1000", $validation);
				}
			}
			
			$col++;
		}
		
		// 添加使用说明
		$sheet->setCellValue('A2', '说明：带 * 的为必填项。第三行开始填写实际数据。');
		$sheet->mergeCells('A2:' . $this->getColumnLetter(count($fields)) . '2');
		$sheet->getStyle('A2')
		      ->getFont()
		      ->setItalic(true);
		$sheet->getRowDimension(2)
		      ->setRowHeight(30);
		
		// 冻结首行
		$sheet->freezePane('A3');
		
		// 自动调整列宽
		foreach (range(1, count($fields)) as $col) {
			$sheet->getColumnDimensionByColumn($col)
			      ->setAutoSize(true);
		}
		
		// 生成文件
		$filename = $this->getImportTemplateName();
		$filepath = $this->getSavePath('import') . '/' . $filename;
		
		// 保存Excel文件
		$writer = new Xlsx($spreadsheet);
		$writer->save($filepath);
		
		return $filepath;
	}
	
	
	/**
	 * 获取导入模板文件名
	 *
	 * @return string 文件名
	 */
	protected function getImportTemplateName(): string
	{
		$table = '';
		if ($this->model) {
			$table = $this->model->getTable();
		}
		
		return $table . '_import_template.xlsx';
	}
	
	/**
	 * 获取导入表名
	 *
	 * @return string 表名
	 */
	protected function getImportTable(): string
	{
		if ($this->model) {
			return $this->model->getTable();
		}
		
		return '';
	}
} 