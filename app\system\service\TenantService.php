<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\system\model\TenantModel;

/**
 * 租户表服务类
 */
class TenantService extends BaseService
{
	use CrudServiceTrait;
    /**
     * 构造函数
     */
    protected function __construct()
    {
        $this->model = new TenantModel();
        parent::__construct();
    }
} 