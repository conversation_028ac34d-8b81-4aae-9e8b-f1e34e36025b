-- 检查工作流表单配置
-- 查看所有工作流类型
SELECT 
    id,
    name,
    module_code,
    business_code,
    status,
    created_at
FROM workflow_type 
WHERE business_code IN (
    'hr_business_trip',
    'hr_outing', 
    'office_sample_mail',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'ims_outbound_approval',
    'ims_shipment_approval'
)
ORDER BY business_code;

-- 查看对应的工作流定义
SELECT 
    wd.id as definition_id,
    wd.name as definition_name,
    wd.status as definition_status,
    wt.business_code,
    wt.name as type_name
FROM workflow_definition wd
JOIN workflow_type wt ON wd.type_id = wt.id
WHERE wt.business_code IN (
    'hr_business_trip',
    'hr_outing', 
    'office_sample_mail',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'ims_outbound_approval',
    'ims_shipment_approval'
)
ORDER BY wt.business_code;

-- 检查缺少的工作流类型
SELECT 'Missing workflow types:' as message;
SELECT business_code FROM (
    SELECT 'hr_business_trip' as business_code
    UNION SELECT 'hr_outing'
    UNION SELECT 'office_sample_mail'
    UNION SELECT 'finance_payment_approval'
    UNION SELECT 'finance_expense_reimbursement'
    UNION SELECT 'ims_outbound_approval'
    UNION SELECT 'ims_shipment_approval'
) expected
WHERE business_code NOT IN (
    SELECT business_code FROM workflow_type WHERE business_code IS NOT NULL
);

-- 检查缺少工作流定义的类型
SELECT 'Types without definitions:' as message;
SELECT wt.business_code, wt.name
FROM workflow_type wt
LEFT JOIN workflow_definition wd ON wt.id = wd.type_id
WHERE wt.business_code IN (
    'hr_business_trip',
    'hr_outing', 
    'office_sample_mail',
    'finance_payment_approval',
    'finance_expense_reimbursement',
    'ims_outbound_approval',
    'ims_shipment_approval'
)
AND wd.id IS NULL;
