<?php
declare(strict_types=1);

namespace app\project\service;

use app\common\core\base\BaseService;
use app\project\model\ProjectTaskRecord;
use app\project\model\ProjectTask;

/**
 * 任务记录表（评论+跟进）服务类
 */
class ProjectTaskRecordService extends BaseService
{
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new ProjectTaskRecord();
		parent::__construct();
	}
	
	/**
	 * 初始化配置
	 */
	protected function initialize(): void
	{
		// 任务记录需要数据权限过滤
		$this->crudService->setEnableDataPermission(true);
		
		// 设置字段场景
		$this->crudService->setFieldScenes([
			'list'   => [
				'id',
				'task_id',
				'record_type',
				'content',
				'follow_type',
				'follow_date',
				'next_plan',
				'next_date',
				'attachments',
				'creator_id',
				'created_at',
				'updated_at',
				'can_edit',
				'can_delete'
			],
			'detail' => ['*'],
			'select' => [
				'id',
				'content',
				'record_type',
				'created_at',
				'can_edit',
				'can_delete'
			]
		]);
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'task_id' => ['type' => 'eq'],
			
			'record_type' => ['type' => 'eq'],
			
			'follow_type' => ['type' => 'eq'],
			
			'follow_date' => ['type' => 'between'],
			
			'next_date' => ['type' => 'between'],
		
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'task_id'     => 'require|integer',
			'record_type' => 'require|in:comment,follow',
			'content'     => 'require',
			'follow_type' => 'in:phone,meeting,email,other',
			'follow_date' => 'date',
			'next_date'   => 'date',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => array_merge($rules, [
				'task_id'     => 'integer',
				// 编辑时不要求必填
				'record_type' => 'in:comment,follow',
				// 编辑时不要求必填
			]),
			'comment' => [
				'task_id' => 'require|integer',
				'content' => 'require',
			],
			'follow' => [
				'task_id'     => 'require|integer',
				'content'     => 'require',
				'follow_type' => 'require|in:phone,meeting,email,other',
				'follow_date' => 'require|date',
			],
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	/**
	 * 添加评论
	 */
	public function addComment(int $taskId, string $content, array $attachments = []): int
	{
		// 验证任务是否存在
		$task = ProjectTask::find($taskId);
		if (!$task) {
			throw new \Exception('任务不存在');
		}
		
		// 验证数据
		$this->validate([
			'task_id' => $taskId,
			'content' => $content,
		], 'comment');
		
		return (new ProjectTaskRecord())->addComment($taskId, $content, $attachments);
	}
	
	/**
	 * 添加跟进
	 */
	public function addFollow(int $taskId, array $data): int
	{
		// 验证任务是否存在
		$task = ProjectTask::find($taskId);
		if (!$task) {
			throw new \Exception('任务不存在');
		}
		
		// 补充必要字段
		$data['task_id'] = $taskId;
		
		// 验证数据
		$this->validate($data, 'follow');
		
		$recordId = (new ProjectTaskRecord())->addFollow($taskId, $data);
		
		// 更新任务的跟进时间
		if (!empty($data['follow_date'])) {
			$task->saveByUpdate([
				'last_followed_at' => $data['follow_date']
			]);
		}
		
		return $recordId;
	}
	
	/**
	 * 获取任务的评论列表
	 */
	public function getTaskComments(int $taskId, int $page = 1, int $limit = 20): array
	{
		return $this->model->getTaskComments($taskId, $page, $limit);
	}
	
	/**
	 * 获取任务的跟进列表
	 */
	public function getTaskFollows(int $taskId, int $page = 1, int $limit = 20): array
	{
		return $this->model->getTaskFollows($taskId, $page, $limit);
	}
	
	/**
	 * 获取任务的所有记录
	 */
	public function getTaskRecords(int $taskId, int $page = 1, int $limit = 50): array
	{
		return $this->model->getTaskRecords($taskId, $page, $limit);
	}
	
	/**
	 * 检查记录操作权限
	 */
	public function checkRecordPermission(int $recordId, string $action = 'edit'): bool
	{
		$record = $this->crudService->getDetail($recordId);
		if ($record->isEmpty()) {
			return false;
		}
		
		// 使用模型中的权限检查方法
		return $record->checkRecordPermission($action);
	}
}