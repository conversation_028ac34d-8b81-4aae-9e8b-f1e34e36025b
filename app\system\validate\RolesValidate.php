<?php
declare (strict_types=1);

namespace app\system\validate;

use think\Validate;


/*
 * 管理员验证类
 * */

class RolesValidate extends Validate
{
	/**
	 * 定义验证规则
	 * 格式：'字段名' =>  ['规则1','规则2'...]
	 *
	 * @var array
	 */
	protected $rule = [
		'name'                => 'require',
		'data_scope'          => 'require|in:1,2,3,4,5',
		'data_scope_dept_ids' => 'array',
		'remark'              => 'max:255',
		'sort'                => 'number',
		'menu_ids'            => 'array',
		'status'              => 'in:0,1',
	];
	
	/**
	 * 定义错误信息
	 * 格式：'字段名.规则名' =>  '错误信息'
	 *
	 * @var array
	 */
	protected $message = [
		'name.require'              => '请输入角色名称',
		'data_scope.require'        => '请选择数据权限',
		'data_scope.in'             => '数据权限不正确',
		'data_scope_dept_ids.array' => '数据范围格式错误',
		'status.in'                 => '状态值不正确',
		'remark.max'                => '备注不能超过255个字符',
		'sort.number'               => '排序值不正确',
		'menu_ids.array'            => '角色权限格式错误',
	];
}
