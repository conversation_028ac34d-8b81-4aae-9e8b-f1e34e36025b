<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\storage;

/**
 * 存储接口
 */
interface StorageInterface
{
    /**
     * 递增计数器
     *
     * @param string $key 存储键名
     * @param int $expire 过期时间(秒)
     * @return int 递增后的值
     */
    public function increment(string $key, int $expire): int;
    
    /**
     * 获取计数器值
     *
     * @param string $key 存储键名
     * @return int 计数器值
     */
    public function get(string $key): int;
    
    /**
     * 重置计数器
     *
     * @param string $key 存储键名
     * @return bool 是否成功
     */
    public function reset(string $key): bool;
    
    /**
     * 获取计数器过期剩余时间
     *
     * @param string $key 存储键名
     * @return int 剩余过期时间(秒)
     */
    public function ttl(string $key): int;
    
    /**
     * 批量递增计数器（滑动窗口）
     *
     * @param array $keys 键名数组
     * @param int $expire 过期时间(秒)
     * @return array 各键递增后的值
     */
    public function batchIncrement(array $keys, int $expire): array;
    
    /**
     * 批量获取计数器值
     *
     * @param array $keys 键名数组
     * @return array 各键的计数器值
     */
    public function batchGet(array $keys): array;
} 