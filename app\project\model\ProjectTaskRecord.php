<?php
declare(strict_types=1);

namespace app\project\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 任务记录表（评论+跟进）模型
 */
class ProjectTaskRecord extends BaseModel
{
    use CreatorTrait;

    // 设置表名
    protected $name = 'project_task_record';

    // 设置主键
    protected $pk = 'id';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 时间字段转换
    protected $dateFormat = 'Y-m-d H:i:s';

    // 软删除
    protected string $deleteTime = 'deleted_at';

    // 记录类型常量
    const TYPE_COMMENT = 'comment';
    const TYPE_FOLLOW = 'follow';

    // 跟进方式常量
    const FOLLOW_TYPE_PHONE = 'phone';
    const FOLLOW_TYPE_MEETING = 'meeting';
    const FOLLOW_TYPE_EMAIL = 'email';
    const FOLLOW_TYPE_OTHER = 'other';

    // 字段类型转换
    protected $type = [
        'task_id' => 'integer',
        'follow_date' => 'datetime',
        'next_date' => 'datetime',
    ];

    public function __construct(array $data = [])
    {
        parent::__construct($data);
    }

    /**
     * 所属任务关联
     */
    public function task()
    {
        return $this->belongsTo(ProjectTask::class, 'task_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\app\system\model\AdminModel::class, 'creator_id');
    }

    /**
     * 获取记录类型文本
     */
    public function getRecordTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_COMMENT => '评论',
            self::TYPE_FOLLOW => '跟进',
        ];
        return $types[$data['record_type']] ?? '未知';
    }

    /**
     * 获取跟进方式文本
     */
    public function getFollowTypeTextAttr($value, $data)
    {
        $types = [
            self::FOLLOW_TYPE_PHONE => '电话',
            self::FOLLOW_TYPE_MEETING => '会议',
            self::FOLLOW_TYPE_EMAIL => '邮件',
            self::FOLLOW_TYPE_OTHER => '其他',
        ];
        return $types[$data['follow_type']] ?? '';
    }

    /**
     * 获取是否可编辑权限
     */
    public function getCanEditAttr($value, $data)
    {
        return $this->checkRecordPermission('edit');
    }

    /**
     * 获取是否可删除权限
     */
    public function getCanDeleteAttr($value, $data)
    {
        return $this->checkRecordPermission('delete');
    }

    /**
     * 检查记录操作权限
     */
    public function checkRecordPermission(string $action = 'edit'): bool
    {
        $userId = get_user_id();

        // 超级管理员直接通过
        if (is_super_admin() || is_tenant_super_admin()) {
            return true;
        }

        // 记录创建者可以操作自己的记录
        if ($this->creator_id == $userId) {
            return true;
        }

        // 获取任务信息
        $task = $this->task;
        if (!$task) {
            return false;
        }

        // 任务负责人可以操作任务内所有记录
        if ($task->assignee_id == $userId) {
            return true;
        }

        // 项目负责人可以操作项目内所有记录
        $project = $task->project;
        if ($project && $project->owner_id == $userId) {
            return true;
        }

        return false;
    }

    /**
     * 添加评论
     */
    public function addComment(int $taskId, string $content, array $attachments = []): int
    {
        return $this->saveByCreate([
            'task_id' => $taskId,
            'record_type' => self::TYPE_COMMENT,
            'content' => $content,
            'attachments' => $attachments ? json_encode($attachments) : '',
        ]);
    }

    /**
     * 添加跟进
     */
    public function addFollow(int $taskId, array $data): int
    {
        $data['task_id'] = $taskId;
        $data['record_type'] = self::TYPE_FOLLOW;

        // 处理附件
        if (isset($data['attachments']) && is_array($data['attachments'])) {
            $data['attachments'] = json_encode($data['attachments']);
        }

        return $this->saveByCreate($data);
    }

    /**
     * 获取任务的评论列表
     */
    public function getTaskComments(int $taskId, int $page = 1, int $limit = 20): array
    {
        $query = $this->with(['creator'])
                     ->where('task_id', $taskId)
                     ->where('record_type', self::TYPE_COMMENT)
                     ->order('created_at desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取任务的跟进列表
     */
    public function getTaskFollows(int $taskId, int $page = 1, int $limit = 20): array
    {
        $query = $this->with(['creator'])
                     ->where('task_id', $taskId)
                     ->where('record_type', self::TYPE_FOLLOW)
                     ->order('follow_date desc, created_at desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 获取任务的所有记录（评论+跟进）
     */
    public function getTaskRecords(int $taskId, int $page = 1, int $limit = 50): array
    {
        $query = $this->with(['creator'])
                     ->where('task_id', $taskId)
                     ->order('created_at desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }
}