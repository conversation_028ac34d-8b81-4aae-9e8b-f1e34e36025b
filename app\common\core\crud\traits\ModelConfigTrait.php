<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use think\Model;

/**
 * 模型配置相关功能
 */
trait ModelConfigTrait
{
    /**
     * 设置模型实例
     */
    public function setModel(Model $model): self
    {
        $this->model = $model;
        
        // 从模型获取自定义配置
        $this->loadModelConfiguration();
        
        return $this;
    }
    
    /**
     * 从模型加载配置
     */
    protected function loadModelConfiguration(): void
    {
        // 如果模型有自定义的数据权限字段
        if (method_exists($this->model, 'getDataRangeField')) {
            $this->dataRangeField = $this->model->getDataRangeField();
        }
        
        // 如果模型有自定义的默认搜索字段
        if (method_exists($this->model, 'getDefaultSearchFields')) {
            $this->defaultSearchFields = $this->model->getDefaultSearchFields();
        }
        
        // 如果模型有自定义的允许单字段编辑的字段
        if (method_exists($this->model, 'getAllowUpdateFields')) {
            $this->allowUpdateFields = $this->model->getAllowUpdateFields();
        }
        
        // 如果模型有自定义的禁止单字段编辑的字段
        if (method_exists($this->model, 'getForbidUpdateFields')) {
            $this->forbidUpdateFields = array_merge($this->forbidUpdateFields, $this->model->getForbidUpdateFields());
        }
        
        // 如果模型有自定义的允许排序的字段
        if (method_exists($this->model, 'getAllowSortFields')) {
            $this->allowSortFields = $this->model->getAllowSortFields();
        }
    }
    
    /**
     * 设置是否启用数据权限过滤
     */
    public function setEnableDataPermission(bool $enable): self
    {
        $this->enableDataPermission = $enable;
        return $this;
    }
    
    /**
     * 获取是否启用数据权限过滤
     */
    public function getEnableDataPermission(): bool
    {
        return $this->enableDataPermission;
    }
} 