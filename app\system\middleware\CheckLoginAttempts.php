<?php

namespace app\system\middleware;

use app\common\core\constants\CacheConstant;
use app\common\core\constants\ValidateCodeConstant;
use app\common\exception\ValidateFailedException;
use app\system\service\ConfigService;
use think\facade\Cache;
use think\Request;
use think\Response;

class CheckLoginAttempts
{
	public function handle(Request $request, \Closure $next): Response
	{
		// 获取用户标识（这里以手机号为例）
		$userName = $request->post('username/s');
		if (!$userName) {
			throw new ValidateFailedException('请输入用户名');
		}
		
		$ip       = $request->ip();
		$cacheKey = CacheConstant::LOGIN_CACHE_KEY . $ip . md5($userName);
		
		$loginLimitsConfig = ConfigService::getInstance()
		                                  ->getInfo('login_limits');
		// 获取配置参数
		$maxAttempts = $loginLimitsConfig['max_attempts'] ?? 5;
		$lockTime    = $loginLimitsConfig['lock_time'] ?? 300;
		
		// 初始化或获取当前状态
		$data = Cache::get($cacheKey, [
			'attempts'    => 0,
			'last_failed' => 0
		]);
		
		$currentTime = time();
		
		// 检查是否处于锁定状态
		if ($data['attempts'] >= $maxAttempts && ($currentTime - $data['last_failed']) < $lockTime) {
			$remaining = $lockTime - ($currentTime - $data['last_failed']);
			return json([
				'code' => 429,
				'msg'  => "操作过于频繁，请{$remaining}秒后再试"
			]);
		}
		
		// 继续执行请求
		$response = $next($request);
		
		// 处理响应结果
		$responseData = $response->getData();
		if (!$responseData || !isset($responseData['code'])) {
			return $response;
		}
		if ($responseData['code'] == ValidateCodeConstant::LOGIN_FAILED) { // 登录失败
			$data['attempts']++;
			$data['last_failed'] = $currentTime;
			
			Cache::tag(CacheConstant::LOGIN_PREFIX)
			     ->set($cacheKey, $data, $lockTime);
		}
		else {
			// 登录成功或其他请求
			Cache::delete($cacheKey);
		}
		
		return $response;
	}
}