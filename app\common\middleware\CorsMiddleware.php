<?php

namespace app\common\middleware;

use think\Config;
use think\Response;

class CorsMiddleware
{
	protected $cookieDomain;
	
	protected $header = [
		'Access-Control-Allow-Credentials' => 'true',
		'Access-Control-Max-Age'           => 1800,
		'Access-Control-Allow-Methods'     => 'GET, POST, PATCH, PUT, DELETE, OPTIONS',
		'Access-Control-Allow-Headers'     => 'Authorization, Content-Type, X-CSRF-TOKEN, X-Requested-With,token',
	];
	
	public function __construct(Config $config)
	{
		$this->cookieDomain = $config->get('cookie.domain', '');
	}
	
	/**
	 * 处理请求
	 *
	 * @param \think\Request $request
	 * @param \Closure $next
	 */
	public function handle($request, \Closure $next, ?array $header = [])
	{
		$header = !empty($header) ? array_merge($this->header, $header) : $this->header;
		
		if (!isset($header['Access-Control-Allow-Origin'])) {
			$origin = $request->header('origin');
			
			if ($origin && ('' == $this->cookieDomain || strpos($origin, $this->cookieDomain))) {
				$header['Access-Control-Allow-Origin'] = $origin;
			} else {
				$header['Access-Control-Allow-Origin'] = '*';
			}
		}
		if ($request->method() == 'OPTIONS') {
			return Response::create('','json')->header($header)->code(200);
		}
		
		return $next($request)->header($header);
	}
	
}