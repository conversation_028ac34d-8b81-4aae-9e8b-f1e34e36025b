<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端菜单测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            position: relative;
            width: 100%;
            height: 100vh;
        }
        
        /* 模拟菜单容器 */
        .layout-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 101;
            display: flex;
            height: 100vh;
            background: white;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .menu-left {
            width: 240px;
            background: white;
            border-right: 1px solid #e0e0e0;
        }
        
        .menu-header {
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            color: #333;
        }
        
        .menu-items {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .menu-item:hover {
            background: #f5f5f5;
            color: #333;
        }
        
        /* 头部栏 */
        .header-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 20px;
            z-index: 100;
        }
        
        .menu-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .menu-btn:hover {
            background: #f0f0f0;
        }
        
        .menu-btn::before {
            content: '☰';
            font-size: 18px;
            color: #666;
        }
        
        .page-title {
            font-weight: bold;
            color: #333;
        }
        
        /* 主内容区域 */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            margin-left: 240px;
            transition: margin-left 0.3s ease;
        }
        
        /* 移动端样式 */
        @media (max-width: 768px) {
            .layout-sidebar {
                transform: translateX(-100%);
            }
            
            .layout-sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            /* 移动端遮罩层 */
            .mobile-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                background: rgba(0, 0, 0, 0.5);
                z-index: 50;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease, visibility 0.3s ease;
            }
            
            .mobile-overlay.show {
                opacity: 1;
                visibility: visible;
            }
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .test-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .test-info p {
            color: #666;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- 头部栏 -->
        <div class="header-bar">
            <div class="menu-btn" id="menuBtn"></div>
            <div class="page-title">移动端菜单测试</div>
        </div>
        
        <!-- 侧边菜单 -->
        <div class="layout-sidebar" id="sidebar">
            <div class="menu-left">
                <div class="menu-header">
                    系统菜单
                </div>
                <div class="menu-items">
                    <div class="menu-item">首页</div>
                    <div class="menu-item">用户管理</div>
                    <div class="menu-item">系统设置</div>
                    <div class="menu-item">数据统计</div>
                    <div class="menu-item">日志管理</div>
                    <div class="menu-item">权限管理</div>
                </div>
            </div>
        </div>
        
        <!-- 移动端遮罩层 -->
        <div class="mobile-overlay" id="overlay"></div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="test-info">
                <h3>测试说明</h3>
                <p>1. 在桌面端：菜单默认显示在左侧</p>
                <p>2. 在移动端（屏幕宽度 ≤ 768px）：</p>
                <p>   - 点击左上角菜单按钮打开菜单</p>
                <p>   - 点击遮罩层（菜单外的灰色区域）关闭菜单</p>
                <p>   - 点击菜单项也会关闭菜单</p>
                <p>3. 请调整浏览器窗口大小来测试不同屏幕尺寸下的效果</p>
            </div>
            
            <div style="background: white; padding: 20px; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h2>主要内容区域</h2>
                <p>这里是页面的主要内容。在移动端，当菜单打开时，这个区域会被遮罩层覆盖。</p>
                <br>
                <p>修复的问题：</p>
                <ul style="margin-left: 20px; line-height: 1.6;">
                    <li>✅ 移动端遮罩层z-index正确设置，不会覆盖菜单</li>
                    <li>✅ 点击遮罩层可以关闭菜单</li>
                    <li>✅ 点击菜单项可以正常选择并关闭菜单</li>
                    <li>✅ 菜单按钮在移动端正确显示</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        const menuBtn = document.getElementById('menuBtn');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const menuItems = document.querySelectorAll('.menu-item');
        
        let isMenuOpen = false;
        
        // 检查是否为移动端
        function isMobile() {
            return window.innerWidth <= 768;
        }
        
        // 打开/关闭菜单
        function toggleMenu() {
            isMenuOpen = !isMenuOpen;
            
            if (isMobile()) {
                sidebar.classList.toggle('open', isMenuOpen);
                overlay.classList.toggle('show', isMenuOpen);
            }
        }
        
        // 关闭菜单
        function closeMenu() {
            if (isMobile() && isMenuOpen) {
                isMenuOpen = false;
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            }
        }
        
        // 事件监听
        menuBtn.addEventListener('click', toggleMenu);
        overlay.addEventListener('click', closeMenu);
        
        // 点击菜单项关闭菜单
        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                console.log('点击菜单项:', item.textContent);
                closeMenu();
            });
        });
        
        // 窗口大小改变时的处理
        window.addEventListener('resize', () => {
            if (!isMobile()) {
                // 桌面端时重置状态
                isMenuOpen = false;
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            }
        });
    </script>
</body>
</html>
