<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出差天数计算测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-case.pass {
            background-color: #f0f8f0;
            border-color: #4caf50;
        }
        .test-case.fail {
            background-color: #fff0f0;
            border-color: #f44336;
        }
        .result {
            font-weight: bold;
            margin-top: 10px;
        }
        .pass .result {
            color: #4caf50;
        }
        .fail .result {
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>出差天数计算测试</h1>
    <p>验证前端calculateDays函数的计算结果</p>
    
    <div id="test-results"></div>

    <script>
        /**
         * 前端calculateDays函数（从utils/date.ts复制）
         */
        function calculateDays(startDate, endDate) {
            if (!startDate || !endDate) return 0

            const start = new Date(startDate)
            const end = new Date(endDate)

            // 检查日期有效性
            if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0

            // 如果结束日期早于开始日期，返回0
            if (end < start) return 0

            // 将时间设置为当天的开始时间（00:00:00），确保按天计算
            const startDay = new Date(start.getFullYear(), start.getMonth(), start.getDate())
            const endDay = new Date(end.getFullYear(), end.getMonth(), end.getDate())

            // 计算天数差异并加1（包含开始和结束日期）
            const diffTime = endDay.getTime() - startDay.getTime()
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1

            return Math.max(0, diffDays)
        }

        /**
         * 测试用例
         */
        const testCases = [
            {
                name: '同一天出差',
                startDate: '2025-07-29 09:00:00',
                endDate: '2025-07-29 18:00:00',
                expected: 1,
                description: '同一天的不同时间应该计算为1天'
            },
            {
                name: '跨天出差（3天）',
                startDate: '2025-07-29 09:00:00',
                endDate: '2025-07-31 18:00:00',
                expected: 3,
                description: '7月29日到7月31日应该计算为3天'
            },
            {
                name: '跨天出差（2天）',
                startDate: '2025-07-29',
                endDate: '2025-07-30',
                expected: 2,
                description: '7月29日到7月30日应该计算为2天'
            },
            {
                name: '跨月出差',
                startDate: '2025-07-31 09:00:00',
                endDate: '2025-08-02 18:00:00',
                expected: 3,
                description: '7月31日到8月2日应该计算为3天'
            },
            {
                name: '长期出差',
                startDate: '2025-07-01',
                endDate: '2025-07-31',
                expected: 31,
                description: '整个7月应该计算为31天'
            },
            {
                name: '无效日期',
                startDate: '',
                endDate: '2025-07-31',
                expected: 0,
                description: '空的开始日期应该返回0'
            },
            {
                name: '结束日期早于开始日期',
                startDate: '2025-07-31',
                endDate: '2025-07-29',
                expected: 0,
                description: '结束日期早于开始日期应该返回0'
            }
        ];

        /**
         * 运行测试
         */
        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = calculateDays(testCase.startDate, testCase.endDate);
                const passed = result === testCase.expected;
                
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = `test-case ${passed ? 'pass' : 'fail'}`;
                testDiv.innerHTML = `
                    <h3>测试 ${index + 1}: ${testCase.name}</h3>
                    <p><strong>开始时间:</strong> ${testCase.startDate}</p>
                    <p><strong>结束时间:</strong> ${testCase.endDate}</p>
                    <p><strong>期望结果:</strong> ${testCase.expected}天</p>
                    <p><strong>实际结果:</strong> ${result}天</p>
                    <p><strong>说明:</strong> ${testCase.description}</p>
                    <div class="result">${passed ? '✅ 通过' : '❌ 失败'}</div>
                `;
                
                resultsContainer.appendChild(testDiv);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-case ${passCount === totalCount ? 'pass' : 'fail'}`;
            summaryDiv.innerHTML = `
                <h3>测试总结</h3>
                <p>通过: ${passCount}/${totalCount}</p>
                <div class="result">${passCount === totalCount ? '✅ 所有测试通过' : '❌ 部分测试失败'}</div>
            `;
            resultsContainer.appendChild(summaryDiv);
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
