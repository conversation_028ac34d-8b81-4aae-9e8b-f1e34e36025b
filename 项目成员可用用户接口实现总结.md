# 项目成员可用用户接口实现总结

## 📋 需求概述

为 `ProjectMembers.vue` 组件的 `loadAvailableUsers` 方法适配后端接口，实现权限控制的用户列表获取功能。

## 🎯 实现要求

1. **数据表**: `project_member` 表
2. **权限控制**: 只有项目负责人或租户管理员才可以添加成员
3. **全局方法**: 使用 `is_tenant_super_admin()` 进行权限判断
4. **角色字段**: `role` 字段暂时弃用，无需适配

## 🔧 后端实现

### 1. 服务层实现

**文件**: `app/project/service/ProjectMemberService.php`

```php
/**
 * 获取可添加的用户列表
 * 权限控制：只有项目负责人或租户管理员才可以添加成员
 *
 * @param int $projectId 项目ID
 * @return array
 */
public function getAvailableUsers(int $projectId): array
{
    // 获取当前用户ID
    $currentUserId = get_user_id();
    
    // 检查权限：是否为租户管理员
    if (is_tenant_super_admin()) {
        // 租户管理员可以添加任何用户（排除已是项目成员的用户）
        return $this->getAllActiveUsers($projectId);
    }
    
    // 检查是否为项目负责人
    $projectService = \app\project\service\ProjectProjectService::getInstance();
    if (!$projectService->isProjectOwner($projectId, $currentUserId)) {
        // 非项目负责人且非租户管理员，无权限添加成员
        return [];
    }
    
    // 项目负责人可以添加用户（排除已是项目成员的用户）
    return $this->getAllActiveUsers($projectId);
}

/**
 * 获取所有活跃用户（排除已是项目成员的用户）
 *
 * @param int $projectId 项目ID
 * @return array
 */
private function getAllActiveUsers(int $projectId = 0): array
{
    $adminModel = new \app\system\model\AdminModel();
    
    $query = $adminModel->where('status', 1)
                       ->where('deleted_at', null)
                       ->field('id as value, real_name as label, username, email');
    
    // 如果指定了项目ID，排除已是项目成员的用户
    if ($projectId > 0) {
        $existingMemberIds = $this->model->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->column('user_id');
        
        if (!empty($existingMemberIds)) {
            $query->whereNotIn('id', $existingMemberIds);
        }
    }
    
    return $query->select()->toArray();
}
```

### 2. 控制器实现

**文件**: `app/project/controller/ProjectMemberController.php`

```php
/**
 * 获取可添加的用户列表
 * 权限控制：只有项目负责人或租户管理员才可以添加成员
 *
 * @param int $projectId 项目ID
 * @return \think\Response
 */
public function availableUsers(int $projectId)
{
    try {
        $result = $this->service->getAvailableUsers($projectId);
        return $this->success('获取成功', $result);
    } catch (\Exception $e) {
        return $this->error($e->getMessage());
    }
}
```

### 3. 路由配置

**文件**: `route/project_member.php`

```php
// 获取可添加的用户列表（权限控制）
Route::get('availableUsers/:projectId', 'app\project\controller\ProjectMemberController@availableUsers');
```

## 🎨 前端实现

### 1. API接口定义

**文件**: `frontend/src/api/project/projectMember.ts`

```typescript
/**
 * 获取可添加的用户列表
 * 权限控制：只有项目负责人或租户管理员才可以添加成员
 * @param projectId 项目ID
 */
static availableUsers(projectId: number | string) {
  return request.get<BaseResult>({
    url: `/project/project_member/availableUsers/${projectId}`
  })
}
```

### 2. 组件实现

**文件**: `frontend/src/views/project/components/ProjectMembers.vue`

```typescript
// 导入API
import { ProjectMemberApi } from '@/api/project/projectMember'

// 类型定义
const availableUsers = ref<Array<{ value: number; label: string; username?: string; email?: string }>>([])

// 加载可用用户列表
const loadAvailableUsers = async () => {
  try {
    const response = await ProjectMemberApi.availableUsers(props.projectId)
    if (response.code === 0) {
      // 后端返回的数据格式：{ value: id, label: name, username, email }
      // 转换为前端需要的格式：{ value, label }
      availableUsers.value = response.data.map((user: any) => ({
        value: user.value,
        label: user.label
      }))
    } else {
      ElMessage.error(response.message || '加载用户列表失败')
      availableUsers.value = []
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
    availableUsers.value = []
  }
}
```

## 🔐 权限控制逻辑

1. **租户管理员**: 使用 `is_tenant_super_admin()` 判断，可以添加任何活跃用户
2. **项目负责人**: 使用 `ProjectProjectService::isProjectOwner()` 判断，可以添加任何活跃用户
3. **普通用户**: 无权限添加成员，返回空数组
4. **排除逻辑**: 自动排除已是项目成员的用户，避免重复添加

## 📊 数据格式

### 后端返回格式
```json
[
  {
    "value": 1,
    "label": "张三",
    "username": "zhangsan",
    "email": "<EMAIL>"
  },
  {
    "value": 2,
    "label": "李四",
    "username": "lisi",
    "email": "<EMAIL>"
  }
]
```

### 前端使用格式
```typescript
{
  value: number,    // 用户ID
  label: string     // 用户真实姓名
}
```

## ✅ 实现特点

1. **权限安全**: 严格的权限控制，防止未授权用户添加成员
2. **数据过滤**: 自动排除已是项目成员的用户
3. **错误处理**: 完善的异常处理和用户提示
4. **类型安全**: TypeScript类型定义，提供良好的开发体验
5. **性能优化**: 只查询必要字段，减少数据传输量

## 🧪 测试建议

1. **权限测试**: 验证不同角色用户的权限控制
2. **数据过滤**: 验证已是成员的用户是否被正确排除
3. **异常处理**: 验证网络错误和权限错误的处理
4. **界面交互**: 验证用户选择和表单提交流程

## 📝 注意事项

1. `role` 字段暂时弃用，添加的成员统一为 `member` 角色
2. 权限判断依赖 `is_tenant_super_admin()` 和项目负责人检查
3. 数据查询已考虑软删除和租户隔离
4. 前端组件需要传递正确的 `projectId` 参数
