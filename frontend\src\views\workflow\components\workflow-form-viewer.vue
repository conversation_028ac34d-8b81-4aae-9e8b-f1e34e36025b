<template>
  <div class="workflow-form-viewer">
    <!-- 通用表单数据展示 -->
    <el-descriptions border :column="2" size="small">
      <template v-for="(value, key) in formData" :key="key">
        <el-descriptions-item :label="formatLabel(key)" v-if="shouldDisplayField(key)">
          {{ formatValue(key, value) }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem } from 'element-plus'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: ''
    }
  })

  /**
   * 格式化字段标签
   */
  const formatLabel = (key: string): string => {
    // 字段标签映射
    const labelMap: Record<string, string> = {
      // 通用字段
      id: 'ID',
      created_at: '创建时间',
      updated_at: '更新时间',
      creator_name: '创建人',
      remark: '备注',

      // HR模块
      start_time: '开始时间',
      end_time: '结束时间',
      duration: '时长',
      purpose: '目的',
      reason: '事由',
      dept_name: '部门',

      // IMS模块
      outbound_no: '出库单号',
      inbound_no: '入库单号',
      shipment_no: '出货单号',
      purchase_no: '采购单号',
      outbound_type: '出库类型',
      inbound_type: '入库类型',
      outbound_date: '出库日期',
      inbound_date: '入库日期',
      shipment_date: '出货日期',
      purchase_date: '采购日期',
      warehouse_name: '仓库',
      supplier_name: '供应商',
      customer_name: '客户',
      total_quantity: '总数量',
      total_amount: '总金额',

      // Finance模块
      payment_no: '付款单号',
      expense_no: '报销单号',
      payee_name: '收款人',
      payee_account: '收款账号',
      payee_bank: '收款银行',
      payment_amount: '付款金额',
      payment_date: '付款日期',
      payment_method: '付款方式',
      expense_type: '报销类型',
      expense_date: '报销日期',

      // Office模块
      sample_name: '样品名称',
      sample_description: '样品描述',
      sender_phone: '寄件人电话'
    }

    return labelMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
  }

  /**
   * 格式化字段值
   */
  const formatValue = (key: string, value: any): string => {
    if (value === null || value === undefined || value === '') {
      return '-'
    }

    // 特殊字段格式化
    if (key.includes('_time') || key.includes('_date')) {
      return value
    }

    if (key.includes('amount') || key.includes('price')) {
      return `¥${value}`
    }

    if (key === 'duration') {
      return `${value} 小时`
    }

    if (Array.isArray(value)) {
      return value.join(', ')
    }

    if (typeof value === 'object') {
      return JSON.stringify(value)
    }

    return String(value)
  }

  /**
   * 判断是否应该显示字段
   */
  const shouldDisplayField = (key: string): boolean => {
    // 隐藏的字段
    const hiddenFields = [
      'id',
      'tenant_id',
      'workflow_instance_id',
      'approval_status',
      'deleted_at',
      'items' // 明细数据单独处理
    ]

    return !hiddenFields.includes(key) && !key.startsWith('_')
  }
</script>

<style scoped lang="scss">
  .workflow-form-viewer {
    padding: 16px;
  }
</style>
