<?php
declare(strict_types=1);

namespace app\hr\service;

use app\common\core\base\BaseService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use app\hr\model\HrOuting;
use app\common\exception\BusinessException;
use think\facade\Log;

/**
 * 外出申请服务类
 */
class HrOutingService extends BaseService implements FormServiceInterface
{
	protected string $modelClass = HrOuting::class;
	
	public function __construct()
	{
		$this->model = new HrOuting();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = $this->model->with([
			'submitter',
			'creator'
		])
		                     ->find($id);
		
		if (!$model) {
			throw new BusinessException('外出申请记录不存在');
		}
		
		return $model->toArray();
	}
	
	/**
	 * 创建表单数据
	 */
	public function saveForm(array $data): array
	{
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			//			$formData = $this->model->filterData($formData);
			// 验证数据
			$validatedData = $this->validateFormData($formData, 'create');
			
			// 自动计算时长
			$validatedData['duration'] = $this->calculateDuration($validatedData['start_time'], $validatedData['end_time']);
			
			// 创建主记录
			$id = $this->model->saveByCreate($validatedData);
			
			// 返回完整数据
			$formData = $this->getFormData($id);
			
			return [
				$id,
				$formData
			];
			
		}
		catch (\Exception $e) {
			Log::error('外出申请创建失败: ' . $e->getMessage(), [
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('外出申请创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		try {
			$model = (new HrOuting())->find($id);
			if (!$model) {
				throw new BusinessException('外出申请记录不存在');
			}
			
			// 验证数据
			$validatedData = $this->validateFormData($data, 'update');
			
			// 自动计算时长
			$validatedData['duration'] = $this->calculateDuration($validatedData['start_time'], $validatedData['end_time']);
			
			// 更新记录
			return $model->saveByUpdate($validatedData);
			
		}
		catch (\Exception $e) {
			Log::error('外出申请更新失败: ' . $e->getMessage(), [
				'id'    => $id,
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('外出申请更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			$updateData = ['approval_status' => $status];
			
			// 处理额外数据
			if (!empty($extra['workflow_instance_id'])) {
				$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
			}
			
			if (!empty($extra['submit_time'])) {
				$updateData['submit_time'] = $extra['submit_time'];
			}
			
			if (!empty($extra['approval_time'])) {
				$updateData['approval_time'] = $extra['approval_time'];
			}
			
			return $model->saveByUpdate($updateData);
			
		}
		catch (\Exception $e) {
			Log::error('外出申请状态更新失败: ' . $e->getMessage(), [
				'id'     => $id,
				'status' => $status,
				'extra'  => $extra,
				'trace'  => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 删除表单
	 */
	public function deleteForm(int $id): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			// 软删除主记录
			$model->delete();
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('外出申请删除失败: ' . $e->getMessage(), [
				'id'    => $id,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 获取流程实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		
		if (is_array($formData)) {
			$parts[] = $formData['submitter_name'] ?? null;
			$parts   = ['外出申请'];
			$parts[] = $formData['duration']
				? $formData['duration'] . '小时'
				: null;
			return implode('-', array_filter($parts));
		}
		
		return '外出申请';
	}
	
	/**
	 * 验证表单数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		$rules = [
			'start_time' => 'require|dateFormat:Y-m-d H:i:s',
			'end_time'   => 'require|dateFormat:Y-m-d H:i:s',
			'purpose'    => 'require|max:500',
		];
		
		$messages = [
			'start_time.require'    => '请选择开始时间',
			'start_time.dateFormat' => '开始时间格式错误',
			'end_time.require'      => '请选择结束时间',
			'end_time.dateFormat'   => '结束时间格式错误',
			'purpose.require'       => '请填写外出事由',
			'purpose.max'           => '外出事由不能超过500个字符',
		];
		
		
		$validate = validate($rules, $messages);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		// 业务逻辑验证
		if (strtotime($data['end_time']) <= strtotime($data['start_time'])) {
			throw new BusinessException('结束时间必须大于开始时间');
		}
		
		return $data;
	}
	
	/**
	 * 计算外出时长（小时）
	 */
	private function calculateDuration(string $startTime, string $endTime): float
	{
		$start = strtotime($startTime);
		$end   = strtotime($endTime);
		
		if ($end <= $start) {
			return 0;
		}
		
		$diffSeconds = $end - $start;
		$diffHours   = $diffSeconds / 3600; // 转换为小时
		
		return round($diffHours, 1); // 保留一位小数
	}
	
	/**
	 * 工作流状态变更后的处理
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		// TODO: Implement afterWorkflowStatusChange() method.
		return true;
	}
}
