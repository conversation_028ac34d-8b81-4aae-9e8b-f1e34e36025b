<?php
declare(strict_types=1);

namespace app\project\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\system\model\AdminModel;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 任务表模型
 */
class ProjectTask extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'project_task';
	
	// 设置主键
	protected $pk = 'id';
	
	// 自动写入时间戳
	protected $autoWriteTimestamp = true;
	
	// 时间字段转换
	protected $dateFormat = 'Y-m-d H:i:s';
	
	// 软删除
	protected string $deleteTime = 'deleted_at';
	
	public function __construct(array $data = [])
	{
		parent::__construct($data);
	}
	
	/**
	 * 所属项目关联
	 */
	public function project(): BelongsTo
	{
		return $this->belongsTo(ProjectProject::class, 'project_id');
	}
	
	/**
	 * 任务负责人关联
	 */
	public function assignee(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'assignee_id')
		            ->bind([
			            'assignee_name' => 'real_name'
		            ]);
	}
	
	/**
	 * 任务评论关联（更新为新的记录表）
	 */
	public function comments(): HasMany
	{
		return $this->hasMany(ProjectTaskRecord::class, 'task_id')
		           ->where('record_type', ProjectTaskRecord::TYPE_COMMENT);
	}
	
	/**
	 * 更新任务状态
	 */
	public function updateStatus(int $status, int $userId): bool
	{
		$oldStatus        = $this->status;
		$this->status     = $status;
		$this->updated_id = $userId;
		
		if ($status === 3) { // 已完成
			$this->completed_at = date('Y-m-d H:i:s');
		}
		
		$result = $this->save();
		
		// 更新项目进度
		if ($result) {
			$project = $this->project;
			if ($project) {
				$project->progress = $project->calculateProgress();
				$project->save();
			}
		}
		
		return $result;
	}
	
	/**
	 * 分配任务
	 */
	public function assignTo(int $assigneeId, int $operatorId): bool
	{
		$this->assignee_id = $assigneeId;
		$this->updated_id  = $operatorId;
		return $this->save();
	}
	
	/**
	 * 检查任务是否逾期
	 */
	public function isOverdue(): bool
	{
		if (!$this->due_date) {
			return false;
		}
		
		return strtotime($this->due_date) < time() && $this->status != 3;
	}
	
	/**
	 * 获取任务状态文本
	 */
	public function getStatusText(): string
	{
		$statusMap = [
			1 => '待办',
			2 => '进行中',
			3 => '已完成',
			4 => '已关闭'
		];
		
		return $statusMap[$this->status] ?? '未知';
	}
	
	/**
	 * 获取优先级文本
	 */
	public function getPriorityText(): string
	{
		$priorityMap = [
			1 => '低',
			2 => '中',
			3 => '高'
		];
		
		return $priorityMap[$this->priority] ?? '中';
	}
	
	/**
	 * 任务记录关联（评论+跟进）
	 */
	public function records()
	{
		return $this->hasMany(ProjectTaskRecord::class, 'task_id');
	}



	/**
	 * 任务跟进关联
	 */
	public function follows()
	{
		return $this->hasMany(ProjectTaskRecord::class, 'task_id')
		           ->where('record_type', ProjectTaskRecord::TYPE_FOLLOW);
	}

	/**
	 * 获取最新跟进记录
	 */
	public function getLatestFollowAttr()
	{
		return $this->follows()
		           ->order('follow_date desc, created_at desc')
		           ->find();
	}

	/**
	 * 获取评论数量
	 */
	public function getCommentsCountAttr()
	{
		return $this->comments()->count();
	}

	/**
	 * 获取跟进数量
	 */
	public function getFollowsCountAttr()
	{
		return $this->follows()->count();
	}
}