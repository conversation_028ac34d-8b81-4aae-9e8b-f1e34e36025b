<?php
/**
 * 查询ims_supplier数据并生成crm_product测试数据
 */

// 数据库连接配置
$host = '*************';
$port = '3306';
$database = 'www_bs_com';
$username = 'www_bs_com';
$password = 'PdadjMXmNy8Pn9tj';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 查询ims_supplier数据表 ===\n\n";
    
    // 查询供应商数据
    $stmt = $pdo->query("
        SELECT 
            id,
            name,
            code,
            contact_name,
            phone,
            province,
            city,
            district,
            detailed_address,
            remark,
            status,
            created_at
        FROM ims_supplier 
        WHERE deleted_at IS NULL
        ORDER BY id
    ");
    
    $suppliers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "找到 " . count($suppliers) . " 个供应商:\n";
    echo str_repeat("-", 80) . "\n";
    printf("%-5s %-20s %-15s %-10s %-15s %-6s\n", "ID", "供应商名称", "编码", "联系人", "联系电话", "状态");
    echo str_repeat("-", 80) . "\n";
    
    foreach ($suppliers as $supplier) {
        printf("%-5s %-20s %-15s %-10s %-15s %-6s\n", 
            $supplier['id'],
            mb_substr($supplier['name'], 0, 18),
            $supplier['code'],
            $supplier['contact_name'],
            $supplier['phone'],
            $supplier['status'] == 1 ? '启用' : '停用'
        );
    }
    
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "=== 查询产品分类和单位数据 ===\n\n";
    
    // 查询产品分类
    $stmt = $pdo->query("
        SELECT id, name
        FROM crm_product_category
        WHERE deleted_at IS NULL
        AND status = 1
        ORDER BY id
        LIMIT 10
    ");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "产品分类:\n";
    foreach ($categories as $category) {
        echo "ID: {$category['id']}, 名称: {$category['name']}\n";
    }

    // 查询产品单位
    $stmt = $pdo->query("
        SELECT id, unit_name as name
        FROM crm_product_unit
        WHERE deleted_at IS NULL
        AND status = 1
        ORDER BY id
        LIMIT 10
    ");
    $units = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "\n产品单位:\n";
    foreach ($units as $unit) {
        echo "ID: {$unit['id']}, 名称: {$unit['name']}\n";
    }
    
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "=== 生成crm_product测试数据 ===\n\n";
    
    // 如果没有分类或单位，创建默认的
    if (empty($categories)) {
        echo "创建默认产品分类...\n";
        $pdo->exec("
            INSERT INTO crm_product_category (tenant_id, name, code, sort, status, created_at, updated_at)
            VALUES
            (0, '电子产品', 'ELECTRONICS', 1, 1, NOW(), NOW()),
            (0, '办公用品', 'OFFICE', 2, 1, NOW(), NOW()),
            (0, '工业设备', 'INDUSTRIAL', 3, 1, NOW(), NOW())
        ");

        $stmt = $pdo->query("SELECT id, name FROM crm_product_category WHERE deleted_at IS NULL ORDER BY id LIMIT 10");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    if (empty($units)) {
        echo "创建默认产品单位...\n";
        $pdo->exec("
            INSERT INTO crm_product_unit (tenant_id, unit_name, unit_code, status, created_at, updated_at)
            VALUES
            (0, '个', 'PCS', 1, NOW(), NOW()),
            (0, '台', 'SET', 1, NOW(), NOW()),
            (0, '套', 'SUIT', 1, NOW(), NOW()),
            (0, '箱', 'BOX', 1, NOW(), NOW())
        ");

        $stmt = $pdo->query("SELECT id, unit_name as name FROM crm_product_unit WHERE deleted_at IS NULL ORDER BY id LIMIT 10");
        $units = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // 产品名称模板
    $productTemplates = [
        '笔记本电脑', '台式电脑', '显示器', '键盘', '鼠标', '打印机', '扫描仪', '投影仪',
        '办公桌', '办公椅', '文件柜', '白板', '复印纸', '签字笔', '订书机', '计算器',
        '工业机器人', '数控机床', '焊接设备', '检测仪器', '传感器', '电机', '变频器', 'PLC控制器'
    ];
    
    $specs = ['标准版', '专业版', '企业版', '豪华版', '基础版', '高级版', '旗舰版'];
    
    // 清空现有测试数据
    echo "清空现有测试产品数据...\n";
    $pdo->exec("DELETE FROM crm_product WHERE name LIKE '%测试%' OR code LIKE 'TEST%'");
    
    echo "开始生成测试产品数据...\n";
    
    $insertCount = 0;
    $activeSuppliers = array_filter($suppliers, function($s) { return $s['status'] == 1; });
    
    if (empty($activeSuppliers)) {
        echo "警告: 没有找到启用状态的供应商，将使用所有供应商\n";
        $activeSuppliers = $suppliers;
    }
    
    foreach ($activeSuppliers as $supplier) {
        // 为每个供应商生成3-5个产品
        $productCount = rand(3, 5);
        
        for ($i = 0; $i < $productCount; $i++) {
            $productName = $productTemplates[array_rand($productTemplates)];
            $spec = $specs[array_rand($specs)];
            $category = $categories[array_rand($categories)];
            $unit = $units[array_rand($units)];
            
            $code = 'TEST' . str_pad($supplier['id'], 3, '0', STR_PAD_LEFT) . str_pad($i + 1, 2, '0', STR_PAD_LEFT);
            $name = "测试{$productName}-{$spec}";
            $price = rand(100, 10000) + (rand(0, 99) / 100); // 100.00 到 10000.99
            $cost = $price * (0.6 + rand(0, 30) / 100); // 成本为售价的60%-90%
            
            $description = "由{$supplier['name']}供应的{$productName}，规格：{$spec}，适用于各种商业场景。";
            
            $sql = "
                INSERT INTO crm_product (
                    tenant_id, category_id, unit_id, supplier_id, spec, name, code,
                    price, cost, description, status, creator_id,
                    created_at, updated_at
                ) VALUES (
                    0, :category_id, :unit_id, :supplier_id, :spec, :name, :code,
                    :price, :cost, :description, 1, 1,
                    NOW(), NOW()
                )
            ";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                'category_id' => $category['id'],
                'unit_id' => $unit['id'],
                'supplier_id' => $supplier['id'],
                'spec' => $spec,
                'name' => $name,
                'code' => $code,
                'price' => $price,
                'cost' => $cost,
                'description' => $description
            ]);
            
            $insertCount++;
            
            if ($insertCount % 10 == 0) {
                echo "已生成 {$insertCount} 个产品...\n";
            }
        }
    }
    
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "=== 数据生成完成 ===\n\n";
    
    // 验证生成的数据
    $stmt = $pdo->query("
        SELECT 
            p.id,
            p.name,
            p.code,
            p.spec,
            p.price,
            p.cost,
            c.name as category_name,
            u.unit_name as unit_name,
            s.name as supplier_name
        FROM crm_product p
        LEFT JOIN crm_product_category c ON p.category_id = c.id
        LEFT JOIN crm_product_unit u ON p.unit_id = u.id
        LEFT JOIN ims_supplier s ON p.supplier_id = s.id
        WHERE p.name LIKE '%测试%' OR p.code LIKE 'TEST%'
        ORDER BY p.supplier_id, p.id
        LIMIT 20
    ");
    
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "生成的测试产品数据 (前20条):\n";
    echo str_repeat("-", 120) . "\n";
    printf("%-5s %-25s %-12s %-10s %-8s %-8s %-15s %-8s %-15s\n", 
        "ID", "产品名称", "编码", "规格", "售价", "成本", "分类", "单位", "供应商");
    echo str_repeat("-", 120) . "\n";
    
    foreach ($products as $product) {
        printf("%-5s %-25s %-12s %-10s %-8.2f %-8.2f %-15s %-8s %-15s\n",
            $product['id'],
            mb_substr($product['name'], 0, 23),
            $product['code'],
            $product['spec'],
            $product['price'],
            $product['cost'],
            mb_substr($product['category_name'], 0, 13),
            $product['unit_name'],
            mb_substr($product['supplier_name'], 0, 13)
        );
    }
    
    // 统计信息
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_products,
            COUNT(DISTINCT supplier_id) as supplier_count,
            AVG(price) as avg_price,
            MIN(price) as min_price,
            MAX(price) as max_price
        FROM crm_product 
        WHERE name LIKE '%测试%' OR code LIKE 'TEST%'
    ");
    
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "=== 统计信息 ===\n";
    echo "总产品数量: {$stats['total_products']}\n";
    echo "涉及供应商: {$stats['supplier_count']}\n";
    echo "平均售价: " . number_format($stats['avg_price'], 2) . " 元\n";
    echo "最低售价: " . number_format($stats['min_price'], 2) . " 元\n";
    echo "最高售价: " . number_format($stats['max_price'], 2) . " 元\n";
    
    echo "\n测试数据生成完成！\n";
    echo "可以在CRM产品管理页面查看生成的测试数据。\n";
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "执行失败: " . $e->getMessage() . "\n";
    exit(1);
}
