<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\system\model\OperationLogModel;

/**
 * 日志服务类
 */
class OperationLogService extends BaseService
{
	
	protected function __construct()
	{
		$this->model = new OperationLogModel();
		parent::__construct();
	}
	
	public function getList(array $params): array
	{
		
		$where = [];
		
		// todo 条件搜索待完善，数据权限
		if (!empty($params['username'])) {
			$where[] = [
				[
					'username|real_name',
					'like',
					'%' . $params['username'] . '%'
				],
			];
		}
		
		// 排序
		$order = 'created_at desc';
		
		// 分页
		$total = $this->getCrudService()
		              ->getCount($where);
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;
		
		$list = $this->getCrudService()
		             ->getPageList($where, $order, (int)$page, (int)$limit, [
			             'admin',
		             ]);
		
		return [
			'list'  => $list,
			'total' => $total,
			'page'  => (int)$page,
			'limit' => (int)$limit,
		];
	}
	
	public function create(array $data, $tenantId): bool
	{
		$data['tenant_id'] = $tenantId;
		return $this->model->save($data);
	}
	
	public function getDetail($id)
	{
		return $this->getCrudService()
		            ->getOne([
			            [
				            'id',
				            '=',
				            $id
			            ]
		            ]);
	}
	
	public function delete($id): bool
	{
		$info = $this->getCrudService()
		             ->getOne([
			             [
				             'id',
				             '=',
				             $id
			             ]
		             ]);
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		return $info->delete();
	}
	
}