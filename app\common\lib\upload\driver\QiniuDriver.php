<?php

namespace app\common\lib\upload\driver;


use app\common\lib\upload\UploadStorageInterface;
use <PERSON>iu\Auth;
use Qiniu\Storage\UploadManager;

class QiniuDriver implements UploadStorageInterface
{
	public function getUploadToken(array $config, array $params = []): string|array
	{
		$auth     = new Auth($config['access_key'], $config['secret_key']);
		$tenantId = $params['tenant_id'];
		$policy   = [
			//			'callbackUrl'      => $params['callback_url'] ?? request()->domain() . '/api/attachment/callback/qiniu',
			'callbackBody'     => json_encode([
				'key'        => '$(key)',
				'hash'       => '$(etag)',
				'size'       => '$(fsize)',
				'name'       => '$(fname)',
				'mime_type'  => '$(mimeType)',
				'extension'  => '$(ext)',
				'tenant_id'  => $tenantId,
				'cate_id'    => $params['cate_id'] ?? 0,
				'fsizeLimit' => '',
				// todo 限制的文件大小
				'mimeLimit'  => '', // todo 需要根据允许的后缀获取
			]),
			'callbackBodyType' => 'application/json',
			//			'insertOnly'       => 1,
			'saveKey'          => 'tenant/' . $tenantId . '/${year}${mon}${day}/$(etag)$(ext)'
		];
		
		$token = $auth->uploadToken($config['bucket'], null, intval($config['deadline'] ?? 3600), $policy);
		
		return [
			'token'  => $token,
			'domain' => $config['domain'],
		];
	}
	
	public function upload(array $file, array $config): array
	{
		$auth      = new Auth($config['access_key'], $config['secret_key']);
		$uploadMgr = new UploadManager();
		
		$token = $auth->uploadToken($config['bucket']);
		
		$fileName = md5(microtime(true) . mt_rand(1000, 9999)) . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
		$savePath = $config['base_path'] . date('Y/m/d') . '/' . $fileName;
		
		list($ret, $err) = $uploadMgr->putFile($token, $savePath, $file['tmp_name']);
		
		if ($err !== null) {
			throw new \Exception($err->message());
		}
		
		return [
			'name'       => $file['name'],
			'real_name'  => $file['name'],
			'path'       => $savePath,
			'url'        => $config['domain'] . '/' . $savePath,
			'extension'  => pathinfo($file['name'], PATHINFO_EXTENSION),
			'size'       => $file['size'],
			'mime_type'  => $file['type'],
			'storage'    => 'qnoss',
			'storage_id' => $ret['key'],
		];
	}
	
	public function delete(string $filePath, array $config): bool
	{
		$auth          = new Auth($config['access_key'], $config['secret_key']);
		$bucketManager = new \Qiniu\Storage\BucketManager($auth);
		
		$err = $bucketManager->delete($config['bucket'], $filePath);
		return $err === null;
	}
	
	public function callback(array $params, array $config): array
	{
		// 验证回调签名
		$auth    = new Auth($config['access_key'], $config['secret_key']);
		$isValid = $auth->verifyCallback($_SERVER['HTTP_AUTHORIZATION'], request()->url(true), file_get_contents('php://input'), 'application/json');
		
		if (!$isValid) {
			throw new \Exception('回调签名验证失败');
		}
		
		$callbackData = $params;
		
		return [
			'name'       => $callbackData['name'],
			'real_name'  => $callbackData['name'],
			'path'       => $callbackData['key'],
			'url'        => $config['domain'] . '/' . $callbackData['key'],
			'extension'  => $callbackData['extension'],
			'size'       => $callbackData['size'],
			'mime_type'  => $callbackData['mime_type'],
			'storage'    => 'qnoss',
			'storage_id' => $callbackData['hash'],
			'tenant_id'  => $callbackData['tenant_id'],
			'cate_id'    => $callbackData['cate_id'],
		];
	}
}