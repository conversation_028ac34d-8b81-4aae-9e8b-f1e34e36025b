# 简化版项目任务管理详细实施计划

## 📋 实施概述

### 技术要求
- **后端架构**: 控制器使用CrudService单例，复杂业务逻辑使用模型方法
- **数据安全**: 新增编辑避免静态方法，确保租户隔离
- **前端复用**: 充分利用现有components和utils组件
- **开发周期**: 预计3-4周完成

### 实施阶段
1. **第1周**: 后端API开发
2. **第2周**: 前端基础页面开发  
3. **第3周**: 前端高级功能开发
4. **第4周**: 测试优化和部署

## 🔧 第一阶段：后端开发实施（第1周）

### 1.1 CRUD代码生成（第1天）

#### 执行生成器命令
```bash
# 按顺序执行，确保依赖关系正确
php think generator:crud project_project --module=project --frontend --overwrite
php think generator:crud project_member --module=project --frontend --overwrite  
php think generator:crud project_task --module=project --frontend --overwrite
php think generator:crud project_task_comment --module=project --frontend --overwrite
```

#### 验证生成结果
- [ ] 控制器文件生成正确
- [ ] 模型文件生成正确
- [ ] 服务层文件生成正确
- [ ] 前端页面文件生成正确

### 1.2 模型层优化（第2天）

#### 1. 项目模型 (ProjectProject.php)
```php
<?php
namespace app\project\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

class ProjectProject extends BaseModel
{
    use CreatorTrait;
    
    protected $name = 'project_project';
    
    /**
     * 项目成员关联
     */
    public function members()
    {
        return $this->hasMany(ProjectMember::class, 'project_id');
    }
    
    /**
     * 项目任务关联
     */
    public function tasks()
    {
        return $this->hasMany(ProjectTask::class, 'project_id');
    }
    
    /**
     * 项目负责人关联
     */
    public function owner()
    {
        return $this->belongsTo(\app\system\model\AdminModel::class, 'owner_id');
    }
    
    /**
     * 计算项目进度
     */
    public function calculateProgress(): float
    {
        $totalTasks = $this->tasks()->count();
        if ($totalTasks === 0) {
            return 0.00;
        }
        
        $completedTasks = $this->tasks()->where('status', 3)->count();
        return round(($completedTasks / $totalTasks) * 100, 2);
    }
    
    /**
     * 获取项目统计信息
     */
    public function getProjectStats(): array
    {
        return [
            'total_tasks' => $this->tasks()->count(),
            'completed_tasks' => $this->tasks()->where('status', 3)->count(),
            'in_progress_tasks' => $this->tasks()->where('status', 2)->count(),
            'todo_tasks' => $this->tasks()->where('status', 1)->count(),
            'member_count' => $this->members()->count(),
            'progress' => $this->calculateProgress()
        ];
    }
}
```

#### 2. 任务模型 (ProjectTask.php)
```php
<?php
namespace app\project\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

class ProjectTask extends BaseModel
{
    use CreatorTrait;
    
    protected $name = 'project_task';
    
    /**
     * 所属项目关联
     */
    public function project()
    {
        return $this->belongsTo(ProjectProject::class, 'project_id');
    }
    
    /**
     * 任务负责人关联
     */
    public function assignee()
    {
        return $this->belongsTo(\app\system\model\AdminModel::class, 'assignee_id');
    }
    
    /**
     * 任务评论关联
     */
    public function comments()
    {
        return $this->hasMany(ProjectTaskComment::class, 'task_id');
    }
    
    /**
     * 更新任务状态
     */
    public function updateStatus(int $status, int $userId): bool
    {
        $oldStatus = $this->status;
        $this->status = $status;
        $this->updated_id = $userId;
        
        if ($status === 3) { // 已完成
            $this->completed_at = date('Y-m-d H:i:s');
        }
        
        $result = $this->save();
        
        // 更新项目进度
        if ($result) {
            $project = $this->project;
            if ($project) {
                $project->progress = $project->calculateProgress();
                $project->save();
            }
        }
        
        return $result;
    }
    
    /**
     * 分配任务
     */
    public function assignTo(int $assigneeId, int $operatorId): bool
    {
        $this->assignee_id = $assigneeId;
        $this->updated_id = $operatorId;
        return $this->save();
    }
}
```

### 1.3 控制器层优化（第3天）

#### 1. 项目控制器 (ProjectProjectController.php)
```php
<?php
namespace app\project\controller;

use app\common\core\base\BaseController;
use app\project\service\ProjectProjectService;
use think\response\Json;

class ProjectProjectController extends BaseController
{
    protected ProjectProjectService $service;
    
    public function initialize(): void
    {
        parent::initialize();
        // 使用单例模式
        $this->service = ProjectProjectService::getInstance();
    }
    
    /**
     * 获取我的项目列表
     */
    public function myProjects(): Json
    {
        try {
            $userId = $this->getCurrentUserId();
            $result = $this->service->getMyProjects($userId, $this->request->param());
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取项目详情（包含统计信息）
     */
    public function detail(): Json
    {
        try {
            $id = $this->request->param('id');
            $result = $this->service->getProjectDetail($id);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取项目看板数据
     */
    public function kanban(): Json
    {
        try {
            $projectId = $this->request->param('project_id');
            $result = $this->service->getKanbanData($projectId);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

#### 2. 任务控制器 (ProjectTaskController.php)
```php
<?php
namespace app\project\controller;

use app\common\core\base\BaseController;
use app\project\service\ProjectTaskService;
use think\response\Json;

class ProjectTaskController extends BaseController
{
    protected ProjectTaskService $service;
    
    public function initialize(): void
    {
        parent::initialize();
        $this->service = ProjectTaskService::getInstance();
    }
    
    /**
     * 获取我的任务列表
     */
    public function myTasks(): Json
    {
        try {
            $userId = $this->getCurrentUserId();
            $result = $this->service->getMyTasks($userId, $this->request->param());
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 更新任务状态
     */
    public function updateStatus(): Json
    {
        try {
            $id = $this->request->param('id');
            $status = $this->request->param('status');
            $userId = $this->getCurrentUserId();
            
            $result = $this->service->updateTaskStatus($id, $status, $userId);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 分配任务
     */
    public function assign(): Json
    {
        try {
            $id = $this->request->param('id');
            $assigneeId = $this->request->param('assignee_id');
            $userId = $this->getCurrentUserId();
            
            $result = $this->service->assignTask($id, $assigneeId, $userId);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

### 1.4 服务层优化（第4天）

#### 1. 项目服务 (ProjectProjectService.php)
```php
<?php
namespace app\project\service;

use app\common\core\crud\CrudService;
use app\project\model\ProjectProject;
use app\project\model\ProjectMember;

class ProjectProjectService extends CrudService
{
    protected $model = ProjectProject::class;
    
    /**
     * 获取我的项目列表
     */
    public function getMyProjects(int $userId, array $params = []): array
    {
        // 获取我参与的项目ID列表
        $memberModel = new ProjectMember();
        $projectIds = $memberModel->where('user_id', $userId)
                                 ->column('project_id');
        
        $query = $this->getModel()->whereIn('id', $projectIds);
        
        // 搜索条件
        if (!empty($params['name'])) {
            $query->whereLike('name', '%' . $params['name'] . '%');
        }
        
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        return $this->getPageList($query, $params);
    }
    
    /**
     * 获取项目详情
     */
    public function getProjectDetail(int $id): array
    {
        $project = $this->getModel()->with(['owner', 'members.user'])->find($id);
        if (!$project) {
            throw new \Exception('项目不存在');
        }
        
        $stats = $project->getProjectStats();
        
        return [
            'project' => $project->toArray(),
            'stats' => $stats
        ];
    }
    
    /**
     * 获取看板数据
     */
    public function getKanbanData(int $projectId): array
    {
        $taskModel = new \app\project\model\ProjectTask();
        
        $statuses = [
            ['id' => 1, 'name' => '待办', 'color' => '#8C8C8C'],
            ['id' => 2, 'name' => '进行中', 'color' => '#1664FF'],
            ['id' => 3, 'name' => '已完成', 'color' => '#00BC70'],
            ['id' => 4, 'name' => '已关闭', 'color' => '#F54A45']
        ];
        
        $kanbanData = [];
        foreach ($statuses as $status) {
            $tasks = $taskModel->where('project_id', $projectId)
                              ->where('status', $status['id'])
                              ->with(['assignee'])
                              ->order('sort', 'asc')
                              ->select()
                              ->toArray();
            
            $kanbanData[] = [
                'status' => $status,
                'tasks' => $tasks
            ];
        }
        
        return $kanbanData;
    }
}
```

### 1.5 API路由配置（第5天）

#### 添加路由 (route/project.php)
```php
<?php
use think\facade\Route;

// 项目管理路由组
Route::group('project', function () {
    
    // 项目相关路由
    Route::group('project', function () {
        Route::get('my', 'ProjectProjectController/myProjects');
        Route::get('detail/:id', 'ProjectProjectController/detail');
        Route::get('kanban', 'ProjectProjectController/kanban');
    });
    
    // 任务相关路由
    Route::group('task', function () {
        Route::get('my', 'ProjectTaskController/myTasks');
        Route::put('status/:id', 'ProjectTaskController/updateStatus');
        Route::put('assign/:id', 'ProjectTaskController/assign');
    });
    
})->middleware(['TokenAuth', 'PermissionCheck']);
```

## 🎨 第二阶段：前端开发实施（第2-3周）

### 2.1 API接口封装（第2周第1天）

#### 创建API文件 (frontend/src/api/project/projectApi.ts)
```typescript
import { http } from '@/utils/http'

export interface Project {
  id: number
  name: string
  description: string
  status: number
  priority: number
  start_date: string
  end_date: string
  progress: number
  owner_id: number
  color: string
  is_archived: number
}

export interface Task {
  id: number
  project_id: number
  title: string
  description: string
  status: number
  priority: number
  assignee_id: number
  start_date: string
  due_date: string
  estimated_hours: number
  actual_hours: number
}

export const ProjectApi = {
  // 项目相关
  getList: (params: any) => http.get('/project/project/index', { params }),
  getMyProjects: (params: any) => http.get('/project/project/my', { params }),
  getDetail: (id: number) => http.get(`/project/project/detail/${id}`),
  getKanban: (projectId: number) => http.get('/project/project/kanban', { params: { project_id: projectId } }),
  create: (data: Partial<Project>) => http.post('/project/project/save', data),
  update: (id: number, data: Partial<Project>) => http.put(`/project/project/update/${id}`, data),
  delete: (id: number) => http.delete(`/project/project/delete/${id}`),
  
  // 任务相关
  getTaskList: (params: any) => http.get('/project/task/index', { params }),
  getMyTasks: (params: any) => http.get('/project/task/my', { params }),
  createTask: (data: Partial<Task>) => http.post('/project/task/save', data),
  updateTask: (id: number, data: Partial<Task>) => http.put(`/project/task/update/${id}`, data),
  updateTaskStatus: (id: number, status: number) => http.put(`/project/task/status/${id}`, { status }),
  assignTask: (id: number, assigneeId: number) => http.put(`/project/task/assign/${id}`, { assignee_id: assigneeId }),
  deleteTask: (id: number) => http.delete(`/project/task/delete/${id}`),
}
```

### 2.2 状态管理（第2周第2天）

#### 创建Pinia Store (frontend/src/stores/project.ts)
```typescript
import { defineStore } from 'pinia'
import { ProjectApi, type Project, type Task } from '@/api/project/projectApi'

export const useProjectStore = defineStore('project', {
  state: () => ({
    currentProject: null as Project | null,
    projectList: [] as Project[],
    taskList: [] as Task[],
    kanbanData: [] as any[],
    loading: false
  }),
  
  actions: {
    async fetchMyProjects(params = {}) {
      this.loading = true
      try {
        const result = await ProjectApi.getMyProjects(params)
        this.projectList = result.data.list
        return result
      } finally {
        this.loading = false
      }
    },
    
    async fetchProjectDetail(id: number) {
      const result = await ProjectApi.getDetail(id)
      this.currentProject = result.data.project
      return result.data
    },
    
    async fetchKanbanData(projectId: number) {
      const result = await ProjectApi.getKanban(projectId)
      this.kanbanData = result.data
      return result.data
    },
    
    async updateTaskStatus(taskId: number, status: number) {
      await ProjectApi.updateTaskStatus(taskId, status)
      // 乐观更新
      this.updateTaskInKanban(taskId, status)
    },
    
    updateTaskInKanban(taskId: number, newStatus: number) {
      // 在看板数据中更新任务状态
      this.kanbanData.forEach(column => {
        const taskIndex = column.tasks.findIndex((task: Task) => task.id === taskId)
        if (taskIndex !== -1) {
          const task = column.tasks.splice(taskIndex, 1)[0]
          task.status = newStatus
          
          // 找到新状态的列并添加任务
          const newColumn = this.kanbanData.find(col => col.status.id === newStatus)
          if (newColumn) {
            newColumn.tasks.push(task)
          }
        }
      })
    }
  }
})
```

### 2.3 核心组件开发（第2周第3-5天）

#### 1. 项目卡片组件 (frontend/src/components/project/ProjectCard.vue)
```vue
<template>
  <div class="project-card" :style="{ borderLeftColor: project.color }" @click="$emit('click', project)">
    <div class="card-header">
      <div class="project-info">
        <h3 class="project-name">{{ project.name }}</h3>
        <p class="project-desc">{{ project.description }}</p>
      </div>
      <el-dropdown @command="handleCommand" @click.stop>
        <el-button type="text" icon="el-icon-more" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="edit">编辑</el-dropdown-item>
            <el-dropdown-item command="members">成员管理</el-dropdown-item>
            <el-dropdown-item command="archive" divided>归档</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="card-body">
      <div class="progress-section">
        <div class="progress-info">
          <span>进度</span>
          <span class="progress-text">{{ project.progress }}%</span>
        </div>
        <el-progress :percentage="project.progress" :color="getProgressColor(project.progress)" />
      </div>

      <div class="meta-info">
        <div class="meta-item">
          <el-icon><User /></el-icon>
          <span>{{ project.owner_name }}</span>
        </div>
        <div class="meta-item">
          <el-icon><Calendar /></el-icon>
          <span>{{ formatDate(project.end_date) }}</span>
        </div>
        <el-tag :type="getStatusType(project.status)" size="small">
          {{ getStatusText(project.status) }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { User, Calendar } from '@element-plus/icons-vue'
import type { Project } from '@/api/project/projectApi'

interface Props {
  project: Project
}

interface Emits {
  (e: 'click', project: Project): void
  (e: 'edit', project: Project): void
  (e: 'members', project: Project): void
  (e: 'archive', project: Project): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleCommand = (command: string) => {
  emit(command as any, props.project)
}

const getProgressColor = (progress: number) => {
  if (progress < 30) return '#F54A45'
  if (progress < 70) return '#FF8800'
  return '#00BC70'
}

const getStatusType = (status: number) => {
  const types = { 1: 'primary', 2: 'success', 3: 'warning', 4: 'danger' }
  return types[status] || 'info'
}

const getStatusText = (status: number) => {
  const texts = { 1: '进行中', 2: '已完成', 3: '已暂停', 4: '已取消' }
  return texts[status] || '未知'
}

const formatDate = (date: string) => {
  return date ? new Date(date).toLocaleDateString() : ''
}
</script>

<style scoped>
.project-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 4px solid #1664FF;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.project-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1F2329;
}

.project-desc {
  font-size: 14px;
  color: #646A73;
  margin: 0;
  line-height: 1.4;
}

.progress-section {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-text {
  font-weight: 600;
  color: #1664FF;
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #646A73;
}
</style>
```

#### 2. 任务卡片组件 (frontend/src/components/project/TaskCard.vue)
```vue
<template>
  <div
    class="task-card"
    :class="[`priority-${task.priority}`, `status-${task.status}`]"
    draggable="true"
    @dragstart="onDragStart"
    @click="$emit('click', task)"
  >
    <div class="task-header">
      <div class="priority-indicator" :class="`priority-${task.priority}`"></div>
      <el-dropdown @command="handleCommand" @click.stop>
        <el-button type="text" icon="el-icon-more" size="small" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="edit">编辑</el-dropdown-item>
            <el-dropdown-item command="assign">分配</el-dropdown-item>
            <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="task-content">
      <h4 class="task-title">{{ task.title }}</h4>
      <p class="task-desc" v-if="task.description">{{ task.description }}</p>
    </div>

    <div class="task-meta">
      <div class="assignee" v-if="task.assignee_name">
        <el-avatar :size="20" :src="task.assignee_avatar" />
        <span>{{ task.assignee_name }}</span>
      </div>
      <div class="assignee" v-else>
        <el-avatar :size="20" icon="el-icon-user" />
        <span class="unassigned">待分配</span>
      </div>

      <div class="due-date" v-if="task.due_date">
        <el-icon><Calendar /></el-icon>
        <span :class="{ 'overdue': isOverdue(task.due_date) }">
          {{ formatDate(task.due_date) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Calendar } from '@element-plus/icons-vue'
import type { Task } from '@/api/project/projectApi'

interface Props {
  task: Task
}

interface Emits {
  (e: 'click', task: Task): void
  (e: 'edit', task: Task): void
  (e: 'assign', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'dragstart', event: DragEvent, task: Task): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onDragStart = (event: DragEvent) => {
  emit('dragstart', event, props.task)
}

const handleCommand = (command: string) => {
  emit(command as any, props.task)
}

const isOverdue = (dueDate: string) => {
  return new Date(dueDate) < new Date()
}

const formatDate = (date: string) => {
  return date ? new Date(date).toLocaleDateString() : ''
}
</script>

<style scoped>
.task-card {
  background: white;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  border-left: 3px solid #E5E6EB;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.task-card.priority-1 { border-left-color: #8C8C8C; }
.task-card.priority-2 { border-left-color: #FF8800; }
.task-card.priority-3 { border-left-color: #F54A45; }

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.priority-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #E5E6EB;
}

.priority-indicator.priority-1 { background: #8C8C8C; }
.priority-indicator.priority-2 { background: #FF8800; }
.priority-indicator.priority-3 { background: #F54A45; }

.task-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #1F2329;
  line-height: 1.4;
}

.task-desc {
  font-size: 12px;
  color: #646A73;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.assignee {
  display: flex;
  align-items: center;
  gap: 6px;
}

.unassigned {
  color: #8F959E;
}

.due-date {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #646A73;
}

.overdue {
  color: #F54A45;
}
</style>
```

### 2.4 页面开发（第3周）

#### 1. 项目列表页 (frontend/src/views/project/project/list.vue)
```vue
<template>
  <div class="project-list-page">
    <div class="page-header">
      <div class="header-left">
        <h1>项目管理</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>
    </div>

    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item>
          <el-input
            v-model="searchForm.name"
            placeholder="搜索项目名称..."
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.status" placeholder="项目状态" clearable>
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="已暂停" :value="3" />
            <el-option label="已取消" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="view-controls">
      <div class="view-toggle">
        <el-radio-group v-model="viewMode">
          <el-radio-button label="card">卡片视图</el-radio-button>
          <el-radio-button label="list">列表视图</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="content-section">
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="card-grid">
        <ProjectCard
          v-for="project in projectList"
          :key="project.id"
          :project="project"
          @click="goToDetail"
          @edit="handleEdit"
          @members="handleMembers"
          @archive="handleArchive"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view">
        <ArtTable
          :data="projectList"
          :loading="loading"
          @row-click="goToDetail"
        >
          <el-table-column prop="name" label="项目名称" min-width="200">
            <template #default="{ row }">
              <div class="project-name-cell">
                <div class="color-indicator" :style="{ backgroundColor: row.color }"></div>
                <div>
                  <div class="name">{{ row.name }}</div>
                  <div class="desc">{{ row.description }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <TagColumn :value="row.status" :options="statusOptions" />
            </template>
          </el-table-column>

          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <ProgressColumn :value="row.progress" />
            </template>
          </el-table-column>

          <el-table-column prop="owner_name" label="负责人" width="120" />
          <el-table-column prop="end_date" label="截止日期" width="120" />

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click.stop="goToDetail(row)">查看</el-button>
              <el-button type="text" @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click.stop="handleMembers(row)">成员</el-button>
            </template>
          </el-table-column>
        </ArtTable>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSearch"
        @current-change="handleSearch"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <ProjectFormDialog
      v-model="dialogVisible"
      :project="currentProject"
      @success="handleSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search } from '@element-plus/icons-vue'
import { ProjectApi, type Project } from '@/api/project/projectApi'
import { ArtTable } from '@/components/core/tables'
import { TagColumn, ProgressColumn } from '@/components/core/tables/columns'
import ProjectCard from '@/components/project/ProjectCard.vue'
import ProjectFormDialog from './form-dialog.vue'

const router = useRouter()

// 响应式数据
const viewMode = ref('card')
const loading = ref(false)
const dialogVisible = ref(false)
const projectList = ref<Project[]>([])
const currentProject = ref<Project | null>(null)

const searchForm = reactive({
  name: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const statusOptions = [
  { label: '进行中', value: 1, type: 'primary' },
  { label: '已完成', value: 2, type: 'success' },
  { label: '已暂停', value: 3, type: 'warning' },
  { label: '已取消', value: 4, type: 'danger' }
]

// 方法
const handleSearch = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    }
    const result = await ProjectApi.getList(params)
    projectList.value = result.data.list
    pagination.total = result.data.total
  } catch (error) {
    console.error('获取项目列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  Object.assign(searchForm, { name: '', status: '' })
  pagination.page = 1
  handleSearch()
}

const showCreateDialog = () => {
  currentProject.value = null
  dialogVisible.value = true
}

const goToDetail = (project: Project) => {
  router.push(`/project/detail/${project.id}`)
}

const handleEdit = (project: Project) => {
  currentProject.value = project
  dialogVisible.value = true
}

const handleMembers = (project: Project) => {
  // 打开成员管理对话框
  console.log('管理成员:', project)
}

const handleArchive = (project: Project) => {
  // 归档项目
  console.log('归档项目:', project)
}

onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.project-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1F2329;
}

.search-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.project-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-indicator {
  width: 4px;
  height: 32px;
  border-radius: 2px;
}

.name {
  font-weight: 500;
  color: #1F2329;
}

.desc {
  font-size: 12px;
  color: #646A73;
  margin-top: 2px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
```

## 🧪 第三阶段：测试验证（第4周）

### 3.1 功能测试清单

#### 后端API测试
- [ ] 项目CRUD操作
- [ ] 任务CRUD操作
- [ ] 项目成员管理
- [ ] 任务状态更新
- [ ] 看板数据获取
- [ ] 权限验证
- [ ] 租户隔离验证

#### 前端功能测试
- [ ] 项目列表展示
- [ ] 项目详情页面
- [ ] 看板拖拽功能
- [ ] 任务创建编辑
- [ ] 响应式布局
- [ ] 组件交互

### 3.2 性能测试
- [ ] 页面加载速度
- [ ] API响应时间
- [ ] 大数据量处理
- [ ] 内存使用情况

### 3.3 兼容性测试
- [ ] Chrome浏览器
- [ ] Firefox浏览器
- [ ] Safari浏览器
- [ ] 移动端适配

## 📋 实施检查清单

### 后端开发完成标准
- [x] 数据库表创建完成
- [ ] 模型关联关系正确
- [ ] 服务层业务逻辑完整
- [ ] 控制器API接口完整
- [ ] 权限验证正常
- [ ] 租户隔离有效

### 前端开发完成标准
- [ ] API接口封装完成
- [ ] 状态管理配置完成
- [ ] 核心组件开发完成
- [ ] 页面功能完整
- [ ] 响应式布局正常
- [ ] 用户体验良好

### 整体验收标准
- [ ] 所有核心功能正常
- [ ] 界面符合飞书风格
- [ ] 性能指标达标
- [ ] 兼容性测试通过
- [ ] 用户操作流畅

---

*本实施计划基于现有技术架构，确保代码质量和系统稳定性，预计3-4周完成全部开发工作。*
