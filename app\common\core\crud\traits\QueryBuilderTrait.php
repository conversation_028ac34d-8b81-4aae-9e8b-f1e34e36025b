<?php
declare(strict_types=1);

namespace app\common\core\crud\traits;

use think\db\Query;

/**
 * 查询构建相关功能
 */
trait QueryBuilderTrait
{
    /**
     * 构建基础查询
     */
    public function buildBaseQuery(array $where = [], array $with = [], bool $applyDataPermission = null)
    {
        $this->checkModel();

        // 安全过滤查询条件
        $safeWhere = $this->filterWhereCondition($where);

        $query = $this->model->where($safeWhere);

        // 安全过滤关联查询
        if (!empty($with)) {
            $safeWith = $this->filterWithRelation($with);
            if (!empty($safeWith)) {
                $query->with($safeWith);
            }
        }

        // 应用数据权限
        $shouldApplyDataPermission = $applyDataPermission ?? $this->enableDataPermission;
        if ($shouldApplyDataPermission) {
            $query = $this->applyDataPermission($query, $this->dataRangeField);
        }

        return $query;
    }

    /**
     * 过滤关联查询，防止加载不必要的关联
     */
    protected function filterWithRelation(array $with): array
    {
        $result = [];
        foreach ($with as $key => $value) {
            if (is_numeric($key) && is_string($value)) {
                // 简单关联: ['user', 'role']
                if ($this->isValidRelation($value)) {
                    $result[] = $value;
                }
            }
            elseif (is_string($key)) {
                // 带条件的关联: ['user' => function($query){...}]
                if ($this->isValidRelation($key)) {
                    $result[$key] = $value;
                }
            }
        }
        return $result;
    }
    
    /**
     * 检查关联是否有效
     */
    protected function isValidRelation(string $relation): bool
    {
        // 防止SQL注入，检查关联名是否包含危险字符
        return !preg_match('/[^a-zA-Z0-9_\.]/', $relation);
    }
    
    /**
     * 检查模型是否已设置
     */
    protected function checkModel(): void
    {
        if (!$this->model) {
            throw new \app\common\exception\BusinessException('未设置模型实例');
        }
    }
} 