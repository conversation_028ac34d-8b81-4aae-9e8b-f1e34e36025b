<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\core\traits\model\CreatorTrait;
use app\crm\model\CrmProductUnit;

use app\common\core\crud\traits\ExportableTrait;


use app\common\core\crud\traits\ImportableTrait;


/**
 * 产品单位表服务类
 */
class CrmProductUnitService extends BaseService
{
	
	use ExportableTrait,ImportableTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new CrmProductUnit();
		parent::__construct();
	}
	
	/**
	 * 获取产品单位选项
	 *
	 * @return array
	 */
	public function getOptions(): array
	{
		$where = [
			[
				'status',
				'=',
				1
			]
			// 只获取启用状态的单位
		];
		
		$list = $this->model->field('id,unit_name as name')
		                    ->where($where)
		                    ->order('id asc')
		                    ->select();
		
		return $list->toArray();
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			// 在这里定义验证规则
			// 例如：'username' => 'require|unique:crm_product_unit',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
} 