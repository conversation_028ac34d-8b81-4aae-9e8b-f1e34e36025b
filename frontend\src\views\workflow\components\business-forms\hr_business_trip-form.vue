<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="hr-business-trip-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
        class="business-trip-form"
      >
        <!--        <ElRow :gutter="20">
                  <ElCol :span="12">
                    <ElFormItem label="出差开始时间" prop="start_time">
                      <ElDatePicker
                        v-model="formData.start_time"
                        type="datetime"
                        placeholder="请选择开始时间"
                        style="width: 100%"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        :disabled="!isEditable"
                      />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="出差结束时间" prop="end_time">
                      <ElDatePicker
                        v-model="formData.end_time"
                        type="datetime"
                        placeholder="请选择结束时间"
                        style="width: 100%"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        :disabled="!isEditable"
                        @change="calculateDuration"
                      />
                    </ElFormItem>
                  </ElCol>
                </ElRow>-->

        <ElFormItem label="出差事由" prop="purpose">
          <ElInput
            v-model="formData.purpose"
            type="textarea"
            :rows="3"
            placeholder="请输入出差事由"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="出差天数" prop="duration">
              <ElInput
                v-model="formData.duration"
                style="width: 100%"
                :disabled="true"
                readonly
                placeholder="自动计算"
              >
                <template #append>天</template>
              </ElInput>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="同行人">
              <DepartmentPersonForm
                v-model="formData.companions"
                placeholder="请选择同行人"
                :multiple="true"
                :disabled="!isEditable"
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
            :disabled="!isEditable"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="附件">
          <FormUploader
            v-model="formData.attachment"
            :disabled="!isEditable"
            fileType="image"
            :limit="5"
            multiple
          />
        </ElFormItem>
      </ElForm>

      <!-- 行程明细 - 独立的卡片区域 -->
      <ElCard class="items-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">行程明细</span>
          </div>
        </template>

        <TripItemTable
          v-model="formData.items"
          :readonly="!isEditable"
          :item-template="getItemTemplate"
          @change="onItemsChange"
        />
      </ElCard>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import DepartmentPersonForm from '@/components/custom/DepartmentPersonForm.vue'
  import FormUploader from '@/components/custom/FormUploader/index.vue'
  import TripItemTable from '@/components/business/TripItemTable.vue'
  import { calculateDays } from '@/utils/date'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface HrBusinessTripFormData {
    id?: number
    duration: number
    purpose: string
    companions: any[]
    remark: string
    attachment: any[]
    items: any[]
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `出差申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起出差申请'
  })

  /** 表单数据 */
  const formData = reactive<HrBusinessTripFormData & any>({
    duration: 0,
    purpose: '',
    companions: [],
    remark: '',
    attachment: [],
    items: [],
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  // ==================== 辅助方法 ====================



  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    duration: [
      { required: true, message: '请输入出差天数', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          const numValue = Number(value)
          if (isNaN(numValue) || numValue <= 0) {
            callback(new Error('出差天数必须大于0'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    purpose: [{ required: true, message: '请输入出差事由', trigger: 'blur' }]
  }

  // ==================== 方法定义 ====================

  /**
   * 计算出差天数（根据明细自动计算）
   *
   * ⚠️ 重要：后端需要使用相同的算法进行校验
   * 算法逻辑：
   * 1. 遍历所有明细项，找到最早的开始时间和最晚的结束时间
   * 2. 计算时间差：(最晚结束时间 - 最早开始时间) / (1000 * 60 * 60 * 24)
   * 3. 保留一位小数：Math.round(diffDays * 10) / 10
   * 4. 同时更新主表单的开始时间和结束时间
   */
  const calculateDuration = () => {
    // 根据明细中的时间范围自动计算总天数
    if (formData.items && formData.items.length > 0) {
      let minStartTime: Date | null = null
      let maxEndTime: Date | null = null

      formData.items.forEach((item: any) => {
        if (item.start_time && item.end_time) {
          const startTime = new Date(item.start_time)
          const endTime = new Date(item.end_time)

          if (!minStartTime || startTime < minStartTime) {
            minStartTime = startTime
          }
          if (!maxEndTime || endTime > maxEndTime) {
            maxEndTime = endTime
          }
        }
      })

      if (minStartTime && maxEndTime) {
        // 使用工具方法计算天数
        const startTimeStr = (minStartTime as Date).toISOString().slice(0, 19).replace('T', ' ')
        const endTimeStr = (maxEndTime as Date).toISOString().slice(0, 19).replace('T', ' ')

        formData.duration = Number(calculateDays(startTimeStr, endTimeStr))

        // 注释：主表单不再需要 start_time 和 end_time 字段
      }
    }
  }

  // 明细表格相关方法
  const getItemTemplate = () => ({
    transport_type: null,
    trip_mode: null,
    departure_city: '',
    destination_city: '',
    departure_city_code: [],
    destination_city_code: [],
    start_time: '',
    end_time: '',
    duration: 0
  })

  const onItemsChange = (items: any[]) => {
    formData.items = items
    // 明细变化时重新计算出差天数
    calculateDuration()
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('hr_business_trip-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      duration: 0,
      purpose: '',
      companions: [],
      remark: '',
      attachment: [],
      items: [],
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        // 确保items是数组
        if (!Array.isArray(formData.items)) {
          formData.items = []
        }

        console.log('出差申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('hr_business_trip-form setFormData called with:', data)
    Object.assign(formData, data)
    if (!Array.isArray(formData.items)) {
      formData.items = []
    }

    // 确保 companions 字段是数组格式
    if (!Array.isArray(formData.companions)) {
      formData.companions = []
    }

    // 处理明细项的地区数据
    formData.items.forEach((item: any) => {
      // 处理出发地代码 - 支持数字数组转字符串数组
      if (item.departure_city_code && Array.isArray(item.departure_city_code)) {
        // 将数字数组转换为字符串数组（RegionSelector需要字符串数组）
        item.departure_city_code = item.departure_city_code.map((code: any) => String(code))
      } else if (item.departure_city_code && typeof item.departure_city_code === 'string') {
        try {
          const parsed = JSON.parse(item.departure_city_code)
          item.departure_city_code = Array.isArray(parsed) ? parsed.map((code: any) => String(code)) : []
        } catch {
          console.warn('解析出发地代码失败:', item.departure_city_code)
          item.departure_city_code = []
        }
      } else {
        item.departure_city_code = []
      }

      // 处理目的地代码 - 支持数字数组转字符串数组
      if (item.destination_city_code && Array.isArray(item.destination_city_code)) {
        // 将数字数组转换为字符串数组（RegionSelector需要字符串数组）
        item.destination_city_code = item.destination_city_code.map((code: any) => String(code))
      } else if (item.destination_city_code && typeof item.destination_city_code === 'string') {
        try {
          const parsed = JSON.parse(item.destination_city_code)
          item.destination_city_code = Array.isArray(parsed) ? parsed.map((code: any) => String(code)) : []
        } catch {
          console.warn('解析目的地代码失败:', item.destination_city_code)
          item.destination_city_code = []
        }
      } else {
        item.destination_city_code = []
      }
    })
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('hr_business_trip-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      // 保存时不强制验证明细项，但如果有明细项则验证其完整性
      if (formData.items && formData.items.length > 0) {
        for (let i = 0; i < formData.items.length; i++) {
          const item = formData.items[i]
          if (item.start_time && item.end_time) {
            const startTime = new Date(item.start_time)
            const endTime = new Date(item.end_time)
            if (endTime <= startTime) {
              ElMessage.warning(`第${i + 1}行：结束时间必须晚于开始时间`)
              return
            }
          }
        }
      }

      saving.value = true

      // 准备提交数据
      const submitData: HrBusinessTripFormData = {
        duration: formData.duration,
        purpose: formData.purpose,
        companions: formData.companions,
        remark: formData.remark,
        attachment: formData.attachment,
        items: formData.items
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('出差申请保存数据:', submitData)
      emit('save', submitData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 验证明细项数据
   */
  const validateItems = (): boolean => {
    if (!formData.items || formData.items.length === 0) {
      ElMessage.warning('请至少添加一条行程明细')
      return false
    }

    for (let i = 0; i < formData.items.length; i++) {
      const item = formData.items[i]
      const rowNum = i + 1

      if (!item.transport_type) {
        ElMessage.warning(`第${rowNum}行：请选择交通工具`)
        return false
      }
      if (!item.trip_mode) {
        ElMessage.warning(`第${rowNum}行：请选择单程往返`)
        return false
      }
      if (!item.departure_city) {
        ElMessage.warning(`第${rowNum}行：请输入出发地`)
        return false
      }
      if (!item.destination_city) {
        ElMessage.warning(`第${rowNum}行：请输入目的地`)
        return false
      }
      if (!item.start_time) {
        ElMessage.warning(`第${rowNum}行：请选择开始时间`)
        return false
      }
      if (!item.end_time) {
        ElMessage.warning(`第${rowNum}行：请选择结束时间`)
        return false
      }

      // 验证时间逻辑
      const startTime = new Date(item.start_time)
      const endTime = new Date(item.end_time)
      if (endTime <= startTime) {
        ElMessage.warning(`第${rowNum}行：结束时间必须晚于开始时间`)
        return false
      }
    }

    return true
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      // 验证明细项
      if (!validateItems()) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: HrBusinessTripFormData = {
        duration: formData.duration,
        purpose: formData.purpose,
        companions: formData.companions,
        remark: formData.remark,
        attachment: formData.attachment,
        items: formData.items
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      console.log('出差申请提交数据:', submitData)
      emit('submit', submitData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 注释：不再需要监听主表单的时间字段，因为已移除

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .hr-business-trip-dialog {
    // 参考请假表单的对话框样式
    :deep(.el-dialog) {
      margin-top: 5vh !important;
      margin-bottom: 5vh !important;
      display: flex;
      flex-direction: column;
      max-height: 90vh;
    }

    :deep(.el-dialog__body) {
      overflow: auto;
      padding: 20px;
      max-height: 65vh;
    }

    .dialog-content {
      max-height: 60vh;
      padding: 10px 30px;
      overflow-y: auto;
      border-bottom: 1px solid #eaeaea;
    }

    .business-trip-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      .el-textarea {
        .el-textarea__inner {
          resize: vertical;
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 16px;

      .el-button {
        min-width: 100px;
      }
    }
  }

  // 卡片样式
  .items-card {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .hr-business-trip-dialog {
      .dialog-footer {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
</style>
