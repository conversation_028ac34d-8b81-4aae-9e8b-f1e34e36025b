<?php

namespace app\common\core\traits\model;

use app\system\model\AdminModel;
use think\model\relation\BelongsTo;

/**
 * 关联创建人
 *
 * Trait CreatorTrait
 */
trait CreatorTrait
{
	/**
	 * 关联提交人
	 *
	 * @return BelongsTo
	 */
	public function creator(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'creator_id', 'id')
		            ->bind([
			            'creator_name' => 'real_name'
		            ]);
	}
}