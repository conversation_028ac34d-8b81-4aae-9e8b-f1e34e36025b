# 每日报价单审批系统完整设计方案

## 📋 项目概述

### 设计目标
基于现有工作流审批系统，设计一套完整的每日报价单管理系统，实现报价单的创建、编辑、审批、生效、作废等全生命周期管理，确保价格变动的合规性和权威性。

### 模块架构
- **模块名称**：daily（每日报价模块）
- **数据表前缀**：daily_price_*
- **工作流代码**：daily_price_order
- **独立模块**：从CRM模块分离，独立管理

### 核心特性
1. **审批流程集成**：与现有工作流系统深度集成，复用成熟的审批机制
2. **状态驱动设计**：基于审批状态控制业务逻辑和UI展示
3. **数据安全保障**：通过审批流程确保价格变动的合规性
4. **完整审计追踪**：记录完整的操作历史和审批过程
5. **用户体验优化**：直观的状态展示和操作引导

### 适用场景
- 中小企业每日价格管理
- 需要审批确认的价格变动流程
- 对外报价的权威性管控
- 价格变动的审计追踪需求

## 🗂️ 数据库设计

### 核心表结构

#### 1. 每日报价单主表 (daily_price_order)
```sql
CREATE TABLE `daily_price_order` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '报价单ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `order_number` varchar(50) NOT NULL DEFAULT '' COMMENT '报价单编号',
    `title` varchar(200) NOT NULL DEFAULT '' COMMENT '报价单标题',
    `price_date` date NOT NULL COMMENT '报价日期',
    `total_items` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品总数',
    `remark` text COMMENT '备注说明',
    `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '单据状态:0=停用,1=启用',
    
    -- 工作流审批字段
    `approval_status` tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `workflow_instance_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '工作流实例ID',
    `submit_time` datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time` datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '提交人ID',
    `void_reason` varchar(500) DEFAULT NULL COMMENT '作废原因',
    `void_time` datetime DEFAULT NULL COMMENT '作废时间',
    `void_user_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '作废人ID',
    
    -- 基础字段
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_order_number` (`tenant_id`, `order_number`),
    UNIQUE KEY `uk_tenant_price_date` (`tenant_id`, `price_date`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_price_date` (`price_date`)
) ENGINE=InnoDB COMMENT='每日报价单表';
```

#### 2. 报价明细表 (daily_price_item)
```sql
CREATE TABLE `daily_price_item` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `order_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '报价单ID',
    `supplier_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '供应商ID',
    `product_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品ID',
    
    -- 价格信息
    `unit_price` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '当前单价',
    `old_price` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '原价格(用于计算涨幅)',
    `price_change` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '价格变动金额',
    `change_rate` decimal(8,4) NOT NULL DEFAULT 0.0000 COMMENT '变动比例(%)',
    `stock_price` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '库存价格',
    `stock_qty` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '库存数量',
    `policy_remark` varchar(200) NOT NULL DEFAULT '' COMMENT '优惠政策/备注',
    `is_manual_price` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否手动修改价格:0=自动继承,1=手动修改',
    
    -- 排序和状态
    `sort_order` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
    `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=停用,1=启用',
    
    -- 基础字段
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_supplier_product` (`order_id`, `supplier_id`, `product_id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_supplier_product` (`supplier_id`, `product_id`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB COMMENT='报价明细表';
```

#### 3. 价格历史记录表 (daily_price_history)
```sql
CREATE TABLE `daily_price_history` (
    -- 记录审批通过后的价格变动历史
    -- 只有审批通过的报价单才会写入此表
    -- 详细字段见SQL文件
);
```

### 数据表关系
```
daily_price_order (1) ←→ (N) daily_price_item
daily_price_order (1) ←→ (1) workflow_instance
daily_price_item (N) ←→ (1) ims_supplier
daily_price_item (N) ←→ (1) crm_product
daily_price_history (N) ←→ (1) daily_price_order
```

## 🔄 业务流程设计

### 审批状态流转图
```
创建报价单 → 0(草稿) → 1(审批中) → 2(已通过) → 生效
    ↓           ↓         ↓         ↓
   删除        撤回      拒绝      作废
                ↓         ↓         ↓
              编辑      修改      失效
                ↓         ↓
              提交    重新提交
```

### 详细业务流程

#### 1. 报价单创建流程
```
1. 用户创建新报价单
   ├─ 系统生成报价单编号
   ├─ 设置默认报价日期为今日
   ├─ 状态设为草稿(0)
   └─ 可选择从昨日报价复制

2. 添加产品明细
   ├─ 选择供应商和产品
   ├─ 输入价格信息
   ├─ 系统自动计算涨跌幅
   └─ 保存明细数据

3. 保存草稿
   ├─ 验证必填字段
   ├─ 更新产品总数
   └─ 保存到数据库
```

#### 2. 审批提交流程
```
1. 提交前验证
   ├─ 检查是否有产品明细
   ├─ 验证价格数据完整性
   ├─ 检查价格变动是否合理
   └─ 验证通过后可提交

2. 提交审批
   ├─ 调用工作流服务
   ├─ 创建工作流实例
   ├─ 更新审批状态为审批中(1)
   ├─ 记录提交时间和提交人
   └─ 发送审批通知

3. 审批处理
   ├─ 审批人收到通知
   ├─ 查看报价单详情
   ├─ 填写审批意见
   └─ 选择审批结果
```

#### 3. 审批结果处理
```
审批通过(2):
├─ 更新审批状态和时间
├─ 生成价格历史记录
├─ 报价单正式生效
└─ 发送通过通知

审批拒绝(3):
├─ 更新审批状态
├─ 记录拒绝原因
├─ 允许修改后重新提交
└─ 发送拒绝通知

审批终止(4):
├─ 更新审批状态
├─ 软删除产品明细
├─ 报价单失效
└─ 发送终止通知

撤回审批(5):
├─ 更新审批状态
├─ 清除审批信息
├─ 恢复为草稿状态
└─ 允许继续编辑

作废处理(6):
├─ 更新审批状态
├─ 记录作废原因和操作人
├─ 软删除整个报价单
└─ 报价单完全失效
```

## 🔧 技术实现方案

### 后端架构设计

#### 1. 模型层扩展 (基于生成器生成的基础模型)
```php
// 报价单主表模型扩展
class CrmDailyPriceOrder extends BaseModel
{
    // 基础CRUD由生成器自动生成，以下为业务扩展

    // 获取审批状态文本
    public function getApprovalStatusTextAttr($value, $data)
    {
        $statusMap = [
            0 => '草稿',
            1 => '审批中',
            2 => '已通过',
            3 => '已拒绝',
            4 => '已终止',
            5 => '已撤回',
            6 => '已作废'
        ];
        return $statusMap[$data['approval_status']] ?? '未知';
    }

    // 获取审批状态样式类
    public function getApprovalStatusClassAttr($value, $data)
    {
        $classMap = [
            0 => 'status-draft',
            1 => 'status-pending',
            2 => 'status-approved',
            3 => 'status-rejected',
            4 => 'status-terminated',
            5 => 'status-recalled',
            6 => 'status-voided'
        ];
        return $classMap[$data['approval_status']] ?? 'status-unknown';
    }

    // 检查是否可编辑
    public function getCanEditAttr($value, $data)
    {
        return in_array($data['approval_status'], [0, 3, 5]); // 草稿、已拒绝、已撤回
    }

    // 检查是否可提交审批
    public function getCanSubmitAttr($value, $data)
    {
        return $data['approval_status'] === 0 && $data['total_items'] > 0;
    }

    // 检查是否可撤回
    public function getCanWithdrawAttr($value, $data)
    {
        return $data['approval_status'] === 1;
    }
}

// 报价明细模型扩展
class CrmDailyPriceItem extends BaseModel
{
    // 基础CRUD由生成器自动生成，以下为业务扩展

    // 计算价格变动
    public function calculatePriceChange($newPrice)
    {
        $oldPrice = $this->old_price ?: 0;
        $this->price_change = $newPrice - $oldPrice;
        $this->change_rate = $oldPrice > 0 ? ($this->price_change / $oldPrice) * 100 : 0;
        $this->is_manual_price = 1;
    }

    // 获取价格变动样式类
    public function getPriceChangeClassAttr($value, $data)
    {
        if ($data['price_change'] > 0) return 'price-rise';
        if ($data['price_change'] < 0) return 'price-fall';
        return 'price-stable';
    }

    // 格式化价格变动显示
    public function getPriceChangeTextAttr($value, $data)
    {
        $change = $data['price_change'];
        $rate = $data['change_rate'];

        if ($change > 0) {
            return "+{$change}(+{$rate}%)↗️";
        } elseif ($change < 0) {
            return "{$change}({$rate}%)↘️";
        } else {
            return "0(0.0%)➡️";
        }
    }
}
```

#### 2. 业务服务层 (基于生成器生成的基础服务扩展)
```php
// 报价单业务服务扩展
class CrmDailyPriceOrderService extends CrmDailyPriceOrderCrudService
{
    // 基础CRUD由生成器自动生成，以下为业务逻辑扩展

    /**
     * 创建报价单（扩展基础创建方法）
     */
    public function createOrderWithDefaults(array $data): array
    {
        // 设置默认值
        $data['order_number'] = $this->generateOrderNumber();
        $data['approval_status'] = 0; // 草稿状态
        $data['price_date'] = $data['price_date'] ?? date('Y-m-d');
        $data['total_items'] = 0;

        // 调用基础创建方法
        return $this->create($data);
    }

    /**
     * 批量保存明细
     */
    public function saveItems(int $orderId, array $items): array
    {
        // 验证报价单状态
        $order = CrmDailyPriceOrder::find($orderId);
        if (!$order || !$order->can_edit) {
            return $this->error('报价单当前状态不允许编辑');
        }

        Db::startTrans();
        try {
            // 软删除原有明细
            CrmDailyPriceItem::where('order_id', $orderId)->delete();

            // 批量插入新明细
            $validItems = 0;
            foreach ($items as $index => $item) {
                $item['order_id'] = $orderId;
                $item['tenant_id'] = get_tenant_id();
                $item['sort_order'] = $index + 1;

                // 计算价格变动
                if (isset($item['unit_price']) && isset($item['old_price'])) {
                    $item['price_change'] = $item['unit_price'] - $item['old_price'];
                    $item['change_rate'] = $item['old_price'] > 0 ?
                        ($item['price_change'] / $item['old_price']) * 100 : 0;
                }

                CrmDailyPriceItem::create($item);
                $validItems++;
            }

            // 更新产品总数
            $order->total_items = $validItems;
            $order->save();

            Db::commit();
            return $this->success('保存成功', ['total_items' => $validItems]);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('保存失败：' . $e->getMessage());
        }
    }

    /**
     * 从昨日报价复制
     */
    public function copyFromYesterday(string $targetDate): array
    {
        $yesterdayDate = date('Y-m-d', strtotime($targetDate . ' -1 day'));

        // 查找昨日已通过的报价单
        $yesterdayOrder = CrmDailyPriceOrder::where('price_date', $yesterdayDate)
            ->where('approval_status', 2)
            ->find();

        if (!$yesterdayOrder) {
            return $this->error('未找到昨日已通过的报价单');
        }

        // 获取昨日明细
        $yesterdayItems = CrmDailyPriceItem::where('order_id', $yesterdayOrder->id)->select();

        if ($yesterdayItems->isEmpty()) {
            return $this->error('昨日报价单无有效明细');
        }

        // 创建今日报价单
        $newOrder = $this->createOrderWithDefaults([
            'title' => "今日报价单_{$targetDate}",
            'price_date' => $targetDate,
            'remark' => "基于{$yesterdayDate}报价单复制"
        ]);

        if (!$newOrder['success']) {
            return $newOrder;
        }

        // 复制明细，价格作为old_price
        $newItems = [];
        foreach ($yesterdayItems as $item) {
            $newItems[] = [
                'supplier_id' => $item->supplier_id,
                'product_id' => $item->product_id,
                'unit_price' => $item->unit_price,
                'old_price' => $item->unit_price, // 昨日价格作为基准
                'stock_price' => $item->stock_price,
                'stock_qty' => $item->stock_qty,
                'policy_remark' => $item->policy_remark,
                'is_manual_price' => 0 // 标记为自动继承
            ];
        }

        $result = $this->saveItems($newOrder['data']['id'], $newItems);

        return $this->success('复制成功', [
            'order' => $newOrder['data'],
            'items_count' => count($newItems)
        ]);
    }

    /**
     * 生成报价单编号
     */
    private function generateOrderNumber(): string
    {
        $prefix = 'BJ';
        $date = date('Ymd');

        // 获取今日最大序号
        $maxNumber = CrmDailyPriceOrder::where('order_number', 'like', $prefix . $date . '%')
            ->max('order_number');

        if ($maxNumber) {
            $sequence = intval(substr($maxNumber, -3)) + 1;
        } else {
            $sequence = 1;
        }

        return $prefix . $date . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }
}

#### 3. 工作流服务 (核心业务逻辑)
```php
// 工作流服务
class CrmDailyPriceOrderWorkflowService extends WorkflowableService
{
    protected function getBusinessCode(): string
    {
        return 'crm_daily_price_order';
    }

    protected function getApprovalTitle(object $record): string
    {
        return "每日报价审批-{$record->order_number}({$record->price_date})";
    }

    protected function validateForApproval(object $record): void
    {
        parent::validateForApproval($record);

        // 验证报价单基本信息
        if ($record->total_items <= 0) {
            throw new BusinessException('报价单必须包含产品明细');
        }

        if (empty($record->title)) {
            throw new BusinessException('报价单标题不能为空');
        }

        // 验证是否已存在当日已通过的报价单
        $existsApproved = CrmDailyPriceOrder::where('price_date', $record->price_date)
            ->where('approval_status', 2)
            ->where('id', '<>', $record->id)
            ->count();

        if ($existsApproved > 0) {
            throw new BusinessException('当日已存在已通过的报价单，不能重复提交');
        }

        // 验证价格变动是否合理
        $this->validatePriceChanges($record);
    }

    /**
     * 验证价格变动合理性
     */
    private function validatePriceChanges(object $record): void
    {
        $items = CrmDailyPriceItem::where('order_id', $record->id)->select();

        foreach ($items as $item) {
            // 检查价格变动幅度是否过大（超过50%需要特别审批）
            if (abs($item->change_rate) > 50) {
                throw new BusinessException("产品{$item->product->name}价格变动幅度过大({$item->change_rate}%)，请确认后重新提交");
            }

            // 检查价格是否为负数
            if ($item->unit_price < 0) {
                throw new BusinessException("产品{$item->product->name}价格不能为负数");
            }
        }
    }

    protected function afterApprovalComplete(object $record, int $status, string $opinion): void
    {
        switch ($status) {
            case 2: // 已通过
                $this->handleOrderApproved($record);
                break;
            case 3: // 已拒绝
                $this->handleOrderRejected($record, $opinion);
                break;
            case 4: // 已终止
                $this->handleOrderTerminated($record);
                break;
            case 6: // 已作废
                $this->handleOrderVoided($record);
                break;
        }
    }

    /**
     * 处理审批通过
     */
    private function handleOrderApproved(object $order): void
    {
        Db::startTrans();
        try {
            // 生成价格历史记录
            $this->generatePriceHistory($order);

            // 清除当日其他草稿状态的报价单
            CrmDailyPriceOrder::where('price_date', $order->price_date)
                ->where('approval_status', 0)
                ->where('id', '<>', $order->id)
                ->delete();

            Db::commit();

            // 发送通知
            $this->sendApprovalNotification($order, '审批通过，报价单已生效');
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 处理审批拒绝
     */
    private function handleOrderRejected(object $order, string $opinion): void
    {
        // 记录拒绝原因到备注
        $order->remark = ($order->remark ? $order->remark . "\n" : '') . "审批拒绝：{$opinion}";
        $order->save();

        // 发送通知
        $this->sendApprovalNotification($order, "审批被拒绝：{$opinion}");
    }

    /**
     * 处理审批终止
     */
    private function handleOrderTerminated(object $order): void
    {
        // 软删除所有明细
        CrmDailyPriceItem::where('order_id', $order->id)->delete();

        // 发送通知
        $this->sendApprovalNotification($order, '审批已终止，报价单失效');
    }

    /**
     * 处理作废
     */
    private function handleOrderVoided(object $order): void
    {
        // 软删除报价单和明细
        CrmDailyPriceItem::where('order_id', $order->id)->delete();
        $order->delete();

        // 发送通知
        $this->sendApprovalNotification($order, '报价单已作废');
    }

    /**
     * 生成价格历史记录
     */
    private function generatePriceHistory(object $order): void
    {
        $items = CrmDailyPriceItem::where('order_id', $order->id)->select();

        foreach ($items as $item) {
            // 记录所有明细到历史表（包括无变动的，用于完整追踪）
            CrmPriceHistory::create([
                'tenant_id' => $order->tenant_id,
                'order_id' => $order->id,
                'item_id' => $item->id,
                'supplier_id' => $item->supplier_id,
                'product_id' => $item->product_id,
                'old_price' => $item->old_price,
                'new_price' => $item->unit_price,
                'price_change' => $item->price_change,
                'change_rate' => $item->change_rate,
                'change_date' => $order->price_date,
                'approval_time' => $order->approval_time,
                'creator_id' => $order->creator_id
            ]);
        }
    }

    /**
     * 发送审批通知
     */
    private function sendApprovalNotification(object $order, string $message): void
    {
        // 这里可以集成消息通知系统
        // 例如：短信、邮件、站内消息等
        Log::info('报价单审批通知', [
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'message' => $message,
            'user_id' => $order->creator_id
        ]);
    }
}
```

#### 4. 控制器扩展 (基于生成器生成的基础控制器扩展)
```php
class CrmDailyPriceOrderController extends CrmDailyPriceOrderCrudController
{
    // 基础CRUD由生成器自动生成，以下为业务扩展

    /**
     * 提交审批
     */
    public function submitApproval(): Json
    {
        $id = $this->request->post('id', 0, 'int');

        try {
            $workflowService = new CrmDailyPriceOrderWorkflowService();
            $result = $workflowService->submitApproval($id);

            return $this->success('提交成功', $result);
        } catch (\Exception $e) {
            return $this->error('提交失败：' . $e->getMessage());
        }
    }

    /**
     * 撤回审批
     */
    public function withdrawApproval(): Json
    {
        $id = $this->request->post('id', 0, 'int');

        try {
            $workflowService = new CrmDailyPriceOrderWorkflowService();
            $result = $workflowService->withdrawApproval($id);

            return $this->success('撤回成功', $result);
        } catch (\Exception $e) {
            return $this->error('撤回失败：' . $e->getMessage());
        }
    }

    /**
     * 作废报价单
     */
    public function voidOrder(): Json
    {
        $id = $this->request->post('id', 0, 'int');
        $reason = $this->request->post('reason', '', 'string');

        if (empty($reason)) {
            return $this->error('请填写作废原因');
        }

        try {
            $workflowService = new CrmDailyPriceOrderWorkflowService();
            $result = $workflowService->voidOrder($id, $reason);

            return $this->success('作废成功', $result);
        } catch (\Exception $e) {
            return $this->error('作废失败：' . $e->getMessage());
        }
    }

    /**
     * 保存明细
     */
    public function saveItems(): Json
    {
        $orderId = $this->request->post('order_id', 0, 'int');
        $items = $this->request->post('items', [], 'array');

        if (empty($items)) {
            return $this->error('明细数据不能为空');
        }

        try {
            $service = new CrmDailyPriceOrderService();
            $result = $service->saveItems($orderId, $items);

            return $result['success'] ?
                $this->success($result['message'], $result['data']) :
                $this->error($result['message']);
        } catch (\Exception $e) {
            return $this->error('保存失败：' . $e->getMessage());
        }
    }

    /**
     * 从昨日复制
     */
    public function copyFromYesterday(): Json
    {
        $targetDate = $this->request->post('target_date', date('Y-m-d'), 'string');

        try {
            $service = new CrmDailyPriceOrderService();
            $result = $service->copyFromYesterday($targetDate);

            return $result['success'] ?
                $this->success($result['message'], $result['data']) :
                $this->error($result['message']);
        } catch (\Exception $e) {
            return $this->error('复制失败：' . $e->getMessage());
        }
    }

    /**
     * 导出报价单
     */
    public function export(): Json
    {
        $id = $this->request->param('id', 0, 'int');

        try {
            $exportService = new CrmDailyPriceOrderExportService();
            $filePath = $exportService->exportToExcel($id);

            return $this->success('导出成功', ['file_path' => $filePath]);
        } catch (\Exception $e) {
            return $this->error('导出失败：' . $e->getMessage());
        }
    }

    /**
     * 获取价格统计
     */
    public function getStatistics(): Json
    {
        $date = $this->request->param('date', date('Y-m-d'), 'string');

        try {
            $service = new CrmDailyPriceOrderService();
            $statistics = $service->getDateStatistics($date);

            return $this->success('获取成功', $statistics);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
}
```

```

### 前端架构设计

#### 1. 页面组件结构
```
src/views/crm/daily_price_order/
├── index.vue                 # 报价单列表页
├── form.vue                  # 报价单表单页
├── detail.vue                # 报价单详情页
└── components/
    ├── PriceOrderForm.vue     # 报价单表单组件
    ├── PriceItemTable.vue     # 明细表格组件
    ├── ApprovalStatus.vue     # 审批状态组件
    ├── PriceTrendChart.vue    # 价格趋势图组件
    └── ProductSelector.vue    # 产品选择器组件
```

#### 2. 状态管理 (Pinia Store)
```typescript
// stores/dailyPriceOrder.ts
export const useDailyPriceOrderStore = defineStore('dailyPriceOrder', {
  state: () => ({
    orderList: [] as DailyPriceOrder[],
    currentOrder: null as DailyPriceOrder | null,
    loading: false,
    submitting: false,
    approvalStatuses: [
      { value: 0, label: '草稿', color: 'info' },
      { value: 1, label: '审批中', color: 'warning' },
      { value: 2, label: '已通过', color: 'success' },
      { value: 3, label: '已拒绝', color: 'danger' },
      { value: 4, label: '已终止', color: 'warning' },
      { value: 5, label: '已撤回', color: 'info' },
      { value: 6, label: '已作废', color: 'danger' }
    ]
  }),

  getters: {
    canEdit: (state) => {
      return state.currentOrder?.approval_status === 0 ||
             state.currentOrder?.approval_status === 3 ||
             state.currentOrder?.approval_status === 5
    },

    canSubmit: (state) => {
      return state.currentOrder?.approval_status === 0 &&
             state.currentOrder?.total_items > 0
    },

    canWithdraw: (state) => {
      return state.currentOrder?.approval_status === 1
    }
  },

  actions: {
    async fetchOrderList(params: any) {
      this.loading = true
      try {
        const response = await DailyPriceOrderApi.getList(params)
        this.orderList = response.data.list
        return response
      } finally {
        this.loading = false
      }
    },

    async submitApproval(id: number) {
      this.submitting = true
      try {
        const response = await DailyPriceOrderApi.submitApproval(id)
        await this.fetchOrderDetail(id) // 刷新数据
        return response
      } finally {
        this.submitting = false
      }
    }
  }
})
```

#### 3. API接口定义
```typescript
// api/crm/dailyPriceOrder.ts
export const DailyPriceOrderApi = {
  // 获取列表
  getList: (params: any) => {
    return request.get('/crm/daily-price-order', { params })
  },

  // 获取详情
  getDetail: (id: number) => {
    return request.get(`/crm/daily-price-order/${id}`)
  },

  // 创建报价单
  create: (data: any) => {
    return request.post('/crm/daily-price-order', data)
  },

  // 更新报价单
  update: (id: number, data: any) => {
    return request.put(`/crm/daily-price-order/${id}`, data)
  },

  // 保存明细
  saveItems: (id: number, items: any[]) => {
    return request.post(`/crm/daily-price-order/${id}/items`, { items })
  },

  // 提交审批
  submitApproval: (id: number) => {
    return request.post(`/crm/daily-price-order/${id}/submit-approval`)
  },

  // 撤回审批
  withdrawApproval: (id: number) => {
    return request.post(`/crm/daily-price-order/${id}/withdraw-approval`)
  },

  // 作废报价单
  voidOrder: (id: number, reason: string) => {
    return request.post(`/crm/daily-price-order/${id}/void`, { reason })
  },

  // 导出报价单
  export: (id: number) => {
    return request.get(`/crm/daily-price-order/${id}/export`, {
      responseType: 'blob'
    })
  }
}
```

## 📱 UI/UX设计规范

### 1. 色彩系统
```css
/* 审批状态色彩定义 */
.status-draft { color: #909399; background: #f4f4f5; }
.status-pending { color: #409eff; background: #ecf5ff; }
.status-approved { color: #67c23a; background: #f0f9ff; }
.status-rejected { color: #f56c6c; background: #fef0f0; }
.status-terminated { color: #e6a23c; background: #fdf6ec; }
.status-recalled { color: #909399; background: #f4f4f5; }
.status-voided { color: #606266; background: #f0f0f0; }

/* 价格变动色彩 */
.price-rise { color: #f56c6c; }
.price-fall { color: #67c23a; }
.price-stable { color: #909399; }
```

### 2. 响应式设计
```css
/* 桌面端 */
@media (min-width: 1200px) {
  .price-order-form { max-width: 1200px; margin: 0 auto; }
  .form-columns { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
}

/* 平板端 */
@media (min-width: 768px) and (max-width: 1199px) {
  .price-items-table { font-size: 14px; }
  .action-buttons .el-button { padding: 8px 12px; }
}

/* 移动端 */
@media (max-width: 767px) {
  .price-items-table { display: none; }
  .mobile-card-list { display: block; }
}
```

### 3. 交互动画
```css
/* 状态切换动画 */
.status-transition {
  transition: all 0.3s ease-in-out;
}

/* 表格行hover效果 */
.el-table__row:hover {
  background-color: #f5f7fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 按钮loading动画 */
.el-button.is-loading {
  position: relative;
  pointer-events: none;
}
```

## 🚀 部署实施指南

### 第一阶段：数据库准备 (1-2天)
```sql
-- 1. 创建数据表
-- 执行 crm_daily_price_order 表创建SQL
-- 执行 crm_daily_price_item 表创建SQL

-- 2. 创建索引
CREATE INDEX idx_order_date_status ON crm_daily_price_order(price_date, approval_status);
CREATE INDEX idx_item_price_change ON crm_daily_price_item(price_change);

-- 3. 初始化基础数据
INSERT INTO workflow_type (code, name, description)
VALUES ('crm_daily_price_order', '每日报价审批', '每日报价单审批流程');
```

### 第二阶段：后端开发 (3-5天)
```bash
# 1. 生成模型文件
php think make:model crm/CrmDailyPriceOrder
php think make:model crm/CrmDailyPriceItem

# 2. 生成控制器
php think make:controller crm/CrmDailyPriceOrderController

# 3. 生成服务类
php think make:service crm/CrmDailyPriceOrderService
php think make:service crm/CrmDailyPriceOrderWorkflowService

# 4. 配置路由
# 在 route/crm.php 中添加路由配置
```

### 第三阶段：前端开发 (4-6天)
```bash
# 1. 创建页面组件
mkdir -p src/views/crm/daily_price_order
mkdir -p src/views/crm/daily_price_order/components

# 2. 创建API接口
touch src/api/crm/dailyPriceOrder.ts

# 3. 创建状态管理
touch src/stores/dailyPriceOrder.ts

# 4. 配置路由
# 在 src/router/modules/crm.ts 中添加路由
```

### 第四阶段：集成测试 (2-3天)
```bash
# 1. 单元测试
php think test:unit CrmDailyPriceOrderTest

# 2. 接口测试
# 使用 Postman 或 Apifox 测试所有API接口

# 3. 前端测试
npm run test:unit

# 4. 端到端测试
npm run test:e2e
```

### 第五阶段：上线部署 (1天)
```bash
# 1. 生产环境数据库迁移
php think migrate:run

# 2. 前端构建
npm run build:prod

# 3. 部署到服务器
# 更新后端代码
# 更新前端静态文件

# 4. 配置监控
# 添加日志监控
# 配置性能监控
```

## 📊 质量保证

### 1. 代码质量标准
- **PSR-12** 代码规范
- **单元测试覆盖率** ≥ 80%
- **代码复杂度** ≤ 10
- **TypeScript** 严格模式

### 2. 性能指标
- **页面加载时间** ≤ 2秒
- **API响应时间** ≤ 500ms
- **数据库查询** ≤ 100ms
- **并发支持** ≥ 100用户

### 3. 安全要求
- **权限验证** 所有接口
- **数据验证** 前后端双重
- **SQL注入防护** 参数化查询
- **XSS防护** 输出转义

## 📈 监控与维护

### 1. 日志监控
```php
// 关键操作日志
Log::info('报价单审批提交', [
    'order_id' => $orderId,
    'user_id' => get_user_id(),
    'action' => 'submit_approval'
]);

// 错误日志
Log::error('报价单保存失败', [
    'order_id' => $orderId,
    'error' => $e->getMessage(),
    'trace' => $e->getTraceAsString()
]);
```

### 2. 性能监控
```javascript
// 前端性能监控
const performanceObserver = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    if (entry.duration > 1000) {
      console.warn('慢操作检测:', entry.name, entry.duration)
    }
  })
})
performanceObserver.observe({ entryTypes: ['measure'] })
```

### 3. 业务监控
```sql
-- 审批效率统计
SELECT
    DATE(submit_time) as date,
    COUNT(*) as total_submissions,
    AVG(TIMESTAMPDIFF(HOUR, submit_time, approval_time)) as avg_approval_hours
FROM crm_daily_price_order
WHERE approval_status = 2
GROUP BY DATE(submit_time);

-- 价格变动统计
SELECT
    supplier_id,
    COUNT(*) as change_count,
    AVG(ABS(change_rate)) as avg_change_rate
FROM crm_daily_price_item
WHERE ABS(price_change) > 0
GROUP BY supplier_id;
```

## 🎯 总结

这个完整的设计方案为每日报价单审批系统提供了全面的技术实现指导，包括：

1. **完整的数据库设计**：主从表结构，支持审批流程
2. **规范的业务流程**：状态驱动的审批流转
3. **成熟的技术架构**：前后端分离，组件化设计
4. **详细的实施计划**：分阶段开发，质量保证
5. **完善的监控体系**：日志、性能、业务监控

该方案确保了系统的可靠性、可维护性和用户体验，为下一步开发提供了坚实的技术支撑。
