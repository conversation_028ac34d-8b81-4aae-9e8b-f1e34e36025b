<?php
declare(strict_types=1);

namespace app\project\service;

use app\common\core\base\BaseService;
use app\project\model\ProjectMember;
use app\system\model\AdminModel;


/**
 * 项目成员表服务类
 */
class ProjectMemberService extends BaseService
{
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new ProjectMember();
		parent::__construct();
		$this->crudService->setEnableDataPermission(false);
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'project_id' => ['type' => 'eq'],
			
			'user_id' => ['type' => 'eq'],
			
			'role' => ['type' => 'eq'],
			
			'joined_at' => ['type' => 'between'],
		
		];
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			// 在这里定义验证规则
			// 例如：'username' => 'require|unique:project_member',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	/**
	 * 获取可添加的用户列表
	 * 权限控制：只有项目负责人或租户管理员才可以添加成员
	 *
	 * @param int $projectId 项目ID
	 * @return array
	 */
	public function getAvailableUsers(int $projectId): array
	{
		// 获取当前用户ID
		$currentUserId = get_user_id();
		
		// 检查权限：是否为租户管理员
		if (is_tenant_super_admin()) {
			// 租户管理员可以添加任何用户（排除已是项目成员的用户）
			return $this->getAllActiveUsers($projectId);
		}
		
		// 检查是否为项目负责人
		$projectService = ProjectProjectService::getInstance();
		if (!$projectService->isProjectOwner($projectId, $currentUserId)) {
			// 非项目负责人且非租户管理员，无权限添加成员
			return [];
		}
		
		// 项目负责人可以添加用户（排除已是项目成员的用户）
		return $this->getAllActiveUsers($projectId);
	}
	
	/**
	 * 获取所有活跃用户（排除已是项目成员的用户）
	 *
	 * @param int $projectId 项目ID
	 * @return array
	 */
	private function getAllActiveUsers(int $projectId = 0): array
	{
		$adminModel = new AdminModel();
		
		$query = $adminModel->where('status', 1)
		                    ->field('id as value, real_name as label, username, email,status,gender');
		
		// 如果指定了项目ID，排除已是项目成员的用户
		if ($projectId > 0) {
			$existingMemberIds = $this->model->where('project_id', $projectId)
			                                 ->column('user_id');
			
			if (!empty($existingMemberIds)) {
				$query->whereNotIn('id', $existingMemberIds);
			}
		}
		
		return $query->select()
		             ->toArray();
	}
} 