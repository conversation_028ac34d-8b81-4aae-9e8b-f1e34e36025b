<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\crm\model\CrmProductPrice;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 价格策略配置管理服务
 * 负责价格策略的动态配置管理
 */
class PriceStrategyConfigService extends BaseService
{
    /**
     * 缓存键前缀
     */
    const CACHE_PREFIX = 'crm:price_strategy_config:';
    const CACHE_TTL = 3600; // 1小时
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CrmProductPrice();
        parent::__construct();
    }
    
    /**
     * 获取策略配置列表
     * 
     * @param array $params 查询参数
     * @return array 配置列表
     */
    public function getStrategyConfigs(array $params = []): array
    {
        $tenantId = $this->getTenantId();
        
        $query = $this->model->where('tenant_id', $tenantId);
        
        // 按产品筛选
        if (!empty($params['product_id'])) {
            $query->where('product_id', $params['product_id']);
        }
        
        // 按策略类型筛选
        if (!empty($params['strategy_type'])) {
            $query->where('strategy_type', $params['strategy_type']);
        }
        
        // 按状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        $strategies = $query->order('strategy_type asc, id asc')
            ->select()
            ->toArray();
        
        // 添加策略类型名称
        foreach ($strategies as &$strategy) {
            $strategy['strategy_type_name'] = CrmProductPriceService::STRATEGY_TYPE_MAP[$strategy['strategy_type']] ?? '未知';
            $strategy['is_active'] = $this->isStrategyActive($strategy);
        }
        
        return $strategies;
    }
    
    /**
     * 批量启用/禁用策略
     * 
     * @param array $strategyIds 策略ID数组
     * @param int $status 状态 1=启用 0=禁用
     * @return bool 操作结果
     * @throws BusinessException
     */
    public function batchUpdateStatus(array $strategyIds, int $status): bool
    {
        if (empty($strategyIds)) {
            throw new BusinessException('策略ID不能为空');
        }
        
        if (!in_array($status, [0, 1])) {
            throw new BusinessException('状态值无效');
        }
        
        $tenantId = $this->getTenantId();
        $adminId = $this->getAdminId();
        
        Db::startTrans();
        try {
            $result = $this->model->where('tenant_id', $tenantId)
                ->whereIn('id', $strategyIds)
                ->update([
                    'status' => $status,
                    'updated_id' => $adminId,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            // 清除相关缓存
            $this->clearStrategyCache($strategyIds);
            
            Db::commit();
            
            Log::info('批量更新策略状态', [
                'strategy_ids' => $strategyIds,
                'status' => $status,
                'admin_id' => $adminId
            ]);
            
            return $result !== false;
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('批量更新策略状态失败', [
                'strategy_ids' => $strategyIds,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException('批量更新失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 调整策略优先级
     * 
     * @param array $priorityData 优先级数据 [['id' => 1, 'priority' => 1], ...]
     * @return bool 操作结果
     * @throws BusinessException
     */
    public function adjustPriority(array $priorityData): bool
    {
        if (empty($priorityData)) {
            throw new BusinessException('优先级数据不能为空');
        }
        
        $tenantId = $this->getTenantId();
        $adminId = $this->getAdminId();
        
        Db::startTrans();
        try {
            foreach ($priorityData as $item) {
                if (empty($item['id']) || !isset($item['priority'])) {
                    continue;
                }
                
                $this->model->where('tenant_id', $tenantId)
                    ->where('id', $item['id'])
                    ->update([
                        'priority' => (int)$item['priority'],
                        'updated_id' => $adminId,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }
            
            // 清除相关缓存
            $strategyIds = array_column($priorityData, 'id');
            $this->clearStrategyCache($strategyIds);
            
            Db::commit();
            
            Log::info('调整策略优先级', [
                'priority_data' => $priorityData,
                'admin_id' => $adminId
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('调整策略优先级失败', [
                'priority_data' => $priorityData,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException('调整优先级失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 复制策略到其他产品
     * 
     * @param int $sourceProductId 源产品ID
     * @param array $targetProductIds 目标产品ID数组
     * @param array $options 复制选项
     * @return array 复制结果
     * @throws BusinessException
     */
    public function copyStrategiesToProducts(int $sourceProductId, array $targetProductIds, array $options = []): array
    {
        if (empty($targetProductIds)) {
            throw new BusinessException('目标产品不能为空');
        }
        
        $tenantId = $this->getTenantId();
        $adminId = $this->getAdminId();
        
        // 获取源产品的策略
        $sourceStrategies = $this->model->where('tenant_id', $tenantId)
            ->where('product_id', $sourceProductId)
            ->where('status', 1)
            ->select()
            ->toArray();
        
        if (empty($sourceStrategies)) {
            throw new BusinessException('源产品没有可复制的策略');
        }
        
        $results = [];
        $copyCount = 0;
        
        Db::startTrans();
        try {
            foreach ($targetProductIds as $targetProductId) {
                $productResults = [];
                
                foreach ($sourceStrategies as $strategy) {
                    // 检查是否已存在相同策略
                    $exists = $this->model->where('tenant_id', $tenantId)
                        ->where('product_id', $targetProductId)
                        ->where('strategy_type', $strategy['strategy_type'])
                        ->where('strategy_target', $strategy['strategy_target'])
                        ->find();
                    
                    if ($exists && !($options['overwrite'] ?? false)) {
                        $productResults[] = [
                            'strategy_name' => $strategy['strategy_name'],
                            'status' => 'skipped',
                            'reason' => '策略已存在'
                        ];
                        continue;
                    }
                    
                    // 准备新策略数据
                    $newStrategy = $strategy;
                    unset($newStrategy['id']);
                    $newStrategy['product_id'] = $targetProductId;
                    $newStrategy['creator_id'] = $adminId;
                    $newStrategy['updated_id'] = $adminId;
                    $newStrategy['created_at'] = date('Y-m-d H:i:s');
                    $newStrategy['updated_at'] = date('Y-m-d H:i:s');
                    
                    if ($exists) {
                        // 覆盖现有策略
                        $exists->save($newStrategy);
                        $productResults[] = [
                            'strategy_name' => $strategy['strategy_name'],
                            'status' => 'updated',
                            'id' => $exists['id']
                        ];
                    } else {
                        // 创建新策略
                        $newId = $this->model->insertGetId($newStrategy);
                        $productResults[] = [
                            'strategy_name' => $strategy['strategy_name'],
                            'status' => 'created',
                            'id' => $newId
                        ];
                    }
                    
                    $copyCount++;
                }
                
                $results[$targetProductId] = $productResults;
            }
            
            Db::commit();
            
            Log::info('复制策略到其他产品', [
                'source_product_id' => $sourceProductId,
                'target_product_ids' => $targetProductIds,
                'copy_count' => $copyCount,
                'admin_id' => $adminId
            ]);
            
            return [
                'copy_count' => $copyCount,
                'results' => $results
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('复制策略失败', [
                'source_product_id' => $sourceProductId,
                'target_product_ids' => $targetProductIds,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException('复制策略失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 判断策略是否激活
     */
    private function isStrategyActive(array $strategy): bool
    {
        if ($strategy['status'] != 1) {
            return false;
        }
        
        $currentDate = date('Y-m-d');
        
        if (!empty($strategy['start_date']) && $strategy['start_date'] > $currentDate) {
            return false;
        }
        
        if (!empty($strategy['end_date']) && $strategy['end_date'] < $currentDate) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 清除策略缓存
     */
    private function clearStrategyCache(array $strategyIds): void
    {
        foreach ($strategyIds as $id) {
            $cacheKey = self::CACHE_PREFIX . "strategy:{$id}";
            Cache::delete($cacheKey);
        }
    }
    
    /**
     * 获取租户ID
     */
    private function getTenantId(): int
    {
        return request()->header('tenant-id', 0);
    }
    
    /**
     * 获取管理员ID
     */
    private function getAdminId(): int
    {
        return request()->header('admin-id', 0);
    }
}
