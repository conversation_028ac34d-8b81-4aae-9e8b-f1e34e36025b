<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\core\constants\CacheConstant;
use app\common\exception\ValidateFailedException;
use app\common\utils\CacheUtil;
use app\system\model\ConfigModel;

/**
 * 配置服务类
 */
class ConfigService extends BaseService
{
	
	protected string $cache_tag = CacheConstant::CONFIG_PREFIX;
	
	protected function __construct()
	{
		$this->model = new ConfigModel();
		parent::__construct();
	}
	
	/**
	 * @notes 设置配置值
	 * @param string $group
	 * @param array  $params
	 * @return bool
	 * @date  2021/12/27 15:00
	 */
	public function create(string $group, array $params): bool
	{
		if (empty($group) || empty($params)) {
			throw new ValidateFailedException('参数错误');
		}
		
		foreach ($params as $itemKey => $itemValue) {
			
			$info = $this->model->where([
				[
					'group',
					'=',
					$group
				],
				[
					'item_key',
					'=',
					$itemKey
				]
			])
			                                  ->findOrEmpty();
			
			if ($info->isEmpty()) {
				$info->group    = $group;
				$info->item_key = $itemKey;
			}
			
			$info->item_value = $itemValue;
			$res         = $info->save();
			if (!$res) {
				throw new ValidateFailedException('配置保存失败');
			}
		}
		
		// 缓存处理
		CacheUtil::delete($this->cache_tag . $group, false);
		
		// 返回原始值
		return true;
	}
	
	/**
	 * @notes 获取配置值
	 * @param string $group
	 * @param string $itemKey
	 * @return mixed
	 * @date  2021/7/15 15:16
	 */
	public function getInfo(string $group, string $itemKey = ''): mixed
	{
		$data = [];
		
		if (!empty($group)) {
			
			$cacheTag = $this->cache_tag . $group;
//			$data = CacheUtil::get($cacheTag, []);
			
			if (empty($data)) {
				$where = [
					[
						'group',
						'=',
						$group
					]
				];
				
				if (!empty($itemKey)) {
					$where[] = [
						'item_key',
						'=',
						$itemKey
					];
				}
				
				$data = $this->model->where($where)->column('item_value','item_key');
				if ($data) {
					CacheUtil::set($cacheTag, $data, null, false);
				}
			}
			
		}
		
		return $data;
	}
	
}