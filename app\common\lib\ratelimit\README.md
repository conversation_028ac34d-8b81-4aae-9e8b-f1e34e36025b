# 接口限流系统

基于ThinkPHP 8框架的多租户、多级别接口限流系统。

## 特性

- 支持固定窗口和滑动窗口限流算法
- 支持全局、租户级、用户级三种限流模式
- 可通过数据库配置不同租户的阈值和时间窗口
- 基于Redis的高性能限流实现
- 模块化设计，易于扩展
- 支持缓存配置，减少数据库压力

## 使用方法

### 1. 中间件方式

在路由或者控制器上使用中间件进行限流：

```php
// 在路由定义中使用
Route::group('api', function () {
    Route::get('users', 'user/index')->middleware(\app\common\lib\ratelimit\middleware\RateLimitMiddleware::class);
});

// 在控制器中使用
class UserController
{
    protected $middleware = [
        \app\common\lib\ratelimit\middleware\RateLimitMiddleware::class,
    ];
}
```

### 2. 手动检查方式

在代码中手动检查是否被限流：

```php
use app\common\lib\ratelimit\facade\RateLimit;

class UserService
{
    public function getList()
    {
        // 检查是否允许请求通过，如果超过限制会抛出RateLimitException异常
        try {
            RateLimit::check('user/list', $tenantId);
            
            // 业务逻辑...
            
        } catch (RateLimitException $e) {
            // 处理限流情况
            return [
                'code' => 429,
                'msg' => '请求频率超限',
                'data' => [
                    'remaining' => $e->getRemaining(),
                    'reset_time' => $e->getResetTime(),
                ]
            ];
        }
    }
}
```

### 3. 消息中间件中使用

在消息队列处理器中使用：

```php
use app\common\lib\ratelimit\facade\RateLimit;
use app\common\lib\ratelimit\exception\RateLimitException;

class MessageConsumer
{
    public function processMessage($message)
    {
        $tenantId = $message['tenant_id'] ?? null;
        
        try {
            // 对消息处理进行限流
            RateLimit::check('message/process', $tenantId);
            
            // 处理消息...
            
        } catch (RateLimitException $e) {
            // 将消息重新入队或延迟处理
            // ...
        }
    }
}
```

## 配置说明

### 数据库表

系统使用两个数据库表存储限流规则和日志：

1. `system_rate_limit_rule`: 存储限流规则配置
2. `system_rate_limit_log`: 存储限流日志记录

### 规则配置

可以通过后台管理系统添加、修改和删除限流规则。规则类型包括：

- `global`: 全局限流规则，应用于所有请求
- `tenant`: 租户级限流规则，仅应用于特定租户
- `user`: 用户级限流规则，应用于特定用户

### 算法选择

- `fixed`: 固定窗口算法，简单高效，但可能存在边界突刺问题
- `sliding`: 滑动窗口算法，更加平滑，避免边界突刺，但消耗更多资源

## 高级使用

### 自定义标识符

```php
use app\common\lib\ratelimit\facade\RateLimit;

// 创建自定义标识符
$ipIdentifier = RateLimit::createIpIdentifier();
$userIdentifier = RateLimit::createUserIdentifier();
$tenantIdentifier = RateLimit::createTenantIdentifier();

// 使用自定义标识符创建限流算法
$fixedWindow = RateLimit::createFixedWindow('custom_key', 100, 60, $ipIdentifier);
$slidingWindow = RateLimit::createSlidingWindow('custom_key', 100, 60, $userIdentifier);
```

### 清除规则缓存

```php
// 清除所有规则缓存
RateLimit::clearRulesCache();

// 清除特定类型规则缓存
RateLimit::clearRulesCache('global');

// 清除特定租户规则缓存
RateLimit::clearRulesCache('tenant', 1);

// 清除特定键名规则缓存
RateLimit::clearRulesCache('global', null, 'user/list');
``` 