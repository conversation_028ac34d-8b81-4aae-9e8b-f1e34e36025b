# 通知模板数据分析报告

## 📊 数据概览

**分析时间**: 2025-07-16  
**数据来源**: `notice_template` 表  
**总模板数**: 17个  
**有效模板数**: 16个 (1个已删除)

## 🔍 模板分类统计

### 按模块分类
| 模块 | 数量 | 模板编码 |
|------|------|----------|
| workflow | 6个 | workflow_task_approval, workflow_task_approved, workflow_task_urge, workflow_request, workflow_task_cc, workflow_task_transfer, workflow_task_terminated |
| crm | 5个 | crm_lead_convert, crm_customer_assign, crm_business_stage_change, crm_quotation_create, crm_contract_approval |
| system | 1个 | system_notice |
| approval | 1个 | approval_result (已删除) |
| attendance | 2个 | attendance_leave_apply, attendance_leave_result (已删除) |
| inventory | 2个 | inventory_low_stock, purchase_order_approval |
| finance | 2个 | finance_payment_notice, finance_invoice_notice |
| notice | 1个 | notice_announcement |

### 按状态分类
| 状态 | 数量 | 说明 |
|------|------|------|
| 启用 (status=1) | 16个 | 正常使用的模板 |
| 已删除 | 3个 | approval_result, attendance_leave_apply, attendance_leave_result |

## ✅ 正确配置的模板

### 1. workflow_task_approval (ID: 15) ⭐ **标准模板**
```json
{
  "code": "workflow_task_approval",
  "name": "工作流任务审批通知",
  "title": "您有一个待审批任务",
  "content": "标题：${流程标题}\n当前环节：${任务名称}\n提交人：${提交人}\n提交时间：${提交时间}\n紧急程度：${紧急程度}",
  "variables_config": {
    "variables": [
      {
        "name": "任务名称",
        "code": "task_name",
        "field": "task_name",
        "required": true,
        "description": "审批任务名称"
      },
      {
        "name": "流程标题",
        "code": "title",
        "field": "title",
        "required": true,
        "description": "工作流程标题"
      },
      {
        "name": "提交人",
        "code": "submitter_name",
        "field": "submitter_name",
        "required": true,
        "description": "流程提交人姓名"
      },
      {
        "name": "提交时间",
        "code": "created_at",
        "field": "created_at",
        "required": true,
        "description": "流程提交时间"
      },
      {
        "name": "详情链接",
        "code": "detail_url",
        "field": "detail_url",
        "required": false,
        "description": "流程详情页链接"
      }
    ]
  }
}
```
**评价**: ✅ 配置完整，变量规范，是标准的模板配置

### 2. workflow_task_approved (ID: 16) ⭐ **标准模板**
```json
{
  "code": "workflow_task_approved",
  "variables_config": {
    "variables": [
      {
        "name": "流程标题",
        "code": "title",
        "field": "title",
        "required": true,
        "description": "工作流程标题"
      },
      {
        "name": "审批结果",
        "code": "result",
        "field": "result",
        "required": true,
        "description": "审批结果：通过/拒绝"
      },
      {
        "name": "审批意见",
        "code": "opinion",
        "field": "opinion",
        "required": false,
        "description": "审批人的意见"
      },
      {
        "name": "审批人",
        "code": "approver_name",
        "field": "approver_name",
        "required": true,
        "description": "审批人姓名"
      },
      {
        "name": "审批时间",
        "code": "completed_at",
        "field": "completed_at",
        "required": true,
        "description": "审批完成时间"
      }
    ]
  }
}
```
**评价**: ✅ 配置完整，变量规范

### 3. workflow_task_cc (ID: 56) ⭐ **标准模板**
```json
{
  "code": "workflow_task_cc",
  "send_channels": "site,wework",
  "variables_config": {
    "variables": [
      {
        "name": "流程标题",
        "code": "title",
        "field": "title",
        "required": true,
        "description": "工作流程标题"
      },
      {
        "name": "提交人",
        "code": "submitter_name",
        "field": "submitter_name",
        "required": true,
        "description": "流程提交人姓名"
      },
      {
        "name": "节点名称",
        "code": "node_name",
        "field": "node_name",
        "required": false,
        "description": "当前节点名称"
      },
      {
        "name": "抄送时间",
        "code": "cc_time",
        "field": "cc_time",
        "required": true,
        "description": "抄送时间"
      }
    ]
  }
}
```
**评价**: ✅ 配置完整，支持多渠道发送

## ⚠️ 需要修复的模板

### 1. workflow_task_urge (ID: 17) ❌ **变量配置错误**
**问题**:
- 变量的 `code` 字段使用了中文，应该使用英文
- 变量配置格式不规范

**当前配置**:
```json
{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",  // ❌ 应该是英文
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    }
  ]
}
```

**修复建议**:
```sql
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "流程标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "任务名称",
      "code": "task_name",
      "field": "task_name",
      "required": true,
      "description": "待处理任务名称"
    },
    {
      "name": "催办人",
      "code": "urger_name",
      "field": "urger_name",
      "required": true,
      "description": "催办人姓名"
    },
    {
      "name": "催办时间",
      "code": "created_at",
      "field": "created_at",
      "required": true,
      "description": "催办时间"
    },
    {
      "name": "催办原因",
      "code": "reason",
      "field": "reason",
      "required": false,
      "description": "催办原因"
    }
  ]
}' WHERE id = 17;
```

### 2. workflow_task_transfer (ID: 59) ❌ **变量配置错误**
**问题**: 同样存在中文 `code` 字段问题

**修复建议**:
```sql
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "流程标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "节点名称",
      "code": "node_name",
      "field": "node_name",
      "required": true,
      "description": "转交任务节点名称"
    },
    {
      "name": "转交人",
      "code": "from_user",
      "field": "from_user",
      "required": true,
      "description": "转交人姓名"
    },
    {
      "name": "接收人",
      "code": "to_user",
      "field": "to_user",
      "required": true,
      "description": "接收人姓名"
    },
    {
      "name": "转交时间",
      "code": "transfer_time",
      "field": "transfer_time",
      "required": true,
      "description": "转交时间"
    },
    {
      "name": "详情链接",
      "code": "detail_url",
      "field": "detail_url",
      "required": false,
      "description": "任务详情页链接"
    }
  ]
}' WHERE id = 59;
```

### 3. workflow_task_terminated (ID: 60) ❌ **变量配置错误**
**问题**: 中文 `code` 字段，且变量名称不一致

**修复建议**:
```sql
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "流程标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "终止结果",
      "code": "result",
      "field": "result",
      "required": true,
      "description": "终止结果"
    },
    {
      "name": "提交时间",
      "code": "submit_time",
      "field": "submit_time",
      "required": false,
      "description": "流程提交时间"
    },
    {
      "name": "终止时间",
      "code": "terminate_time",
      "field": "terminate_time",
      "required": true,
      "description": "流程终止时间"
    },
    {
      "name": "终止人",
      "code": "terminate_by",
      "field": "terminate_by",
      "required": true,
      "description": "终止操作人"
    },
    {
      "name": "终止原因",
      "code": "reason",
      "field": "reason",
      "required": false,
      "description": "终止原因"
    },
    {
      "name": "详情链接",
      "code": "detail_url",
      "field": "detail_url",
      "required": false,
      "description": "流程详情页链接"
    }
  ]
}' WHERE id = 60;
```

### 4. workflow_request (ID: 18) ❌ **变量配置不完整**
**问题**: 
- `required` 字段为空字符串，应该是布尔值
- 缺少部分必要变量

**修复建议**:
```sql
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "审批事项",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "审批的事项标题"
    },
    {
      "name": "申请人",
      "code": "submitter_name",
      "field": "submitter_name",
      "required": true,
      "description": "申请人姓名"
    },
    {
      "name": "申请时间",
      "code": "created_at",
      "field": "created_at",
      "required": true,
      "description": "申请提交时间"
    }
  ]
}' WHERE id = 18;
```

## 🔧 批量修复SQL脚本

```sql
-- 修复 workflow_task_urge 模板
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "流程标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "任务名称",
      "code": "task_name",
      "field": "task_name",
      "required": true,
      "description": "待处理任务名称"
    },
    {
      "name": "催办人",
      "code": "urger_name",
      "field": "urger_name",
      "required": true,
      "description": "催办人姓名"
    },
    {
      "name": "催办时间",
      "code": "created_at",
      "field": "created_at",
      "required": true,
      "description": "催办时间"
    },
    {
      "name": "催办原因",
      "code": "reason",
      "field": "reason",
      "required": false,
      "description": "催办原因"
    }
  ]
}' WHERE id = 17;

-- 修复 workflow_task_transfer 模板
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "流程标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "节点名称",
      "code": "node_name",
      "field": "node_name",
      "required": true,
      "description": "转交任务节点名称"
    },
    {
      "name": "转交人",
      "code": "from_user",
      "field": "from_user",
      "required": true,
      "description": "转交人姓名"
    },
    {
      "name": "接收人",
      "code": "to_user",
      "field": "to_user",
      "required": true,
      "description": "接收人姓名"
    },
    {
      "name": "转交时间",
      "code": "transfer_time",
      "field": "transfer_time",
      "required": true,
      "description": "转交时间"
    },
    {
      "name": "详情链接",
      "code": "detail_url",
      "field": "detail_url",
      "required": false,
      "description": "任务详情页链接"
    }
  ]
}' WHERE id = 59;

-- 修复 workflow_task_terminated 模板
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "流程标题",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "终止结果",
      "code": "result",
      "field": "result",
      "required": true,
      "description": "终止结果"
    },
    {
      "name": "提交时间",
      "code": "submit_time",
      "field": "submit_time",
      "required": false,
      "description": "流程提交时间"
    },
    {
      "name": "终止时间",
      "code": "terminate_time",
      "field": "terminate_time",
      "required": true,
      "description": "流程终止时间"
    },
    {
      "name": "终止人",
      "code": "terminate_by",
      "field": "terminate_by",
      "required": true,
      "description": "终止操作人"
    },
    {
      "name": "终止原因",
      "code": "reason",
      "field": "reason",
      "required": false,
      "description": "终止原因"
    },
    {
      "name": "详情链接",
      "code": "detail_url",
      "field": "detail_url",
      "required": false,
      "description": "流程详情页链接"
    }
  ]
}' WHERE id = 60;

-- 修复 workflow_request 模板
UPDATE notice_template SET variables_config = '{
  "variables": [
    {
      "name": "审批事项",
      "code": "title",
      "field": "title",
      "required": true,
      "description": "审批的事项标题"
    },
    {
      "name": "申请人",
      "code": "submitter_name",
      "field": "submitter_name",
      "required": true,
      "description": "申请人姓名"
    },
    {
      "name": "申请时间",
      "code": "created_at",
      "field": "created_at",
      "required": true,
      "description": "申请提交时间"
    }
  ]
}' WHERE id = 18;
```

## 📋 总结建议

### ✅ 正确的模板 (13个)
- workflow_task_approval (ID: 15) ⭐
- workflow_task_approved (ID: 16) ⭐
- workflow_task_cc (ID: 56) ⭐
- system_notice (ID: 14)
- 所有CRM模板 (ID: 51-55)
- inventory和finance模板 (ID: 24-27)
- notice_announcement (ID: 28)

### ❌ 需要修复的模板 (4个)
- workflow_task_urge (ID: 17)
- workflow_request (ID: 18)
- workflow_task_transfer (ID: 59)
- workflow_task_terminated (ID: 60)

### 🗑️ 已删除的模板 (3个)
- approval_result (ID: 19)
- attendance_leave_apply (ID: 20)
- attendance_leave_result (ID: 21)

### 📝 规范要求
1. **变量code字段**: 必须使用英文，不能使用中文
2. **required字段**: 必须是布尔值 true/false，不能是空字符串
3. **变量命名**: 应该与实际业务数据字段对应
4. **描述信息**: 应该清晰说明变量的用途和格式

执行上述修复SQL后，所有模板将符合规范要求。
