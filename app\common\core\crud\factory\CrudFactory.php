<?php
declare(strict_types=1);

namespace app\common\core\crud\factory;

use app\common\core\crud\CrudService;
use app\common\exception\BusinessException;
use think\Model;

/**
 * CRUD工厂类
 * 用于创建不同类型的CRUD服务实例
 */
class CrudFactory
{
    /**
     * 已注册的CRUD服务类
     * @var array
     */
    protected static array $services = [];
    
    /**
     * 注册CRUD服务类
     *
     * @param string $key 服务标识
     * @param string $serviceClass 服务类名
     * @return void
     */
    public static function register(string $key, string $serviceClass): void
    {
        self::$services[$key] = $serviceClass;
    }
    
    /**
     * 创建标准CRUD服务实例
     *
     * @param Model $model 模型实例
     * @return CrudService CRUD服务实例
     */
    public static function create(Model $model): CrudService
    {
        return new CrudService($model);
    }
    
    /**
     * 创建自定义CRUD服务实例
     *
     * @param string $key 服务标识
     * @param Model|null $model 模型实例，可选
     * @return mixed 自定义CRUD服务实例
     * @throws BusinessException 当服务标识未注册时
     */
    public static function createCustom(string $key, ?Model $model = null): mixed
    {
        if (!isset(self::$services[$key])) {
            throw new BusinessException("未注册的CRUD服务: {$key}");
        }
        
        $serviceClass = self::$services[$key];
        
        if ($model) {
            return new $serviceClass($model);
        }
        
        return new $serviceClass();
    }
} 