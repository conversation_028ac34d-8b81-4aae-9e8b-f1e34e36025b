<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;
use app\ims\model\ImsSupplier;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 产品表模型 - 单规格产品设计
 */
class CrmProduct extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_product';
	
	// 字段类型转换
	protected $type   = [
		'category_id' => 'integer',
		'unit_id'     => 'integer',
		'supplier_id' => 'integer',
		'price'       => 'float',
		'cost'        => 'float',
		'status'      => 'integer',
	];
	
	protected $append = [
		'creator',
		'category',
		'unit',
		'supplier'
	];
	
	public function getImagesAttr($value, $data)
	{
		return empty($value)
			? []
			: explode(',', $value);
	}
	
	/**
	 * 关联产品分类
	 */
	public function category(): BelongsTo
	{
		return $this->belongsTo(CrmProductCategory::class, 'category_id', 'id')
		            ->bind([
			            'category_name' => 'name'
		            ]);
	}
	
	/**
	 * 关联产品单位
	 */
	public function unit(): BelongsTo
	{
		return $this->belongsTo(CrmProductUnit::class, 'unit_id', 'id')
		            ->bind([
			            'unit_name'
		            ]);
	}

	/**
	 * 关联供应商
	 */
	public function supplier(): BelongsTo
	{
		return $this->belongsTo(ImsSupplier::class, 'supplier_id', 'id')
		            ->bind([
			            'supplier_name' => 'name',
			            'supplier_code' => 'code',
			            'supplier_contact' => 'contact_name'
		            ]);
	}
	
	/**
	 * 关联合同产品
	 */
	public function products(): HasMany
	{
		return $this->hasMany(CrmContractProduct::class, 'product_id', 'id');
	}
	
	/**
	 * 获取产品完整信息（用于选择器显示）
	 */
	public function getFullNameAttr($value, $data)
	{
		$name = $data['name'] ?? '';
		$code = $data['code'] ?? '';
		$spec = $data['spec'] ?? '';
		
		$fullName = $name;
		if ($code) {
			$fullName .= " ({$code})";
		}
		if ($spec) {
			$fullName .= " - {$spec}";
		}
		
		return $fullName;
	}
	
	/**
	 * 获取状态文本
	 */
	public function getStatusTextAttr($value, $data)
	{
		$statusMap = [
			0 => '停用',
			1 => '启用'
		];
		return $statusMap[$data['status']] ?? '未知';
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			
			'category_id' => ['type' => 'eq'],

			'supplier_id' => ['type' => 'eq'],
			
			'unit_id' => ['type' => 'eq'],
			
			'name' => ['type' => 'like'],
			
			'code' => ['type' => 'like'],
			
			'price' => ['type' => 'between'],
			
			'cost' => ['type' => 'between'],
			
			'status' => ['type' => 'eq'],
		
		];
	}
}