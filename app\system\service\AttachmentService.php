<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\system\model\AttachmentModel;

/**
 * 附件服务类
 */
class AttachmentService extends BaseService
{
	
	protected function __construct()
	{
		$this->model = new AttachmentModel();
		parent::__construct();
	}
	
	/**
	 * 获取附件列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getList(array $params): array
	{
		
		$where = [];
		
		if (isset($params['cate_id']) && $params['cate_id'] >= 0) {
			$where[] = [
				'cate_id',
				'=',
				$params['cate_id']
			];
		}
		
		if (!empty($params['name'])) {
			$where[] = [
				'name|real_name',
				'like',
				'%' . $params['name'] . '%'
			];
		}
		
		if (!empty($params['storage'])) {
			$where[] = [
				'storage',
				'=',
				$params['storage']
			];
		}
		
		// 排序
		$order = 'created_at desc';
		
		// 分页
		$total = $this->getCrudService()
		              ->getCount($where);
		$page  = $params['page']
			? intval($params['page'])
			: 1;
		$limit = $params['limit']
			? intval($params['limit'])
			: 10;
		$list  = $this->getCrudService()
		              ->getPageList($where, $order, $page, $limit);
		
		return [
			'list'  => $list,
			'total' => $total,
			'page'  => $page,
			'limit' => $limit,
		];
	}
	
	public function getDetail(int $id)
	{
		$info = $this->getCrudService()
		             ->getOne([
			             [
				             'id',
				             '=',
				             $id
			             ],
		             ]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		
		return $info;
	}
	
	/**
	 * 删除附件
	 *
	 * @param int|array $id 附件ID
	 * @return bool
	 */
	public function delete(int|array $id): bool
	{
		$ids  = is_array($id)
			? $id
			: [$id];
		$list = $this->getCrudService()
		             ->getList([
			             [
				             'id',
				             'in',
				             $ids
			             ],
		             ]);
		
		// 执行删除
		return $list->delete();
	}
	
}