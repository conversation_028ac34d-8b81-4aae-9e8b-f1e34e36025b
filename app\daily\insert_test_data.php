<?php
// 插入每日报价测试数据的PHP脚本

require_once __DIR__ . '/../../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

try {
    echo "开始插入测试数据...\n";
    
    // 1. 昨天的已审批报价单（用于涨幅计算基准）
    echo "插入昨天的报价单...\n";
    $yesterdayOrderId = Db::name('daily_price_order')->insertGetId([
        'tenant_id' => 1,
        'price_date' => '2025-07-21',
        'total_items' => 2,
        'approval_status' => 2,
        'creator_id' => 1,
        'updated_id' => 1,
        'created_at' => '2025-07-21 09:00:00',
        'updated_at' => '2025-07-21 10:00:00'
    ]);
    echo "昨天报价单ID: {$yesterdayOrderId}\n";
    
    // 2. 昨天的报价明细（基准价格）
    echo "插入昨天的报价明细...\n";
    Db::name('daily_price_item')->insertAll([
        [
            'tenant_id' => 1,
            'order_id' => $yesterdayOrderId,
            'supplier_id' => 20,
            'product_id' => 9,
            'unit_price' => 100.00,
            'stock_price' => 95.00,
            'stock_qty' => 50.00,
            'policy_remark' => '昨日基准价格',
            'sort_order' => 1,
            'status' => 1,
            'created_at' => '2025-07-21 09:00:00',
            'updated_at' => '2025-07-21 09:00:00'
        ],
        [
            'tenant_id' => 1,
            'order_id' => $yesterdayOrderId,
            'supplier_id' => 20,
            'product_id' => 8,
            'unit_price' => 200.00,
            'stock_price' => 190.00,
            'stock_qty' => 30.00,
            'policy_remark' => '昨日基准价格',
            'sort_order' => 2,
            'status' => 1,
            'created_at' => '2025-07-21 09:00:00',
            'updated_at' => '2025-07-21 09:00:00'
        ]
    ]);
    echo "昨天报价明细插入完成\n";
    
    // 3. 今天的草稿报价单（用于测试重复创建限制）
    echo "插入今天的草稿报价单...\n";
    $todayOrderId = Db::name('daily_price_order')->insertGetId([
        'tenant_id' => 1,
        'price_date' => '2025-07-22',
        'total_items' => 1,
        'approval_status' => 0,
        'creator_id' => 1,
        'updated_id' => 1,
        'created_at' => '2025-07-22 08:00:00',
        'updated_at' => '2025-07-22 08:00:00'
    ]);
    echo "今天报价单ID: {$todayOrderId}\n";
    
    // 4. 今天的报价明细（用于测试涨幅计算）
    echo "插入今天的报价明细...\n";
    Db::name('daily_price_item')->insert([
        'tenant_id' => 1,
        'order_id' => $todayOrderId,
        'supplier_id' => 20,
        'product_id' => 9,
        'unit_price' => 120.00,
        'stock_price' => 95.00,
        'stock_qty' => 45.00,
        'policy_remark' => '今日报价',
        'sort_order' => 1,
        'status' => 1,
        'created_at' => '2025-07-22 08:00:00',
        'updated_at' => '2025-07-22 08:00:00'
    ]);
    echo "今天报价明细插入完成\n";
    
    // 5. 前天的已审批报价单（更多历史数据）
    echo "插入前天的报价单...\n";
    $dayBeforeOrderId = Db::name('daily_price_order')->insertGetId([
        'tenant_id' => 1,
        'price_date' => '2025-07-20',
        'total_items' => 3,
        'approval_status' => 2,
        'creator_id' => 1,
        'updated_id' => 1,
        'created_at' => '2025-07-20 09:00:00',
        'updated_at' => '2025-07-20 10:00:00'
    ]);
    echo "前天报价单ID: {$dayBeforeOrderId}\n";
    
    // 6. 前天的报价明细
    echo "插入前天的报价明细...\n";
    Db::name('daily_price_item')->insertAll([
        [
            'tenant_id' => 1,
            'order_id' => $dayBeforeOrderId,
            'supplier_id' => 20,
            'product_id' => 9,
            'unit_price' => 95.00,
            'stock_price' => 95.00,
            'stock_qty' => 55.00,
            'policy_remark' => '前天价格',
            'sort_order' => 1,
            'status' => 1,
            'created_at' => '2025-07-20 09:00:00',
            'updated_at' => '2025-07-20 09:00:00'
        ],
        [
            'tenant_id' => 1,
            'order_id' => $dayBeforeOrderId,
            'supplier_id' => 20,
            'product_id' => 8,
            'unit_price' => 210.00,
            'stock_price' => 190.00,
            'stock_qty' => 25.00,
            'policy_remark' => '前天价格',
            'sort_order' => 2,
            'status' => 1,
            'created_at' => '2025-07-20 09:00:00',
            'updated_at' => '2025-07-20 09:00:00'
        ],
        [
            'tenant_id' => 1,
            'order_id' => $dayBeforeOrderId,
            'supplier_id' => 12,
            'product_id' => 6,
            'unit_price' => 150.00,
            'stock_price' => 140.00,
            'stock_qty' => 20.00,
            'policy_remark' => '前天价格',
            'sort_order' => 3,
            'status' => 1,
            'created_at' => '2025-07-20 09:00:00',
            'updated_at' => '2025-07-20 09:00:00'
        ]
    ]);
    echo "前天报价明细插入完成\n";
    
    // 验证数据插入
    echo "\n验证数据插入结果:\n";
    $orders = Db::name('daily_price_order')
        ->where('tenant_id', 1)
        ->order('price_date', 'desc')
        ->select();
    
    foreach ($orders as $order) {
        $itemCount = Db::name('daily_price_item')
            ->where('order_id', $order['id'])
            ->count();
        
        $statusText = ['草稿', '审批中', '已通过', '已拒绝'][$order['approval_status']];
        echo "日期: {$order['price_date']}, 状态: {$statusText}, 明细数: {$itemCount}\n";
    }
    
    echo "\n测试数据插入完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
