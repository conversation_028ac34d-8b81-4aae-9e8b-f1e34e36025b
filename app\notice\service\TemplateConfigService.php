<?php
declare(strict_types=1);

namespace app\notice\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\utils\CacheUtil;
use app\notice\model\NoticeTemplateTenantConfigModel;
use think\facade\Log;

/**
 * todo 不使用model操作，使用封装的CrudServiceTrait中的对应方法操作
 * 租户消息模板配置服务类
 */
class TemplateConfigService extends BaseService
{
	use CrudServiceTrait;
	
    /**
     * 单例实例
     * 
     * @var TemplateConfigService|null
     */
    protected static ?TemplateConfigService $instance = null;
    
    /**
     * 配置缓存键前缀
     */
    protected const CACHE_PREFIX = 'notice:template:config:';
    
    /**
     * 构造函数
     */
    protected function __construct()
    {
        $this->model = new NoticeTemplateTenantConfigModel();
        parent::__construct();
    }
    
    /**
     * 获取单例
     *
     * @return static
     */
    public static function getInstance(): self
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }
        return static::$instance;
    }
    
    /**
     * 获取模板配置
     *
     * @param int $tenantId 租户ID
     * @param string $templateCode 模板编码
     * @return array|null 模板配置
     */
    public function getTemplateConfig(int $tenantId, string $templateCode): ?array
    {
        // 先从缓存获取
        $cacheKey = self::CACHE_PREFIX . $tenantId . ':' . $templateCode;
        $config = CacheUtil::get($cacheKey);
        
        if ($config === null) {
            // 缓存未命中，从数据库获取
            $configData = $this->model->where('tenant_id', $tenantId)
                                      ->where('template_code', $templateCode)
                                      ->find();
            
            if (!$configData) {
                return null;
            }
            
            $config = $configData->toArray();
            
            // 写入缓存
            CacheUtil::set($cacheKey, $config, 3600);
        }
        
        return $config;
    }
    
    /**
     * 获取模板启用的通道
     *
     * @param int $tenantId 租户ID
     * @param string $templateCode 模板编码
     * @return array 启用的通道列表
     */
    public function getEnabledChannels(int $tenantId, string $templateCode): array
    {
        $config = $this->getTemplateConfig($tenantId, $templateCode);
        
        // 如果配置不存在或模板被禁用，默认返回站内信
        if (!$config || !$config['is_enabled']) {
            return ['site'];
        }
        
        // 定义所有可用的通道
        $availableChannels = [
            'site' => 'site_enabled',
            'email' => 'email_enabled',
            'sms' => 'sms_enabled',
            'wework' => 'wework_enabled',
            'dingtalk' => 'dingtalk_enabled',
            'webhook' => 'webhook_enabled',
        ];
        
        // 过滤出启用的通道
        $enabledChannels = [];
        foreach ($availableChannels as $channel => $field) {
            if (!empty($config[$field])) {
                $enabledChannels[] = $channel;
            }
        }
        
        // 如果没有启用任何通道，默认返回站内信
        return !empty($enabledChannels) ? $enabledChannels : ['site'];
    }
    
    /**
     * 创建或更新模板配置
     *
     * @param array $data 配置数据
     * @return int|bool 成功返回ID，失败返回false
     */
    public function saveTemplateConfig(array $data): int|bool
    {
        try {
            $tenantId = $data['tenant_id'] ?? 0;
            $templateId = $data['template_id'] ?? 0;
            
            if (!$tenantId || !$templateId) {
                Log::error('保存模板配置失败: 租户ID或模板ID为空');
                return false;
            }
            
            // 检查是否已存在
            $existConfig = $this->model->where('tenant_id', $tenantId)
                                      ->where('template_id', $templateId)
                                      ->find();
            
            if ($existConfig) {
                $result = $existConfig->save($data);
                $id = $existConfig->id;
            } else {
                $config = $this->model->create($data);
                $result = $config->id > 0;
                $id = $config->id;
            }
            
            if ($result && isset($data['template_code'])) {
                // 清除缓存
                CacheUtil::delete(self::CACHE_PREFIX . $tenantId . ':' . $data['template_code']);
            }
            
            return $result ? $id : false;
        } catch (\Exception $e) {
            Log::error('保存模板配置失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 删除模板配置
     *
     * @param int $tenantId 租户ID
     * @param int $templateId 模板ID
     * @return bool 是否成功
     */
    public function deleteTemplateConfig(int $tenantId, int $templateId): bool
    {
        try {
            // 先获取配置，用于清除缓存
            $config = $this->model->where('tenant_id', $tenantId)
                                  ->where('template_id', $templateId)
                                  ->find();
            
            if (!$config) {
                return true;
            }
            
            $result = $config->delete();
            
            if ($result) {
                // 清除缓存
                CacheUtil::delete(self::CACHE_PREFIX . $tenantId . ':' . $config->template_code);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('删除模板配置失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 启用/禁用模板
     *
     * @param int $tenantId 租户ID
     * @param int $templateId 模板ID
     * @param bool $enabled 是否启用
     * @return bool 是否成功
     */
    public function setTemplateStatus(int $tenantId, int $templateId, bool $enabled): bool
    {
        try {
            // 先获取配置，用于清除缓存
            $config = $this->model->where('tenant_id', $tenantId)
                                  ->where('template_id', $templateId)
                                  ->find();
            
            if (!$config) {
                return false;
            }
            
            $result = $config->save(['is_enabled' => $enabled ? 1 : 0]);
            
            if ($result) {
                // 清除缓存
                CacheUtil::delete(self::CACHE_PREFIX . $tenantId . ':' . $config->template_code);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('设置模板状态失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 启用/禁用模板通道
     *
     * @param int $tenantId 租户ID
     * @param int $templateId 模板ID
     * @param string $channel 通道编码
     * @param bool $enabled 是否启用
     * @return bool 是否成功
     */
    public function setChannelStatus(int $tenantId, int $templateId, string $channel, bool $enabled): bool
    {
        try {
            // 先获取配置，用于清除缓存
            $config = $this->model->where('tenant_id', $tenantId)
                                  ->where('template_id', $templateId)
                                  ->find();
            
            if (!$config) {
                return false;
            }
            
            $channelField = $channel . '_enabled';
            
            if (!in_array($channelField, $this->model->getTableFields())) {
                return false;
            }
            
            $result = $config->save([$channelField => $enabled ? 1 : 0]);
            
            if ($result) {
                // 清除缓存
                CacheUtil::delete(self::CACHE_PREFIX . $tenantId . ':' . $config->template_code);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('设置通道状态失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取模板配置列表
     *
     * @param int $tenantId 租户ID
     * @param array $filters 过滤条件
     * @param array $pagination 分页参数
     * @return array 配置列表
     */
    public function getTemplateConfigList(int $tenantId, array $filters = [], array $pagination = []): array
    {
        try {
            $page = $pagination['page'] ?? 1;
            $pageSize = $pagination['page_size'] ?? 15;
            
            $query = $this->model->where('tenant_id', $tenantId);
            
            // 应用过滤条件
            if (!empty($filters['template_code'])) {
                $query->where('template_code', 'like', "%{$filters['template_code']}%");
            }
            
            if (isset($filters['is_enabled'])) {
                $query->where('is_enabled', $filters['is_enabled']);
            }
            
            // 关联模板信息
            $query->with(['template' => function ($q) {
                $q->field('id,code,name,title,module_code');
            }]);
            
            $total = $query->count();
            $list = $query->page($page, $pageSize)->select()->toArray();
            
            return [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize,
            ];
        } catch (\Exception $e) {
            Log::error('获取模板配置列表失败: ' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0,
                'page' => $pagination['page'] ?? 1,
                'page_size' => $pagination['page_size'] ?? 15,
            ];
        }
    }
    
    /**
     * 更新模板编码
     *
     * @param int $templateId 模板ID
     * @param string $oldCode 旧编码
     * @param string $newCode 新编码
     * @return bool 是否成功
     */
    public function updateTemplateCode(int $templateId, string $oldCode, string $newCode): bool
    {
        try {
            // 查找所有使用该模板ID的配置
            $configs = $this->model->where('template_id', $templateId)->select();
            
            if ($configs->isEmpty()) {
                return true;
            }
            
            // 批量更新
            foreach ($configs as $config) {
                // 同时更新编码和缓存
                $oldKey = self::CACHE_PREFIX . $config->tenant_id . ':' . $oldCode;
                
                // 更新数据
                $config->template_code = $newCode;
                $result = $config->save();
                
                // 删除旧缓存
                if ($result) {
                    CacheUtil::delete($oldKey);
                }
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('更新模板编码失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 根据模板ID删除所有租户配置
     *
     * @param int $templateId 模板ID
     * @return bool 是否成功
     */
    public function deleteTemplateConfigsByTemplateId(int $templateId): bool
    {
        try {
            // 先获取所有配置用于清理缓存
            $configs = $this->model->where('template_id', $templateId)->select();
            
            if ($configs->isEmpty()) {
                return true;
            }
            
            // 清理缓存
            foreach ($configs as $config) {
                $cacheKey = self::CACHE_PREFIX . $config->tenant_id . ':' . $config->template_code;
                CacheUtil::delete($cacheKey);
            }
            
            // 批量删除
            $result = $this->model->where('template_id', $templateId)->delete();
            
            return $result !== false;
        } catch (\Exception $e) {
            Log::error('删除模板关联的租户配置失败: ' . $e->getMessage());
            return false;
        }
    }
} 