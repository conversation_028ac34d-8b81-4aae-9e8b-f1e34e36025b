<?php

namespace app\common\core\traits\model;

/**
 * 重写 think\Model 的方法
 *
 * Trait RewriteTrait
 * @package catcher\traits\db
 */
trait RewriteTrait
{
	/**
	 * @return array
	 */
	protected function defaultHiddenFields(): array
	{
		return [$this->deleteTime];
	}
	
	/**
	 * @param array $hidden
	 * @param bool $merge
	 * @return $this
	 */
	public function hidden(array $hidden = [], bool $merge = false): static
	{
		// 如果需要合并或者当前隐藏字段为空
		if ($merge || !count($this->hidden)) {
			$this->hidden = array_merge($this->hidden, $hidden);
		} else {
			// 不合并，直接设置
			$this->hidden = $hidden;
		}
		
		return $this;
	}
}