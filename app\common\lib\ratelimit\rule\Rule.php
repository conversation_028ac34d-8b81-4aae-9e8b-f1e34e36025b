<?php
declare(strict_types=1);

namespace app\common\lib\ratelimit\rule;

use app\common\lib\ratelimit\algorithm\AlgorithmInterface;
use app\common\lib\ratelimit\identifier\IdentifierInterface;

/**
 * 限流规则基类
 */
abstract class Rule
{
    /**
     * 规则ID
     */
    protected int $id;
    
    /**
     * 规则名称
     */
    protected string $name;
    
    /**
     * 规则类型
     */
    protected string $type;
    
    /**
     * 键名
     */
    protected string $key;
    
    /**
     * 限流算法
     */
    protected AlgorithmInterface $algorithm;
    
    /**
     * 标识符
     */
    protected IdentifierInterface $identifier;
    
    /**
     * 限流阈值
     */
    protected int $limitCount;
    
    /**
     * 时间窗口(秒)
     */
    protected int $timeWindow;
    
    /**
     * 构造函数
     *
     * @param int $id 规则ID
     * @param string $name 规则名称
     * @param string $key 键名
     * @param AlgorithmInterface $algorithm 限流算法
     * @param IdentifierInterface $identifier 标识符
     * @param int $limitCount 限流阈值
     * @param int $timeWindow 时间窗口(秒)
     */
    public function __construct(
        int $id,
        string $name,
        string $key,
        AlgorithmInterface $algorithm,
        IdentifierInterface $identifier,
        int $limitCount,
        int $timeWindow
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->key = $key;
        $this->algorithm = $algorithm;
        $this->identifier = $identifier;
        $this->limitCount = $limitCount;
        $this->timeWindow = $timeWindow;
    }
    
    /**
     * 检查是否允许请求通过
     *
     * @return bool 是否允许通过
     */
    public function allow(): bool
    {
        return $this->algorithm->allow(
            $this->getKey(),
            $this->identifier->getValue(),
            $this->limitCount,
            $this->timeWindow
        );
    }
    
    /**
     * 获取剩余可用请求次数
     *
     * @return int 剩余可用请求次数
     */
    public function getRemainingLimit(): int
    {
        return $this->algorithm->getRemainingLimit(
            $this->getKey(),
            $this->identifier->getValue(),
            $this->limitCount,
            $this->timeWindow
        );
    }
    
    /**
     * 获取重置时间（秒）
     * 
     * @return int 重置时间
     */
    public function getResetTime(): int
    {
        return $this->algorithm->getResetTime(
            $this->getKey(),
            $this->identifier->getValue(),
            $this->timeWindow
        );
    }
    
    /**
     * 获取规则ID
     * 
     * @return int 规则ID
     */
    public function getId(): int
    {
        return $this->id;
    }
    
    /**
     * 获取规则名称
     * 
     * @return string 规则名称
     */
    public function getName(): string
    {
        return $this->name;
    }
    
    /**
     * 获取规则类型
     * 
     * @return string 规则类型
     */
    public function getType(): string
    {
        return $this->type;
    }
    
    /**
     * 获取完整键名
     * 
     * @return string 完整键名
     */
    abstract protected function getKey(): string;
} 