---
description: 
globs: 
alwaysApply: true
---
{
  "projectName": "Art Design Pro",
  "projectPath": "frontend",
  "framework": "Vue 3",
  "language": "TypeScript",
  "buildTool": "Vite",
  "uiLibrary": "Element Plus",
  "stateManagement": "Pinia",
  "router": "Vue Router",
  "cssPreprocessor": "SCSS",
  "features": [
    "多主题切换",
    "全局搜索",
    "锁屏功能",
    "多标签页",
    "全局面包屑",
    "多语言支持",
    "图标库",
    "富文本编辑器",
    "Echarts图表",
    "工具包",
    "网络异常处理",
    "权限管理",
    "移动端适配"
  ],
  "fileStructure": {
    "src": {
      "api": "API接口",
      "assets": "静态资源",
      "components": "公共组件",
      "composables": "组合式函数",
      "config": "配置文件",
      "directives": "自定义指令",
      "enums": "枚举定义",
      "language": "多语言配置",
      "plugins": "插件",
      "router": "路由配置",
      "store": "状态管理",
      "stores": "Pinia存储",
      "types": "类型定义",
      "utils": "工具函数",
      "views": "页面视图"
    }
  },
  "namingConventions": {
    "component": "PascalCase",
    "view": "PascalCase",
    "composable": "camelCase",
    "util": "camelCase",
    "store": "camelCase",
    "cssClass": "kebab-case"
  },
  "coding": {
    "indentation": 2,
    "lineLength": 100,
    "linters": ["ESLint", "Prettier", "Stylelint"],
    "commitConvention": "cz-git"
  }
}
