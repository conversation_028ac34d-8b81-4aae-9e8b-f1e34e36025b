<?php
declare(strict_types=1);

namespace app\ims\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\common\utils\NumberConverter;
use app\crm\model\CrmProduct;
use app\ims\model\ImsOutboundApproval;
use app\ims\model\ImsOutboundItem;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use think\facade\Log;

/**
 * 出库申请服务类
 */
class ImsOutboundApprovalService extends BaseService implements FormServiceInterface
{
	protected string $modelClass = ImsOutboundApproval::class;
	
	public function __construct()
	{
		$this->model = new ImsOutboundApproval();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = ImsOutboundApproval::findOrEmpty($id);
		
		if ($model->isEmpty()) {
			throw new BusinessException('出库申请记录不存在');
		}
		return $model->toArray();
	}
	
	/**
	 * 创建表单数据
	 */
	public function saveForm(array $data): array
	{
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			// 验证数据
			$validatedData = $this->validateFormData($formData, 'create');
			
			// 创建主记录
			$id = $this->model->saveByCreate($validatedData);
			
			// 处理明细数据
			if (!empty($formData['items'])) {
				$this->saveItems($id, $formData['items']);
			}
			
			// 返回完整数据
			$formData = $this->getFormData($id);
			
			return [
				$id,
				$formData
			];
			
		}
		catch (\Exception $e) {
			Log::error('出库申请创建失败: ' . $e->getMessage(), [
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('出库申请创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				throw new BusinessException('出库申请记录不存在');
			}
			
			// 验证数据
			$validatedData = $this->validateFormData($data, 'update');
			
			// 更新主记录
			$result = $model->saveByUpdate($validatedData);
			
			// 处理明细数据
			if (isset($data['items']) && is_array($data['items'])) {
				$this->saveItems($id, $data['items']);
			}
			
			return $result;
			
		}
		catch (\Exception $e) {
			Log::error('出库申请更新失败: ' . $e->getMessage(), [
				'id'    => $id,
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('出库申请更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		try {
			$updateData = ['approval_status' => $status];
			
			// 根据状态添加时间字段
			if ($status === 1) { // 审批中
				$updateData['submit_time'] = date('Y-m-d H:i:s');
			}
			elseif (in_array($status, [
				2,
				3,
				4,
				5,
				6
			])) { // 已完成状态
				$updateData['approval_time'] = date('Y-m-d H:i:s');
			}
			
			// 添加额外数据
			if (isset($extra['workflow_instance_id'])) {
				$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
			}
			
			return $this->model->where('id', $id)
			                   ->update($updateData) > 0;
			
		}
		catch (\Exception $e) {
			Log::error('出库申请状态更新失败: ' . $e->getMessage(), [
				'id'     => $id,
				'status' => $status,
				'extra'  => $extra,
				'trace'  => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 删除表单
	 */
	public function deleteForm(int $id): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			// 软删除主记录和明细记录
			$model->delete();
			
			// 删除明细记录
			$list = ImsOutboundItem::where('outbound_id', $id)
			                       ->field('id')
			                       ->select();
			
			if (!$list->isEmpty()) {
				$list->delete();
			}
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('出库申请删除失败: ' . $e->getMessage(), [
				'id'    => $id,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 获取流程实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		$outboundDate = $formData['outbound_date'] ?? '';
		$customerName = $formData['customer_name'] ?? '';
		return "出库申请-{$outboundDate}" . ($customerName
				? "-{$customerName}"
				: '');
	}
	
	/**
	 * 验证表单数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		// 基础验证
		
		$rules = [
			'dept_id'       => 'require',
			'outbound_date' => 'require|date',
			'customer_id'   => 'require',
		];
		
		$messages = [
			'dept_id.require'       => '请选择部门',
			'outbound_date.require' => '请选择出货日期',
			'outbound_date.date'    => '出货日期格式错误',
			'customer_id.require'   => '请选择客户',
		];
		
		$validate = validate($rules, $messages);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		// todo 最多为一项，后续推荐租户数据表中的字段
		if (empty($data['items'])) {
			throw new BusinessException('至少添加一条明细');
		}
		
		if (count($data['items']) > 1) {
			throw new BusinessException('最多添加一条明细');
		}
		
		// 移除不需要的字段
		unset($data['items']); // 明细数据单独处理
		
		return $data;
	}
	
	/**
	 * 工作流状态变更后的业务处理
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		try {
			// 根据不同状态执行相应的业务逻辑
			switch ($status) {
				case 2: // 已通过
					// 可以在这里添加库存扣减逻辑
					Log::info("出库申请已通过，业务ID: {$businessId}");
					break;
				
				case 3: // 已拒绝
					Log::info("出库申请已拒绝，业务ID: {$businessId}");
					break;
				
				default:
					break;
			}
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('出库申请工作流状态变更后处理失败: ' . $e->getMessage(), [
				'business_id' => $businessId,
				'status'      => $status,
				'extra'       => $extra,
				'trace'       => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 保存明细数据
	 */
	private function saveItems(int $outboundId, array $items): void
	{
		// 先删除原有明细
		$list = ImsOutboundItem::where('outbound_id', $outboundId)
		                       ->field('id')
		                       ->select();
		if (!$list->isEmpty()) {
			$list->delete();
		}
		
		$totalAmount   = 0;
		$totalQuantity = 0;
		
		// 保存新明细，至少需要有一项明细
		foreach ($items as $item) {
			// 验证必填字段
			$this->validateItemData($item);
			
			// 计算小计金额
			$quantity        = floatval($item['quantity'] ?? 0);
			$unitPrice       = floatval($item['unit_price'] ?? 0);
			$totalAmountItem = NumberConverter::safeMultiply($quantity, $unitPrice);
			
			$productInfo = CrmProduct::where('id', $item['product_id'])
			                         ->where('supplier_id', $item['supplier_id'])
			                         ->findOrEmpty();
			
			if ($productInfo->isEmpty()) {
				throw new BusinessException('产品信息不存在');
			}
			
			// 准备保存数据
			$unitInfo = $productInfo->unit;
			$itemData = [
				'outbound_id'  => $outboundId,
				'supplier_id'  => $item['supplier_id'],
				'product_id'   => $item['product_id'],
				'product_unit' => $unitInfo['unit_name'],
				'quantity'     => $quantity,
				'unit_price'   => $unitPrice,
				'total_amount' => $totalAmountItem,
			];
			
			$itemModel = new ImsOutboundItem();
			$itemModel->saveByCreate($itemData);
			
			// 累计总金额和总数量
			$totalAmount   = NumberConverter::safeAdd($totalAmount, $totalAmountItem);
			$totalQuantity = NumberConverter::safeAdd($totalQuantity, $quantity);
		}
		
		// 更新主表的总金额、总数量和大写金额
		$this->updateTotalAmounts($outboundId, $totalAmount, $totalQuantity);
	}
	
	/**
	 * 验证明细项数据
	 */
	private function validateItemData(array $item): void
	{
		// 验证供应商ID
		if (empty($item['supplier_id'])) {
			throw new BusinessException('请选择供应商');
		}
		
		// 验证产品ID
		if (empty($item['product_id'])) {
			throw new BusinessException('请选择产品');
		}
		
		// 验证出库数量
		if (empty($item['quantity']) || floatval($item['quantity']) <= 0) {
			throw new BusinessException('出库数量必须大于0');
		}
		
		// 验证单价
		if (empty($item['unit_price']) || floatval($item['unit_price']) <= 0) {
			throw new BusinessException('单价必须大于0');
		}
	}
	
	/**
	 * 更新总金额、总数量和大写金额
	 */
	private function updateTotalAmounts(int $outboundId, float $totalAmount, float $totalQuantity): void
	{
		$updateData = [
			'total_amount'       => $totalAmount,
			'total_quantity'     => $totalQuantity,
			'total_amount_upper' => NumberConverter::convertToChineseNumber($totalAmount),
		];
		
		$this->model->where('id', $outboundId)
		            ->update($updateData);
	}
}
