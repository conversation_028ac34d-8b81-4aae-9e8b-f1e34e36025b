<?php
declare(strict_types=1);

namespace app\finance\service;

use app\common\core\base\BaseService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use app\finance\model\FinanceExpenseReimbursement;
use app\finance\model\FinanceExpenseItem;
use app\common\exception\BusinessException;
use think\facade\Log;

/**
 * 报销申请服务类
 */
class FinanceExpenseReimbursementService extends BaseService implements FormServiceInterface
{
	protected string $modelClass = FinanceExpenseReimbursement::class;
	
	public function __construct()
	{
		$this->model = new FinanceExpenseReimbursement();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$model = $this->model->with([
			'submitter',
			'creator',
			'items'
		])
		                     ->findOrEmpty($id);
		
		if ($model->isEmpty()) {
			throw new BusinessException('报销申请记录不存在');
		}
		
		return $model->toArray();
	}
	
	/**
	 * 创建表单数据
	 */
	public function saveForm(array $data): array
	{
		$this->model->startTrans();
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			
			// 验证数据
			$validatedData = $this->validateFormData($formData, 'create');
			
			// 创建主记录
			$id = $this->model->saveByCreate($validatedData);
			
			// 保存明细数据
			if (!empty($formData['items'])) {
				$this->saveItems($id, $formData['items']);
			}
			
			$this->model->commit();
			
			// 返回完整数据
			$formData = $this->getFormData($id);
			
			return [
				$id,
				$formData
			];
			
		}
		catch (\Exception $e) {
			$this->model->rollback();
			Log::error('报销申请创建失败: ' . $e->getMessage(), [
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('报销申请创建失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		$this->model->startTrans();
		try {
			$model = $this->model->find($id);
			if (!$model) {
				throw new BusinessException('报销申请记录不存在');
			}
			
			// 验证数据
			$validatedData = $this->validateFormData($data, 'update');
			
			// 更新主记录
			$result = $model->saveByUpdate($validatedData);
			
			// 更新明细数据
			if (isset($data['items'])) {
				$this->updateItems($id, $data['items']);
			}
			
			$this->model->commit();
			return $result;
			
		}
		catch (\Exception $e) {
			$this->model->rollback();
			Log::error('报销申请更新失败: ' . $e->getMessage(), [
				'id'    => $id,
				'data'  => $data,
				'trace' => $e->getTraceAsString()
			]);
			throw new BusinessException('报销申请更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			$updateData = ['approval_status' => $status];
			
			// 处理额外数据
			if (!empty($extra['workflow_instance_id'])) {
				$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
			}
			
			if (!empty($extra['submit_time'])) {
				$updateData['submit_time'] = $extra['submit_time'];
			}
			
			if (!empty($extra['approval_time'])) {
				$updateData['approval_time'] = $extra['approval_time'];
			}
			
			return $model->saveByUpdate($updateData);
			
		}
		catch (\Exception $e) {
			Log::error('报销申请状态更新失败: ' . $e->getMessage(), [
				'id'     => $id,
				'status' => $status,
				'extra'  => $extra,
				'trace'  => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 删除表单
	 */
	public function deleteForm(int $id): bool
	{
		try {
			$model = $this->model->find($id);
			if (!$model) {
				return false;
			}
			
			// 软删除主记录
			$model->delete();
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('报销申请删除失败: ' . $e->getMessage(), [
				'id'    => $id,
				'trace' => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 获取流程实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		if (is_array($formData)) {
			$submitterName = $formData['submitter_name'] ?? '';
			$amount        = $formData['total_amount'] ?? 0;
			
			if ($amount) {
				return "{$submitterName}报销申请-{$amount}元";
			}
		}
		
		return '报销申请';
	}
	
	/**
	 * 验证表单数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		$rules = [
			'total_amount' => 'require|float|gt:0',
			'expense_type' => 'require|in:1,2,3,4,5,6,7',
		];
		
		$messages = [
			'total_amount.require' => '请输入报销金额',
			'total_amount.float'   => '报销金额格式错误',
			'total_amount.gt'      => '报销金额不能小于0',
			'expense_type.require' => '请选择报销类型',
			'expense_type.in'      => '报销类型错误',
		];
		
		$validate = validate($rules, $messages);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		// 验证明细项（如果有的话）
		if (!empty($data['items']) && is_array($data['items'])) {
			foreach ($data['items'] as $index => $item) {
				$rowNum = $index + 1;
				
				if (empty($item['description'])) {
					throw new BusinessException("第{$rowNum}行：请输入费用说明");
				}
				
				if (strlen($item['description']) > 500) {
					throw new BusinessException("第{$rowNum}行：费用说明不能超过500个字符");
				}
			}
		}
		
		return $data;
	}
	
	/**
	 * 工作流状态变更后的处理
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		// TODO: Implement afterWorkflowStatusChange() method.
		return true;
	}
	
	/**
	 * 保存报销明细
	 */
	private function saveItems(int $reimbursementId, array $items): void
	{
		foreach ($items as $item) {
			// 验证明细项数据
			$this->validateItemData($item);
			
			// 准备保存数据
			$itemData = [
				'reimbursement_id' => $reimbursementId,
				'description'      => $item['description'] ?? '',
			];
			
			$itemModel = new FinanceExpenseItem();
			$itemModel->saveByCreate($itemData);
		}
	}
	
	/**
	 * 更新报销明细
	 */
	private function updateItems(int $reimbursementId, array $items): void
	{
		// 删除原有明细
		$list = FinanceExpenseItem::where('reimbursement_id', $reimbursementId)
		                           ->select();
		if (!$list->isEmpty()) {
			$list->delete();
		}
		
		// 重新保存明细
		$this->saveItems($reimbursementId, $items);
	}
	
	/**
	 * 验证明细项数据
	 */
	private function validateItemData(array $item): void
	{
		if (empty($item['description'])) {
			throw new BusinessException('费用说明不能为空');
		}
		
		if (strlen($item['description']) > 500) {
			throw new BusinessException('费用说明不能超过500个字符');
		}
	}
}
