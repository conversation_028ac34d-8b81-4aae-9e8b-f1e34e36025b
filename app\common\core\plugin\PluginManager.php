<?php
declare(strict_types=1);

namespace app\common\core\plugin;

use app\common\core\contracts\PluginInterface;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

/**
 * 插件管理器
 */
class PluginManager
{
    /**
     * 插件目录
     * @var string
     */
    protected $pluginPath;
    
    /**
     * 已安装的插件列表
     * @var array
     */
    protected $plugins = [];
    
    /**
     * 插件缓存键
     * @var string
     */
    protected $cacheKey = 'system:plugins';
    
    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->pluginPath = app_path() . 'plugins' . DIRECTORY_SEPARATOR;
        $this->initPlugins();
    }
    
    /**
     * 初始化插件
     * @return void
     */
    public function initPlugins(): void
    {
        // 从缓存中获取插件列表
        $plugins = Cache::get($this->cacheKey);
        
        if ($plugins === null) {
            // 从数据库中获取插件列表
            $plugins = $this->getPluginsFromDb();
            
            // 缓存插件信息
            Cache::set($this->cacheKey, $plugins, 3600);
        }
        
        $this->plugins = $plugins;
    }
    
    /**
     * 加载所有已启用的插件
     * @return void
     */
    public function loadPlugins(): void
    {
        foreach ($this->plugins as $name => $plugin) {
            if (isset($plugin['status']) && $plugin['status'] == 1) {
                $this->loadPlugin($name);
            }
        }
    }
    
    /**
     * 加载指定插件
     * @param string $name 插件名称
     * @return bool
     */
    public function loadPlugin(string $name): bool
    {
        $pluginClass = "app\\plugins\\{$name}\\Plugin";
        
        if (!class_exists($pluginClass)) {
            return false;
        }
        
        try {
            $plugin = new $pluginClass();
            
            if ($plugin instanceof PluginInterface) {
                // 初始化插件
                $plugin->init();
                
                // 注册插件路由
                $this->registerPluginRoutes($plugin);
                
                return true;
            }
        } catch (\Exception $e) {
            Log::error("Load plugin {$name} failed: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * 注册插件路由
     * @param PluginInterface $plugin 插件对象
     * @return void
     */
    protected function registerPluginRoutes(PluginInterface $plugin): void
    {
        $routes = $plugin->getRoutes();
        
        if (empty($routes)) {
            return;
        }
        
        // 获取路由对象
        $router = app('route');
        
        foreach ($routes as $route) {
            if (isset($route['method']) && isset($route['rule']) && isset($route['action'])) {
                $method = strtolower($route['method']);
                $rule = $route['rule'];
                $action = $route['action'];
                $options = $route['options'] ?? [];
                
                if ($method === 'any') {
                    $router->any($rule, $action, $options);
                } else {
                    $router->rule($rule, $action, $method, $options);
                }
            }
        }
    }
    
    /**
     * 获取所有插件信息
     * @return array
     */
    public function getAllPlugins(): array
    {
        $this->scanPlugins();
        return $this->plugins;
    }
    
    /**
     * 获取指定插件信息
     * @param string $name 插件名称
     * @return array|null
     */
    public function getPlugin(string $name): ?array
    {
        return $this->plugins[$name] ?? null;
    }
    
    /**
     * 获取已启用的插件
     * @return array
     */
    public function getEnabledPlugins(): array
    {
        $enabledPlugins = [];
        
        foreach ($this->plugins as $name => $plugin) {
            if (isset($plugin['status']) && $plugin['status'] == 1) {
                $enabledPlugins[$name] = $plugin;
            }
        }
        
        return $enabledPlugins;
    }
    
    /**
     * 扫描插件目录
     * @return void
     */
    public function scanPlugins(): void
    {
        // 从数据库获取插件列表
        $installedPlugins = $this->getPluginsFromDb();
        
        // 获取插件目录下的所有插件
        $directories = glob($this->pluginPath . '*', GLOB_ONLYDIR);
        
        foreach ($directories as $dir) {
            $name = basename($dir);
            $pluginClass = "app\\plugins\\{$name}\\Plugin";
            
            // 如果插件主类不存在，则跳过
            if (!file_exists($dir . DIRECTORY_SEPARATOR . 'Plugin.php')) {
                continue;
            }
            
            // 获取插件信息
            try {
                if (class_exists($pluginClass)) {
                    $plugin = new $pluginClass();
                    
                    if ($plugin instanceof PluginInterface) {
                        $info = $plugin->getInfo();
                        
                        // 合并安装状态和启用状态
                        if (isset($installedPlugins[$name])) {
                            $info['installed'] = true;
                            $info['status'] = $installedPlugins[$name]['status'];
                        } else {
                            $info['installed'] = false;
                            $info['status'] = 0;
                        }
                        
                        $this->plugins[$name] = $info;
                    }
                }
            } catch (\Exception $e) {
                Log::error("Scan plugin {$name} failed: " . $e->getMessage());
            }
        }
        
        // 更新缓存
        Cache::set($this->cacheKey, $this->plugins, 3600);
    }
    
    /**
     * 安装插件
     * @param string $name 插件名称
     * @return bool
     */
    public function install(string $name): bool
    {
        $pluginClass = "app\\plugins\\{$name}\\Plugin";
        
        if (!class_exists($pluginClass)) {
            return false;
        }
        
        try {
            $plugin = new $pluginClass();
            
            if ($plugin instanceof PluginInterface) {
                // 安装插件
                if ($plugin->install()) {
                    $info = $plugin->getInfo();
                    
                    // 记录到数据库
                    $this->savePluginToDb($name, $info);
                    
                    // 更新插件缓存
                    $this->plugins[$name] = array_merge($info, ['installed' => true, 'status' => 1]);
                    Cache::set($this->cacheKey, $this->plugins, 3600);
                    
                    return true;
                }
            }
        } catch (\Exception $e) {
            Log::error("Install plugin {$name} failed: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * 卸载插件
     * @param string $name 插件名称
     * @return bool
     */
    public function uninstall(string $name): bool
    {
        $pluginClass = "app\\plugins\\{$name}\\Plugin";
        
        if (!class_exists($pluginClass)) {
            return false;
        }
        
        try {
            $plugin = new $pluginClass();
            
            if ($plugin instanceof PluginInterface) {
                // 卸载插件
                if ($plugin->uninstall()) {
                    // 从数据库中删除
                    $this->removePluginFromDb($name);
                    
                    // 更新插件缓存
                    if (isset($this->plugins[$name])) {
                        $this->plugins[$name]['installed'] = false;
                        $this->plugins[$name]['status'] = 0;
                    }
                    
                    Cache::set($this->cacheKey, $this->plugins, 3600);
                    
                    return true;
                }
            }
        } catch (\Exception $e) {
            Log::error("Uninstall plugin {$name} failed: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * 启用插件
     * @param string $name 插件名称
     * @return bool
     */
    public function enable(string $name): bool
    {
        $pluginClass = "app\\plugins\\{$name}\\Plugin";
        
        if (!class_exists($pluginClass)) {
            return false;
        }
        
        try {
            $plugin = new $pluginClass();
            
            if ($plugin instanceof PluginInterface) {
                // 启用插件
                if ($plugin->enable()) {
                    // 更新数据库状态
                    $this->updatePluginStatus($name, 1);
                    
                    // 更新插件缓存
                    if (isset($this->plugins[$name])) {
                        $this->plugins[$name]['status'] = 1;
                    }
                    
                    Cache::set($this->cacheKey, $this->plugins, 3600);
                    
                    return true;
                }
            }
        } catch (\Exception $e) {
            Log::error("Enable plugin {$name} failed: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * 禁用插件
     * @param string $name 插件名称
     * @return bool
     */
    public function disable(string $name): bool
    {
        $pluginClass = "app\\plugins\\{$name}\\Plugin";
        
        if (!class_exists($pluginClass)) {
            return false;
        }
        
        try {
            $plugin = new $pluginClass();
            
            if ($plugin instanceof PluginInterface) {
                // 禁用插件
                if ($plugin->disable()) {
                    // 更新数据库状态
                    $this->updatePluginStatus($name, 0);
                    
                    // 更新插件缓存
                    if (isset($this->plugins[$name])) {
                        $this->plugins[$name]['status'] = 0;
                    }
                    
                    Cache::set($this->cacheKey, $this->plugins, 3600);
                    
                    return true;
                }
            }
        } catch (\Exception $e) {
            Log::error("Disable plugin {$name} failed: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * 从数据库中获取已安装的插件列表
     * @return array
     */
    protected function getPluginsFromDb(): array
    {
        $plugins = [];
        
        try {
            $list = Db::name('system_plugin')->where('deleted_at', null)->select()->toArray();
            
            foreach ($list as $item) {
                $plugins[$item['name']] = [
                    'id'          => $item['id'],
                    'name'        => $item['name'],
                    'title'       => $item['title'],
                    'description' => $item['description'],
                    'author'      => $item['author'],
                    'version'     => $item['version'],
                    'status'      => $item['status'],
                    'installed'   => true,
                ];
            }
        } catch (\Exception $e) {
            Log::error('Get plugins from database failed: ' . $e->getMessage());
        }
        
        return $plugins;
    }
    
    /**
     * 保存插件信息到数据库
     * @param string $name 插件名称
     * @param array $info 插件信息
     * @return bool
     */
    protected function savePluginToDb(string $name, array $info): bool
    {
        try {
            $data = [
                'name'        => $name,
                'title'       => $info['title'] ?? '',
                'description' => $info['description'] ?? '',
                'author'      => $info['author'] ?? '',
                'version'     => $info['version'] ?? '1.0.0',
                'config'      => json_encode($info['config'] ?? []),
                'status'      => 1,
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ];
            
            Db::name('system_plugin')->insert($data);
            return true;
        } catch (\Exception $e) {
            Log::error('Save plugin to database failed: ' . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * 从数据库中删除插件信息
     * @param string $name 插件名称
     * @return bool
     */
    protected function removePluginFromDb(string $name): bool
    {
        try {
            Db::name('system_plugin')->where('name', $name)->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Remove plugin from database failed: ' . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * 更新插件状态
     * @param string $name 插件名称
     * @param int $status 状态（0禁用，1启用）
     * @return bool
     */
    protected function updatePluginStatus(string $name, int $status): bool
    {
        try {
            Db::name('system_plugin')
                ->where('name', $name)
                ->update([
                    'status'     => $status,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Update plugin status failed: ' . $e->getMessage());
        }
        
        return false;
    }
} 