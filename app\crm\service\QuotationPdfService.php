<?php
declare(strict_types=1);

namespace app\crm\service;

use app\common\exception\BusinessException;
use think\facade\Log;

/**
 * 报价单PDF导出服务
 * 负责将报价单导出为PDF格式
 */
class QuotationPdfService
{
    /**
     * 模板服务
     */
    private $templateService;
    
    /**
     * 报价单服务
     */
    private $quotationService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->templateService = new QuotationTemplateService();
        $this->quotationService = CrmQuotationService::getInstance();
    }
    
    /**
     * 导出报价单为PDF
     * 
     * @param int $quotationId 报价单ID
     * @param int|null $templateId 模板ID，为空时使用默认模板
     * @param array $options 导出选项
     * @return array 导出结果
     * @throws BusinessException
     */
    public function exportToPdf(int $quotationId, ?int $templateId = null, array $options = []): array
    {
        try {
            // 1. 获取报价单详情
            $quotationData = $this->getQuotationData($quotationId);
            
            // 2. 获取模板
            if ($templateId) {
                $template = $this->templateService->getTemplateById($templateId);
                if (!$template) {
                    throw new BusinessException('指定的模板不存在');
                }
            } else {
                $template = $this->templateService->getDefaultTemplate();
                if (!$template) {
                    throw new BusinessException('没有可用的模板');
                }
            }
            
            // 3. 渲染HTML内容
            $htmlContent = $this->templateService->renderTemplate($template['id'], $quotationData);
            
            // 4. 转换为PDF
            $pdfPath = $this->convertHtmlToPdf($htmlContent, $quotationData, $options);
            
            Log::info('导出报价单PDF成功', [
                'quotation_id' => $quotationId,
                'template_id' => $template['id'],
                'pdf_path' => $pdfPath
            ]);
            
            return [
                'quotation_id' => $quotationId,
                'quotation_no' => $quotationData['quotation_no'],
                'pdf_path' => $pdfPath,
                'file_name' => $this->generateFileName($quotationData),
                'template_used' => $template['template_name']
            ];
            
        } catch (\Exception $e) {
            Log::error('导出报价单PDF失败', [
                'quotation_id' => $quotationId,
                'template_id' => $templateId,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException('导出PDF失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 批量导出报价单PDF
     * 
     * @param array $quotationIds 报价单ID数组
     * @param int|null $templateId 模板ID
     * @param array $options 导出选项
     * @return array 导出结果
     */
    public function batchExportToPdf(array $quotationIds, ?int $templateId = null, array $options = []): array
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;
        
        foreach ($quotationIds as $quotationId) {
            try {
                $result = $this->exportToPdf((int)$quotationId, $templateId, $options);
                $results[] = array_merge($result, ['status' => 'success']);
                $successCount++;
            } catch (\Exception $e) {
                $results[] = [
                    'quotation_id' => $quotationId,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
                $failCount++;
            }
        }
        
        return [
            'total' => count($quotationIds),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results
        ];
    }
    
    /**
     * 获取报价单数据
     * 
     * @param int $quotationId 报价单ID
     * @return array 报价单数据
     * @throws BusinessException
     */
    private function getQuotationData(int $quotationId): array
    {
        // 获取报价单基础信息
        $quotation = $this->quotationService->detail($quotationId);
        if (!$quotation) {
            throw new BusinessException('报价单不存在');
        }
        
        // 获取客户信息
        $customer = \think\facade\Db::name('crm_customer')
            ->where('id', $quotation['customer_id'])
            ->find();
        
        // 获取联系人信息
        $contact = \think\facade\Db::name('crm_contact')
            ->where('id', $quotation['contact_id'])
            ->find();
        
        // 获取产品明细
        $products = \think\facade\Db::name('crm_quotation_product')
            ->alias('qp')
            ->leftJoin('crm_product p', 'qp.product_id = p.id')
            ->where('qp.quotation_id', $quotationId)
            ->field('qp.*, p.product_name, p.product_code, p.unit, p.description')
            ->select()
            ->toArray();
        
        // 组装数据
        $quotationData = $quotation;
        $quotationData['customer_name'] = $customer['customer_name'] ?? '';
        $quotationData['customer_address'] = $customer['address'] ?? '';
        $quotationData['customer_phone'] = $customer['phone'] ?? '';
        $quotationData['contact_name'] = $contact['contact_name'] ?? '';
        $quotationData['contact_phone'] = $contact['phone'] ?? '';
        $quotationData['contact_email'] = $contact['email'] ?? '';
        $quotationData['products'] = $products;
        
        return $quotationData;
    }
    
    /**
     * 将HTML转换为PDF
     * 
     * @param string $htmlContent HTML内容
     * @param array $quotationData 报价单数据
     * @param array $options 选项
     * @return string PDF文件路径
     */
    private function convertHtmlToPdf(string $htmlContent, array $quotationData, array $options = []): string
    {
        // 这里使用简化的PDF生成方案
        // 实际项目中可以使用 TCPDF、DOMPDF 或 wkhtmltopdf 等库
        
        $fileName = $this->generateFileName($quotationData);
        $pdfDir = runtime_path('pdf');
        
        if (!is_dir($pdfDir)) {
            mkdir($pdfDir, 0755, true);
        }
        
        $pdfPath = $pdfDir . DIRECTORY_SEPARATOR . $fileName;
        
        // 简化版PDF生成（实际项目中需要使用专业的PDF库）
        $this->generateSimplePdf($htmlContent, $pdfPath, $options);
        
        return $pdfPath;
    }
    
    /**
     * 生成简化版PDF
     * 
     * @param string $htmlContent HTML内容
     * @param string $pdfPath PDF路径
     * @param array $options 选项
     */
    private function generateSimplePdf(string $htmlContent, string $pdfPath, array $options = []): void
    {
        // 这里是简化的实现，实际项目中需要使用专业的PDF库
        // 例如：TCPDF、DOMPDF、wkhtmltopdf等
        
        // 示例：使用TCPDF的基本实现
        /*
        require_once 'vendor/tecnickcom/tcpdf/tcpdf.php';
        
        $pdf = new \TCPDF();
        $pdf->SetCreator('CRM System');
        $pdf->SetAuthor('CRM System');
        $pdf->SetTitle('报价单');
        $pdf->SetSubject('报价单');
        
        $pdf->AddPage();
        $pdf->writeHTML($htmlContent, true, false, true, false, '');
        $pdf->Output($pdfPath, 'F');
        */
        
        // 临时方案：将HTML保存为文件（实际项目中需要替换为真正的PDF生成）
        file_put_contents($pdfPath . '.html', $htmlContent);
        
        // 创建一个空的PDF文件作为占位符
        file_put_contents($pdfPath, "PDF placeholder for quotation");
    }
    
    /**
     * 生成文件名
     * 
     * @param array $quotationData 报价单数据
     * @return string 文件名
     */
    private function generateFileName(array $quotationData): string
    {
        $quotationNo = $quotationData['quotation_no'] ?? 'UNKNOWN';
        $date = date('Ymd');
        
        return "quotation_{$quotationNo}_{$date}.pdf";
    }
    
    /**
     * 获取支持的导出格式
     * 
     * @return array 支持的格式
     */
    public function getSupportedFormats(): array
    {
        return [
            'pdf' => [
                'name' => 'PDF格式',
                'extension' => 'pdf',
                'mime_type' => 'application/pdf'
            ],
            'html' => [
                'name' => 'HTML格式',
                'extension' => 'html',
                'mime_type' => 'text/html'
            ]
        ];
    }
    
    /**
     * 预览报价单
     * 
     * @param int $quotationId 报价单ID
     * @param int|null $templateId 模板ID
     * @return string HTML内容
     * @throws BusinessException
     */
    public function previewQuotation(int $quotationId, ?int $templateId = null): string
    {
        // 获取报价单数据
        $quotationData = $this->getQuotationData($quotationId);
        
        // 获取模板
        if ($templateId) {
            $template = $this->templateService->getTemplateById($templateId);
            if (!$template) {
                throw new BusinessException('指定的模板不存在');
            }
        } else {
            $template = $this->templateService->getDefaultTemplate();
            if (!$template) {
                throw new BusinessException('没有可用的模板');
            }
        }
        
        // 渲染HTML内容
        return $this->templateService->renderTemplate($template['id'], $quotationData);
    }
}
